# Script de Migração de Artigos da Intercom

Este script JavaScript migra artigos da Intercom de um ambiente para outro, incluindo o gerenciamento automático de collections com hierarquia.

## Funcionalidades

✅ **Migração de Artigos**: Busca e republica artigos específicos
✅ **Gerenciamento de Collections**: Identifica, cria e mapeia collections automaticamente
✅ **Hierarquia de Collections**: Respeita a ordem de criação (pais antes de filhos)
✅ **Mapeamento por Título**: Usa títulos como identificador único entre ambientes
✅ **Substituição de Texto**: Substitui globalmente termos específicos (ex: "Friday")
✅ **Preservação de Capitalização**: Mantém maiúsculas/minúsculas originais
✅ **Logging Detalhado**: Acompanhe o progresso em tempo real
✅ **Relatório Completo**: Resumo final com sucessos e erros
✅ **Rate Limiting**: Pausas automáticas para evitar limites da API

## Como Usar

### 1. Configurar Tokens de Autorização

Edite o arquivo `intercom-migration.js` e substitua os tokens:

```javascript
const GET_AUTH_TOKEN = 'Bearer SEU_TOKEN_DE_GET_AQUI';
const POST_AUTH_TOKEN = 'Bearer SEU_TOKEN_DE_POST_AQUI';
```

**Onde obter os tokens:**
- Acesse o [Developer Hub da Intercom](https://app.intercom.com/a/apps/_/developer-hub)
- Crie ou acesse sua aplicação
- Copie os tokens de autorização necessários

### 2. Executar o Script

```bash
node intercom-migration.js
```

## Fluxo de Execução

1. **📖 Busca de Artigos**: Obtém todos os artigos especificados
2. **📁 Processamento de Collections**: 
   - Identifica collections necessárias
   - Busca dados das collections originais
   - Cria hierarquia respeitando dependências
3. **📝 Migração de Artigos**: Publica artigos com collections mapeadas
4. **📊 Relatório Final**: Exibe estatísticas e resultados

## Estrutura dos Dados

### Collections
- **Identificação**: Por título (campo único entre ambientes)
- **Hierarquia**: Collections pai são criadas antes das filhas
- **Mapeamento**: ID original → ID novo armazenado em memória

### Artigos
- **Parent Mapping**: IDs de collections são automaticamente mapeados
- **Preservação**: Todos os campos originais são mantidos
- **Validação**: Verificação de sucesso na publicação

## Exemplo de Saída

```
🚀 Iniciando migração de artigos da Intercom...

🔍 Buscando todos os artigos para identificar collections...

📖 Buscando artigo 6161949...
✅ Artigo 6161949 obtido: "Como fazer pagamento"

📁 Processando collections...

🔍 Buscando collection 123456...
✅ Collection encontrada: "Pagamentos"

📝 Criando collection "Pagamentos"...
✅ Collection "Pagamentos" criada com ID 789012

📝 Iniciando migração dos artigos...

📝 Publicando artigo "Como fazer pagamento"...
✅ Artigo "Como fazer pagamento" publicado com sucesso!

📊 RELATÓRIO FINAL DA MIGRAÇÃO
================================
📁 Collections criadas: 1
✅ Artigos migrados com sucesso: 1
❌ Artigos com erro: 0

📁 COLLECTIONS CRIADAS:
  - "Pagamentos": 123456 → 789012

🎉 ARTIGOS MIGRADOS:
  - 6161949 → 345678: "Como fazer pagamento"

🏁 Migração finalizada!
```

## Tratamento de Erros

- **Rate Limiting**: Pausas automáticas entre requisições
- **Validação**: Verificação de status HTTP em todas as operações
- **Logging**: Erros detalhados com contexto
- **Continuidade**: Falhas individuais não interrompem o processo

## Personalização

### Adicionar Novos Artigos
Edite a lista `articleIds` no início do arquivo:

```javascript
const articleIds = [
  "6161949",
  "6125691",
  // Adicione novos IDs aqui
  "SEU_NOVO_ID"
];
```

### Ajustar Rate Limiting
Modifique os valores de `setTimeout`:

```javascript
// Pausa entre requisições (padrão: 1000ms)
await new Promise(resolve => setTimeout(resolve, 1000));
```

## Requisitos

- Node.js (versão 12 ou superior)
- Tokens de API da Intercom com permissões adequadas
- Acesso aos ambientes de origem e destino

## Limitações

- Funciona apenas com HTTPS nativo do Node.js (sem dependências externas)
- Requer tokens de API válidos para ambos os ambientes
- Collections devem ter títulos únicos para mapeamento correto

## Suporte

Para dúvidas ou problemas:
1. Verifique os logs detalhados do script
2. Confirme se os tokens têm as permissões necessárias
3. Teste com um pequeno conjunto de artigos primeiro
