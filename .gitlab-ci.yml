image: ${CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX}/amazoncorretto:17

default:
  tags:
    - friday

include:
  - project: "via1/gitlab-ci-environment"
    file: "/envs/all-bill-payment-service.yml"
    ref: "main"
  - template: Code-Quality.gitlab-ci.yml
  - template: Workflows/MergeRequest-Pipelines.gitlab-ci.yml
  - local: /.gitlab-ci-interruptible.yml
    rules:
      - if: $CI_COMMIT_BRANCH != "main"

code_quality:
  interruptible: true
  image: ${CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX}/docker:20.10.12
  services:
    - name: ${CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX}/docker:20.10.12-dind
      command: [ '--tls=false', '--host=tcp://0.0.0.0:2375' ]
  variables:
    CODECLIMATE_DEBUG: "true"

  rules:
    - if: '$CODE_QUALITY_DISABLED'
      when: never
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"' # Run code quality job in merge request pipelines
    - if: '$CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH'      # Run code quality job in pipelines on the master branch (but not in other branch pipelines)
    - if: '$CI_COMMIT_TAG'
      when: never

stages:
  - build
  - test
  - release
  - release_environments
  - deploy

variables:
  CONTAINER_RELEASE_IMAGE: $CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA
  TASK_DEFINITION_NAME: "bill-payment-task"
  CLUSTER_NAME: "bill-payment-cluster"
  SERVICE_NAME: "bill-payment-service"



  ME_POUPE_TASK_DEFINITION_NAME: "me-poupe-bill-payment-task"
  ME_POUPE_CLUSTER_NAME: "me-poupe-bill-payment-cluster"
  ME_POUPE_SERVICE_NAME: "me-poupe-bill-payment-service"

  GRADLE_OPTS: "-Dorg.gradle.daemon=false"
  GRADLE_VERSION: "8.10.2"

  RELEASE_TRIGGER_COMMIT_MESSAGE: "/#deploy(stg|mepoupestg|motorola|gigu(stg)?)/"
before_script:
  - chmod +x gradlew

build:
  stage: build
  coverage: '/    - Instruction Coverage: ([0-9.]+)%/'
  services:
    - name: ${CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX}/docker:dind
      command: [ "--tls=false" ]
  variables:
    DOCKER_HOST: "tcp://docker:2375"
    DOCKER_DRIVER: overlay2
    DOCKER_TLS_CERTDIR: ""
  script:
    - ./gradlew build --warning-mode=none --no-daemon --no-configuration-cache
  artifacts:
    reports:
      junit:
        - build/test-results/test/**/TEST-*.xml
        - apps/me-poupe/build/test-results/test/**/TEST-*.xml
    paths:
      - application/build/reports/jacoco/testCodeCoverageReport/testCodeCoverageReport.xml
      - application/build/libs/bill-payment-service-0.1-all.jar
  except:
    - tags

coverage:
  stage: test
  image: registry.gitlab.com/haynes/jacoco2cobertura:1.0.9
  script:
    # convert report from jacoco to cobertura, using relative project path
    - python /opt/cover2cover.py application/build/reports/jacoco/testCodeCoverageReport/testCodeCoverageReport.xml $CI_PROJECT_DIR/src/main/java/ > application/build/reports/cobertura.xml
  needs: [ "build" ]
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: application/build/reports/cobertura.xml

release:
  image: registry.gitlab.com/gitlab-org/cloud-deploy/aws-base:latest
  stage: release
  script:
    # - apt update && apt install -y wget
    #- wget -O dd-java-agent.jar 'https://dtdg.co/latest-java-tracer'
    #- wget -O dd-java-agent.jar 'https://repo1.maven.org/maven2/com/datadoghq/dd-java-agent/1.37.1/dd-java-agent-1.37.1.jar'
    - docker login -u $CI_DEPENDENCY_PROXY_USER -p $CI_DEPENDENCY_PROXY_PASSWORD $CI_DEPENDENCY_PROXY_SERVER
    - docker build --build-arg DOCKER_IMAGE_MIRROR="${CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX}/" --build-arg APP_VERSION=$CI_COMMIT_SHORT_SHA -t $CONTAINER_RELEASE_IMAGE .
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - docker push $CONTAINER_RELEASE_IMAGE
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule"
      when: never
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH || $CI_COMMIT_MESSAGE =~ $RELEASE_TRIGGER_COMMIT_MESSAGE
      when: on_success

.release_environment: &release_environment
  - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  - docker pull $CONTAINER_RELEASE_IMAGE
  - docker tag $CONTAINER_RELEASE_IMAGE $AWS_ECR_REPOSITORY:$CI_COMMIT_SHORT_SHA
  - aws configure set region $AWS_REGION
  - aws configure set aws_access_key_id $AWS_ACCESS_KEY_ID
  - aws configure set aws_secret_access_key $AWS_SECRET_ACCESS_KEY
  - aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $AWS_ECR_REPOSITORY
  - docker push $AWS_ECR_REPOSITORY:$CI_COMMIT_SHORT_SHA

friday_staging_release:
  image: registry.gitlab.com/gitlab-org/cloud-deploy/aws-base
  stage: release_environments
  when: manual
  environment:
    name: staging
  script:
    - *release_environment
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule"
      when: never
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH || $CI_COMMIT_MESSAGE =~ /#deploystg/
      when: on_success

friday_production_release:
  image: registry.gitlab.com/gitlab-org/cloud-deploy/aws-base
  stage: release_environments
  allow_failure: true
  environment:
    name: production
  script:
    - *release_environment
  only:
    - main

me-poupe_staging_release:
  image: registry.gitlab.com/gitlab-org/cloud-deploy/aws-base
  stage: release_environments
  when: manual
  environment:
    name: me-poupe-stg
  script:
    - *release_environment
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule"
      when: never
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH || $CI_COMMIT_MESSAGE =~ /#deploymepoupestg/
      when: on_success

me-poupe_production_release:
  image: registry.gitlab.com/gitlab-org/cloud-deploy/aws-base
  stage: release_environments
  allow_failure: true
  environment:
    name: me-poupe-prod
  script:
    - *release_environment
  only:
    - main



.deploy-ecs: &deploy-ecs
  - aws configure set region $AWS_REGION
  - TASK_DEFINITION=$(aws ecs describe-task-definition --task-definition "$TASK_DEFINITION_NAME")
  - NEW_TASK_DEFINITION=$(echo $TASK_DEFINITION | python $CI_PROJECT_DIR/ci/update_task_definition.py $AWS_ECR_REPOSITORY $CI_COMMIT_SHORT_SHA)
  - aws ecs register-task-definition --cli-input-json "${NEW_TASK_DEFINITION}"
  - aws ecs update-service --cluster "${CLUSTER_NAME}" --service "${SERVICE_NAME}"  --task-definition "${TASK_DEFINITION_NAME}" --desired-count $DESIRED_COUNT



.deploy-ecs-me-poupe: &deploy-ecs-me-poupe
  - aws configure set region $AWS_REGION
  - TASK_DEFINITION=$(aws ecs describe-task-definition --task-definition "$ME_POUPE_TASK_DEFINITION_NAME")
  - NEW_TASK_DEFINITION=$(echo $TASK_DEFINITION | python $CI_PROJECT_DIR/ci/update_task_definition.py $AWS_ECR_REPOSITORY $CI_COMMIT_SHORT_SHA)
  - aws ecs register-task-definition --cli-input-json "${NEW_TASK_DEFINITION}"
  - aws ecs update-service --cluster "${ME_POUPE_CLUSTER_NAME}" --service "${ME_POUPE_SERVICE_NAME}"  --task-definition "${ME_POUPE_TASK_DEFINITION_NAME}" --desired-count $DESIRED_COUNT

friday_staging_deploy:
  stage: deploy
  needs: [ friday_staging_release ]
  when: manual
  image:
    name: ${CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX}/amazon/aws-cli:2.27.50
    entrypoint: [ "" ]
  environment:
    name: staging
  script:
    - export DESIRED_COUNT=1
    - *deploy-ecs
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule"
      when: never
    - if: ($CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH && $AUTO_DEPLOY == "enabled") || ($CI_COMMIT_MESSAGE =~ /#deploystg/)
      when: on_success
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH && $AUTO_DEPLOY != "enabled"
      when: manual

friday_production_deploy:
  stage: deploy
  needs: [ friday_production_release ]
  when: manual
  image:
    name: ${CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX}/amazon/aws-cli:2.27.50
    entrypoint: [ "" ]
  environment:
    name: production
  script:
    - export DESIRED_COUNT=1
    - *deploy-ecs
  only:
    - main



me-poupe_staging_deploy:
  stage: deploy
  needs: [ me-poupe_staging_release ]
  when: manual
  image:
    name: ${CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX}/amazon/aws-cli:2.27.50
    entrypoint: [ "" ]
  environment:
    name: me-poupe-stg
  script:
    - export DESIRED_COUNT=1
    - *deploy-ecs-me-poupe
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule"
      when: never
    - if: ($CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH && $AUTO_DEPLOY == "enabled") || ($CI_COMMIT_MESSAGE =~ /#deploymepoupestg/)
      when: on_success
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH && $AUTO_DEPLOY != "enabled"
      when: manual

me-poupe_production_deploy:
  stage: deploy
  needs: [ me-poupe_production_release ]
  when: manual
  image:
    name: ${CI_DEPENDENCY_PROXY_GROUP_IMAGE_PREFIX}/amazon/aws-cli:2.27.50
    entrypoint: [ "" ]
  environment:
    name: me-poupe-prod
  script:
    - export DESIRED_COUNT=1
    - *deploy-ecs-me-poupe
  only:
    - main