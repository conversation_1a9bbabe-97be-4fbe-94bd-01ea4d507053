tenant:
  id: MOTOROLA
  env: prod
  name: motorola
  displayName: Motorola
  domain: via1.app
  api-subdomain: api-${tenant.name}
  client-subdomain: use-${tenant.name}
  mail-domain: "dimo.com.br"
  document-mail-address: "seucpf@${tenant.mail-domain}"
  citation: "a Dimo"
  encryption:
    masterkey: "FROM_AWS_SECRETS"

arbi.host: "https://gapp.bancoarbi.com.br"

dda:
  maxAddUser: 1

micronaut:
  application:
    name: bill-payment-service
  metrics:
    enabled: ${micronaut.otel.enabled}
    export:
      statsd:
        enabled: ${micronaut.otel.enabled}
      datadog:
        enabled: ${micronaut.otel.enabled}
    tags:
      application: "${tenant.name}-${micronaut.application.name}"
      provider: ${tenant.name}
      env: ${tenant.env}
  server:
    cors:
      enabled: true
      configurations:
        web:
          allowed-origins:
            - https://${tenant.client-subdomain}.${tenant.domain}
          allowed-methods:
            - GET
            - POST
            - PUT
            - DELETE
            - OPTIONS
  http:
    services:
      celcoin:
        url: ${configuration.staging.celcoin.url}
        read-timeout: 10s
        pool:
          max-connections: 5
          enabled: true
      jazz-pin-code:
        url: "https://friday-manager.jazztech.com.br"
      jazz-auth:
        url: ${integrations.jazz.host}
        read-timeout: 30s
        pool:
          max-connections: 30
          enabled: true
      settlement-service:
        url: https://liquidacao.${tenant.domain}
        read-timeout: 1m
        pool:
          max-connections: 30
          enabled: true
      vehicle-debts-service:
        url: https://debitos-veiculares.friday.ai
        pool:
          max-connections: 30
          enabled: true
      arbi:
        url: ${arbi.host}
        read-timeout: 60s
        pool:
          max-connections: 30
          enabled: true
        ssl:
          enabled: true
          isInsecureTrustAllCertificates: true
  #          client-authentication: WANT
  #          key-store:
  #            path: classpath:ssl/friday-arbi.p12
  #            type: PKCS12
  #            password: FROM_SECRETS
  session:
    http:
      redis:
        value-serializer: io.micronaut.jackson.serialize.JacksonObjectSerializer
  executors:
    scheduled:
      type: scheduled
      core-pool-size: 50
  security:
    enabled: true
    authentication: cookie
    token:
      jwt:
        enabled: true
        generator:
          access-token-expiration: 720000
        cookie:
          enabled: true
          login-success-target-url: https://${tenant.client-subdomain}.${tenant.domain}/
          login-failure-target-url: https://${tenant.client-subdomain}.${tenant.domain}/falha-ao-autenticar
          cookie-same-site: Lax
          cookie-secure: true
          cookie-domain: .${tenant.domain}
        signatures:
          jwks: [ ]
          secret:
            generator:
              secret: ${jwt.friday.secret}
              jws-algorithm: HS256
  router:
    versioning:
      enabled: true
      default-version: 2
      header:
        enabled: true
        names:
          - 'X-API-VERSION'

modules:
  manual-entry:
    enabled: true
  pfm:
    enabled: true
  chatbot-ai:
    enabled: true
  event-api:
    enabled: true
  openfinance:
    enabled: false
  push-notification:
    enabled: true
  in-app-subscription-coupon:
    enabled: false

application:
  region: us-east-1
  accountNumber: ************

endpoints:
  caches:
    enabled: true
    sensitive: false
  env:
    enabled: true
    sensitive: false

concessionariaDireto:
  financialGateway: CELCOIN

integrations:
  clearsale:
    trustedCodes:
      - CRT0004
      - CRT0005
  softwareExpress:
    url: https://esitef.softwareexpress.com.br
  celcoin:
    enabled: false
  jazz:
    host: https://onboarding-dimo.jazztech.com.br
    clientId: "mobile-app"
  cielo:
    host: https://api.cieloecommerce.cielo.com.br
    checkStatusHost: https://apiquery.cieloecommerce.cielo.com.br
  bigdatacorp:
    host: https://bigboost.bigdatacorp.com.br
    accessToken: xxx
    peoplePath: /peoplev2
    companyPath: /companies
  arbi:
    authHost: ${arbi.host}
    newHost: ${arbi.host}
    newAuthHost: ${arbi.host}
    contaTitular: "**********"
    contaLiquidacao: "**********"
    inscricao: **************
    inscricaoTitular: **************
    inscricaoParceiro: ${integrations.arbi.inscricao}
    contaCashin: "0000388880"
    pixCheckoutWaitTime: 100 #miliseconds
    fepweb:
      username: friday.hml
      password: Fri#2023
  cognito:
    userPoolId: ${application.region}_S0DGZEW5J
    jwtCookie:
      userPoolClientId: 3og5ib4dn9av4vdv3u8rqthvtd
  intercom:
    enabled: false
    token: ************************************************************
    webIdVerificationSecret: uEQeg-W3ddKuYirm3LMXpOh-ePWjbSdjXCw0SdcR
    iosIdVerificationSecret: QCKelP1l1XhDIN49NT6X3ju7ECD1xwH8ORfs6FGk
    androidIdVerificationSecret: Q7sS4G6eVmbdYJO42Op-bWcrvV7xW4pRSwp6019Y
  firebase:
    measurementId: "G-BDYBP1PJEG"
  liveness:
    host: "https://liveness.${tenant.domain}"
    user: "LIVENESS_CLIENT_ID-f96298e0-a338-11ed-8b23-4f406dbefac8"
    password: "STG_ENVIRONMENT_PASSWORD"

  settlement:
    host: https://liquidacao.friday.ai
    username: 77cfdc76-111e-4e8c-82e8-d3acdc013fb5
    password: $2y$19$npO3lIz0ToTa.YZ/xWx07eymYxtSw.Y8QigUc9HaT4uJsewvsLKL2
    requestProtocol: HTTP
  btg:
    host: https://api-h.developer.btgpactual.com/
    clientId: fc1dddad-08bd-44ed-a368-6e0e8e22f8b1
    clientSecret: K5GDFBJZFF8VQSWS29SUB64ZDXTMFR5MM161645VWLB97UBD
    redirectUrl: "https://use.${tenant.domain}/web/saldo-adicionado-itp"
  openfinance:
    host: "https://open-finance.${tenant.domain}"
    clientid: OPEN_FINANCE_CLIENT_ID-5daed27f-daf1-49ae-93cd-67195a758064
    clientsecret: OPEN_FINANCE_CLIENT_SECRET-9c87d947-e943-4154-82d9-37286cac2816
    participants:
      - name: mock bank
        shortName: mocked
        id: 0b919e9b-bee0-4549-baa3-bb6d003575ce

urls:
  site: https://${tenant.client-subdomain}.${tenant.domain}
  api: https://${tenant.api-subdomain}.${tenant.domain}

email:
  sender:
    email: <EMAIL>
    display-name: Notificações ${tenant.displayName}
  return-path: <EMAIL>
  bucket-unprocessed-emails: ${application.accountNumber}-ses-unprocessed-emails
  configuration-set-name: notification_sender_configuration_set
  finance-report-recipients: ""
  notification:
    email: <EMAIL>
    display-name: Notificações ${tenant.displayName}
  receipt:
    email: <EMAIL>
    display-name: Comprovantes ${tenant.displayName}
  newAccountReport:
    recipients: <EMAIL>
  newUpgradeAccount:
    recipients: <EMAIL>
    sensitiveRecipients: <EMAIL>
    subject: "[upgrade de conta está pendente de aprovação interna] - %s"
    message: |
      Um upgrade de conta está pendente de aprovação interna:
      Id: %s
      Nome: %s
      Email: %s
  newAccount:
    pendingInternalApprove:
      recipients: <EMAIL>
      sensitive-recipients: ${email.newAccount.pendingInternalApprove.recipients}
    pendingInternalReview:
      recipients: <EMAIL>
      sensitive-recipients: ${email.newAccount.pendingInternalReview.recipients}
    pendingActivation:
      recipients: <EMAIL>
      sensitive-recipients: ${email.newAccount.pendingActivation.recipients}

friday-callback:
  identity: ${integrations.settlement.username}
  secret: ${integrations.settlement.password}

celcoin-callback:
  identity: fde714cc-61b8-493e-9086-9e36e17d1c7b
  secret: $2y$17$bkBQLvoS/NrktlZu9jilK.vofvK1Na90wpWIZonmO1g/f9zRW6dX2

revenue-cat-callback:
  identity: c5fcf4ee-966e-446f-b56a-4d9e26373c54
  secret: password

arbi-callback:
  identity: fde714cc-61b8-493e-9086-9e36e17d1c7c
  secret: $2y$17$bkBQLvoS/NrktlZu9jilK.vofvK1Na90wpWIZonmO1g/f9zRW6dX2



communication-centre:
  landingPageUrl: "App Dimo"
  email:
    region: "us-east-2"
    sender:
      email: <EMAIL>
      display-name: Notificações ${tenant.displayName}
    return-path: <EMAIL>
    bucket-unprocessed-emails: ${application.accountNumber}-ses-unprocessed-emails
    configuration-set-name: failure_rendering_notification_configuration_set
    notification:
      email: <EMAIL>
      display-name: Notificações ${tenant.displayName}
    receipt:
      email: <EMAIL>
      display-name: Comprovantes ${tenant.displayName}
    virus:
      bucket: ${application.accountNumber}-quarantine-emails
    templates:
      local:
        walletSummaryReportPath: "templates/wallet-summary-report.motorola"
        pixReceipt: "templates/bill-receipt-attachment.dimo"
        investmentReceipt: "templates/investment-receipt.dimo"
        invoiceReceipt: "templates/bill-receipt-attachment.dimo"
        barcodeBillReceipt: "templates/bill-receipt-attachment.dimo"
  forward:
    configuration-set: failure_rendering_notification_configuration_set
    sender: <EMAIL>
  integration:
    blip:
      host: https://via1-pagamentos-digitais.http.msging.net
      namespace: 1d3afeae_c48c_4c2a_8d65_02b4bbf01f83
      command:
        path: /commands
      message:
        path: /messages
      templates:
        barcodeBillAddKnownAuthorWithDescriptionSuccess: barcode_bill_add_known_author_with_description_success__moto_4_11_0
        barcodeBillAddKnownAuthorNoDescriptionSuccess: barcode_bill_add_known_author_no_description_success__moto_4_11_0
        barcodeBillAddKnownAuthorNoDescriptionSuccessWithHint: barcode_bill_add_known_author_no_description_success_with_hint__moto_4_11_0
        barcodeBillAddNoAuthorWithDescriptionSuccess: barcode_bill_add_no_author_with_description_success__moto_4_12_0
        barcodeBillAddNoAuthorNoDescriptionSuccess: barcode_bill_add_no_author_no_description_success__moto_4_11_0
        invoiceAddNoDescriptionSuccess: invoice_add_no_description_success__4_11_1
        invoiceAddWithDescriptionSuccess: invoice_add_with_description_success__4_11_0
        barcodeBillCloseOverdueKnownAuthorWithDescription: barcode_bill_close_overdue_known_author_with_description__4_11_0
        barcodeBillCloseOverdueKnownAuthorNoDescription: barcode_bill_close_overdue_known_author_no_description__moto_4_11_0
        barcodeBillCloseOverdueKnownAuthorNoDescriptionWithHint: barcode_bill_close_overdue_known_author_no_description_with_hint__4_11_1
        barcodeBillCloseOverdueNoAuthorWithDescription: barcode_bill_close_overdue_no_author_with_description__moto_4_17_0
        barcodeBillCloseOverdueNoAuthorNoDescription: barcode_bill_close_overdue_no_author_no_description__moto_4_17_0
        invoiceCloseOverdueWithDescription: invoice_close_overdue_with_description__4_11_0
        invoiceCloseOverdueNoDescription: invoice_close_overdue_no_description__4_17_0
        billComingDueSecondaryWallet: bill_coming_due_secondary_wallet_{{i}}__1_0_5
        walletOnePixPay: wallet_one_pix_pay__moto_4_17_0
        walletOnePixPaySingular: wallet_one_pix_pay_singular__moto_4_17_0
        walletCashInWithCreditCardFailed: wallet_cash_in_with_credit_card_failed__moto_4_17_0
        walletPostalBoxAddDuplicate: wallet_postal_box_add_duplicate__4_17_0
        walletPostalBoxAddNotAuthorized: wallet_postal_box_add_not_authorized__4_12_0
        walletPostalBoxAddNonPayableNoData: wallet_postal_box_add_non_payable_no_data__4_11_1
        walletPostalBoxAddNonPayable: wallet_postal_box_add_non_payable__4_11_0
        walletPostalBoxAddPaidExternally: wallet_postal_box_add_paid_externally__4_11_1
        walletPostalBoxAddPaidExternallyWithoutData: wallet_postal_box_add_paid_externally_without_data__4_12_0
        walletPostalBoxAddValidationFailure: wallet_postal_box_add_validation_failure__4_11_0
        walletInvoicePaymentFailure: wallet_invoice_payment_failure__4_17_0
        walletLastAlertPaymentOverdueToday: wallet_last_alert_payment_overdue_today_{{i}}_moto__1_0_1
        walletLastAlertPaymentOverdueTodayWithHint: wallet_last_alert_payment_overdue_today_with_hint_{{i}}__4_12_0
        walletPaymentOverdueYesterday: wallet_payment_overdue_yesterday_moto__4_17_0
        walletPaymentOverdueYesterdayWithHint: wallet_payment_overdue_yesterday_with_hint_moto__4_10_0
        welcomeAccountCreatedWithoutChatbot: welcome_account_created_without_chatbot__1_0_0
        upgradeCompleted: upgrade_completed__moto_1_0_2
        registerDenied: register_denied__moto_4_26_0
        registerUpgraded: register_upgraded__4_26_0
        walletMemberJoined: wallet_member_joined__moto_4_17_0
        userAuthenticationRequired: user_authentication_required__moto_4_12_0
        registerUpdated: register__updated__moto_4_12_1
        walletInvoicePaymentReturned: wallet_invoice_payment_returned__moto_4_17_0
        walletBarcodeBillReceipt: wallet_barcode_bill_receipt__moto_4_17_0
        walletInvoiceBillReceipt: wallet_invoice_bill_receipt__moto_4_11_1
        walletPixBillReceipt: wallet_pix_bill_receipt__moto_4_11_1
        walletInvestmentBillReceiptWithBadge: wallet_investment_bill_receipt_with_badge__1_0_0
        walletBillReceiptImage: wallet_bill_receipt_image__3_1_0
        walletCashInInsufficientBalance: wallet_cash_in_insufficient_balance__moto_4_17_0
        walletBarcodeBillApprovedPaymentNonPayable: wallet_barcode_bill_approved_payment_non_payable__moto_4_17_0
        walletInsufficientBalanceAfterHours: wallet_insufficient_balance_after_hours__moto_4_12_4
        walletInsufficientBalance: wallet_insufficient_balance__moto_4_12_3
        insufficientBalanceFirstCashIn: insufficient_balance_first_cash_in__moto_4_12_0
        walletInsufficientBalanceSecondaryWallet: wallet_insufficient_balance_secondary_wallet__moto_1_0_2
        walletBillSchedulePostponedDueLimitReached: wallet_bill_schedule_postponed_due_limit_reached__moto_4_11_2
        walletBillScheduleCanceledDueAmountHigherThanDailyLimit: wallet_bill_schedule_canceled_due_amount_higher_than_daily_limit__moto_4_11_3
        fredOnboardingWelcome: dime_onboarding_welcome_moto_1_0_9
        triPixReminderNextDay: tri_pix_reminder_next_day__1_0_0
        triPixReminderLastDay: tri_pix_reminder_last_day__1_0_0
        triPixExpired: tri_pix_expired_1_0_3
        walletBillScheduleCanceledDueCreditCardDenied: wallet_bill_schedule_canceled_due_credit_card_denied__moto_4_24_1
        postalBoxAddNotVisibleBillAlreadyExists: postal_box_add_not_visible_bill_already_exists__moto_4_12_2
        walletInviteAssistantWithAccount: wallet_invite_assistant_with_account__moto_4_13_0
        walletInviteReminderAssistantWithAccount: wallet_invite_reminder_assistant_with_account__moto_4_13_0
        firstBillScheduled: first_bill_scheduled__moto_4_12_0
        registerToken: register_token__4_14_0
        itpTransactionFailed: itp_transaction_failed__moto_4_17_1
        signUpBasicUpdateDataNeeded: sign_up_basic_update_data_needed__moto_4_18_0
        pixNotReceivedFailure: pix_not_received_failure__moto_4_21_0
        subscriptionInsufficientBalance: subscription_insufficient_balance__moto_4_17_0
        inAppSubscriptionOverdue: in_app_subscription_overdue__moto_1_0_0
        subscriptionOverdue: subscription_overdue__moto_4_18_2
        pixSubscriptionOverdueNotificationChannelDowngradeWarning: TBD_1
        inAppSubscriptionOverdueNotificationChannelDowngradeWarning: TBD_2
        subscriptionOverdueCloseAccount: subscription_overdue_close_account__moto_1_0_0
        subscriptionOverdueWarningAccountClosure: subscription_overdue_warning_account_closure__moto_1_0_0
        subscriptionOverdueDay32: subscription_overdue_day32__moto_1_00_3
        subscriptionOverdueDay04: subscription_overdue_day04__1_00_0
        subscriptionOverdueDay02: subscription_overdue_day02__moto_1_00_2
        subscriptionOverdueDay01: subscription_overdue_day01__moto_1_00_3
        subscriptionCreated: subscription_created__moto_4_17_1
        walletOnePixPayFailure: wallet_one_pix_pay_failure__moto_4_17_0
        creditCardEnabled: credit_card_enabled__4_20_0
        utilityAccountInvoiceScanError: utility_account_invoice_scan_error__moto_1_0_0
        utilityAccountInvoiceNotFound: utility_account_invoice_not_found__moto_1_0_0
        utilityAccountDisconnectLegacyAccount: utility_account_disconnect_legacy_account__moto_1_0_2
        utilityAccountRequestReconnection: utility_account_request_reconnection__moto_1_0_1
        connectedFlowUtilityAccount: connected_flow_utility_account__moto_2_3_0
        connectedFlowUtilityAccountWithBills: connected_flow_utility_account_with_bills__moto_2_3_0
        connectedUtilityAccount: connected_utility_account__moto_4_26_0
        utilityAccountUpdateStatusWithDetails: utility_account_update_status_with_details__moto_1_0_0
        disconnectedScrapingUtilityAccount: disconnected_scraping_utility_account__moto_1_0_0
        utilityAccountUpdateStatus: utility_account_update_status__moto_4_22_0
        walletCashInNoPaymentsPending: wallet_cash_in_no_payments_pending__moto_4_17_1
        walletCashInSufficientBalance: wallet_cash_in_sufficient_balance__moto_4_17_2
        reminderExpiredNotification: reminder_expired_notification__moto_1_0_2
        reminderExpiredNotificationSingular: reminder_expired_notification_singular__moto_1_0_0
        reminderNotification: reminder_notification__moto_1_0_6
        postalBoxAddManualReview: postal_box_add_manual_review__moto_4_10_0
        mailboxBillInsecure: mailbox_bill_insecure__moto_1_0_0
        walletApprovedPaymentCancelled: wallet_approved_payment_cancelled__moto_4_17_0
        walletBillScheduleCanceledDueAmountChanged: wallet_bill_schedule_canceled_due_amount_changed__moto_4_23_1
        sweepingCashInError: sweeping_transfer_error__moto_1_0_0
        sweepingCashInInsufficientBalanceError: sweeping_transfer_balance_error__moto_1_1_0
        investmentRedemptionCreated: investment_redemption_created__1_0_0
        investmentRedemptionCompleted: investment_redemption_completed__1_0_0
        investmentRedemptionFailed: investment_redemption_failed__1_0_0
        investmentRedemptionPartialFailed: investment_redemption_partial_failed__1_0_0
        investmentValueOptimizedIncreased: investment_value_optimized_increased__1_0_0
        investmentValueOptimizedDecreased: investment_value_optimized_decreased__1_0_0
        sweepingAccountConnected: open_finance_connection_success__1_0_1
        sweepingAccountConnectionFailed: open_finance_connection_failed_generic__1_0_2
        sweepingAccountEdit: open_finance_connection_edit_success__1_0_0
        sweepingAccountEditFailed: open_finance_connection_edit_failed__1_0_4
        goalWithDailyLiquidityCompletedByDate: goal_with_daily_liquidity_completed_by_date__1_0_0
        goalWithDailyLiquidityCompletedByAmount: goal_with_daily_liquidity_completed_by_amount__1_0_0
        goalWithMaturityLiquidityCompletedByDate: goal_with_maturity_liquidity_completed_by_date__1_0_0
        goalWithMaturityLiquidityCompletedByAmount: goal_with_maturity_liquidity_completed_by_amount__1_0_0

        goalWithDailyLiquidityCompletedByDateWithBadge: goal_with_daily_liquidity_completed_by_date_with_badge__1_0_0
        goalWithDailyLiquidityCompletedByAmountWithBadge: goal_with_daily_liquidity_completed_by_amount_with_badge__1_0_0
        goalWithMaturityLiquidityCompletedByDateWithBadge: goal_with_maturity_liquidity_completed_by_date_with_badge__1_0_0
        goalWithMaturityLiquidityCompletedByAmountWithBadge: goal_with_maturity_liquidity_completed_by_amount_with_badge__1_0_0

        goalWithDailyLiquidityPausedByDate: goal_with_daily_liquidity_paused_by_date__1_0_0
        goalWithDailyLiquidityPausedByAmount: goal_with_daily_liquidity_paused_by_amount__1_0_0

emailDomain: "meupagador.com.br"

accountRegister:
  user_files:
    bucket: ${application.accountNumber}-user-documents


sqs:
  signUpQueueName: sign-up-users-${tenant.env}
  jazzOnboardingQueueName: jazz_onboarding
  emailQueueName: incoming-emails-${tenant.env}
  rollbackTransactionQueueName: bill_payment_rollback_transaction
  walletEventsQueueName: wallet-events-queue-${tenant.env}
  accountEventsQueueName: account-events-queue-${tenant.env}
  sqsWaitTime: 20
  dlqArn: arn:aws:sqs:${application.region}:${application.accountNumber}:bill_events_dlq

sns:
  topics:
    billEvent:
      name: bill-events-${tenant.env}
    walletEvent:
      name: wallet-events-${tenant.env}
    accountEvent:
      name: account-events-${tenant.env}
    eventBus:
      name: event-bus-${tenant.env}
  billEventTopicArn: ${sns.arnPrefix}:${sns.topics.billEvent.name}
  walletEventTopicArn: ${sns.arnPrefix}:${sns.topics.walletEvent.name}
  accountEventTopicArn: ${sns.arnPrefix}:${sns.topics.accountEvent.name}
  eventBusTopicArn: ${sns.arnPrefix}:${sns.topics.eventBus.name}
  sms:
    maxPrice: 20.00

features:
  createTopic: true
  vehicleDebts: true
  sendEmailReceipt: false
  eventBus:
    enabled: true
    publisher:
      sns.enabled: true
  inAppSubscription: 1
  asyncSettlement: true
  forwardEmailToManualWorkflow: false
  automaticUserRegister: true
  zeroAuthEnabled: false
  creditCardChallenge: true
  updateScheduleOnAmountLowered: true
  pinCode:
    enabled: true
  credit-card:
    provider: "software-express"
  credit-card-risk-analisys:
    provider: "clearsale"
  imageReceipt: true

receipt:
  bucketRegion: ${application.region}
  bucketName: ${application.accountNumber}-bill-receipts

jwt:
  friday:
    audience: "friday.ai"
    issuer: "https://friday.ai"
    duration: "720h"
    secret: "BillPaymentGiganteBillPaymentGigante"

jwtValidation:
  providers:
    msisdn:
      issuer: ${jwt.friday.issuer}
      audience:
        - "phone"
    nonce:
      issuer: ${jwt.friday.issuer}
      audience:
        - "webapp"

settlementFundsTransfer:
  payerName: Friday Pagamentos Digitais LTDA
  payerDocument: **************
  recipientName: Friday Pagamentos Digitais LTDA
  recipientDocument: **************
  description: pagamento de conta
  fraudFundsAccount: 3201012
  originSettlementBankAccount:
    bankNo: 213
    routingNo: 1
    accountNo: 38831
    accountDv: 6
  originCashinBankAccount:
    accountType: CHECKING
    bankNo: 213
    routingNo: 1
    accountNo: 38888
    accountDv: 0
    ispb: ********
  recipientBankAccount: # é o fluxo da celcoin. Botamos um valor qualquer para não transferir
    accountType: CHECKING
    routingNo: 1
    accountNo: 0000000
    accountDv: 2
    ispb: ********

internalBankService:
  omnibusBankAccount:
    accountNo: 325513

creditCard:
  installments:
    fees:
      1: 2.12
      2: 2.57
      3: 2.57
      4: 2.57
      5: 2.57
      6: 2.57
      7: 2.86
      8: 2.86
      9: 2.86
      10: 2.86
      11: 2.86
      12: 2.86


createBillService:
  idSubscriptionBy:
    recipientDocument: **************

subscription:
  amount: 990
  dayOfMonth: 10
  description: "%s"
  recipientName: Assinatura ${tenant.displayName}
  recipientDocument: **************
  bankAccount:
    accountType: CHECKING
    bankNo: 213
    routingNo: 1
    accountNo: *********
    accountDv: 2
    ispb: ********

redis:
  uri: redis://bill-payment-cache.friday.internal:6379
  cache:
    value-serializer: io.micronaut.jackson.serialize.JacksonObjectSerializer
  caches:
    balance:
      charset: 'UTF-8'
      expire-after-write: 2m
    find-pix-key-details:
      charset: 'UTF-8'
      expire-after-write: 5m
      expire-after-access: 5m



kms:
  openfinance:
    keyid: 43764d7e-efd9-48f7-8fe0-20375978f17e
  hmacKey: UJKvKAPO2aErdBmfI3Vgs7rnKJ9GmEr3P642VCqHw3odGzzKQQ1Wb2s3chozVVQDCvmdghqHmznSenYNQ+y/IQ==

disable:
  export-s3: true
  cognito: true

jackson:
  deserialization:
    fail-on-unknown-properties: false
    read-unknown-enum-values-using-default-value: true

jobs:
  NewAccountReportJob:
    enabled: false
  DisableMediumCreditCardJob:
    enabled: false
  SynchronizeOmnibusBankAccountJob:
    enabled: false
  OmnibusAccountReportJob:
    enabled: false
  SendBillComingDueReportJob:
    enabled: false # talvez faça sentido ter quando tiver um dominio

notificationHints:
  billCreated:
    - "Além dos WhatsApps te lembrando de pagar as contas, ${tenant.citation} faz seus pagamentos de um jeito automático. Experimente!"
    - "Perdendo tempo pagando conta? Com ${tenant.citation} você paga várias contas de uma vez só com apenas 3 cliques. Experimente e economize tempo."
    - "Com ${tenant.citation} você nunca mais vai esquecer de pagar seu personal trainer, diarista ou terapeuta. Agende transferências recorrentes e receba os avisos de vencimento e comprovantes no WhatsApp."
    - "Quer pagar alguma conta que não está no seu CPF ? Basta mandar o PDF do boleto para ${tenant.document-mail-address} para que ele apareça na sua timeline."
  billComingDue:
    - "Precisa pagar uma conta para outra pessoa? Apenas mande o PDF do boleto para ${tenant.document-mail-address} e ele aparece automaticamente na sua timeline."
    - "Tenha total controle e nunca mais esqueça de fazer pagamentos importantes. Com ${tenant.citation} você agenda recorrências e pode suspendê-las quando quiser."
    - "Imagine um mundo em que você não precisa pagar conta por conta em diferentes dias? Com ${tenant.citation} você resolve todas de uma só vez. Experimente!"
    - "Está gostando de receber os lembretes das suas contas a pagar pelo WhatsApp? Imagina se elas forem pagas de jeito automático? Experimente!"
  billComingDueLastWarn:
    - "Suas contas de luz, Internet e outros serviços também podem ser pagas com ${tenant.citation}. Envie o PDF do boleto para ${tenant.document-mail-address} e a conta aparece na sua timeline. Experimente!"
    - "Agende transferências recorrentes com ${tenant.citation} e receba os lembretes de pagamento no seu WhatsApp. Experimente!"
    - "Com ${tenant.citation} você tem a praticidade de pagar vários tipos diferentes de contas de uma vez só. Experimente!"
    - "Os avisos de vencimento no WhatsApp ajudam a lembrar das contas a pagar. Mas que tal simplificar ainda mais seus pagamentos? Agende e pague tudo pel${tenant.citation}? Experimente!"
  billOverdueYesterday:
    - "Perdeu um pagamento? Envie o boleto para ${tenant.document-mail-address} e receba os avisos de vencimento no WhatsApp. Experimente!"
    - "Agende transferências recorrentes com ${tenant.citation} e receba os lembretes de pagamento no seu WhatsApp. Experimente!"
    - "Ainda dá tempo de fazer esse pagamento com ${tenant.citation} e aproveite também para agendar futuros pagamentos. Experimente!"
    - "Deixou a conta vencer mesmo com o lembrete no WhatsApp? Agende seus pagamentos e não pague mais multa. Experimente!"

dynamodb:
  billPaymentTableName: Via1-BillPayment
  openFinanceDataTableName: Friday-OpenFinanceData
  userEventsTableName: Via1-UserEvents
  billsSearchTableName: Via1-BillsSearch
  billEventsTableName: Via1-BillEvents

logger:
  levels:
    ROOT: INFO
    ai.friday.billpayment: INFO
    io.micrometer: OFF
    io.micrometer.core.instrument.push.PushMeterRegistry: OFF
    ai.friday.billpayment.adapters.api.XRequestIdFilter: DEBUG
    ai.friday.billpayment.app.instrumentation: DEBUG
    ai.friday.billpayment.adapters.intercom: DEBUG
    #ai.friday.billpayment.app.job: INFO
    #ai.friday.billpayment.adapters.jobs: INFO
    #ai.friday.billpayment.adapters.messaging: INFO
    #io.micronaut: DEBUG
    #io.micronaut.context: OFF
    #io.micronaut.context.condition: OFF
    #io.micronaut.management: OFF