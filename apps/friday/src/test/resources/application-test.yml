micronaut:
  metrics:
    enabled: true
  caches:
    wallet:
      charset: 'UTF-8'
      expire-after-write: 0s
      maximum-size: 0
  otel:
    enabled: false
  server:
    ssl:
      enabled: false
    netty:
      listeners:
        httpsListener:
          port: -1
          ssl: false
    port: -1
    max-request-size: 6291456 #1024L*1024*6=>6MB
    multipart:
      enabled: true
      disk: false
      mixed: false
      max-file-size: 6291456 #6291456
  application:
    name: bill-payment-service
  security:
    enabled: true
    endpoints:
      login:
        enabled: true
    token:
      cookie:
        enabled: true
      bearer:
        enabled: false
      jwt:
        enabled: true
        cookie:
          secure: true
        signatures:
          jwks:
            aggregator:
              url: 'https://www.googleapis.com/oauth2/v3/certs'
          secret:
            generator:
              secret: PleaseChangeThisSecretForANewOne
  router:
    versioning:
      enabled: true
      default-version: 2
      header:
        enabled: true
        names:
          - 'X-API-VERSION'
  http:
    client:
      read-timeout: 15s

endpoints:
  health:
    enabled: true
    sensitive: false
    details-visible: ANONYMOUS
    disk-space:
      enabled: false

celcoin-callback:
  identity: fde714cc-61b8-493e-9086-9e36e17d1c7a
  secret: $2y$17$bkBQLvoS/NrktlZu9jilK.vofvK1Na90wpWIZonmO1g/f9zRW6dX2

arbi-callback:
  identity: test-123
  secret: passwd

integrations:
  api.basic-credentials: '{ "BACKOFFICE": "aWRlbnRpdHk6c2VjcmV0" }'
  celcoin:
    host: https://sandbox-apicorp.celcoin.com.br
    host-da: https://sandbox.openfinance.celcoin.com.br
    username: teste
    password: teste
    authorize:
      path: /v4/transactions/billpayments/authorize
    pendency:
      path: /v4/transactions/pendency
    token:
      client_id: 41b44ab9a56440.teste.celcoinapi.v5
      client_secret: e9d15cde33024c1494de7480e69b7a18c09d7cd25a8446839b3be82a56a044a3
  cielo:
    host: https://apisandbox.cieloecommerce.cielo.com.br
    checkStatusHost: https://apiquerysandbox.cieloecommerce.cielo.com.br
  arbi:
    host: https://apihml-bancoarbi.sensedia.com
    userToken: KEv7OxEmpjiy2scHdDOTMFaMfCjFV2sp
    clientId: 1553934b-b107-34c1-91e0-a7c2adf45c38
    clientSecret: 24498b2a-ed8f-30dd-9281-0d57b35a5e93
    contaTitular: "3714000090"
    contaLiquidacao: "3714000090"
    inscricao: **************
    contaCashin: ${integrations.arbi.contaLiquidacao}
    pixCheckoutWaitTime: 1 #miliseconds
  bigdatacorp:
    host: https://bigboost.bigdatacorp.com.br
    accessToken: xxx
    login: xxx
    password: xxx
    peoplePath: /peoplev2
    companyPath: /companies
  intercom:
    token: ************************************************************
  xp:
    pixCheckoutWaitTime: 1 #miliseconds
  btg:
    host: "https://api-h.developer.btgpactual.com"
    clientId: "fc1dddad-08bd-44ed-a368-6e0e8e22f8b1"
    clientSecret: "K5GDFBJZFF8VQSWS29SUB64ZDXTMFR5MM161645VWLB97UBD"
    redirectUrl: https://use.meupagador.com.br/web/saldo-adicionado-itp
    itp.static.institutions:
      "1":
        ispb: "ispb"
        title: "banco"
      "2":
        ispb: "ispb"
        title: "banco"
  firebase:
    measurementId: "G-BDYBP1PJEG"
  userpilot:
    token: xxx

communication-centre:
  email:
    virus:
      bucket: test-bucket

sqs:
  emailQueueName: test-email-receiver-notification
  rollbackTransactionQueueName: bill_payment_rollback_transaction
  sqsWaitTime: 20

features:
  maintenanceServices: [ ]
  forwardEmailToManualWorkflow: false
  automaticUserRegister: true
  activateUserDDANotificationJob.enabled: false
  blockDuplicateByIdNumber: true
  contasEmDia4All.enabled: false
  schedule:
    fingerprint:
      enabled: true
  userGroupsFilter:
    enabled: true
    versionRestrictions:
      - version: "11.0.157"
        platform: "ANDROID"
        group: "MULTI_SELECT_BILLS"
      - version: "11.0.101"
        platform: "IOS"
        group: "MULTI_SELECT_BILLS"
      - version: "11.0.189"
        platform: "ANDROID"
        group: "TIMELINE_FACELIFT"
      - version: "11.0.124"
        platform: "IOS"
        group: "TIMELINE_FACELIFT"


accountRegister:
  maxDocumentSize: 3145728 #1024L * 1024 * 3
  user_files:
    bucket: fake-bucket


receipt:
  shouldSaveReceiptFilesOnDisk: true
  bucketRegion: us-east-1
  bucketName: stg-bill-receipts

jwtValidation:
  providers:
    google:
      audience:
        - fake1.apps.googleusercontent.com
        - fake2.apps.googleusercontent.com
      issuer: "https://accounts.google.com"
    apple:
      audience:
        - apple.friday.org
      issuer: "https://appleid.apple.com"
    msisdn:
      audience:
        - ${msisdn-authentication.jwt.audience}
      issuer: ${msisdn-authentication.jwt.issuer}
    me-poupe:
      audience:
        - me-poupe-friday-teste
      issuer: "me-poupe-friday-teste-iss"

subscription:
  amount: 990
  dayOfMonth: 10
  description: "%s"
  recipientName: Assinatura Friday
  recipientDocument: **************
  bankAccount:
    accountType: CHECKING
    bankNo: 213
    routingNo: 1
    accountNo: *********
    accountDv: 2
    ispb: ********

internalBankService:
  omnibusBankAccount:
    accountNo: 123450



fraud-list:
  documents:
    - "***********"

kms:
  hmacKey: UJKvKAPO2aErdBmfI3Vgs7rnKJ9GmEr3P642VCqHw3odGzzKQQ1Wb2s3chozVVQDCvmdghqHmznSenYNQ+y/IQ==

logger:
  levels:
    ai.friday.billpayment: OFF

chatbotAI-auth:
  secret: CHATBOT_AI_SECRET

modules:
  pfm:
    enabled: true
  manual-entry:
    enabled: true
  chatbot-ai:
    enabled: true
  event-api:
    enabled: true