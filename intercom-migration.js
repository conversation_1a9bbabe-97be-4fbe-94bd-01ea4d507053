const https = require('https');

// Configurações - substitua pelos seus tokens de autorização
const GET_AUTH_TOKEN = 'Bearer dG9rOjA2NTQxNmQ4XzNjNWNfNGFlMF85NjQ1XzRmMzVjMzM0MzRhYzoxOjA=';
const POST_AUTH_TOKEN = 'Bearer dG9rOmM1NGY0ZmVmXzhkNGRfNDA4Nl84YzM2XzlkYzgwMzVlZWJjODoxOjA=';

// IDs dos artigos para migrar
const articleIds = [
  // "6161949",
  // "6125691",
  // "11471588",
  // "6085002",
  // "6229423",
  // "5794929",
  // "6387490",
  // "6467978",
  "8776428",
  // "5905095",
  // "8167431",
  // "5992676",
  // "5952016",
  // "5952023",
  // "5952018",
  // "7264780",
  // "8081614",
  // "8148934",
  // "8313475",
  // "8981555"
];

// Função para fazer requisição GET
function getArticle(articleId) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'api.intercom.io',
      path: `/articles/${articleId}`,
      method: 'GET',
      headers: {
        'Authorization': GET_AUTH_TOKEN,
        'Accept': 'application/json'
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const article = JSON.parse(data);
          resolve(article);
        } catch (error) {
          reject(new Error(`Erro ao parsear JSON para artigo ${articleId}: ${error.message}`));
        }
      });
    });

    req.on('error', (error) => {
      reject(new Error(`Erro na requisição GET para artigo ${articleId}: ${error.message}`));
    });

    req.end();
  });
}

// Função para fazer requisição POST
function postArticle(articleData) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify({
      title: articleData.title,
      author_id: articleData.author_id,
      description: articleData.description,
      body: articleData.body,
      state: articleData.state,
      parent_id: articleData.parent_id,
      parent_type: articleData.parent_type
    });

    const options = {
      hostname: 'api.intercom.io',
      path: '/articles',
      method: 'POST',
      headers: {
        'Authorization': POST_AUTH_TOKEN,
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          resolve({ statusCode: res.statusCode, data: result });
        } catch (error) {
          reject(new Error(`Erro ao parsear resposta do POST: ${error}`));
        }
      });
    });

    req.on('error', (error) => {
      reject(new Error(`Erro na requisição POST: ${error.message}`));
    });

    req.write(postData);
    req.end();
  });
}

// Função principal de migração
async function migrateArticles() {
  console.log('🚀 Iniciando migração de artigos da Intercom...\n');
  
  const results = {
    success: [],
    errors: []
  };

  for (const articleId of articleIds) {
    try {
      console.log(`📖 Buscando artigo ${articleId}...`);
      
      // GET do artigo
      const article = await getArticle(articleId);
      console.log(`✅ Artigo ${articleId} obtido: "${article.title}"`);
      
      // POST do artigo
      console.log(`📝 Publicando artigo ${articleId}...`);
      const postResult = await postArticle(article);
      
      if (postResult.statusCode >= 200 && postResult.statusCode < 300) {
        console.log(`✅ Artigo ${articleId} publicado com sucesso!`);
        results.success.push({
          originalId: articleId,
          title: article.title,
          newId: postResult.data.id
        });
      } else {
        console.log(`❌ Erro ao publicar artigo ${articleId}. Status: ${postResult.statusCode}`);
        results.errors.push({
          articleId,
          error: `HTTP ${postResult.statusCode}`,
          response: postResult.data
        });
      }
      
      console.log('---');
      
      // Pequena pausa entre requisições para evitar rate limiting
      await new Promise(resolve => setTimeout(resolve, 1000));
      
    } catch (error) {
      console.log(`❌ Erro ao processar artigo ${articleId}: ${error.message}`);
      results.errors.push({
        articleId,
        error: error.message
      });
      console.log('---');
    }
  }

  // Relatório final
  console.log('\n📊 RELATÓRIO FINAL DA MIGRAÇÃO');
  console.log('================================');
  console.log(`✅ Artigos migrados com sucesso: ${results.success.length}`);
  console.log(`❌ Artigos com erro: ${results.errors.length}`);
  
  if (results.success.length > 0) {
    console.log('\n🎉 SUCESSOS:');
    results.success.forEach(item => {
      console.log(`  - ${item.originalId} → ${item.newId}: "${item.title}"`);
    });
  }
  
  if (results.errors.length > 0) {
    console.log('\n💥 ERROS:');
    results.errors.forEach(item => {
      console.log(`  - ${item.articleId}: ${item.error}`);
      if (item.response) {
        console.log(`    Resposta: ${JSON.stringify(item.response, null, 2)}`);
      }
    });
  }
  
  console.log('\n🏁 Migração finalizada!');
}

// Executar o script
if (require.main === module) {
  migrateArticles().catch(error => {
    console.error('💥 Erro fatal na migração:', error);
    process.exit(1);
  });
}

module.exports = { migrateArticles, getArticle, postArticle };
