package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.adapters.api.RegisterErrors.ADDRESS_NOT_FOUND
import ai.friday.billpayment.adapters.api.RegisterErrors.GENERIC_ERROR
import ai.friday.billpayment.app.account.Address
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.integrations.AddressNotFoundException
import ai.friday.billpayment.app.integrations.BigDataService
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import com.fasterxml.jackson.annotation.JsonInclude
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.security.annotation.Secured
import io.micronaut.validation.Validated
import jakarta.validation.constraints.Pattern
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Secured(Role.Code.GUEST, Role.Code.OWNER)
@Controller("/zipcode")
@Version("2")
@Validated
class ZipcodeController(private val bigDataService: BigDataService) {

    private val logger = LoggerFactory.getLogger(ZipcodeController::class.java)

    @Get("/{zipcode}")
    fun getZipcode(
        @PathVariable
        @Pattern(
            regexp = "^\\d{8}$",
            message = "Zipcode must be 8 digits",
        )
        zipcode: String,
    ): HttpResponse<*> {
        return bigDataService.getAddress(zipcode)
            .map<HttpResponse<*>> {
                HttpResponse.ok(it.toAddressTO())
            }
            .getOrElse {
                when (it) {
                    is AddressNotFoundException -> {
                        logger.warn(append("zipcode", zipcode).andAppend("context", "Address not found"), "ZipcodeController", it)
                        StandardHttpResponses.notFound(
                            ResponseTO(
                                ADDRESS_NOT_FOUND.code,
                                ADDRESS_NOT_FOUND.description,
                            ),
                        )
                    }

                    else -> {
                        logger.error(append("zipcode", zipcode), "ZipcodeController", it)
                        StandardHttpResponses.serverError(ResponseTO(GENERIC_ERROR.code, GENERIC_ERROR.description))
                    }
                }
            }
    }
}

private fun Address.toAddressTO(): AddressTO =
    AddressTO(
        streetType = streetType,
        streetName = streetName,
        number = number.orEmpty(),
        complement = complement,
        neighborhood = neighborhood,
        city = city,
        state = state,
        zipCode = zipCode,
    )

@JsonInclude(JsonInclude.Include.ALWAYS)
data class AddressTO(
    val streetType: String?,
    val streetName: String,
    val number: String,
    val complement: String?,
    val neighborhood: String,
    val city: String,
    val state: String,
    val zipCode: String,
)