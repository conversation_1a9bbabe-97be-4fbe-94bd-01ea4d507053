package ai.friday.billpayment.adapters.api.caf

import ai.friday.billpayment.app.caf.CafService
import ai.friday.billpayment.app.caf.FileData
import ai.friday.morning.log.andAppend
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import io.micronaut.security.rules.SecurityRule
import java.time.LocalDateTime
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Controller("/caf")
@Secured(SecurityRule.IS_AUTHENTICATED)
class CafController(
    private val cafService: CafService,
) {
    @Post("/transactions")
    fun createTransaction(
        @Body request: CreateTransactionRequestTO,
    ): HttpResponse<*> {
        val markers = append("templateId", request.templateId)

        return cafService.createTransaction(templateId = request.templateId, files = request.files.toListFileData()).fold(
            ifLeft = { ex ->
                logger.error(markers, "CafController#createTransaction", ex)
                val errorResponse = ErrorResponseTO(
                    message = "Failed to create CAF transaction: ${ex.message}",
                    code = "CAF_CREATE_ERROR",
                )
                HttpResponse.status<ErrorResponseTO>(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse)
            },
            ifRight = { response ->
                val responseTO = CreateTransactionResponseTO(
                    requestId = response.requestId,
                    id = response.id,
                    status = response.status,
                    success = true,
                )
                logger.info(
                    markers.andAppend("transactionId", response.id),
                    "CafController#createTransaction",
                )
                HttpResponse.status<CreateTransactionResponseTO>(HttpStatus.CREATED).body(responseTO)
            },
        )
    }

    @Get("/transactions")
    fun listTransactions(): HttpResponse<*> {
        logger.info("CafController#listTransactions")

        return cafService.listTransactions().fold(
            ifLeft = { ex ->
                logger.error("CafController#listTransactions", ex)
                val errorResponse = ErrorResponseTO(
                    message = "Failed to list CAF transactions: ${ex.message}",
                    code = "CAF_LIST_ERROR",
                )
                HttpResponse.status<ErrorResponseTO>(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse)
            },
            ifRight = { response ->
                val responseTO = ListTransactionsResponseTO(
                    requestId = response.requestId,
                    items = response.items.map { item ->
                        TransactionItemTO(
                            id = item.id,
                            status = item.status,
                            createdAt = item.createdAt,
                            data = item.data?.let { data ->
                                TransactionDataTO(
                                    cpf = data.cpf,
                                    birthDate = data.birthDate,
                                    name = data.name,
                                )
                            },
                        )
                    },
                    totalItems = response.totalItems,
                    success = true,
                )
                logger.info(
                    append("totalItems", response.totalItems),
                    "CafController#listTransactions",
                )
                HttpResponse.ok(responseTO)
            },
        )
    }

    @Get("/transactions/{transactionId}")
    fun getTransaction(@PathVariable transactionId: String): HttpResponse<*> {
        val markers = append("transactionId", transactionId)

        return cafService.getTransaction(transactionId).fold(
            ifLeft = { ex ->
                logger.error(markers, "CafController#getTransaction", ex)
                val errorResponse = ErrorResponseTO(
                    message = "Failed to retrieve CAF transaction: ${ex.message}",
                    code = "CAF_GET_ERROR",
                )
                HttpResponse.status<ErrorResponseTO>(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse)
            },
            ifRight = { response ->
                val responseTO = GetTransactionResponseTO(
                    requestId = response.requestId,
                    id = response.id,
                    status = response.status,
                    success = true,
                )
                logger.info(
                    markers.andAppend("status", response.status),
                    "CafController#getTransaction",
                )
                HttpResponse.ok(responseTO)
            },
        )
    }

    companion object {
        private val logger = LoggerFactory.getLogger(CafController::class.java)
    }
}

fun List<FileDataTO>.toListFileData(): List<FileData> =
    this.map {
        FileData(
            data = it.data,
            type = it.type,
        )
    }

data class CreateTransactionRequestTO(
    val templateId: String,
    val files: List<FileDataTO>,
)

data class FileDataTO(
    val data: String,
    val type: String,
)

data class CreateTransactionResponseTO(
    val requestId: String,
    val id: String?,
    val status: String?,
    val success: Boolean = true,
    val message: String? = null,
)

data class ListTransactionsResponseTO(
    val requestId: String,
    val items: List<TransactionItemTO>,
    val totalItems: Int,
    val success: Boolean = true,
    val message: String? = null,
)

data class TransactionItemTO(
    val id: String,
    val status: String,
    val createdAt: LocalDateTime,
    val data: TransactionDataTO?,
)

data class TransactionDataTO(
    val cpf: String?,
    val birthDate: String?,
    val name: String?,
)

data class GetTransactionResponseTO(
    val requestId: String,
    val id: String,
    val status: String,
    val success: Boolean = true,
    val message: String? = null,
)

data class ErrorResponseTO(
    val success: Boolean = false,
    val message: String,
    val code: String? = null,
    val details: Map<String, Any>? = null,
)