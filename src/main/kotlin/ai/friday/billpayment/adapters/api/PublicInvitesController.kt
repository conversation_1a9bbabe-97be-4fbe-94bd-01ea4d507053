package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.wallet.InviteErrors
import ai.friday.billpayment.app.wallet.ReadInviteError
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import arrow.core.getOrElse
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpResponseFactory
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.security.annotation.Secured
import io.micronaut.security.rules.SecurityRule
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Secured(SecurityRule.IS_ANONYMOUS)
@Controller("/public")
@Version("2")
class PublicInvitesController(private val walletService: WalletService) {

    @Get("/invite/{walletId}/{document}")
    fun getInvite(
        @PathVariable walletId: String,
        @PathVariable document: String,
    ): HttpResponse<*> {
        val markers = append("walletId", walletId).and<LogstashMarker>(append("document", document))
        return walletService.findPendingInvite(WalletId(walletId), document)
            .map {
                HttpResponse.ok(it.toInviteResponseTO())
            }.getOrElse {
                when (it) {
                    InviteErrors.ItemNotFound -> StandardHttpResponses.notFound(InviteErrorsResponse.InviteNotFound.toResponseTO())
                    InviteErrors.InviteNotPending -> HttpResponseFactory.INSTANCE.status<Any>(HttpStatus.GONE)
                        .body(InviteErrorsResponse.InviteNotPending.toResponseTO())

                    is InviteErrors.ServerError -> StandardHttpResponses.serverError(
                        ResponseTO(
                            code = "5000",
                            message = it.error.message.orEmpty(),
                        ),
                    )
                }.also { response ->
                    logError(it, response, markers)
                }
            }
    }

    private fun logError(error: ReadInviteError, response: HttpResponse<out Any>, markers: LogstashMarker) {
        markers.and<LogstashMarker>(append("error", error.javaClass.name))
            .and<LogstashMarker>(append("response", response.status))
        when (error) {
            InviteErrors.InviteNotPending,
            InviteErrors.ItemNotFound,
            -> LOG.warn(markers, "findPublicInvite")

            is InviteErrors.ServerError -> LOG.error(markers, "findPublicInvite", error.error)
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(PublicInvitesController::class.java)
    }
}