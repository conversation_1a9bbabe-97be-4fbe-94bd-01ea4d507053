package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.auth.getMainRole
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.documentscan.DocumentScanService
import ai.friday.billpayment.app.documentscan.UpsertDocumentScanIdError
import ai.friday.billpayment.app.integrations.DocumentType
import ai.friday.morning.log.andAppend
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Secured(Role.Code.GUEST, Role.Code.OWNER)
@Controller("/register")
@Version("2")
class DocumentScanController(
    private val documentScanService: DocumentScanService,
) {

    @Post("/document-scan")
    fun postDocumentScan(
        authentication: Authentication,
        @Body request: PostDocumentScanRequestTO,
    ): HttpResponse<*> {
        val markers = Markers.append("accountId", authentication.toAccountId())
            .andAppend("role", authentication.roles)

        return documentScanService.upsertDocumentScanId(
            authentication.toAccountId(),
            DocumentType.of(request.documentType),
            authentication.getMainRole(),
        )
            .fold(
                ifLeft = {
                    markers.andAppend("error", it)
                    when (it) {
                        is UpsertDocumentScanIdError.Unexpected -> {
                            logger.error(markers, "DocumentScanController#postDocumentScan")
                            HttpResponse.serverError(mapOf("error" to it))
                        }

                        UpsertDocumentScanIdError.AccountNotFound -> {
                            logger.warn(markers, "DocumentScanController#postDocumentScan")
                            StandardHttpResponses.notFound("Account not found")
                        }

                        UpsertDocumentScanIdError.IllegalAccountRole,
                        UpsertDocumentScanIdError.IllegalAccountStatus,
                        UpsertDocumentScanIdError.IllegalAccountType,
                        -> {
                            logger.warn(markers, "DocumentScanController#postDocumentScan")
                            StandardHttpResponses.conflict(
                                ResponseTO(
                                    "4090",
                                    "Não é possível realizar o scan do documento",
                                ),
                            )
                        }
                    }
                },
                ifRight = {
                    logger.info(markers.andAppend("documentScanId", it.value), "DocumentScanController#postDocumentScan")
                    HttpResponse.created(mapOf("documentScanId" to it.value))
                },
            )
    }

    companion object {
        private val logger = LoggerFactory.getLogger(DocumentScanController::class.java)
    }
}

data class PostDocumentScanRequestTO(
    val documentType: String,
)