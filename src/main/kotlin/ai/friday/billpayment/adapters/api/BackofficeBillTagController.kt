package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillTag
import ai.friday.billpayment.app.bill.BillTagAdded
import ai.friday.billpayment.app.bill.BillTagDeleted
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Delete
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Put
import io.micronaut.security.annotation.Secured
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Secured(Role.Code.BACKOFFICE)
@Controller("/backoffice/bill")
class BackofficeBillTagController(
    private val billEventRepository: BillEventRepository,
    private val billEventPublisher: BillEventPublisher,
) {
    private val logger = LoggerFactory.getLogger(BackofficeBillTagController::class.java)

    @Put("/{billId}/tag/{tag}")
    fun put(@PathVariable billId: String, @PathVariable tag: BillTag): HttpResponse<*> {
        val markers = Markers.append("billId", billId)
            .andAppend("tag", tag)

        return billEventRepository.getBillById(BillId(billId)).map {
            billEventPublisher.publish(
                bill = it,
                event = BillTagAdded(
                    billId = it.billId,
                    walletId = it.walletId,
                    actionSource = ActionSource.System,
                    tag = tag,
                ),
            )

            logger.info(markers, "BackofficeBillTagController#put")
            HttpResponse.accepted<Unit>()
        }.getOrElse {
            logger.error(markers, "BackofficeBillTagController#put", it)

            HttpResponse.serverError(it.message)
        }
    }

    @Delete("/{billId}/tag/{tag}")
    fun delete(@PathVariable billId: String, @PathVariable tag: BillTag): HttpResponse<*> {
        val markers = Markers.append("billId", billId)
            .andAppend("tag", tag)

        return billEventRepository.getBillById(BillId(billId)).map {
            billEventPublisher.publish(
                bill = it,
                event = BillTagDeleted(
                    billId = it.billId,
                    walletId = it.walletId,
                    actionSource = ActionSource.System,
                    tag = tag,
                ),
            )

            logger.info(markers, "BackofficeBillTagController#delete")
            HttpResponse.accepted<Unit>()
        }.getOrElse {
            logger.error(markers, "BackofficeBillTagController#delete", it)

            HttpResponse.serverError(it.message)
        }
    }
}