package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.account.AccountGroupFilter
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.GroupFilterContext
import ai.friday.billpayment.app.account.RegistrationType
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.account.SignInPendingAction
import ai.friday.billpayment.app.account.UpgradeStatus
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.account.hasDeveloperEarlyAccess
import ai.friday.billpayment.app.auth.AuthenticationService
import ai.friday.billpayment.app.auth.UserPrincipal
import ai.friday.billpayment.app.auth.getMainRole
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.auth.toUserPrincipal
import ai.friday.billpayment.app.feature.FeaturesRepository
import ai.friday.billpayment.app.feature.isMaintenanceMode
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.morning.log.andAppend
import com.fasterxml.jackson.annotation.JsonInclude
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.Header
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Secured(Role.Code.OWNER, Role.Code.GUEST)
@Controller("/principal")
@Version("2")
class PrincipalController(
    private val accountService: AccountService,
    private val accountGroupFilter: AccountGroupFilter,
    private val authenticationService: AuthenticationService,
    private val featuresRepository: FeaturesRepository,
) {
    private val logger = LoggerFactory.getLogger(PrincipalController::class.java)

    @Get
    fun getPrincipal(
        authentication: Authentication,
        @Header("X-FRIDAY-VERSION") version: String? = null,
        @Header("X-FRIDAY-PLATFORM") platform: String? = null,
        @Header("x-device-fingerprint") deviceFingerprint: String? = null,
    ): HttpResponse<*> {
        val logName = "GetPrincipal"
        val markers = append("accountId", authentication.name)
            .andAppend("xFridayVersion", version)
            .andAppend("xFridayPlatform", platform)
            .andAppend("xDeviceFingerPrint", deviceFingerprint)

        val role = authentication.getMainRole()
        markers.andAppend("role", role)

        try {
            val user = authentication.toUserPrincipal(deviceFingerprint = deviceFingerprint)
            markers.andAppend("user", user)

            if (user == null) {
                logger.warn(markers, logName)
                return StandardHttpResponses.customStatusResponse(
                    HttpStatus.FORBIDDEN,
                    ResponseTO(
                        code = "4031",
                        message = "You don't have permission to access this resource",
                    ),
                )
            }

            val principalTO = user.toPrincipalTO(version = version, platform = platform)
            markers.andAppend("principalTO", principalTO)

            logger.info(markers, logName)
            return HttpResponse.ok(principalTO)
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return StandardHttpResponses.serverError(e.message.orEmpty())
        }
    }

    private fun UserPrincipal.toPrincipalTO(version: String?, platform: String?) = when (this) {
        is UserPrincipal.Guest -> toGuestPrincipalTO()
        is UserPrincipal.Owner -> toOwnerPrincipalTO(version = version, platform = platform)
    }

    private fun UserPrincipal.Guest.toGuestPrincipalTO(): GuestPrincipalTO {
        val partialAccount = accountService.findPartialAccountById(accountId = accountId)

        if (partialAccount.registrationType == RegistrationType.FULL) {
            logger.warn(
                append("type", RegistrationType.FULL.name).andAppend("accountId", partialAccount.id.value),
                "GetPrincipal#toGuestPrincipalTO",
            )
        }

        return GuestPrincipalTO(
            accountId = accountId.value,
            role = Role.GUEST.name,
            nickname = partialAccount.name,
            maintenanceMode = featuresRepository.getAll().isMaintenanceMode(accountId),
            groups = partialAccount.groups.map { it.value },
            registrationType = partialAccount.registrationType.name,
            subscriptionType = partialAccount.subscriptionType,
        )
    }

    private fun UserPrincipal.Owner.toOwnerPrincipalTO(version: String?, platform: String?): OwnerPrincipalTO {
        val account = accountService.findAccountById(accountId)

        val pendingAction = authenticationService.getSignInPendingAction(this).let {
            if (it == SignInPendingAction.DEVICE_BINDING_REQUIRED && account.hasGroup(AccountGroup.APP_REVIEWER)) {
                SignInPendingAction.NONE
            } else {
                it
            }
        }

        val upgradeStatus = account.upgradeStatus
            ?: if (account.type == UserAccountType.FULL_ACCOUNT) UpgradeStatus.COMPLETED else UpgradeStatus.INCOMPLETE

        val userGroups = accountGroupFilter.filterGroupsByVersion(
            GroupFilterContext(account, version = version, platform = platform),
        )

        return OwnerPrincipalTO(
            accountId = accountId.value,
            role = Role.OWNER.name,
            nickname = account.name,
            migrated = when (pendingAction) {
                SignInPendingAction.NONE -> null
                SignInPendingAction.CREATE_PASSWORD_REQUIRED -> false
                SignInPendingAction.PASSWORD_AUTHENTICATION_REQUIRED -> true
                SignInPendingAction.DEVICE_BINDING_REQUIRED -> null
            },
            maintenanceMode = featuresRepository.getAll().isMaintenanceMode(accountId),
            maintenanceServices = if (accountId.hasDeveloperEarlyAccess()) {
                emptyList()
            } else {
                featuresRepository.getAll().maintenanceServices.map { it.name }
            },
            status = account.status.name,
            upgradeStatus = upgradeStatus.name,
            type = account.type.name,
            groups = userGroups.map { it.value },
            subscriptionType = account.subscriptionType,
            pendingAction = when (pendingAction) {
                SignInPendingAction.NONE -> null
                else -> pendingAction.name
            },
        )
    }
}

fun Authentication.toContactAccountId() =
    if (asWalletMember().permissions.founderContactsEnabled) getWallet().founder.accountId else toAccountId()

infix fun Authentication.hasMainRole(role: Role) = role == getMainRole()
infix fun Authentication.hasRole(role: Role) = roles.contains(role.name)

interface PrincipalTO {
    val accountId: String
    val role: String
    val nickname: String
    val maintenanceMode: Boolean
    val groups: List<String>
    val subscriptionType: SubscriptionType
}

@JsonInclude(JsonInclude.Include.ALWAYS)
data class GuestPrincipalTO(
    override val accountId: String,
    override val role: String,
    override val nickname: String,
    override val maintenanceMode: Boolean,
    override val groups: List<String>,
    val registrationType: String,
    override val subscriptionType: SubscriptionType,
) : PrincipalTO

@JsonInclude(JsonInclude.Include.ALWAYS)
data class OwnerPrincipalTO(
    override val accountId: String,
    override val role: String,
    override val nickname: String,
    val status: String,
    val upgradeStatus: String,
    @Deprecated("Usamos o pendingAction")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    val migrated: Boolean? = null,
    override val maintenanceMode: Boolean,
    val maintenanceServices: List<String>,
    override val groups: List<String>,
    val type: String,
    override val subscriptionType: SubscriptionType,
    val pendingAction: String?,
) : PrincipalTO