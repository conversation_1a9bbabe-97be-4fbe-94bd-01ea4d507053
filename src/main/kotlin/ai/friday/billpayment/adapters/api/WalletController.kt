package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.account.SystemActivityService
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.account.canSetAsDefault
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.MoveBillErrorReason
import ai.friday.billpayment.app.bill.MoveBillService
import ai.friday.billpayment.app.bill.MoveBillsError
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.pfm.WalletBillCategory
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.usage.AccountUsageService
import ai.friday.billpayment.app.usage.MAX_DAILY_PAYMENT_LIMIT_AMOUNT
import ai.friday.billpayment.app.usage.MAX_NIGHTTIME_PAYMENT_LIMIT_AMOUNT
import ai.friday.billpayment.app.usage.MAX_WHATSAPP_PAYMENT_LIMIT_AMOUNT
import ai.friday.billpayment.app.usage.Usage
import ai.friday.billpayment.app.usage.WalletLimitsService
import ai.friday.billpayment.app.wallet.BillPermission
import ai.friday.billpayment.app.wallet.CreateWalletErrors
import ai.friday.billpayment.app.wallet.CustomPermissionsMemberType
import ai.friday.billpayment.app.wallet.CustomPermissionsOptions
import ai.friday.billpayment.app.wallet.CustomViewBillPermission
import ai.friday.billpayment.app.wallet.DailyPaymentLimitType
import ai.friday.billpayment.app.wallet.GetLimitsErrors
import ai.friday.billpayment.app.wallet.Invite
import ai.friday.billpayment.app.wallet.InviteAnswer
import ai.friday.billpayment.app.wallet.InviteErrors
import ai.friday.billpayment.app.wallet.MemberPermissions
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.ReadInviteError
import ai.friday.billpayment.app.wallet.RemoveMemberErrors
import ai.friday.billpayment.app.wallet.SecondaryWalletService
import ai.friday.billpayment.app.wallet.UpdateLimitsErrors
import ai.friday.billpayment.app.wallet.UpdateMemberCommand
import ai.friday.billpayment.app.wallet.UpdateMemberErrors
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletPersonType
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.app.wallet.WalletType
import ai.friday.billpayment.app.wallet.canViewStatement
import ai.friday.billpayment.app.wallet.toMemberType
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.dateTimeFormat
import ai.friday.morning.log.andAppend
import arrow.core.flatMap
import arrow.core.getOrElse
import com.fasterxml.jackson.annotation.JsonInclude
import io.micronaut.core.annotation.Introspected
import io.micronaut.core.annotation.Nullable
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpResponseFactory
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Delete
import io.micronaut.http.annotation.Error
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.http.annotation.Put
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import io.micronaut.validation.Validated
import jakarta.inject.Singleton
import jakarta.validation.ConstraintViolationException
import jakarta.validation.Valid
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Validated
@Secured(Role.Code.OWNER)
@Controller("/wallet")
@Version("2")
class WalletController(
    private val walletService: WalletService,
    private val accountService: AccountService,
    private val walletBillCategoryService: PFMWalletCategoryService?,
    @Nullable private val secondaryWalletService: SecondaryWalletService?,
) {
    @Get
    fun listWallets(
        authentication: Authentication,
    ): HttpResponse<*> {
        val markers = append("accountId", authentication.name)

        return try {
            val account = accountService.findAccountById(authentication.toAccountId())
            val wallets = walletService.findWallets(authentication.toAccountId())

            HttpResponse.ok(
                wallets.map { wallet ->
                    val categories = walletBillCategoryService?.findWalletCategories(wallet.id)
                    val pixKeys = walletService.walletPixKeys(wallet)

                    markers.andAppend("founderDocument", wallet.founder.document).andAppend("walletId", wallet.id.value)
                    wallet.toWalletTO(account, pixKeys.email, pixKeys.evp, categories)
                },
            )
        } catch (e: Exception) {
            LOG.error(markers, "getWallets", e)
            StandardHttpResponses.serverError(ResponseTO(code = "5000", message = e.message.orEmpty()))
        }
    }

    @Get("/{walletId}")
    fun getWallet(authentication: Authentication, @PathVariable walletId: String): HttpResponse<*> {
        val wallet = walletService.findWalletOrNull(WalletId(walletId))

        if (wallet == null) {
            return StandardHttpResponses.notFound(ResponseTO("4004", "wallet $walletId not found"))
        }
        if (!wallet.hasActiveMember(authentication.toAccountId())) {
            return HttpResponseFactory.INSTANCE.status<Unit>(HttpStatus.FORBIDDEN)
        }

        val account = accountService.findAccountById(authentication.toAccountId())

        val pixKeys = walletService.walletPixKeys(wallet)

        val billCategories = walletBillCategoryService?.findWalletCategories(wallet.id)
        return HttpResponse.ok(wallet.toWalletTO(account, pixKeys.email, pixKeys.evp, billCategories))
    }

    @Post
    fun createWallet(
        @Body @Valid
        createWalletTO: CreateWalletTO,
        authentication: Authentication,
    ): HttpResponse<*> {
        return try {
            val accountId = authentication.toAccountId()
            val account = accountService.findAccountById(accountId)

            secondaryWalletService!!.create(account, createWalletTO.name).map { wallet ->
                val pixKeys = walletService.walletPixKeys(wallet)

                val billCategories = walletBillCategoryService?.findWalletCategories(wallet.id)
                HttpResponse.created(wallet.toWalletTO(account, pixKeys.email, pixKeys.evp, billCategories))
            }.getOrElse {
                when (it) {
                    CreateWalletErrors.MaxWalletsAllowed -> StandardHttpResponses.badRequest(
                        ResponseTO(
                            code = "4001",
                            message = "User reached max wallets allowed",
                        ),
                    )

                    CreateWalletErrors.WalletNameAlreadyExists -> StandardHttpResponses.badRequest(
                        ResponseTO(
                            code = "4002",
                            message = "User already has a wallet named '${createWalletTO.name}'",
                        ),
                    )

                    CreateWalletErrors.RegisterNaturalPersonError -> StandardHttpResponses.badRequest(
                        ResponseTO(
                            code = "4003",
                            message = "Error creating register natural person",
                        ),
                    )

                    CreateWalletErrors.BasicAccountNotAllowed -> StandardHttpResponses.badRequest(
                        ResponseTO(
                            code = "4004",
                            message = "Basic account cannot create secondary wallet",
                        ),
                    )

                    CreateWalletErrors.RegisterEvpPixKey -> StandardHttpResponses.badRequest(
                        ResponseTO(
                            code = "4005",
                            message = "Register EVP Pix Key error",
                        ),
                    )
                }
            }
        } catch (e: Exception) {
            LOG.error(append("accountId", authentication.name), "createWallet", e)
            StandardHttpResponses.serverError(ResponseTO(code = "5000", message = e.message.orEmpty()))
        }
    }

    @Post("/{walletId}/invite")
    fun createInvite(
        authentication: Authentication,
        @PathVariable walletId: String,
        @Body @Valid
        request: InviteRequestTO,
    ): HttpResponse<*> {
        val markers = append("accountId", authentication.name)
            .and<LogstashMarker>(append("walletId", walletId))
            .and<LogstashMarker>(append("inviteRequest", request))
        return walletService.createInvite(
            accountId = authentication.toAccountId(),
            walletId = WalletId(walletId),
            document = request.document,
            name = request.name,
            type = request.type.toMemberType(),
            email = EmailAddress(request.email),
            permissions = if (request.permissions != null) MemberPermissions.of(CustomPermissionsOptions(memberType = request.type, viewBills = request.permissions.viewBills)) else MemberPermissions.of(request.type.toMemberType()),
        ).map {
            LOG.info(markers.and(append("invite", it)), "createInvite")
            HttpResponse.created(it.toInviteResponseTO())
        }.getOrElse {
            handleErrors(it, markers, "createInvite")
        }
    }

    @Get("/{walletId}/invites")
    fun listInvites(
        authentication: Authentication,
        @PathVariable walletId: String,
    ): HttpResponse<*> {
        val markers = append("accountId", authentication.name)
            .and<LogstashMarker>(append("walletId", walletId))

        return walletService.authorizeMember(accountId = authentication.toAccountId(), walletId = WalletId(walletId))
            .flatMap { walletService.listInvites(walletId = WalletId(walletId)) }
            .map { invites ->
                LOG.info(markers, "listInvites")
                HttpResponse.ok(invites.map { it.toInviteResponseTO() })
            }
            .getOrElse { handleErrors(it, markers, "listInvites") }
    }

    @Get("/{walletId}/invite")
    fun getInvite(authentication: Authentication, @PathVariable walletId: String): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val markers = append("walletId", walletId).and<LogstashMarker>(append("accountId", accountId.value))
        val account = accountService.findAccountById(accountId)
        return walletService.findPendingInvite(WalletId(walletId), account.document)
            .map {
                HttpResponse.ok(it.toInviteResponseTO())
            }.getOrElse {
                when (it) {
                    InviteErrors.ItemNotFound -> StandardHttpResponses.notFound(InviteErrorsResponse.InviteNotFound.toResponseTO())
                    InviteErrors.InviteNotPending -> HttpResponseFactory.INSTANCE.status<Any>(HttpStatus.GONE)
                        .body(InviteErrorsResponse.InviteNotPending.toResponseTO())

                    is InviteErrors.ServerError -> StandardHttpResponses.serverError(
                        ResponseTO(
                            code = "5000",
                            message = it.error.message.orEmpty(),
                        ),
                    )
                }.also { response ->
                    logError(it, response, markers)
                }
            }
    }

    private fun logError(error: ReadInviteError, response: HttpResponse<out Any>, markers: LogstashMarker) {
        markers.and<LogstashMarker>(append("error", error.javaClass.name))
            .and<LogstashMarker>(append("response", response.status))
        when (error) {
            InviteErrors.InviteNotPending,
            InviteErrors.ItemNotFound,
            -> LOG.warn(markers, "findPublicInvite")

            is InviteErrors.ServerError -> LOG.error(markers, "findPublicInvite", error.error)
        }
    }

    @Put("/{walletId}/invite")
    fun inviteAnswer(
        authentication: Authentication,
        @PathVariable walletId: String,
        @Body inviteAnswer: InviteAnswerTO,
    ): HttpResponse<*> {
        val markers = append("accountId", authentication.name)
            .and<LogstashMarker>(append("walletId", walletId))
            .and<LogstashMarker>(append("answer", inviteAnswer.action))
        val account = accountService.findAccountById(authentication.toAccountId())

        return walletService.answerInvite(
            account = account,
            walletId = WalletId(walletId),
            answer = inviteAnswer.action,
        )
            .map {
                LOG.info(markers, "inviteAnswer")
                HttpResponse.noContent<Unit>()
            }.getOrElse {
                when (it) {
                    InviteErrors.ItemNotFound -> StandardHttpResponses.notFound(
                        ResponseTO(
                            code = "4001",
                            message = "Invite not found for document",
                        ),
                    )

                    InviteErrors.InviteNotPending -> HttpResponseFactory.INSTANCE.status<Any>(HttpStatus.GONE)
                    is InviteErrors.ServerError -> StandardHttpResponses.serverError(
                        ResponseTO(
                            code = "5000",
                            it.error.message.orEmpty(),
                        ),
                    )

                    else -> StandardHttpResponses.serverError(ResponseTO(code = "5001", "Unknown error $it"))
                }.also { response ->
                    logError(it, response, markers, "inviteAnswer")
                }
            }
    }

    @Delete("/{walletId}/invite/{document}")
    fun cancelInvite(
        authentication: Authentication,
        @PathVariable walletId: String,
        @PathVariable document: String,
    ): HttpResponse<*> {
        val markers = append("walletId", walletId)
            .and<LogstashMarker>(append("accountId", authentication.toAccountId()))
            .and<LogstashMarker>(append("document", document))
        return walletService.authorizeManageMembers(authentication.toAccountId(), WalletId(walletId))
            .flatMap { walletService.cancelInvite(WalletId(walletId), document) }
            .map {
                LOG.info(markers, "cancelInvite")
                HttpResponseFactory.INSTANCE.status<Any>(HttpStatus.ACCEPTED)
            }
            .getOrElse { handleCancelInviteErrors(it, markers, "cancelInvite") }
    }

    @Delete("/{walletId}/member/{accountId}")
    fun removeMember(
        authentication: Authentication,
        @PathVariable walletId: String,
        @PathVariable accountId: String,
    ): HttpResponse<*> {
        val marker = append("accountId", authentication.toAccountId().value)
            .andAppend("walletId", walletId)
            .andAppend("targetAccountId", accountId)

        walletService.removeMember(
            walletId = WalletId(walletId),
            source = ActionSource.Api(accountId = authentication.toAccountId()),
            targetAccountId = AccountId(accountId),
        ).getOrElse {
            marker.and<LogstashMarker>(append("removeMemberErrors", it.javaClass.name))
            return when (it) {
                RemoveMemberErrors.WalletNotFound, RemoveMemberErrors.MemberNotFound -> {
                    LOG.warn(marker, "walletRemoveMember")
                    StandardHttpResponses.notFound("Wallet not found")
                }

                RemoveMemberErrors.FounderCannotBeRemoved, RemoveMemberErrors.CoFounderCannotBeRemovedFromLegalPersonWallet, RemoveMemberErrors.AccountHasNotPermissionToRemoveMembers, RemoveMemberErrors.InvalidSource -> {
                    LOG.warn(marker, "walletRemoveMember")
                    HttpResponse.unprocessableEntity<Unit>()
                }

                is RemoveMemberErrors.Unknown -> {
                    LOG.error(marker, "walletRemoveMember", it.e)
                    StandardHttpResponses.serverError()
                }
            }
        }
        LOG.info(marker, "walletRemoveMember")
        return HttpResponse.noContent<Unit>()
    }

    @Put("/{walletId}/member/{accountId}")
    fun updateMember(
        authentication: Authentication,
        @PathVariable walletId: String,
        @PathVariable accountId: String,
        @Body options: CustomPermissionsOptions,
    ): HttpResponse<*> {
        val marker = append("accountId", authentication.toAccountId().value)
            .andAppend("walletId", walletId)
            .andAppend("targetAccountId", accountId)
            .andAppend("body", options)

        walletService.updateMember(
            UpdateMemberCommand(
                requesterAccountId = authentication.toAccountId(),
                memberAccountId = AccountId(accountId),
                walletId = WalletId(walletId),
                options = options,
            ),
        ).getOrElse {
            LOG.warn(marker.andAppend("updateMemberError", it.javaClass.simpleName), "walletUpdateMember")
            return when (it) {
                UpdateMemberErrors.WalletNotFound -> StandardHttpResponses.notFound("Wallet not found")
                UpdateMemberErrors.AccountHasNotPermissionToUpdateMembers -> HttpResponse.unprocessableEntity<Unit>()
                UpdateMemberErrors.MemberNotFound -> HttpResponse.notFound("Member not found")

                UpdateMemberErrors.MemberCantUpdateItself -> StandardHttpResponses.customStatusResponse(
                    HttpStatus.UNPROCESSABLE_ENTITY,
                    ResponseTO("42202", "Founder can't be updated"),
                )

                is UpdateMemberErrors.Unknown -> {
                    LOG.error(marker, "walletUpdateMember", it.e)
                    StandardHttpResponses.serverError()
                }
            }
        }
        LOG.info(marker, "walletUpdateMember")
        return HttpResponse.noContent<Unit>()
    }

    private fun handleCancelInviteErrors(
        error: InviteErrors,
        markers: LogstashMarker,
        message: String,
    ): HttpResponse<*> {
        return when (error) {
            InviteErrors.ItemNotFound -> StandardHttpResponses.notFound(InviteErrorsResponse.InviteNotFound.toResponseTO())
            InviteErrors.InviteNotPending -> HttpResponseFactory.INSTANCE.status(HttpStatus.GONE)
            is InviteErrors.UserForbidden -> HttpResponseFactory.INSTANCE.status(HttpStatus.FORBIDDEN)
            else -> StandardHttpResponses.serverError(ResponseTO(code = "5001", message = "unknown error"))
        }.also { response ->
            logError(error, response, markers, message)
        }
    }

    private fun handleErrors(error: InviteErrors, markers: LogstashMarker, message: String): HttpResponse<*> {
        return when (error) {
            InviteErrors.PendingInvitationsLimitReached -> StandardHttpResponses.badRequest(InviteErrorsResponse.PendingInvitationsLimitReached.toResponseTO())
            InviteErrors.UserAlreadyMember -> StandardHttpResponses.badRequest(InviteErrorsResponse.UserAlreadyMember.toResponseTO())
            InviteErrors.InviteFounderNotAllowed -> StandardHttpResponses.badRequest(InviteErrorsResponse.InviteFounderNotAllowed.toResponseTO())
            is InviteErrors.UserForbidden -> HttpResponseFactory.INSTANCE.status(HttpStatus.FORBIDDEN)
            InviteErrors.ItemNotFound -> StandardHttpResponses.notFound(InviteErrorsResponse.WalletNotFound.toResponseTO())
            is InviteErrors.UserAlreadyInvited -> HttpResponseFactory.INSTANCE.status<Any>(HttpStatus.CONFLICT)
                .body(error.invite.toInviteResponseTO())

            is InviteErrors.ServerError -> StandardHttpResponses.serverError(
                ResponseTO(
                    code = "5000",
                    message = error.error.message.orEmpty(),
                ),
            )

            else -> StandardHttpResponses.serverError(ResponseTO(code = "5001", message = "unknown error"))
        }.also { response ->
            logError(error, response, markers, message)
        }
    }

    private fun logError(
        error: InviteErrors,
        response: HttpResponse<out Any>,
        markers: LogstashMarker,
        message: String,
    ) {
        markers.and<LogstashMarker>(append("error", error.javaClass.name))
            .and<LogstashMarker>(append("response", response.status))
        when (error) {
            InviteErrors.PendingInvitationsLimitReached,
            is InviteErrors.UserAlreadyInvited,
            InviteErrors.UserAlreadyMember,
            -> LOG.info(markers, message)

            is InviteErrors.UserForbidden,
            InviteErrors.ItemNotFound,
            -> LOG.error(markers, message)

            is InviteErrors.ServerError -> LOG.error(markers, message, error.error)

            else -> LOG.error(markers.and(append("unexpectedError", error)), message)
        }
    }

    @Error
    fun handleConstraintViolationException(
        request: HttpRequest<*>,
        exception: ConstraintViolationException,
    ): HttpResponse<ResponseTO> {
        return HttpResponse.badRequest(
            ResponseTO(
                message = exception.constraintViolations.first().message,
                code = "4004",
            ),
        )
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(WalletController::class.java)
    }
}

@Singleton
@Version("2")
@Secured(Role.Code.OWNER)
@Controller("/wallet/{walletId}/whatsAppPayment")
class WalletWhatsAppPaymentController(
    private val systemActivityService: SystemActivityService,
) {

    @Get("/enable")
    fun checkWhatsAppPaymentIsEnabled(
        authentication: Authentication,
        @PathVariable walletId: String,
    ): HttpResponse<*> {
        val logName = "WalletLimitController#checkWhatsAppPaymentIsEnabled"
        val markers = append("accountId", authentication.toAccountId().value).andAppend("walletId", walletId)

        return try {
            val isEnabled = systemActivityService.getWhatsAppPayment(walletId = WalletId(walletId))
            LOG.info(markers.andAppend("isEnabled", isEnabled), logName)
            HttpResponse.ok(WhatsAppPaymentLimitIsEnabledResponseTO(isEnabled))
        } catch (e: Exception) {
            LOG.error(markers, logName, e)
            StandardHttpResponses.serverError()
        }
    }

    @Put("/enable")
    fun enableWhatsAppPayment(
        authentication: Authentication,
        @PathVariable walletId: String,
        @Body body: WhatsAppPaymentLimitRequestTO,
    ): HttpResponse<*> {
        val logName = "WalletLimitController#enableWhatsAppPayment"
        val markers = append("accountId", authentication.toAccountId().value).andAppend("walletId", walletId).andAppend("enabled", body.enabled)

        return try {
            systemActivityService.setWhatsAppPayment(walletId = WalletId(walletId), active = body.enabled)
            LOG.info(markers, logName)
            HttpResponse.ok<Unit>()
        } catch (e: Exception) {
            LOG.error(markers, logName, e)
            StandardHttpResponses.serverError()
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(WalletWhatsAppPaymentController::class.java)
    }
}

@Validated
@Secured(Role.Code.OWNER)
@Controller("/wallet")
@Version("2")
class WalletLimitController(
    private val accountService: AccountService,
    private val walletLimitsService: WalletLimitsService,
    private val accountUsageService: AccountUsageService,
) {

    @Put("/{walletId}/limit")
    fun updateLimit(
        authentication: Authentication,
        @PathVariable walletId: String,
        @Body updateLimits: DailyPaymentLimitRequestTO,
    ): HttpResponse<*> {
        val markers = append("accountId", authentication.toAccountId().value).andAppend("walletId", walletId)
            .andAppend("type", updateLimits.type).andAppend("amount", updateLimits.amount)

        return walletLimitsService.updateLimit(
            authentication.toAccountId(),
            WalletId(walletId),
            updateLimits.type,
            updateLimits.amount,
            ActionSource.Api(accountId = authentication.toAccountId()),
        )
            .map {
                LOG.info(
                    markers.andAppend("updatedLimit", it),
                    "UpdateLimits",
                )
                HttpResponse.noContent<Unit>()
            }
            .getOrElse {
                when (it) {
                    UpdateLimitsErrors.PendingLimitChange -> StandardHttpResponses.conflict(UpdateLimitsErrorsResponse.PendingLimitChange.toResponseTO())
                    UpdateLimitsErrors.ItemNotFound -> StandardHttpResponses.notFound(UpdateLimitsErrorsResponse.WalletNotFound.toResponseTO())
                    UpdateLimitsErrors.UpdateLimitNotAllowed -> HttpResponse.status<ResponseTO>(HttpStatus.FORBIDDEN)
                        .body(UpdateLimitsErrorsResponse.UpdateLimitNotAllowed.toResponseTO())

                    UpdateLimitsErrors.NighttimeLimitGreaterThanDaily -> StandardHttpResponses.badRequest(
                        UpdateLimitsErrorsResponse.NighttimeLimitGreaterThanDaily.toResponseTO(),
                    )

                    UpdateLimitsErrors.InvalidAmount -> {
                        StandardHttpResponses.badRequest(UpdateLimitsErrorsResponse.InvalidAmount.toResponseTO())
                    }

                    UpdateLimitsErrors.LimitIsNotActive -> HttpResponse.status<ResponseTO>(HttpStatus.FORBIDDEN)

                    UpdateLimitsErrors.WhatsAppPaymentLimitGreaterThanDaily -> HttpResponse.status<ResponseTO>(HttpStatus.FORBIDDEN)

                    is UpdateLimitsErrors.UnknownError -> {
                        LOG.error(
                            markers,
                            "UpdateLimits",
                            it.error,
                        )
                        StandardHttpResponses.serverError()
                    }
                }
            }
    }

    @Get("/{walletId}/limit")
    fun getLimits(authentication: Authentication, @PathVariable walletId: String): HttpResponse<*> {
        val wallet = authentication.getWallet()
        val founderAccount = accountService.findAccountById(wallet.founder.accountId)

        return accountUsageService.calculateUsage(authentication.toAccountId(), wallet.id)
            .map { HttpResponse.ok(it.toDailyPaymentLimitsTO(founderAccount)) }
            .getOrElse {
                when (it) {
                    GetLimitsErrors.WalletNotFound -> StandardHttpResponses.notFound(
                        ResponseTO(
                            code = "4041",
                            message = "Wallet not found",
                        ),
                    )

                    is GetLimitsErrors.UnknownError -> {
                        LOG.error(
                            append("accountId", authentication.toAccountId()).and(append("walletId", walletId)),
                            "GetLimits",
                            it.error,
                        )
                        StandardHttpResponses.serverError()
                    }
                }
            }
    }

    @Error
    fun handleConstraintViolationException(
        request: HttpRequest<*>,
        exception: ConstraintViolationException,
    ): HttpResponse<ResponseTO> {
        return HttpResponse.badRequest(
            ResponseTO(
                message = exception.constraintViolations.first().message,
                code = "4004",
            ),
        )
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(WalletController::class.java)
    }
}

@Validated
@Secured(Role.Code.OWNER)
@Controller("/wallet")
@Version("2")
class WalletBillController(
    private val moveBillService: MoveBillService,
) {
    @Post("/{walletId}/moveBillsTo/{destinationWalletId}")
    fun moveBills(
        authentication: Authentication,
        @PathVariable walletId: String,
        @PathVariable destinationWalletId: String,
        @Body moveBillsTO: MoveBillsTO,
    ): HttpResponse<*> {
        val markers = Markers.append("accountId", authentication.toAccountId().value)
            .andAppend("walletId", walletId)
            .andAppend("destinationWalletId", destinationWalletId)
            .andAppend("billIds", moveBillsTO.billList)
        return try {
            moveBillService.moveBills(
                accountId = authentication.toAccountId(),
                originWalletId = WalletId(value = walletId),
                destinationWalletId = WalletId(value = destinationWalletId),
                billIds = moveBillsTO.billList.map { BillId(it) },
            ).map { resultList ->
                val list = resultList.map {
                    MoveBillResultTO(billId = it.billId.value, moved = it.moved, reason = it.reason)
                }
                markers.andAppend("result", resultList)
                LOG.info(markers, "WalletBillController#moveBills")
                HttpResponse.ok(MoveBillsResultTO(list))
            }.getOrElse {
                LOG.error(markers.andAppend("error", it), "WalletBillController#moveBills")
                when (it) {
                    MoveBillsError.InvalidBillList -> StandardHttpResponses.badRequest(
                        ResponseTO(
                            code = "4001",
                            "BillId list can not contain duplicated elements",
                        ),
                    )

                    MoveBillsError.UserNotAllowed -> HttpResponse.status<Unit>(HttpStatus.FORBIDDEN)
                    is MoveBillsError.WalletNotFound -> StandardHttpResponses.notFound(
                        ResponseTO(
                            code = "4004",
                            "Wallet ${it.walletId.value} not found",
                        ),
                    )
                }
            }
        } catch (ex: Exception) {
            LOG.error(markers.andAppend("error", ex), "WalletBillController#moveBills")
            StandardHttpResponses.serverError(ResponseTO(code = "5000", message = ex.message.orEmpty()))
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(WalletBillController::class.java)
    }
}

data class InviteAnswerTO(val action: InviteAnswer)

internal enum class InviteErrorsResponse(val code: String, val message: String) {
    PendingInvitationsLimitReached("4000", "Pending invitations limit reached"),
    UserAlreadyMember("4001", "Invitee is already a wallet member"),
    ViewNoBillsMemberPermission("4002", "MemberPermissions to view NO_BILLS is not allowed"),
    InviteFounderNotAllowed("4003", "A member can't be invited to be a founder"),
    WalletNotFound(code = "4004", message = "Wallet not found"),
    InviteNotFound(code = "4005", message = "Invite not found"),
    InviteNotPending(code = "4100", message = "Invite not pending"),
    ;

    fun toResponseTO() = ResponseTO(code, message)
}

internal enum class UpdateLimitsErrorsResponse(val code: String, val message: String) {
    PendingLimitChange("4000", "Pending limit change"),
    WalletNotFound(code = "4004", message = "Wallet not found"),
    UpdateLimitNotAllowed(code = "4300", message = "Not allowed to update limits"),
    NighttimeLimitGreaterThanDaily(code = "4010", message = "Nighttime limit is greater than daily limit"),
    InvalidAmount(code = "4011", message = "Invalid amount"),
    LimitIsNotActive(code = "4012", message = "Limit is not active"),
    ;

    fun toResponseTO() = ResponseTO(code, message)
}

@Introspected
@JsonInclude(JsonInclude.Include.ALWAYS)
data class CreateWalletTO(
    @field:Size(min = 1, max = 20, message = "Invalid wallet name size")
    @field:Pattern(regexp = "^\$|^[\\w\\-\\s]+\$", message = "Wallet name with invalid characters")
    val name: String,
)

@JsonInclude(JsonInclude.Include.ALWAYS)
data class SummaryWalletTO(
    val id: String,
    val name: String,
    val members: List<MemberTO>,
    val theme: Int = 1,
    val thumbnailUrl: String = "",
)

@JsonInclude(JsonInclude.Include.ALWAYS)
data class WalletTO(
    val id: String,
    val name: String,
    val type: WalletType,
    val members: List<MemberTO>,
    val theme: Int = 1,
    val thumbnailUrl: String = "",
    val isDefault: Boolean,
    val canBeSetAsDefault: Boolean,
    val pixKey: PixKeyRequestTO,
    val evpPixKey: PixKeyRequestTO? = null,
    val personType: WalletPersonType,
    val billCategories: List<WalletBillCategoryTO>,
)

data class WalletBillCategoryTO(
    val billCategoryId: String,
    val name: String,
    val icon: String,
    val enabled: Boolean,
    val default: Boolean,
)

fun WalletBillCategory.toWalletBillCategoryTO() = WalletBillCategoryTO(
    billCategoryId = categoryId.value,
    name = name,
    icon = icon,
    enabled = enabled,
    default = default,
)

data class DailyPaymentLimitsTO(
    val editable: Boolean,
    val daily: DailyPaymentLimitTO,
    val nighttime: DailyPaymentLimitTO,
    val monthly: MontlhyPaymentLimitTO?,
    val whatsAppPayment: WhatsAppPaymentLimitTO,
    val automaticPix: DailyPaymentLimitTO,
    val creditCardMonthly: CreditCardMonthlyLimitTO?,
)

data class DailyPaymentLimitTO(
    val activeFrom: String,
    val currentAmount: Long,
    val nextAmount: Long? = null,
    val maxAmount: Long,
    val usedAmount: Long,
    val capped: Boolean,
)

data class WhatsAppPaymentLimitTO(
    val activeFrom: String,
    val currentAmount: Long,
    val nextAmount: Long? = null,
    val maxAmount: Long,
    val enabled: Boolean = true,
)

data class MontlhyPaymentLimitTO(
    val currentAmount: Long,
    val usedAmount: Long,
    val forecastedAmount: Long,
)

data class CreditCardMonthlyLimitTO(
    val currentAmount: Long,
    val usedAmount: Long,
)

data class MemberTO(
    val name: String,
    val alias: String,
    val document: String,
    val accountId: String,
    val type: MemberType,
    val permissions: MemberPermissionsTO,
)

@Introspected
data class InviteRequestTO(
    @field:Pattern(regexp = "^\\d{11}|\\d{14}$", message = "document must be 11 digits for CPF or 14 digits for CNPJ")
    val document: String,
    val name: String,
    @field:Email(message = "Invalid email")
    val email: String,
    val type: CustomPermissionsMemberType,
    val permissions: CustomPermissionRequestTO? = null,
)

data class CustomPermissionRequestTO(
    val viewBills: CustomViewBillPermission,
)

data class InviteResponseTO(
    val memberDocument: String,
    val memberName: String,
    val memberType: MemberType,
    val status: String,
    val validUntil: String,
    val walletId: String,
    val permissions: MemberPermissionsTO,
    val walletName: String,
    val founderName: String,
    val founderDocument: String,
)

data class MemberPermissionsTO(
    val viewBills: BillPermission,
    val scheduleBills: BillPermission,
    val founderContactsEnabled: Boolean = false,
    val cashinEnabled: Boolean,
    val manageMembers: Boolean,
    val viewBalance: Boolean,
    val viewStatement: Boolean,
)

data class MoveBillsTO(val billList: List<String>)

@JsonInclude(JsonInclude.Include.ALWAYS)
data class MoveBillsResultTO(val billList: List<MoveBillResultTO>)

@JsonInclude(JsonInclude.Include.ALWAYS)
data class MoveBillResultTO(
    val billId: String,
    val moved: Boolean,
    val reason: MoveBillErrorReason?,
)

private fun Wallet.toWalletTO(account: Account, pixKey: PixKey, evpPixKey: PixKey?, billCategories: List<WalletBillCategory>?): WalletTO {
    val members = activeMembers.map {
        MemberTO(
            name = it.name,
            alias = it.name,
            document = it.document,
            accountId = it.accountId.value,
            type = it.type,
            permissions = MemberPermissionsTO(
                viewBills = it.permissions.viewBills,
                scheduleBills = it.permissions.scheduleBills,
                founderContactsEnabled = it.permissions.founderContactsEnabled,
                cashinEnabled = it.permissions.cashinEnabled,
                manageMembers = it.permissions.manageMembers,
                viewBalance = it.permissions.viewBalance,
                viewStatement = it.permissions.canViewStatement(),
            ),
        )
    }

    return WalletTO(
        id = id.value,
        name = name,
        type = type,
        members = members,
        isDefault = id == account.configuration.defaultWalletId,
        canBeSetAsDefault = account.canSetAsDefault(this),
        pixKey = pixKey.toPixKeyRequestTO(),
        evpPixKey = evpPixKey?.toPixKeyRequestTO(),
        personType = getPersonType(),
        billCategories = billCategories?.map { it.toWalletBillCategoryTO() } ?: emptyList(),
    )
}

private fun PixKey.toPixKeyRequestTO(): PixKeyRequestTO {
    return PixKeyRequestTO(
        value = value,
        type = type,
    )
}

fun Usage.toDailyPaymentLimitsTO(founderAccount: Account) = DailyPaymentLimitsTO(
    editable = wallet.editable,
    daily = DailyPaymentLimitTO(
        activeFrom = wallet.limits.daily.updatedAt.plusHours(24).format(dateTimeFormat),
        currentAmount = wallet.limits.daily.activeAmount,
        nextAmount = wallet.limits.daily.nextAmount,
        maxAmount = MAX_DAILY_PAYMENT_LIMIT_AMOUNT,
        usedAmount = wallet.usage.daily,
        capped = founderAccount.type == UserAccountType.BASIC_ACCOUNT,
    ),
    nighttime = DailyPaymentLimitTO(
        activeFrom = wallet.limits.nighttime.updatedAt.plusHours(24).format(dateTimeFormat),
        currentAmount = wallet.limits.nighttime.activeAmount,
        nextAmount = wallet.limits.nighttime.nextAmount,
        maxAmount = MAX_NIGHTTIME_PAYMENT_LIMIT_AMOUNT,
        usedAmount = wallet.usage.nighttime,
        capped = founderAccount.type == UserAccountType.BASIC_ACCOUNT,
    ),
    monthly = wallet.limits.monthly?.let {
        MontlhyPaymentLimitTO(
            currentAmount = it.activeAmount,
            usedAmount = wallet.usage.monthly?.used ?: 0,
            forecastedAmount = wallet.usage.monthly?.forecasted ?: 0,
        )
    },
    creditCardMonthly = creditCard?.let {
        CreditCardMonthlyLimitTO(
            currentAmount = creditCard.quota,
            usedAmount = creditCard.usage,
        )
    },
    whatsAppPayment = wallet.limits.whatsAppPayment.let {
        WhatsAppPaymentLimitTO(
            activeFrom = it.updatedAt.plusHours(24).format(dateTimeFormat),
            currentAmount = it.activeAmount,
            nextAmount = it.nextAmount,
            maxAmount = MAX_WHATSAPP_PAYMENT_LIMIT_AMOUNT,
        )
    },

    automaticPix = DailyPaymentLimitTO(
        activeFrom = wallet.limits.automaticPix.updatedAt.plusHours(24).format(dateTimeFormat),
        currentAmount = wallet.limits.automaticPix.activeAmount,
        nextAmount = wallet.limits.automaticPix.nextAmount,
        maxAmount = MAX_DAILY_PAYMENT_LIMIT_AMOUNT, // TODO: Vai precisar voltar do Arbi esse valor
        usedAmount = wallet.usage.automaticPix,
        capped = false,
    ),
)

fun Invite.toInviteResponseTO() = InviteResponseTO(
    memberDocument = memberDocument,
    memberName = memberName,
    memberType = memberType,
    status = status.name,
    validUntil = validUntil.format(dateFormat),
    walletId = walletId.value,
    permissions = MemberPermissionsTO(
        viewBills = permissions.viewBills,
        scheduleBills = permissions.scheduleBills,
        founderContactsEnabled = permissions.founderContactsEnabled,
        cashinEnabled = permissions.cashinEnabled,
        manageMembers = permissions.manageMembers,
        viewBalance = permissions.viewBalance,
        viewStatement = permissions.canViewStatement(),
    ),
    walletName = walletName,
    founderName = founderName,
    founderDocument = founderDocument,
)

data class DailyPaymentLimitRequestTO(
    val amount: Long,
    val type: DailyPaymentLimitType,
)

data class WhatsAppPaymentLimitRequestTO(
    val enabled: Boolean,
)

data class WhatsAppPaymentLimitIsEnabledResponseTO(
    val enabled: Boolean,
)