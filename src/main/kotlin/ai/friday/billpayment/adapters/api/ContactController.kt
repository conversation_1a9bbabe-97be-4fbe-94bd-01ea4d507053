package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.adapters.api.ContactErrors.CONSTRAINT_VALIDATION_ERROR
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.contact.BankAccountId
import ai.friday.billpayment.app.contact.Contact
import ai.friday.billpayment.app.contact.ContactId
import ai.friday.billpayment.app.contact.ContactService
import ai.friday.billpayment.app.contact.IllegalBankAccountUpdate
import ai.friday.billpayment.app.contact.InvalidPixKeyOwner
import ai.friday.billpayment.app.contact.InvalidationCode
import ai.friday.billpayment.app.contact.LastUsed
import ai.friday.billpayment.app.contact.SavedBankAccount
import ai.friday.billpayment.app.contact.SavedPixKey
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.PixKeyManagement
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyDetails
import ai.friday.billpayment.app.pix.PixKeyError
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.pix.isValid
import arrow.core.getOrElse
import com.fasterxml.jackson.annotation.JsonInclude
import io.micronaut.core.annotation.Introspected
import io.micronaut.core.annotation.Nullable
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpResponse.accepted
import io.micronaut.http.HttpResponse.badRequest
import io.micronaut.http.HttpResponse.noContent
import io.micronaut.http.HttpResponse.notFound
import io.micronaut.http.HttpResponse.ok
import io.micronaut.http.HttpResponse.serverError
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Delete
import io.micronaut.http.annotation.Error
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.http.annotation.Put
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import io.micronaut.validation.Validated
import jakarta.validation.ConstraintViolationException
import jakarta.validation.Valid
import jakarta.validation.constraints.Size
import java.math.BigInteger
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Secured(Role.Code.OWNER)
@Controller("/recipient")
@Version("2")
@Validated
class ContactController(
    private val contactService: ContactService,
    private val pixKeyManagement: PixKeyManagement,
    private val accountService: AccountService,
    private val financialInstitutionController: FinancialInstitutionController,
) {

    @Get
    fun getContacts(authentication: Authentication): HttpResponse<*> {
        val savedRecipients = contactService.getAll(authentication.toContactAccountId())
        val response = savedRecipients.convertToContactTO()
        LOG.info(
            append("accountId", authentication.name)
                .and<LogstashMarker>(append("contactAccountId", authentication.toContactAccountId()))
                .and(append("response", response)),
            "GetContact",
        )
        return ok(response)
    }

    @Put("/{contactId}")
    fun updateRecipient(
        authentication: Authentication,
        @PathVariable contactId: String,
        @Body @Valid
        updateContactTO: UpdateContactTO,
    ): HttpResponse<*> {
        val markers: LogstashMarker = append("accountId", authentication.name)
            .and<LogstashMarker>(append("contactId", contactId))
            .and(append("request", updateContactTO))
        return try {
            val recipient = contactService.updateContact(
                accountId = authentication.toContactAccountId(),
                contactId = ContactId(contactId),
                alias = updateContactTO.alias.orEmpty(),
            )

            ok(recipient.convertToContactTO())
                .also { logUpdateContact(markers, it) }
        } catch (e: Exception) {
            when (e) {
                is ItemNotFoundException -> notFound(ContactErrors.RESOURCE_NOT_FOUND.toResponseTO())
                else -> StandardHttpResponses.serverError(ContactErrors.SERVER_ERROR.toResponseTO())
            }.also { logUpdateContact(markers, it, e) }
        }
    }

    @Delete(uris = ["/{contactId}", "/id/{contactId}"/*deprecated*/])
    fun deleteContact(authentication: Authentication, @PathVariable contactId: String): HttpResponse<Void> {
        val markers: LogstashMarker = append("accountId", authentication.name)
            .and<LogstashMarker>(append("contactId", contactId))
            .and(append("contactAccountId", authentication.toContactAccountId()))
        return executeDeletion(
            deletionSupplier = {
                contactService.delete(authentication.toContactAccountId(), ContactId(contactId))
                LOG.info(markers.and(append("httpStatus", HttpStatus.ACCEPTED)), "DeleteContact")
            },
            onError = { logDeletionError(markers, it, "DeleteContact") },
        )
    }

    @Post("/{contactId}/bankAccount")
    fun addBankAccount(
        authentication: Authentication,
        @PathVariable contactId: String,
        @Body @Valid
        bankDetailsTO: RecipientBankDetailsTO,
    ): HttpResponse<*> {
        val marker = append("accountId", authentication.toAccountId().value).and<LogstashMarker>(
            append("contactId", contactId).and(
                append("request", bankDetailsTO),
            ),
        )
        val operation: () -> HttpResponse<*> = {
            contactService.addBankAccount(
                authentication.toContactAccountId(),
                ContactId(contactId),
                bankDetailsTO.toBankAccount(),
            )
            LOG.info(marker.and(append("httpStatus", HttpStatus.NO_CONTENT)), "addBankAccount")
            noContent<Unit>()
        }
        return bankAccountOperation(
            bankDetailsTO,
            operation,
            onError = {
                LOG.error(marker, "addBankAccount", it)
            },
        )
    }

    @Put("/{contactId}/bankAccount/{bankAccountId}")
    fun updateBankAccount(
        authentication: Authentication,
        @PathVariable contactId: String,
        @PathVariable bankAccountId: String,
        @Body @Valid
        bankDetailsTO: RecipientBankDetailsTO,
    ): HttpResponse<*> {
        val marker = append("accountId", authentication.toAccountId().value).and<LogstashMarker>(
            append("contactId", contactId).and(
                append("request", bankDetailsTO).and(
                    append("bankAccountId", bankAccountId),
                ),
            ),
        )
        val operation: () -> HttpResponse<*> = {
            contactService.updateBankAccount(
                authentication.toContactAccountId(),
                ContactId(contactId),
                BankAccountId(bankAccountId),
                bankDetailsTO.toBankAccount(),
            )
            LOG.info(marker.and(append("httpStatus", HttpStatus.NO_CONTENT)), "updateBankAccount")
            noContent<Unit>()
        }
        return bankAccountOperation(
            bankDetailsTO,
            operation,
            onError = {
                LOG.error(marker, "updateBankAccount", it)
            },
        )
    }

    @Delete(uris = ["/{contactId}/bankAccount/{bankAccountId}", "/id/{contactId}/bankAccounts/id/{bankAccountId}" /*deprecated*/])
    fun deleteBankAccount(
        authentication: Authentication,
        @PathVariable contactId: String,
        @PathVariable bankAccountId: String,
    ): HttpResponse<Void> {
        val markers: LogstashMarker = append("accountId", authentication.name)
            .and<LogstashMarker>(append("contactId", contactId))
            .and(append("bankAccountId", bankAccountId))
        return executeDeletion(
            deletionSupplier = {
                contactService.deleteBankAccount(
                    authentication.toContactAccountId(),
                    ContactId(contactId),
                    BankAccountId(bankAccountId),
                )
                LOG.info(markers.and(append("httpStatus", HttpStatus.ACCEPTED)), "DeleteBankAccount")
            },
            onError = { logDeletionError(markers, it, "DeleteBankAccount") },
        )
    }

    @Post("/{contactId}/pixKey")
    fun addPixKey(
        authentication: Authentication,
        @PathVariable contactId: String,
        @Body pixKeyRequestTO: PixKeyRequestTO,
    ): HttpResponse<*> {
        val marker = append("accountId", authentication.toAccountId().value)
            .and<LogstashMarker>(
                append("contactId", contactId)
                    .and<LogstashMarker>(append("contactAccountId", authentication.toContactAccountId()))
                    .and(append("request", pixKeyRequestTO)),
            )

        val pixKey = PixKey(pixKeyRequestTO.value, pixKeyRequestTO.type)
        if (pixKey.isValid().not()) {
            return badRequest(ContactErrors.INVALID_PIX_KEY_FORMAT.toResponseTO())
        }

        // FIXME - codigo duplicado do PixController
        val account = accountService.findAccountById(authentication.toAccountId())

        return pixKeyManagement.findKeyDetails(pixKey, account.document)
            .map<HttpResponse<*>> { (pixKeyDetails, _) ->
                return doAddPixKey(
                    pixKeyDetails,
                    authentication.toContactAccountId(),
                    ContactId(contactId),
                    onError = { error ->
                        LOG.error(marker, "addPixKey", error)
                    },
                ).also {
                    LOG.info(marker.and(append("httpStatus", it.status)), "addPixKey")
                }
            }.getOrElse { pixKeyError ->
                val errorMarker = marker.and<LogstashMarker>(
                    append("pixKeyError", pixKeyError).and(
                        append("accountId", account.accountId.value).and(
                            append("pixKey", pixKey),
                        ),
                    ),
                )
                when (pixKeyError) {
                    is PixKeyError.MalformedKey -> {
                        LOG.error(errorMarker, "addPixKey")
                        badRequest(ContactErrors.INVALID_PIX_KEY_FORMAT.toResponseTO())
                    }

                    is PixKeyError.KeyNotFound,
                    is PixKeyError.KeyNotConfirmed,
                    -> badRequest(ContactErrors.PIX_KEY_NOT_FOUND.toResponseTO())

                    is PixKeyError.UnknownError, PixKeyError.SystemUnavailable, PixKeyError.InvalidQrCode -> {
                        LOG.error(errorMarker, "addPixKey")
                        serverError()
                    }
                }
            }
    }

    @Delete("/{contactId}/pixKey/{value}")
    fun deletePixKey(
        authentication: Authentication,
        @PathVariable contactId: String,
        @PathVariable value: String,
    ): HttpResponse<Void> {
        val markers: LogstashMarker = append("accountId", authentication.name)
            .and<LogstashMarker>(append("contactId", contactId))
            .and(append("pixKeyValue", value))
        return executeDeletion(
            deletionSupplier = {
                contactService.deletePixKey(authentication.toContactAccountId(), ContactId(contactId), value)
                LOG.info(markers.and(append("httpStatus", HttpStatus.ACCEPTED)), "DeletePixKey")
            },
            onError = { logDeletionError(markers, it, "DeletePixKey") },
        )
    }

    @Error
    fun handleConstraintViolationException(
        request: HttpRequest<*>,
        exception: ConstraintViolationException,
    ): HttpResponse<ResponseTO> {
        return badRequest(
            ResponseTO(
                message = exception.constraintViolations.first().message,
                code = CONSTRAINT_VALIDATION_ERROR.code,
            ),
        )
    }

    private fun bankAccountOperation(
        bankDetailsTO: RecipientBankDetailsTO,
        operation: () -> HttpResponse<*>,
        onError: (e: Exception) -> Unit,
    ): HttpResponse<*> {
        if (bankDetailsTO.bankNo == null && bankDetailsTO.ispb == null) {
            return badRequest(ContactErrors.BANK_NO_OR_ISPB_MUST_EXISTS.toResponseTO())
        }
        val financialInstitution =
            financialInstitutionController.getFinancialInstitutions().financialInstitutions.find { it.code == bankDetailsTO.bankNo || it.ispb == bankDetailsTO.ispb }

        fun isInvalidIspb() = bankDetailsTO.ispb != null && bankDetailsTO.ispb != financialInstitution?.ispb
        fun isValidBankNo() = bankDetailsTO.bankNo != null && bankDetailsTO.bankNo != financialInstitution?.code
        if (financialInstitution == null || isInvalidIspb() || isValidBankNo()) {
            return badRequest(ContactErrors.INVALID_BANK_NO_AND_OR_ISPB.toResponseTO())
        }
        return try {
            operation()
        } catch (e: Exception) {
            when (e) {
                is ItemNotFoundException -> notFound(ContactErrors.RESOURCE_NOT_FOUND.toResponseTO())
                is IllegalBankAccountUpdate -> badRequest(ContactErrors.BANK_ACCOUNT_ALREADY_EXISTS.toResponseTO())
                else -> {
                    onError(e)
                    serverError()
                }
            }
        }
    }

    private fun doAddPixKey(
        pixKeyDetails: PixKeyDetails,
        accountId: AccountId,
        contactId: ContactId,
        onError: (e: Exception) -> Unit,
    ): HttpResponse<*> {
        return try {
            contactService.addPixKey(
                accountId = accountId,
                contactId = contactId,
                pixKeyDetails = pixKeyDetails,
            )
            noContent<Unit>()
        } catch (e: Exception) {
            when (e) {
                is ItemNotFoundException -> notFound(ContactErrors.RESOURCE_NOT_FOUND.toResponseTO())
                is InvalidPixKeyOwner -> badRequest(ContactErrors.INVALID_PIX_KEY_OWNER.toResponseTO())
                else -> {
                    onError(e)
                    serverError()
                }
            }
        }
    }

    private fun logUpdateContact(markers: LogstashMarker, response: HttpResponse<*>, e: Exception? = null) {
        when (response.status) {
            in listOf(HttpStatus.OK, HttpStatus.NOT_FOUND) -> LOG.info(
                markers
                    .and<LogstashMarker>(append("response", response.body()))
                    .and(append("httpStatus", response.status)),
                "updateContact",
            )

            else -> LOG.error(
                markers
                    .and(append("httpStatus", HttpStatus.INTERNAL_SERVER_ERROR)),
                "updateContact",
                e,
            )
        }
    }

    private fun executeDeletion(deletionSupplier: () -> Unit, onError: (e: Exception) -> Unit): HttpResponse<Void> {
        return try {
            deletionSupplier()
            accepted()
        } catch (e: Exception) {
            onError(e)
            return when (e) {
                is ItemNotFoundException -> notFound()
                else -> serverError()
            }
        }
    }

    private fun logDeletionError(markers: LogstashMarker, e: Exception, message: String) {
        when (e) {
            is ItemNotFoundException -> LOG.warn(
                markers.and(append("httpStatus", HttpStatus.NOT_FOUND)),
                message,
            )

            else -> LOG.error(
                markers.and(append("httpStatus", HttpStatus.INTERNAL_SERVER_ERROR)),
                message,
                e,
            )
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(ContactController::class.java)
    }
}

fun List<Contact>.convertToContactTO() =
    map { savedRecipient -> savedRecipient.convertToContactTO() }

fun Contact.convertToContactTO() = ContactTO(
    id = id.value,
    alias = alias.orEmpty(),
    name = name,
    document = document,
    bankAccounts = bankAccounts.convertToSavedBankAccountsTO(),
    pixKeys = pixKeys.convertToSavedPixKeyTO(),
    lastUsed = lastUsed?.let { lastUsed.convertToLastUsedTO(bankAccounts, pixKeys) },
)

private fun LastUsed.convertToLastUsedTO(
    bankAccounts: List<SavedBankAccount>,
    pixKeys: List<SavedPixKey>,
): LastUsedTO? {
    if (bankAccountId != null) {
        bankAccounts.firstOrNull { it.id == bankAccountId }?.let {
            return LastUsedTO(bankAccountId = bankAccountId.value)
        }
    } else if (pixKey != null) {
        pixKeys.firstOrNull {
            it.value == pixKey
        }?.let {
            return LastUsedTO(pixKey = it.toSavedPixKeyTO())
        }
    }
    return null
}

private fun List<SavedPixKey>.convertToSavedPixKeyTO() = this.map { it.toSavedPixKeyTO() }

private fun SavedPixKey.toSavedPixKeyTO() =
    SavedPixKeyTO(value = value, type = type, invalidated = invalidated, invalidationCode = invalidationCode)

private fun List<SavedBankAccount>.convertToSavedBankAccountsTO() =
    this.map { savedBankAccount -> savedBankAccount.convertToSavedBankAccountsTO() }

private fun SavedBankAccount.convertToSavedBankAccountsTO() =
    SavedBankAccountTO(
        id = id.value,
        accountType = accountType.name,
        bankNo = bankNo,
        routingNo = routingNo,
        accountNo = accountNo,
        accountDv = accountDv,
        invalidated = invalidated,
        invalidationMessage = invalidationMessage,
        ispb = ispb,
        invalidationCode = invalidationCode?.name,
    )

@Introspected
data class UpdateContactTO(
    @field:Nullable
    @field:Size(max = 60, message = "Alias length must be up to 60")
    val alias: String? = "",
)

data class ContactTO(
    val id: String,
    @JsonInclude(JsonInclude.Include.ALWAYS)
    val alias: String,
    val name: String,
    val document: String,
    @JsonInclude(JsonInclude.Include.ALWAYS)
    val bankAccounts: List<SavedBankAccountTO>,
    @JsonInclude(JsonInclude.Include.ALWAYS)
    val pixKeys: List<SavedPixKeyTO>,
    val lastUsed: LastUsedTO?,
)

data class SavedPixKeyTO(
    val value: String,
    val type: PixKeyType,
    val invalidated: Boolean,
    val invalidationCode: InvalidationCode?,
)

data class LastUsedTO(
    val bankAccountId: String? = null,
    val pixKey: SavedPixKeyTO? = null,
)

data class SavedBankAccountTO(
    val id: String,
    val accountType: String,
    val bankNo: Long?,
    val routingNo: Long,
    val accountNo: BigInteger,
    val accountDv: String,
    val invalidated: Boolean = false,
    @JsonInclude(JsonInclude.Include.ALWAYS)
    val invalidationMessage: String = "",
    val ispb: String?,
    val invalidationCode: String?,
)

enum class ContactErrors(val description: String, val code: String) {
    CONSTRAINT_VALIDATION_ERROR("Constraint validation error", "400"),
    INVALID_PIX_KEY_OWNER("Current key owner document is not the same as the contact document", "401"),
    PIX_KEY_NOT_FOUND("Pix Key not found", "402"),
    INVALID_PIX_KEY_FORMAT("Invalid Pix Key format", "403"),
    BANK_ACCOUNT_ALREADY_EXISTS("Bank Account already exists with another id", "404"),
    INVALID_BANK_NO_AND_OR_ISPB("Invalid BankNo and/or ISPB", "405"),
    BANK_NO_OR_ISPB_MUST_EXISTS("BankNo or ISPB must be present", "406"),
    RESOURCE_NOT_FOUND("Resource does not exists", "407"),
    SERVER_ERROR("Internal Server Error, try again later", "408"),
}

private fun ContactErrors.toResponseTO(): ResponseTO =
    ResponseTO(code, description)