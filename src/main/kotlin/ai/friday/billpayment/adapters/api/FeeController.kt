package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.and
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.fee.BillFees
import ai.friday.billpayment.app.fee.BillFeesCommand
import ai.friday.billpayment.app.fee.FeePaymentCreditCardConvenience
import ai.friday.billpayment.app.fee.FeePaymentCreditCardInstallment
import ai.friday.billpayment.app.fee.FeePaymentMethod
import ai.friday.billpayment.app.fee.FeePaymentMethodBalance
import ai.friday.billpayment.app.fee.FeePaymentMethodBalanceCommand
import ai.friday.billpayment.app.fee.FeePaymentMethodCreditCard
import ai.friday.billpayment.app.fee.FeePaymentMethodCreditCardCommand
import ai.friday.billpayment.app.fee.FeeResultSummary
import ai.friday.billpayment.app.fee.FeesCommandScheduleTo
import ai.friday.billpayment.app.fee.FeesWithSummary
import ai.friday.billpayment.app.fee.ListFeesCommand
import ai.friday.billpayment.app.integrations.ListFeesService
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.log
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.annotation.JsonTypeName
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import org.slf4j.LoggerFactory

@Version("2")
@Controller("/fees")
@Secured(Role.Code.OWNER)
class FeeController(private val service: ListFeesService) {
    private val logger = LoggerFactory.getLogger(FeeController::class.java)

    @Post
    fun listFees(authentication: Authentication, @Body request: FeesRequestTO): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val walletId = authentication.toWalletId()

        val markers = log("accountId" to accountId.value, "walletId" to walletId.value, "body" to request)

        val result = service.listFees(request.toListFeesCommand(accountId, walletId)).getOrElse {
            logger.error(markers.and("error_message" to it.message), "FeeController#listFees", it.ex)
            return HttpResponse.serverError(Unit)
        }

        logger.info(markers.andAppend("result", result), "FeeController#listFees")

        return HttpResponse.ok(result.toFeesWithSummaryResponseTO())
    }

    private fun FeesRequestTO.toListFeesCommand(accountId: AccountId, walletId: WalletId) = ListFeesCommand(
        accountId = accountId,
        walletId = walletId,
        scheduleTo = FeesCommandScheduleTo.valueOf(this.scheduleTo),
        bills = this.bills.map {
            BillFeesCommand(
                billId = it.billId,
                methods = listOfNotNull(it.methods.toBalanceOrNull(), it.methods.toCreditCardOrNull()),
            )
        },
    )

    private fun List<FeePaymentMethodRequestTO>.toBalanceOrNull() =
        this.filterIsInstance<FeePaymentMethodBalanceRequestTO>()
            .map { FeePaymentMethodBalanceCommand(amount = it.amount) }
            .firstOrNull()

    private fun List<FeePaymentMethodRequestTO>.toCreditCardOrNull() =
        this.filterIsInstance<FeePaymentMethodCreditCardRequestTO>()
            .map {
                FeePaymentMethodCreditCardCommand(
                    paymentMethodId = it.paymentMethodId,
                    amount = it.amount,
                    quantity = it.quantity,
                )
            }.firstOrNull()
}

data class FeesRequestTO(
    val bills: List<BillFeesRequestTO>,
    val scheduleTo: String,
)

data class BillFeesRequestTO(
    val billId: String,
    val methods: List<FeePaymentMethodRequestTO>,
)

private fun FeesWithSummary.toFeesWithSummaryResponseTO() = FeesWithSummaryTO(
    fees = this.fees.map { it.toBillFeeTO() },
    summary = this.summary?.toFeeResultSummaryTO(),
)

@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.PROPERTY,
    property = "type",
)
sealed class FeePaymentMethodRequestTO

@JsonTypeName("BALANCE")
data class FeePaymentMethodBalanceRequestTO(
    val amount: Long,
) : FeePaymentMethodRequestTO()

@JsonTypeName("CREDIT_CARD")
data class FeePaymentMethodCreditCardRequestTO(
    val paymentMethodId: String,
    val amount: Long,
    val quantity: Int,
) : FeePaymentMethodRequestTO()

private fun BillFees.toBillFeeTO() = BillFeeResponseTO(
    billId = this.billId,
    amount = this.amount,
    calculationId = this.calculationId,
    payments = this.payments.map { it.toFeePaymentMethodTO() },
    error = this.error?.errorMessage,
)

private fun FeeResultSummary.toFeeResultSummaryTO() = FeeResultSummaryTO(
    hasInstallmentQuantityDivergence = this.hasInstallmentQuantityDivergence,
    creditCardSummary = this.creditCardSummary.map { it.toFeePaymentCreditCardInstallmentTO() },
)

private fun FeePaymentMethod.toFeePaymentMethodTO() = FeePaymentMethodTO(
    type = this.method,
    amount = this.amount,
    paymentMethodId = this.paymentMethodId.value,
    convenience = if (this is FeePaymentMethodCreditCard) this.convenience?.toFeePaymentCreditCardConvenienceTO() else null,
    installments = when (this) {
        is FeePaymentMethodBalance -> null
        is FeePaymentMethodCreditCard -> this.installments.map { it.toFeePaymentCreditCardInstallmentTO() }
    },
)

private fun FeePaymentCreditCardConvenience.toFeePaymentCreditCardConvenienceTO() = FeePaymentCreditCardConvenienceTO(
    fee = this.fee,
    totalValue = this.totalValue,
    totalValueRounded = this.totalValueRounded,
    totalInterestValue = this.totalInterestValue,
    totalInterestValueRounded = this.totalInterestValueRounded,
)

private fun FeePaymentCreditCardInstallment.toFeePaymentCreditCardInstallmentTO() = FeePaymentCreditCardInstallmentTO(
    amount = this.amount,
    total = this.total,
    quantity = this.quantity,
    fee = this.fee?.let { (it * 100).toLong() },
    feeAmount = this.feeAmount,
)

data class FeesWithSummaryTO(
    val fees: List<BillFeeResponseTO>,
    var summary: FeeResultSummaryTO?,
)

data class BillFeeResponseTO(
    val billId: String,
    val amount: Long?,
    val calculationId: String?,
    val payments: List<FeePaymentMethodTO>,
    val error: String?,
)

data class FeePaymentMethodTO(
    val type: String,
    val amount: Long,
    val paymentMethodId: String,
    val convenience: FeePaymentCreditCardConvenienceTO? = null,
    val installments: List<FeePaymentCreditCardInstallmentTO>?,
)

data class FeePaymentCreditCardConvenienceTO(
    val fee: Long,
    val totalValue: Long,
    val totalValueRounded: Long,
    val totalInterestValue: Long,
    val totalInterestValueRounded: Long,
)

data class FeePaymentCreditCardInstallmentTO(
    val amount: Long,
    val total: Long,
    val quantity: Int,
    val fee: Long?,
    val feeAmount: Long?,
)

data class FeeResultSummaryTO(
    val hasInstallmentQuantityDivergence: Boolean,
    val creditCardSummary: List<FeePaymentCreditCardInstallmentTO>,
)