package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.adapters.softwareexpress.SoftwareExpressAdapter
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.banking.InternalBankService
import ai.friday.billpayment.app.integrations.TransactionRepository
import ai.friday.billpayment.app.payment.BalanceAuthorization
import ai.friday.billpayment.app.payment.MultiplePaymentData
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithBalance
import ai.friday.billpayment.app.payment.PaymentStatus
import ai.friday.billpayment.app.payment.SinglePaymentData
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.morning.log.andAppend
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

data class GooglePayRequestTO(
    val orderId: String,
    val walletTransactionId: String,
    val walletReturnedCardBrand: String,
    val amount: String,
)

@Secured(Role.Code.ADMIN)
@Controller("/backoffice/settlement")
class BackofficeSettlementController(
    private val internalBankService: InternalBankService,
    private val transactionRepository: TransactionRepository,
    private val softwareExpressAdapter: SoftwareExpressAdapter?,
) {

    private val logger = LoggerFactory.getLogger(BackofficeSettlementController::class.java)

    @Post("/googlePay/captureFunds")
    fun captureFundsGooglePay(authentication: Authentication, @Body requestTO: GooglePayRequestTO): HttpResponse<*> {
        runCatching {
            return HttpResponse.created(
                softwareExpressAdapter?.authorizeWalletPayment(
                    orderId = requestTO.orderId,
                    walletTransactionId = requestTO.walletTransactionId,
                    walletReturnedCardBrand = requestTO.walletReturnedCardBrand,
                    amount = requestTO.amount,
                ),
            )
        }.getOrElse {
            logger.error(Markers.append("requestTO", requestTO), "GoogleCaptureFunds", it)
            return HttpResponse.serverError(it.message)
        }
    }

    @Post("/captureFunds/{transactionIdValue}")
    fun captureFunds(authentication: Authentication, @PathVariable transactionIdValue: String): HttpResponse<*> {
        val markers = Markers.append("transactionId", transactionIdValue)
            .andAppend("authentication", authentication.name)

        try {
            val transaction = transactionRepository.findById(TransactionId(transactionIdValue))

            if (transaction.paymentData is MultiplePaymentData) {
                return HttpResponse.badRequest("MultiplePaymentData is not supported")
            }

            val paymentData = transaction.paymentData.toSingle()

            if (paymentData.payment?.status() != PaymentStatus.ERROR) {
                return HttpResponse.badRequest("PaymentStatus not supported: ${paymentData.payment?.status()}")
            }

            val result = internalBankService.captureFunds(
                accountId = paymentData.accountPaymentMethod.accountId,
                accountPaymentMethodId = paymentData.accountPaymentMethod.id,
                paymentMethod = paymentData.accountPaymentMethod.method,
                amount = (paymentData.details as PaymentMethodsDetailWithBalance).amount,
            )
            markers.andAppend("result", result)

            if (result.status == BankOperationStatus.SUCCESS) {
                (transaction.paymentData as SinglePaymentData).payment = BalanceAuthorization(
                    operationId = result.operationId,
                    status = result.status,
                    amount = result.amount,
                    paymentGateway = result.gateway,
                )
                transactionRepository.save(transaction)
            }
            markers.andAppend("transaction", transaction)

            logger.info(markers, "BackofficeSettlementController#captureFunds")
            return HttpResponse.ok(result)
        } catch (e: Exception) {
            logger.error(markers, "BackofficeSettlementController#captureFunds", e)
            return HttpResponse.serverError(e.message.orEmpty())
        }
    }
}