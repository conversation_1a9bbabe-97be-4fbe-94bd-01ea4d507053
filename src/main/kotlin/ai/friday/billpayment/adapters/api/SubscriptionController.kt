package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.subscription.SubscriptionService
import ai.friday.billpayment.log
import ai.friday.morning.log.andAppend
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import org.slf4j.LoggerFactory

@Version("2")
@Controller("/subscription")
@Secured(Role.Code.OWNER)
class SubscriptionController(
    private val service: SubscriptionService,
) {

    @Get("/fee")
    fun getFee(authentication: Authentication): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val logName = "FeeController#getFee"
        val markers = log("accountId" to accountId.value)

        return try {
            val result = service.find(accountId)

            logger.info(markers.andAppend("result", result), logName)

            HttpResponse.ok(FeeResponseTO(result.amount))
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            HttpResponse.serverError<Unit>()
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(SubscriptionController::class.java)
    }
}

private data class FeeResponseTO(val fee: Long)