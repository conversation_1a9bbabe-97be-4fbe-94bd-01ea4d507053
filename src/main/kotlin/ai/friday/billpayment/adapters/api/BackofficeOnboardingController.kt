package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.onboarding.OnboardingTestPixService
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Put
import io.micronaut.security.annotation.Secured
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Secured(Role.Code.BACKOFFICE)
@Controller("/backoffice/onboarding")
class BackofficeOnboardingController(
    private val onboardingTestPixService: OnboardingTestPixService,
) {
    private val logger = LoggerFactory.getLogger(BackofficeOnboardingController::class.java)

    @Put("/{accountId}/createOnboardingTestPix")
    fun createOnboardingTestPix(@PathVariable accountId: String): HttpResponse<*> {
        val markers = Markers.append("accountId", accountId)

        return onboardingTestPixService.createChatbotExamplePayment(AccountId(accountId)).map {
            markers.andAppend("pixCreatedCount", it)
            logger.info(markers, "BackofficeOnboardingController#createOnboardingTestPix")

            HttpResponse.ok(it)
        }.getOrElse {
            markers.andAppend("error", it)
            logger.error(markers, "BackofficeOnboardingController#createOnboardingTestPix")
            HttpResponse.serverError(it)
        }
    }
}

data class FixTriPixTO(
    val accountIds: List<String>,
)