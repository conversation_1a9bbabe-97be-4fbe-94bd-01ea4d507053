package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import io.micronaut.core.async.publisher.Publishers
import io.micronaut.http.HttpAttributes
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.MutableHttpResponse
import io.micronaut.http.annotation.Filter
import io.micronaut.http.filter.HttpServerFilter
import io.micronaut.http.filter.ServerFilterChain
import io.micronaut.http.filter.ServerFilterPhase
import io.micronaut.security.authentication.Authentication
import io.micronaut.security.filters.SecurityFilter
import io.micronaut.web.router.UriRouteMatch
import io.opentracing.Tracer
import java.util.Optional
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import net.logstash.logback.marker.Markers.append
import org.reactivestreams.Publisher
import org.slf4j.LoggerFactory

const val AUTHENTICATION_ATTRIBUTE_WALLET = "WALLET"

abstract class WalletSecurityFilter(
    private val walletService: WalletService,
    private val tracer: Tracer,
    excludedPaths: List<String>,
) :
    HttpServerFilter {

    private val excludedPathsExpression = excludedPaths.map { Regex(it) }

    abstract fun getWalletId(request: HttpRequest<*>): String?
    abstract fun getMissingWalletIdMessage(): String

    override fun doFilter(request: HttpRequest<*>, chain: ServerFilterChain): Publisher<MutableHttpResponse<*>> {
        if (pathIsExcluded(request)) {
            return chain.proceed(request)
        }

        val markers = Markers.empty()
        val authentication = request.getUserPrincipal(Authentication::class.java)

        if (authentication.isPresent) {
            val accountId = authentication.get().toAccountId()
            val walletId = getWalletId(request)

            markers.and<LogstashMarker>(append("accountId", accountId))
                .and<LogstashMarker>(append("walletId", walletId))

            if (walletId != null) {
                val wallet = walletService.findWalletOrNull(WalletId(walletId))
                if (wallet == null) {
                    LOG.warn(markers.and(append("status", HttpStatus.NOT_FOUND)), "WalletSecurityFilter")
                    return Publishers.just(
                        HttpResponse.notFound(
                            ResponseTO(
                                code = "4004",
                                message = "WalletId $walletId not found",
                            ),
                        ),
                    )
                }
                if (!wallet.hasActiveMember(accountId)) {
                    LOG.warn(markers.and(append("status", HttpStatus.FORBIDDEN)), "WalletSecurityFilter")
                    return Publishers.just(HttpResponse.status<Any>(HttpStatus.FORBIDDEN))
                }

                val span = tracer.activeSpan()
                span?.setBaggageItem("walletId", walletId)

                request.setAttribute(
                    SecurityFilter.AUTHENTICATION,
                    buildCustomAuthentication(authentication.get(), wallet),
                )
            } else {
                LOG.warn(markers.and(append("status", HttpStatus.CONFLICT)), "WalletSecurityFilter")
                return Publishers.just(
                    HttpResponse.status<Any>(HttpStatus.CONFLICT).body(ResponseTO("4009", getMissingWalletIdMessage())),
                )
            }
        }

        LOG.trace(markers, "WalletSecurityFilter")
        return chain.proceed(request)
    }

    private fun buildCustomAuthentication(authentication: Authentication, wallet: Wallet): CustomAuthentication {
        val customAttributes = HashMap<String, Any>(authentication.attributes)
        customAttributes[AUTHENTICATION_ATTRIBUTE_WALLET] = wallet
        return CustomAuthentication(authentication.name, customAttributes, authentication.roles)
    }

    private fun pathIsExcluded(request: HttpRequest<*>): Boolean {
        return excludedPathsExpression.any { it.matches(request.path) }
    }

    override fun getOrder(): Int {
        return ServerFilterPhase.SECURITY.after()
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(WalletSecurityFilter::class.java)
    }
}

@Filter(value = ["/bill/**", "/transaction/**", "/schedule/**", "/v2/schedule/**", "/recipient/**", "/balance/amount", "/balance/cashin", "/balance/checkout", "/payment-method/cashin", "/fees/**", "/entries/**", "/async-entries/**", "/utilityAccount/**", "/category-summary/**", "/manualEntry/**", "/openfinance/**", "/automatic-pix/**"])
class WalletHeaderSecurityFilter(walletService: WalletService, tracer: Tracer) :
    WalletSecurityFilter(walletService, tracer, listOf()) {
    override fun getWalletId(request: HttpRequest<*>): String? {
        return request.headers.firstOrNull { it.key.equals("X-Wallet-Id", ignoreCase = true) }?.value?.first()
    }

    override fun getMissingWalletIdMessage(): String {
        return "Missing x-wallet-id header"
    }
}

@Filter(value = ["/wallet/*/**"])
class WalletPathSecurityFilter(walletService: WalletService, tracer: Tracer) :
    WalletSecurityFilter(walletService, tracer, listOf(".*/invite")) {

    override fun getWalletId(request: HttpRequest<*>): String? {
        val uriRouteMatch: Optional<UriRouteMatch<*, *>> = request
            .attributes[HttpAttributes.ROUTE_MATCH.toString(), UriRouteMatch::class.java]

        if (uriRouteMatch.isPresent) {
            val variableValues: Map<String, Any> = uriRouteMatch.get().variableValues
            return variableValues["walletId"] as String?
        }

        return null
    }

    override fun getMissingWalletIdMessage(): String {
        return "Missing walletId path"
    }
}

fun Authentication.getWallet() = this.attributes[AUTHENTICATION_ATTRIBUTE_WALLET] as Wallet

fun Authentication.asWalletMember(): Member = getWallet().getActiveMember(toAccountId())

class CustomAuthentication(
    private val name: String,
    private val attributes: MutableMap<String, Any>,
    private val roles: MutableCollection<String>,
) : Authentication {

    override fun getName(): String {
        return name
    }

    override fun getAttributes(): MutableMap<String, Any> {
        return attributes
    }

    override fun getRoles(): MutableCollection<String> {
        return roles
    }
}