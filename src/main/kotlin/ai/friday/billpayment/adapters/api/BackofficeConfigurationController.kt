package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountConfigurationName
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.account.AccountGroupResult
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountNotFoundException
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.RiskLevel
import ai.friday.billpayment.app.account.Role.Code.BACKOFFICE
import ai.friday.billpayment.app.backoffice.toUserAccountRegister
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Delete
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.http.annotation.QueryValue
import io.micronaut.security.annotation.Secured
import io.micronaut.validation.Validated
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Validated
@Secured(BACKOFFICE)
@Controller("/backoffice/configuration")
class BackofficeConfigurationController(
    private val accountService: AccountService,
) {
    @Post("/{accountId}/creditCard/quota{/quota}")
    fun postCreditCardQuota(
        @PathVariable
        accountId: String,
        @QueryValue
        quota: Long?,
    ): HttpResponse<*> {
        val markers = Markers.append("accountId", accountId).andAppend("quota", quota)
        return accountService.enableCreditCardUsage(
            accountId = AccountId(accountId),
            quota = quota,
        ).map {
            LOG.info(markers, "BackofficeConfigurationController#postCreditCardQuota")
            HttpResponse.ok(
                it.toCreditCardResponseTO(),
            )
        }.getOrElse {
            when (it) {
                is AccountNotFoundException -> HttpResponse.notFound<Unit>()
                else -> {
                    LOG.error(markers, "BackofficeConfigurationController#postCreditCardQuota", it)
                    HttpResponse.serverError<Unit>()
                }
            }
        }
    }

    @Post("/{accountId}/creditCard/allowedRisk/{allowedRisk}")
    fun postCreditCardAllowedRisk(
        @PathVariable
        accountId: String,
        @PathVariable
        allowedRisk: RiskLevel,
    ): HttpResponse<*> {
        val markers = Markers.append("accountId", accountId).andAppend("allowedRisk", allowedRisk)
        return accountService.updateCreditCardAllowedRisk(
            accountId = AccountId(accountId),
            allowedRisk = allowedRisk,
        ).map {
            LOG.info(markers, "BackofficeConfigurationController#postCreditCardAllowedRisk")
            HttpResponse.ok(
                it.toCreditCardResponseTO(),
            )
        }.getOrElse {
            when (it) {
                is AccountNotFoundException -> HttpResponse.notFound<Unit>()
                else -> {
                    LOG.error(markers, "BackofficeConfigurationController#postCreditCardAllowedRisk", it)
                    HttpResponse.serverError<Unit>()
                }
            }
        }
    }

    @Post("/{accountId}/notifications")
    fun updateConfiguration(
        @PathVariable accountId: String,
        @Body
        body: AccountNotificationsRequestTO,
    ) {
        val markers = Markers.append("accountId", accountId).andAppend("body", body)

        try {
            accountService.updateAccountConfig(
                accountId = AccountId(accountId),
                name = AccountConfigurationName.RECEIVE_DDA_NOTIFICATION,
                value = body.receiveDDANotification.toString(),
            )

            accountService.updateAccountConfig(
                accountId = AccountId(accountId),
                name = AccountConfigurationName.RECEIVE_NOTIFICATION,
                value = body.receiveNotification.toString(),
            )

            LOG.info(markers, "BackofficeConfigurationControllerUpdateNotifications")
            HttpResponse.noContent<Unit>()
        } catch (e: Exception) {
            LOG.error(markers, "BackofficeConfigurationControllerUpdateNotifications", e)
            HttpResponse.serverError<Unit>()
        }
    }

    @Delete("/{accountId}/creditCard")
    fun deleteCreditCard(@PathVariable accountId: String): HttpResponse<*> {
        val markers = Markers.append("accountId", accountId)
        return accountService.disableCreditCardUsage(accountId = AccountId(accountId)).map {
            LOG.info(markers, "BackofficeConfigurationControllerDeleteCreditCard")
            HttpResponse.ok<Unit>()
        }.getOrElse {
            when (it) {
                is AccountNotFoundException -> HttpResponse.notFound<Unit>()
                else -> {
                    LOG.error(markers, "BackofficeConfigurationControllerDeleteCreditCard", it)
                    HttpResponse.serverError<Unit>()
                }
            }
        }
    }

    @Get("/{accountId}/creditCard")
    fun getCreditCard(@PathVariable accountId: String): HttpResponse<*> {
        val markers = Markers.append("accountId", accountId)
        return try {
            val account = accountService.findAccountById(accountId = AccountId(accountId))
            if (!account.hasCreditCardEnabled()) {
                throw AccountNotFoundException(accountId)
            }
            return HttpResponse.ok(
                account.toCreditCardResponseTO(),
            )
        } catch (e: Exception) {
            when (e) {
                is AccountNotFoundException -> HttpResponse.notFound<Unit>()
                else -> {
                    LOG.error(markers, "BackofficeConfigurationControllerGetCreditCard", e)
                    HttpResponse.serverError<Unit>()
                }
            }
        }
    }

    @Get("/group/{group}")
    fun getByGroup(@PathVariable group: AccountGroup): HttpResponse<*> {
        val markers = Markers.append("group", group)
        return try {
            val accounts =
                accountService.findAllUsersWithGroup(group).map { it.toUserAccountRegister().toUserAccountRegisterTO() }

            return HttpResponse.ok(
                accounts,
            )
        } catch (e: Exception) {
            LOG.error(markers, "BackofficeConfigurationControllerGetByGroup", e)
            HttpResponse.serverError<Unit>()
        }
    }

    @Post("/group/{group}")
    fun addGroupToAccount(@PathVariable group: AccountGroup, @Body body: List<String>): HttpResponse<*> {
        val markers = Markers.append("group", group).andAppend("body", body)
        val logName = "BackofficeConfigurationController#addGroupToAccount"
        val (successes, errors) = body.partition { accountId ->
            try {
                accountService.addGroups(AccountId(accountId), listOf(group))
                true
            } catch (e: Exception) {
                LOG.error(markers.andAppend("accountId", accountId), logName, e)
                false
            }
        }
        LOG.info(markers.andAppend("successes", successes).andAppend("errors", errors), logName)
        return HttpResponse.ok(AccountGroupResult(successes.map { it }, errors.map { it }))
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(BackofficeConfigurationController::class.java)
    }
}

data class CreditCardResponseTO(
    val accountId: String,
    val quota: Long,
    val allowedRisk: RiskLevel,
)

data class AccountNotificationsRequestTO(
    val receiveDDANotification: Boolean,
    val receiveNotification: Boolean,
)

private fun Account.toCreditCardResponseTO() = CreditCardResponseTO(
    accountId = accountId.value,
    quota = configuration.creditCardConfiguration.quota,
    allowedRisk = configuration.creditCardConfiguration.allowedRisk,
)