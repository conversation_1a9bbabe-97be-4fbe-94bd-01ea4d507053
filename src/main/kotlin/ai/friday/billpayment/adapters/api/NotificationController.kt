package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.bill.DefaultFindBillService
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.app.wallet.canView
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Secured(Role.Code.OWNER)
@Controller("/notification")
@Version("2")
class NotificationController(
    private val defaultFindBillService: DefaultFindBillService,
    private val walletService: WalletService,
) {

    @Get("/bills")
    fun getBillNotifications(authentication: Authentication): HttpResponse<*> {
        val markers = append("accountId", authentication.toAccountId().value)
        val wallets = walletService.findWallets(authentication.toAccountId())
            .ifEmpty { return HttpResponse.status<Unit>(HttpStatus.FORBIDDEN) }
        val response = wallets.map { wallet ->
            val member = wallet.getActiveMember(authentication.toAccountId())
            val bills = defaultFindBillService.findActiveAndWaitingApprovalBills(wallet.id)
            val billsOverdue = bills.filter { bill ->
                bill.effectiveDueDate.isBefore(getLocalDate()) && member.canView(bill)
            }.size
            val hasBillsWaitingFunds = bills.any { it.schedule?.waitingFunds == true && member.canView(it) }
            BillsNotificationTO(
                walletId = wallet.id.value,
                billsOverdue = billsOverdue,
                billsWaitingFunds = hasBillsWaitingFunds,
            )
        }
        LOG.info(markers.and(append("response", response)), "getBillNotifications")
        return HttpResponse.ok(response)
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(NotificationController::class.java)
    }
}

data class BillsNotificationTO(val walletId: String, val billsOverdue: Int, val billsWaitingFunds: Boolean)