package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.TransactionRepository
import ai.friday.billpayment.app.payment.TransactionId
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.security.annotation.Secured
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Secured(Role.Code.BACKOFFICE)
@Controller("/backoffice/transaction")
class BackofficeTransactionController(
    private val transansactionRepository: TransactionRepository,
) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    @Get("/{transactionId}")
    fun getTransaction(@PathVariable transactionId: String): HttpResponse<*> {
        val markers = append("transactionId", transactionId)
        val logName = "getTransaction"
        return try {
            val transaction = transansactionRepository.findById(TransactionId(transactionId))
            return HttpResponse.ok(transaction)
        } catch (e: ItemNotFoundException) {
            return HttpResponse.notFound("Transaction não encontrada $transactionId")
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return HttpResponse.serverError(e.message)
        }
    }

    @Get("/byBillId/{billId}")
    fun getTransactionsByBillId(@PathVariable billId: String): HttpResponse<*> {
        val markers = append("billId", billId)
        val logName = "getTransactionsByBillId"
        return try {
            val transactions = transansactionRepository.findTransactions(billId = BillId(billId), status = null)
            return HttpResponse.ok(transactions.sortedByDescending { it.created })
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return HttpResponse.serverError(e.message)
        }
    }
}