package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountNotFoundException
import ai.friday.billpayment.app.account.Role.Code.BACKOFFICE
import ai.friday.billpayment.app.backoffice.BackofficeCNPJService
import ai.friday.billpayment.app.backoffice.CNPJCreateData
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.isValidCnpj
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.log
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import org.slf4j.LoggerFactory

@Secured(BACKOFFICE)
@Controller("/backoffice/cnpj")
class BackofficeCNPJController(
    private val backofficeCNPJService: BackofficeCNPJService,
) {
    @Post("/wallet/{walletId}/associatePJ")
    fun associateAccountIdsToWalletId(
        @PathVariable walletId: String,
        @Body request: AssociateToWalletIdTO,
    ): HttpResponse<*> {
        val markers = log("walletId" to walletId, "request" to request)
        val logName = "BackofficeController#associateAccountIdsToWalletId"

        return try {
            val typedWalletId = WalletId(walletId)

            backofficeCNPJService.associateAccountIdsToWalletId(typedWalletId, request.accountIds.map { AccountId(it) }, request.memberType).getOrElse { error ->
                logger.warn(markers.andAppend("error", error), logName)
                return HttpResponse.badRequest(error.toString())
            }

            logger.info(markers.andAppend("walletId", walletId).andAppend("request", request).andAppend("success", true), logName)
            HttpResponse.created("")
        } catch (ex: ItemNotFoundException) {
            logger.warn(markers, logName, ex)
            HttpResponse.badRequest(ex)
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            HttpResponse.serverError(ex)
        }
    }

    @Post("/wallet/{walletId}/dissociatePJ")
    fun dissociateAccountIdsToWalletId(
        @PathVariable walletId: String,
        @Body request: DissociateToWalletIdTO,
    ): HttpResponse<*> {
        val markers = log("walletId" to walletId, "request" to request)
        val logName = "BackofficeController#dissociateAccountIdsToWalletId"

        return try {
            val typedWalletId = WalletId(walletId)

            backofficeCNPJService.dissociateAccountIdsFromWalletId(typedWalletId, request.accountIds.map { AccountId(it) }).getOrElse { error ->
                logger.warn(markers.andAppend("error", error), logName)
                return HttpResponse.badRequest(error.toString())
            }

            logger.info(markers.andAppend("walletId", walletId).andAppend("request", request).andAppend("success", true), logName)
            return HttpResponse.noContent<Unit>()
        } catch (ex: ItemNotFoundException) {
            logger.warn(markers, logName, ex)
            HttpResponse.badRequest(ex)
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            HttpResponse.serverError(ex)
        }
    }

    @Post("/account/{accountId}/createCNPJAccount")
    fun createCNPJAccount(@PathVariable accountId: String, @Body data: CNPJAccountTO): HttpResponse<*> {
        val markers = log("accountId" to accountId, "data" to data)
        val logName = "BackofficeController#createCNPJAccount"
        return try {
            val accountIdPF = AccountId(accountId)

            if (!Document(data.cnpj).isValidCnpj()) {
                logger.error(markers.andAppend("invalidCnpj", data.cnpj), logName)
                HttpResponse.badRequest(ResponseTO("4001", message = "Cnpj inválido"))
            }

            val result = backofficeCNPJService.createCNPJAccount(
                accountIdPF,
                CNPJCreateData(data.cnpj, businessName = data.businessName, accountNo = data.accountNo, accountDv = data.accountDv),
            )

            logger.info(markers.andAppend("accountIdPj", result.accountId).andAppend("result", result).andAppend("success", true), logName)
            HttpResponse.created(mapOf("accountIdPj" to result.accountId, "pixKeyCreated" to result.pixKeyCreated, "crmCreatedPJ" to result.crmCreatedPJ, "crmUpdatedPF" to result.crmUpdatedPF))
        } catch (ex: AccountNotFoundException) {
            logger.warn(markers, logName, ex)
            HttpResponse.badRequest(ex)
        } catch (ex: Exception) {
            logger.error(markers, logName, ex)
            HttpResponse.serverError(ex)
        }
    }

    data class CNPJAccountTO(
        val cnpj: String,
        val businessName: String,
        val accountNo: String,
        val accountDv: String,
    )

    companion object {
        private val logger = LoggerFactory.getLogger(BackofficeCNPJController::class.java)
    }
}

data class AssociateToWalletIdTO(
    val accountIds: List<String>,
    val memberType: MemberType,
)

data class DissociateToWalletIdTO(
    val accountIds: List<String>,
)