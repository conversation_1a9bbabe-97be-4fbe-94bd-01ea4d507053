package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.integrations.BlipConfiguration
import ai.friday.billpayment.app.login.LoginService
import ai.friday.billpayment.app.login.ProviderName
import ai.friday.billpayment.app.login.ProviderUser
import ai.friday.morning.log.andAppend
import io.micronaut.core.async.publisher.Publishers
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.MutableHttpResponse
import io.micronaut.http.annotation.Filter
import io.micronaut.http.filter.HttpServerFilter
import io.micronaut.http.filter.ServerFilterChain
import io.micronaut.http.filter.ServerFilterPhase
import io.micronaut.http.server.util.HttpClientAddressResolver
import io.micronaut.security.authentication.Authentication
import io.micronaut.security.filters.SecurityFilter
import java.util.Optional
import net.logstash.logback.marker.Markers
import org.reactivestreams.Publisher
import org.slf4j.LoggerFactory

const val CALLBACK_ROUTE = "/blip/webhook"

@Filter(value = ["/chatbot/**"])
class ChatbotSecurityFilter(
    private val loginService: LoginService,
    private val addressResolver: HttpClientAddressResolver,
    private val blipConfiguration: BlipConfiguration,
) :
    HttpServerFilter {

    override fun doFilter(request: HttpRequest<*>, chain: ServerFilterChain): Publisher<MutableHttpResponse<*>> {
        val clientIP = addressResolver.resolve(request)
        val markers = Markers.append("currentIp", clientIP)
        if (blipConfiguration.allowedIps.isNotEmpty()) {
            if (!blipConfiguration.allowedIps.contains(clientIP)) {
                LOG.error(
                    markers.andAppend("ACTION", "VERIFY")
                        .andAppend("context", "IP não reconhecido. blip pode ter mudado seus IPs."),
                    "ChatbotHeaderSecurityFilter",
                )
                return Publishers.just(HttpResponse.status<Any>(HttpStatus.FORBIDDEN))
            }
        }
        if (request.uri.path.endsWith(CALLBACK_ROUTE)) {
            return chain.proceed(request)
        }

        val userExternalId = getExternalUserId(request)
        markers.andAppend("userExternalId", userExternalId)

        if (userExternalId == null) {
            LOG.warn(markers.andAppend("status", HttpStatus.FORBIDDEN), "ChatbotHeaderSecurityFilter")
            return Publishers.just(HttpResponse.status<Any>(HttpStatus.FORBIDDEN))
        }

        val providerUser = loginService.findProviderUser(
            emailAddress = EmailAddress(email = userExternalId),
            providerName = ProviderName.WHATSAPP,
        )
        markers.andAppend("providerUser", providerUser)

        if (providerUser == null) {
            LOG.error(markers.andAppend("status", HttpStatus.FORBIDDEN), "ChatbotHeaderSecurityFilter")
            return Publishers.just(HttpResponse.status<Any>(HttpStatus.FORBIDDEN))
        }

        val authentication = request.getUserPrincipal(Authentication::class.java)

        request.setAttribute(
            SecurityFilter.AUTHENTICATION,
            buildCustomAuthentication(authentication, providerUser),
        )

        LOG.info(markers, "ChatbotHeaderSecurityFilter")
        return chain.proceed(request)
    }

    private fun buildCustomAuthentication(
        authentication: Optional<Authentication>,
        providerUser: ProviderUser,
    ): CustomAuthentication {
        return if (authentication.isPresent) {
            with(authentication.get()) {
                CustomAuthentication(providerUser.id, attributes, roles)
            }
        } else {
            CustomAuthentication(providerUser.id, mutableMapOf(), mutableListOf())
        }
    }

    private fun getExternalUserId(request: HttpRequest<*>): String? {
        return request.headers.firstOrNull { it.key.equals("X-EXTERNAL-USER-ID", ignoreCase = true) }?.value?.first()
    }

    override fun getOrder(): Int {
        return ServerFilterPhase.SECURITY.after()
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(ChatbotSecurityFilter::class.java)
    }
}