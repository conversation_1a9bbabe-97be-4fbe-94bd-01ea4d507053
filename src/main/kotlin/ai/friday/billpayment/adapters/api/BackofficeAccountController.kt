package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.adapters.arbi.ArbiAccountAdapter
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.account.getName
import ai.friday.billpayment.app.backoffice.FraudPreventionService
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.CrmRepository
import ai.friday.billpayment.app.integrations.NaturalPersonStatus
import ai.friday.billpayment.app.integrations.NaturalPersonStatusChangeReason
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.utils.PublicHttpLinkGenerator
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.context.annotation.Property
import io.micronaut.http.HttpResponse
import io.micronaut.http.MediaType
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.http.annotation.Put
import io.micronaut.http.annotation.QueryValue
import io.micronaut.security.annotation.Secured
import io.via1.communicationcentre.app.integrations.EmailSenderService
import java.time.Duration
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Secured(Role.Code.ADMIN)
@Controller("/backoffice")
class BackofficeAccountController(
    private val accountRepository: AccountRepository,
    private val walletRepository: WalletRepository,
    private val arbiAccountAdapter: ArbiAccountAdapter,
    private val fraudPreventionService: FraudPreventionService,
    private val crmRepository: CrmRepository,
    private val accountRegisterRepository: AccountRegisterRepository,
    private val publicHttpLinkGenerator: PublicHttpLinkGenerator,
    private val emailSenderService: EmailSenderService,
    @Property(name = "backoffice.document-email.recipient") private val documentEmailRecipient: String,
    @Property(name = "email.notification.email") private val documentEmailSender: String,
) {
    @Get("/walletId/{walletId}/externalAccountStatus")
    fun getExternalAccountStatus(@PathVariable walletId: String): HttpResponse<*> {
        val markers = append("walletId", walletId)
        val logName = "getExternalAccountStatus"
        return try {
            arbiAccountAdapter.getNaturalPersonStatus(
                accountNo = WalletId(walletId).toAccountNumber(),
            ).map {
                markers.andAppend("status", it)
                LOG.info(markers, logName)
                HttpResponse.ok(it)
            }.getOrElse {
                LOG.error(markers, logName, it)
                HttpResponse.serverError(it.message.orEmpty())
            }
        } catch (ex: Exception) {
            LOG.error(markers, logName, ex)
            HttpResponse.serverError(ex.message.orEmpty())
        }
    }

    @Get("/externalAccountStatus{?accounts}")
    fun getExternalAccountsStatus(@QueryValue accounts: List<String>?): HttpResponse<*> {
        val markers = append("accounts", accounts)
        val logName = "getExternalAccountsStatus"
        return try {
            arbiAccountAdapter.getNaturalPersonsStatus(
                accounts?.map { AccountNumber(it) } ?: emptyList(),
            ).map {
                markers.andAppend("status", it)
                LOG.info(markers, logName)
                HttpResponse.ok(it)
            }.getOrElse {
                LOG.error(markers, logName, it)
                HttpResponse.serverError(it.message.orEmpty())
            }
        } catch (ex: Exception) {
            LOG.error(markers, logName, ex)
            HttpResponse.serverError(ex.message.orEmpty())
        }
    }

    @Put("/account/{accountId}/publishCrmEvent")
    fun publishCrmEvent(@PathVariable accountId: String, @Body body: PublishCrmEventRequest): HttpResponse<*> {
        val markers = append("accountId", accountId)
            .andAppend("body", body)
        val logName = "publishCrmEvent"
        return try {
            crmRepository.publishEvent(
                accountId = AccountId(accountId),
                eventName = body.eventName,
                customParams = body.customParams ?: emptyMap(),
            )
            HttpResponse.ok<Unit>()
        } catch (ex: Exception) {
            LOG.error(markers, logName, ex)
            HttpResponse.serverError(ex.message.orEmpty())
        }
    }

    @Put("/walletId/{walletId}/externalAccountStatus")
    fun setExternalAccountStatus(@PathVariable walletId: String, @Body body: SetExternalAccountStatusRequest): HttpResponse<*> {
        val markers = append("walletId", walletId)
            .andAppend("body", body)
        val logName = "setExternalAccountStatus"
        return try {
            arbiAccountAdapter.setNaturalPersonStatus(
                accountNo = WalletId(walletId).toAccountNumber(),
                status = body.status,
                reason = body.reason,
            ).map {
                LOG.info(markers, logName)
                HttpResponse.ok<Unit>()
            }.getOrElse {
                LOG.error(markers, logName, it)
                HttpResponse.serverError(it.message.orEmpty())
            }
        } catch (ex: Exception) {
            LOG.error(markers, logName, ex)
            HttpResponse.serverError(ex.message.orEmpty())
        }
    }

    @Put("/account/{accountId}/internalAccountStatus")
    fun setInternalAccountStatus(@PathVariable accountId: String, @Body body: SetInternalAccountStatusRequest): HttpResponse<*> {
        val markers = append("accountId", accountId)
            .andAppend("body", body)
        val logName = "setInternalAccountStatus"
        return try {
            val partial = accountRepository.findPartialAccountByIdOrNull(AccountId(accountId))

            if (partial == null) {
                accountRepository.updateAccountStatus(AccountId(accountId), body.status)
            } else {
                accountRepository.updatePartialAccountStatus(AccountId(accountId), body.status)
            }

            LOG.info(markers, logName)
            HttpResponse.ok<Unit>()
        } catch (ex: Exception) {
            LOG.error(markers, logName, ex)
            HttpResponse.serverError(ex.message.orEmpty())
        }
    }

    @Put("/walletId/{walletId}/captureAllFunds")
    fun captureAllFundsFromWallet(@PathVariable walletId: String): HttpResponse<*> {
        val markers = append("walletId", walletId)
        val logName = "captureAllFundsFromWallet"
        return try {
            val wallet = walletRepository.findWallet(WalletId(walletId))

            fraudPreventionService.captureAllFundsFromAccount(
                from = wallet.founder.accountId,
                cpf = wallet.founder.document,
            ).map {
                markers.andAppend("bankTransfer", it)
                when (it.status) {
                    BankOperationStatus.SUCCESS -> {
                        LOG.info(markers, logName)
                        HttpResponse.ok(it)
                    }

                    else -> {
                        LOG.error(markers, logName)
                        HttpResponse.serverError(it)
                    }
                }
            }.getOrElse {
                markers.andAppend("error", it)
                LOG.error(markers, logName)
                HttpResponse.serverError(it)
            }
        } catch (ex: Exception) {
            LOG.error(markers, logName, ex)
            HttpResponse.serverError(ex.message.orEmpty())
        }
    }

    private fun WalletId.toAccountNumber(): AccountNumber {
        val wallet = walletRepository.findWallet(this)

        val paymentMethod = accountRepository.findAccountPaymentMethodByIdAndAccountId(
            accountPaymentMethodId = wallet.paymentMethodId,
            accountId = wallet.founder.accountId,
        )

        return paymentMethod.toAccountNumber()
    }

    private fun AccountPaymentMethod.toAccountNumber() = (method as InternalBankAccount).toAccountNumber()

    private fun InternalBankAccount.toAccountNumber() = AccountNumber(
        number = accountNo.toBigInteger(),
        dv = accountDv,
    )

    companion object {
        private val LOG = LoggerFactory.getLogger(BackofficeAccountController::class.java)
    }
}

@Secured(Role.Code.ADMIN, Role.Code.BACKOFFICE_ADMIN)
@Controller("/backoffice")
class BackofficeAdminAccountController(
    private val accountRegisterRepository: AccountRegisterRepository,
    private val publicHttpLinkGenerator: PublicHttpLinkGenerator,
    private val emailSenderService: EmailSenderService,
    @Property(name = "backoffice.document-email.recipient") private val documentEmailRecipient: String,
    @Property(name = "email.notification.email") private val documentEmailSender: String,
) {
    @Get("/account/{accountId}/documents")
    fun getUserDocuments(@PathVariable accountId: String): HttpResponse<*> {
        val markers = append("accountId", accountId)
        val logName = "getUserDocuments"
        return try {
            val accountRegisterData = accountRegisterRepository.findByAccountId(AccountId(accountId))

            val documents = mutableMapOf<String, String?>()

            // Add selfie if available
            accountRegisterData.uploadedSelfie?.let { selfie ->
                val selfieUrl = publicHttpLinkGenerator.generate(
                    selfie,
                    Duration.ofMinutes(10),
                    "image/jpeg",
                )
                documents["selfie"] = selfieUrl
            }

            LOG.info(markers.andAppend("documentCount", documents.size), logName)
            HttpResponse.ok(documents)
        } catch (ex: Exception) {
            LOG.error(markers, logName, ex)
            HttpResponse.serverError(ex.message.orEmpty())
        }
    }

    @Post("/account/{accountId}/documents/email")
    fun sendDocumentsEmail(@PathVariable accountId: String): HttpResponse<*> {
        val markers = append("accountId", accountId)
        val logName = "sendDocumentsEmail"
        return try {
            val accountRegisterData = accountRegisterRepository.findByAccountId(AccountId(accountId))
            val documents = mutableMapOf<String, String?>()
            val duration = Duration.ofMinutes(10)

            // Add selfie if available
            accountRegisterData.uploadedSelfie?.let { selfie ->
                val selfieUrl = publicHttpLinkGenerator.generate(
                    selfie,
                    duration,
                    "image/jpeg",
                )
                documents["Selfie"] = selfieUrl
            }

            // Add front document if available
            accountRegisterData.uploadedDocument?.front?.let { frontDoc ->
                val frontDocUrl = publicHttpLinkGenerator.generate(
                    frontDoc,
                    duration,
                    "image/jpeg",
                )
                documents["Documento - Frente"] = frontDocUrl
            }

            // Add back document if available
            accountRegisterData.uploadedDocument?.back?.let { backDoc ->
                val backDocUrl = publicHttpLinkGenerator.generate(
                    backDoc,
                    duration,
                    "image/jpeg",
                )
                documents["Documento - Verso"] = backDocUrl
            }

            // Add CNH if available
            accountRegisterData.uploadedCNH?.let { cnh ->
                val cnhUrl = publicHttpLinkGenerator.generate(
                    cnh,
                    duration,
                    "image/jpeg",
                )
                documents["CNH"] = cnhUrl
            }

            // Add CNH if available
            accountRegisterData.kycFile?.let { kyc ->
                val kycUrl = publicHttpLinkGenerator.generate(
                    kyc,
                    duration,
                    "application/pdf",
                )
                documents["KYC"] = kycUrl
            }

            val emailBody = """
                <html><body>
                    <h2>Documentos solicitados via backoffice</h2>
                    <p><strong>Nome:</strong> ${accountRegisterData.getName()}</p>
                    <p><strong>Account ID:</strong> $accountId</p>
                    <small>
                        Clique nos links para acessar o documento.<br/>
                        Os links expiram em ${duration.toMinutes()} minutos.
                    </small>
                    <ul>
                        ${
            documents.entries.joinToString("\n") { (key, url) ->
                if (url != null) {
                    "<li><a href=\"$url\"><strong>$key</strong></a></li>"
                } else {
                    ""
                }
            }
            }
                    </ul>
                </body></html>
            """.trimIndent()

            emailSenderService.sendRawEmail(
                documentEmailSender,
                "Documentos solicitados via backoffice - $accountId",
                emailBody,
                documentEmailRecipient,
                emptyList(),
                MediaType.TEXT_HTML,
            )
            LOG.info(markers.andAppend("documentCount", documents.size), logName)
            HttpResponse.ok<Unit>()
        } catch (ex: Exception) {
            LOG.error(markers, logName, ex)
            HttpResponse.serverError(ex.message.orEmpty())
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(BackofficeAdminAccountController::class.java)
    }
}

data class SetExternalAccountStatusRequest(
    val status: NaturalPersonStatus,
    val reason: NaturalPersonStatusChangeReason,
)

data class PublishCrmEventRequest(
    val eventName: String,
    val customParams: Map<String, String>?,
)

data class SetInternalAccountStatusRequest(
    val status: AccountStatus,
)