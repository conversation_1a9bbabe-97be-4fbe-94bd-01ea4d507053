package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountConfigurationService
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role.Code.BACKOFFICE
import ai.friday.billpayment.app.inappsubscription.InAppSubscription
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionReason
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionRepository
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionService
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionStatus
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.CrmService
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.subscription.IgnoreSubscriptionFeeResult
import ai.friday.billpayment.app.subscription.SubscribeResult
import ai.friday.billpayment.app.subscription.Subscription
import ai.friday.billpayment.app.subscription.SubscriptionConfiguration
import ai.friday.billpayment.app.subscription.SubscriptionService
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.app.subscription.UnsubscribeError
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.core.annotation.Introspected
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Delete
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.http.annotation.Put
import io.micronaut.security.annotation.Secured
import io.micronaut.validation.Validated
import jakarta.validation.Valid
import jakarta.validation.constraints.Positive
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Validated
@Secured(BACKOFFICE)
@Controller("/backoffice/subscription")
class BackofficeSubscriptionController(
    private val accountRepository: AccountRepository,
    private val crmService: CrmService,
    private val subscriptionService: SubscriptionService,
    private val subscriptionConfiguration: SubscriptionConfiguration,
    private val accountConfigurationService: AccountConfigurationService,
    private val inAppSubscriptionService: InAppSubscriptionService,
    private val inAppSubscriptionRepository: InAppSubscriptionRepository,
) {
    @Get("/{accountId}")
    fun get(@PathVariable accountId: String): HttpResponse<*> {
        val markers = Markers.append("accountId", accountId)
        return try {
            val subscription = subscriptionService.find(accountId = AccountId(accountId))

            return HttpResponse.ok(
                subscription.toSubscriptionTO(),
            )
        } catch (e: Exception) {
            when (e) {
                is ItemNotFoundException -> HttpResponse.notFound<Unit>()
                else -> {
                    logger.error(markers, "BackofficeSubscriptionControllerGet", e)
                    HttpResponse.serverError<Unit>()
                }
            }
        }
    }

    private fun Subscription.toSubscriptionTO() = SubscriptionTO(
        accountId = accountId.value,
        document = document.value,
        status = status.name,
        amount = amount,
        dayOfMonth = dayOfMonth,
        recurrenceId = recurrenceId.value,
        paymentStatus = paymentStatus.name,
        walletId = walletId.value,
    )

    @Post("/{accountId}")
    fun post(
        @PathVariable
        accountId: String,
        @Body
        @Valid
        body: SubscribeRequestTO,
    ): HttpResponse<*> {
        val markers = Markers.append("accountId", accountId).andAppend("body", body)
        val result = subscriptionService.subscribe(
            accountId = AccountId(accountId),
            amount = body.amount ?: subscriptionConfiguration.amount,
            dayOfMonth = body.dayOfMonth ?: subscriptionConfiguration.dayOfMonth,
            sendNotification = body.sendNotification ?: true,
        )
        markers.andAppend("result", result)

        return when (result) {
            SubscribeResult.Success -> {
                logger.info(markers, "BackofficeSubscriptionControllerPost")
                HttpResponse.ok<Unit>()
            }

            is SubscribeResult.Conflict -> {
                logger.info(markers, "BackofficeSubscriptionControllerPost")
                HttpResponse.status<Unit>(HttpStatus.CONFLICT)
            }

            SubscribeResult.AccountNotFound,
            SubscribeResult.AccountNotActive,
            is SubscribeResult.Error,
            -> {
                logger.error(markers, "BackofficeSubscriptionControllerPost")
                HttpResponse.serverError<Unit>()
            }

            SubscribeResult.AccountIsFreeOfFridaySubscription -> {
                logger.info(markers, "BackofficeSubscriptionControllerPost")
                HttpResponse.status<Unit>(HttpStatus.CONFLICT)
            }
        }
    }

    @Delete("/{accountId}")
    fun deleteSubscripion(
        @PathVariable
        accountId: String,
    ): HttpResponse<*> {
        val markers = Markers.append("accountId", accountId)
        val result = subscriptionService.unsubscribe(
            accountId = AccountId(accountId),
        )
        markers.andAppend("result", result)

        result.getOrElse {
            return when (it) {
                UnsubscribeError.BillProcessing -> {
                    logger.warn(markers, "PostUnsubscribe")
                    HttpResponse.badRequest("BillProcessing")
                }

                UnsubscribeError.AccountNotFound -> {
                    logger.error(markers, "PostUnsubscribe")
                    HttpResponse.serverError("AccountNotFound")
                }

                is UnsubscribeError.ServerError -> {
                    logger.error(markers.andAppend("error_message", it.message), "BackofficeSubscriptionControllerDelete")
                    HttpResponse.serverError(it.message)
                }
            }
        }

        logger.info(markers, "BackofficeSubscriptionControllerDelete")
        return HttpResponse.ok<Unit>()
    }

    @Put("updateAllSubscriptionsToNewAccount")
    fun updateAllSubscriptionsToNewAccount(@Body body: UpdateAllSubscriptionsToNewAccountTO): HttpResponse<*> {
        val result = subscriptionService.updateAllSubscriptionsToNewAccount(body.currentBankAccount, body.limit)

        return HttpResponse.ok(
            object {
                val totalRecurrence = result.first
                val totalBills = result.second
            },
        )
    }

    @Put("/migrateSubscription")
    fun migrateSubscription(@Body body: UpdateSubscriptionTypeTO): HttpResponse<*> {
        val accountId = AccountId(body.accountId)
        val account = accountRepository.findByIdOrNull(accountId) ?: return HttpResponse.notFound("AccountNotFound")

        if (account.subscriptionType != body.currentSubscriptionType) {
            return HttpResponse.badRequest("Current subscription type is not ${body.currentSubscriptionType}")
        }

        return when (body.newSubscriptionType) {
            SubscriptionType.PIX -> {
                migrateSubscriptionToPix(account)
            }

            SubscriptionType.IN_APP -> {
                migrateSubscriptionToInApp(account)
            }
        }
    }

    private fun migrateSubscriptionToPix(
        account: Account,
    ): HttpResponse<*> {
        val logName = "BackofficeSubscriptionController#migrateSubscriptionToPix"
        val markers = Markers.append("accountId", account.accountId.value)

        if (account.subscriptionType != SubscriptionType.IN_APP) {
            return HttpResponse.badRequest("Subscription type is not Pix")
        }

        val subscription = inAppSubscriptionService.getSubscription(account.accountId)
        markers.andAppend("hasInAppSubscription", subscription != null)

        val userHasStoreSubscription = subscription?.checkActiveWithAutoRenewEnabled()

        val updatedAccount = account.copy(subscriptionType = SubscriptionType.PIX)
        accountRepository.save(updatedAccount)
        crmService.upsertContact(updatedAccount)

        logger.info(markers, logName)

        val message = if (userHasStoreSubscription == true) {
            "Usuário tem que ser cancelado na loja manualmente. Talvez com reembolso. Avaliar!"
        } else {
            ""
        }

        return HttpResponse.ok(mapOf("userHasStoreSubscription" to userHasStoreSubscription, "message" to message, "prevousSubscription" to subscription))
    }

    private fun migrateSubscriptionToInApp(
        account: Account,
    ): HttpResponse<*> {
        val logName = "BackofficeSubscriptionController#migrateSubscriptionToInApp"
        val markers = Markers.append("accountId", account.accountId.value)

        if (account.subscriptionType != SubscriptionType.PIX) {
            return HttpResponse.badRequest("Subscription type is not Pix")
        }

        val subscription = subscriptionService.findOrNull(account.accountId)
        markers.andAppend("hasPixSubscription", subscription != null)

        val unsubscribe = subscriptionService.unsubscribe(
            accountId = account.accountId,
        )

        markers.andAppend("unsubscribe", unsubscribe)

        unsubscribe.getOrElse {
            return when (it) {
                UnsubscribeError.BillProcessing -> {
                    HttpResponse.badRequest("BillProcessing")
                }

                UnsubscribeError.AccountNotFound -> {
                    HttpResponse.notFound("AccountNotFound")
                }

                is UnsubscribeError.ServerError -> {
                    markers.andAppend("error_message", it.message)
                    HttpResponse.serverError(it.message)
                }
            }.also {
                logger.error(markers, logName)
            }
        }

        val date = getZonedDateTime()
        val inAppSub = InAppSubscription(
            accountId = account.accountId,
            status = InAppSubscriptionStatus.EXPIRED,
            endsAt = date,
            store = null,
            reason = InAppSubscriptionReason.SUBSCRIPTION,
            inAppSubscriptionAccessConcessionId = null,
            createdAt = date,
            autoRenew = false,
            price = subscription?.amount ?: subscriptionConfiguration.amount,
            productId = null,
            offStoreProductId = null,
        )

        inAppSubscriptionRepository.save(inAppSub)

        val updatedAccount = account.copy(subscriptionType = SubscriptionType.IN_APP)
        accountRepository.save(updatedAccount)
        crmService.upsertContact(updatedAccount)

        logger.info(markers, logName)

        return HttpResponse.ok<Unit>()
    }

    @Post("/{accountId}/ignoreFee/{dueDate}")
    fun postIgnoreFee(
        @PathVariable
        accountId: String,
        dueDate: String,
    ): HttpResponse<*> {
        val markers = Markers.append("accountId", accountId)
            .andAppend("dueDate", dueDate)

        return try {
            val result = subscriptionService.ignoreSubscriptionFee(
                accountId = AccountId(accountId),
                dueDate = LocalDate.parse(dueDate, DateTimeFormatter.ISO_DATE),
            )

            markers.andAppend("result", result)

            when (result) {
                IgnoreSubscriptionFeeResult.Success -> {
                    logger.info(markers, "BackofficeSubscriptionControllerIgnoreFee")
                    HttpResponse.ok<Unit>()
                }

                is IgnoreSubscriptionFeeResult.IgnoreBillError -> {
                    logger.error(markers, "BackofficeSubscriptionControllerIgnoreFee")
                    HttpResponse.serverError<Unit>()
                }

                IgnoreSubscriptionFeeResult.AccountNotFound,
                IgnoreSubscriptionFeeResult.SubscriptionFeeAlreadyPaid,
                IgnoreSubscriptionFeeResult.SubscriptionFeeNotFound,
                IgnoreSubscriptionFeeResult.SubscriptionFeeProcessing,
                IgnoreSubscriptionFeeResult.SubscriptionNotFound,
                -> {
                    logger.warn(markers, "BackofficeSubscriptionControllerIgnoreFee")
                    HttpResponse.badRequest(result.toString())
                }
            }
        } catch (e: Exception) {
            logger.error(markers, "BackofficeSubscriptionControllerIgnoreFee", e)
            HttpResponse.serverError(e.message)
        }
    }

    @Post("/{accountId}/freeOfFridaySubscription")
    fun postFreeOfFridaySubscription(
        @PathVariable accountId: String,
        @Body freeOfFridaySubscriptionTO: FreeOfFridaySubscriptionTO,
    ): HttpResponse<*> {
        val markers = Markers.append("accountId", accountId)
            .andAppend("freeOfFridaySubscriptionTO", freeOfFridaySubscriptionTO)

        return try {
            val result = accountConfigurationService.updateFreeOfFridaySubscription(
                accountId = AccountId(accountId),
                freeOfFridaySubscription = freeOfFridaySubscriptionTO.freeOfFridaySubscription,
            )
            markers.andAppend("result", result)
            logger.info(markers, "BackofficeSubscriptionController#postFreeOfFridaySubscription")
            if (freeOfFridaySubscriptionTO.freeOfFridaySubscription) {
                return deleteSubscripion(accountId)
            }

            HttpResponse.ok<Unit>()
        } catch (e: Exception) {
            logger.error(markers, "BackofficeSubscriptionController#postFreeOfFridaySubscription", e)
            HttpResponse.badRequest(e.message)
        }
    }

    @Post("/ignoreFeeComingMonths")
    fun postIgnoreFeeComingMonths(
        @Body body: IgnoreFeeComingMonthsRequestTO,
    ): HttpResponse<*> {
        val markers = Markers.append("body", body)

        val ignoreResult = mutableMapOf<String, List<String>>()

        return try {
            body.accountIds.forEach { it ->
                val result = subscriptionService.ignoreSubscriptionFeeComingMonths(
                    accountId = AccountId(it),
                    months = body.months,
                    reason = body.reason,
                )

                val printableResult = result.map { it.toString() }

                ignoreResult[it] = printableResult

                markers.andAppend("result", printableResult)
                logger.info(markers, "BackofficeSubscriptionController#IgnoreResultByAccount")
            }

            markers.andAppend("result", ignoreResult)
            logger.info(markers, "BackofficeSubscriptionController#postIgnoreFeeComingMonths")

            return HttpResponse.noContent<Unit>()
        } catch (e: Exception) {
            markers.andAppend("result", ignoreResult)
            logger.error(markers, "BackofficeSubscriptionController#postIgnoreFeeComingMonths", e)
            HttpResponse.serverError(e.message)
        }
    }

    @Post("/ignoreFee/{dueDate}")
    fun postBatchIgnoreFee(
        dueDate: String,
    ): HttpResponse<*> {
        val markers = Markers.append("dueDate", dueDate)

        val ignoreResult = mutableMapOf<String, IgnoreSubscriptionFeeResult>()

        return try {
            val dueDateToIgnore = LocalDate.parse(dueDate, DateTimeFormatter.ISO_DATE)

            subscriptionService.findAllOverdue().filter {
                it.nextEffectiveDueDate == dueDateToIgnore
            }.forEach {
                val result = subscriptionService.ignoreSubscriptionFee(
                    accountId = it.accountId,
                    dueDate = dueDateToIgnore,
                )

                ignoreResult[it.accountId.value] = result

                logger.info(
                    Markers.append("accountId", it.accountId.value).andAppend("result", result),
                    "BackofficeSubscriptionControllerBatchIgnoreFee",
                )
            }

            markers.andAppend("result", ignoreResult)

            when {
                ignoreResult.values.any {
                    it is IgnoreSubscriptionFeeResult.IgnoreBillError
                } -> {
                    logger.error(markers, "BackofficeSubscriptionControllerBatchIgnoreFee")
                    HttpResponse.serverError(ignoreResult)
                }

                ignoreResult.values.all {
                    it is IgnoreSubscriptionFeeResult.Success
                } -> {
                    logger.info(markers, "BackofficeSubscriptionControllerBatchIgnoreFee")
                    HttpResponse.ok(ignoreResult)
                }

                else -> {
                    logger.warn(markers, "BackofficeSubscriptionControllerBatchIgnoreFee")
                    HttpResponse.badRequest(ignoreResult)
                }
            }
        } catch (e: Exception) {
            markers.andAppend("result", ignoreResult)
            logger.error(markers, "BackofficeSubscriptionControllerBatchIgnoreFee", e)
            HttpResponse.serverError(e.message)
        }
    }

    @Post("/{accountId}/synchronizePaymentStatus")
    fun synchronizePaymentStatus(
        @PathVariable
        accountId: String,
    ): HttpResponse<*> {
        val markers = Markers.append("accountId", accountId)

        val result = subscriptionService.synchronizePixSubscriptionPaymentStatus(
            accountId = AccountId(accountId),
        )

        markers.andAppend("result", result)

        logger.info(markers, "BackofficeSubscriptionControllerSynchronizePaymentStatus")
        return HttpResponse.ok<Unit>()
    }

    @Post("/synchronizeAllPaymentStatus")
    fun synchronizeAllPaymentStatus(): HttpResponse<*> {
        subscriptionService.synchronizeAllPixSubscriptionsPaymentStatus()

        logger.info("BackofficeSubscriptionControllerSynchronizeAllPaymentStatus")
        return HttpResponse.ok<Unit>()
    }

    companion object {
        private val logger = LoggerFactory.getLogger(BackofficeSubscriptionController::class.java)
    }
}

data class SubscriptionTO(
    val accountId: String,
    val document: String,
    val status: String,
    val amount: Long,
    val dayOfMonth: Int,
    val recurrenceId: String,
    val paymentStatus: String,
    val walletId: String,
)

@Introspected
data class SubscribeRequestTO(
    @field:Positive val amount: Long?,
    @field:Positive val dayOfMonth: Int?,
    val sendNotification: Boolean?,
)

data class FreeOfFridaySubscriptionTO(
    val freeOfFridaySubscription: Boolean,
)

data class IgnoreFeeComingMonthsRequestTO(
    val accountIds: List<String>,
    val months: Int,
    val reason: String,
)

data class TransferSubscriptionTO(
    val sourceAccountId: String,
    val targetAccountId: String,
)

data class UpdateSubscriptionTypeTO(
    val accountId: String,
    val newSubscriptionType: SubscriptionType,
    val currentSubscriptionType: SubscriptionType,
)

data class UpdateAllSubscriptionsToNewAccountTO(
    val currentBankAccount: String,
    val limit: Int,
)