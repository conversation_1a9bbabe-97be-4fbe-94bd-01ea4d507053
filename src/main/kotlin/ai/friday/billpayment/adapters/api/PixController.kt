package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.adapters.api.builders.BillTOBuilder
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.bill.PixCopyAndPaste
import ai.friday.billpayment.app.integrations.PixKeyManagement
import ai.friday.billpayment.app.integrations.PixQRCodeParserService
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyError
import ai.friday.billpayment.app.pix.PixKeyException
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.pix.isValid
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import io.micronaut.validation.Validated
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Validated
@Secured(Role.Code.OWNER)
@Controller("/pix")
@Version("2")
class PixKeyController(
    private val pixKeyManagement: PixKeyManagement,
    private val accountService: AccountService,
    private val pixQRCodeParserService: PixQRCodeParserService,
    private val billTOBuilder: BillTOBuilder,
) {

    @Post("/QRCODE")
    fun validateQRCode(
        authentication: Authentication,
        @Body body: QrCodeTO,
    ): HttpResponse<*> {
        val logName = "PixKeyController#validateQRCode"
        val markers = append("qrCode", body.qrCode)
        try {
            val account = accountService.findAccountById(authentication.toAccountId())

            markers.andAppend("accountId", account.accountId.value)
            val pixKeyDetailsResult = pixQRCodeParserService.parseQRCodeCacheable(PixCopyAndPaste(body.qrCode), account.document).getOrElse {
                logger.warn(markers.andAppend("pixError", it::class.java.simpleName), logName)
                return HttpResponse.badRequest<Unit>()
            }.let {
                billTOBuilder.toPixQrCodeResponseTO(it)
            }
            logger.info(markers.andAppend("result", pixKeyDetailsResult), logName)
            return HttpResponse.ok(pixKeyDetailsResult)
        } catch (e: Exception) {
            logger.error(markers.andAppend("pixError", e), logName)
            return HttpResponse.serverError<Unit>()
        }
    }

    @Get("/{keyType}/{keyValue}")
    fun findPixKey(
        authentication: Authentication,
        @PathVariable keyType: PixKeyType,
        @PathVariable keyValue: String,
    ): HttpResponse<*> {
        val pixKey = PixKey(keyValue.lowercase(), keyType)
        if (!pixKey.isValid()) {
            return HttpResponse.badRequest<Unit>()
        }

        val account = accountService.findAccountById(authentication.toAccountId())

        return try {
            val pixKeyDetails = pixKeyManagement.findKeyDetailsCacheable(pixKey, account.document).pixKeyDetails
            HttpResponse.ok(billTOBuilder.toPixKeyResponseTO(pixKeyDetails))
        } catch (e: PixKeyException) {
            logger.warn(
                append("pixKeyError", e.pixKeyError)
                    .and<LogstashMarker>(append("accountId", account.accountId.value))
                    .and(append("pixKey", pixKey)),
                "findPixKey",
            )
            when (e.pixKeyError) {
                is PixKeyError.MalformedKey -> HttpResponse.badRequest<Unit>()
                is PixKeyError.KeyNotFound, is PixKeyError.KeyNotConfirmed -> HttpResponse.notFound()
                is PixKeyError.UnknownError, PixKeyError.SystemUnavailable, PixKeyError.InvalidQrCode -> HttpResponse.serverError()
            }
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(PixKeyController::class.java)
    }
}

data class QrCodeTO(
    val qrCode: String,
)