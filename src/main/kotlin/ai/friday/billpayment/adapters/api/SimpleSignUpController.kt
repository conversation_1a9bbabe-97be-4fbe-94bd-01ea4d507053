package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.account.RegisterService
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.UserPoolPasswordValidationException
import ai.friday.billpayment.app.integrations.UserPoolUsernameExistsException
import arrow.core.getOrElse
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Put
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Secured(Role.Code.GUEST_OTP)
@Controller("/register/simpleSignUp")
@Version("2")
class SimpleSignupController(
    private val registerService: RegisterService,
    private val accountRepository: AccountRepository,
) {

    @Put("/{username}")
    fun signupCreatePassword(
        authentication: Authentication,
        @PathVariable username: String,
        @Body signupRequest: SignupRequestTO,
    ): HttpResponse<*> {
        val markers = Markers.append("authentication", authentication)
        val logName = "SimpleSignupController#signupCreatePassword"

        val accountId = authentication.toAccountId()

        val account = try {
            accountRepository.findById(accountId)
        } catch (ex: Exception) {
            logException(markers, ex, logName)
            return StandardHttpResponses.conflict(
                ResponseTO(
                    SimpleSignupErrorResponse.ACCOUNT_NOT_FOUND.code,
                    message = "user not found",
                ),
            )
        }

        if (account.document != username) {
            return StandardHttpResponses.conflict(
                ResponseTO(
                    SimpleSignupErrorResponse.DOCUMENT_MISMATCH.code,
                    message = "document mismatch",
                ),
            )
        }

        val user = registerService.createSimpleOwnerUser(authentication.toAccountId(), signupRequest.password)
        user.map {
            LOG.info(markers, logName)
            return HttpResponse.created(it)
        }.getOrElse {
            logException(markers, it, logName)
            return when (it) {
                is UserPoolPasswordValidationException -> StandardHttpResponses.badRequest(
                    SimpleSignupErrorResponse.INVALID_PASSWORD.code,
                    it.message.orEmpty(),
                )

                is UserPoolUsernameExistsException -> StandardHttpResponses.conflict(
                    ResponseTO(
                        SimpleSignupErrorResponse.USERNAME_ALREADY_EXISTS.code,
                        message = it.message.orEmpty(),
                    ),
                )

                else -> StandardHttpResponses.serverError(
                    ResponseTO(
                        SimpleSignupErrorResponse.SERVER_ERROR.code,
                        message = it.message.orEmpty(),
                    ),
                )
            }
        }
    }

    private fun logException(markers: LogstashMarker, exception: Exception, logName: String) {
        when (exception) {
            is UserPoolPasswordValidationException, is UserPoolUsernameExistsException -> LOG.warn(
                markers.and(
                    Markers.append(
                        "error",
                        exception.javaClass.simpleName,
                    ),
                ),
                logName,
            )

            else -> LOG.error(markers, logName, exception)
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(SimpleSignupController::class.java)
    }
}

enum class SimpleSignupErrorResponse(val code: String) {
    INVALID_PASSWORD("4000"), USERNAME_ALREADY_EXISTS("4001"), DOCUMENT_MISMATCH("4002"), ACCOUNT_NOT_FOUND(
        "4004",
    ),
    SERVER_ERROR("5000"),
}

data class SignupRequestTO(
    val password: String,
)