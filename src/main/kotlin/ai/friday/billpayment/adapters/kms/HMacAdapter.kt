package ai.friday.billpayment.adapters.kms

import ai.friday.billpayment.app.integrations.HMacService
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import javax.crypto.Mac
import javax.crypto.spec.SecretKeySpec
import org.apache.commons.codec.binary.Base64

@Singleton
class HMacAdapter(
    @Property(name = "kms.hmacKey") private val hmacKey: String,
) : HMacService {

    private val base64 = Base64()
    private val algorithm = "HmacSHA512"

    override fun sign(creditCard: String, expirationDate: String): String {
        val secretKeySpec = SecretKeySpec(hmacKey.toByteArray(), algorithm)
        val mac: Mac = Mac.getInstance(algorithm)
        mac.init(secretKeySpec)
        val data = "$creditCard$expirationDate"
        return base64.encodeToString(mac.doFinal(data.toByteArray()))
    }
}