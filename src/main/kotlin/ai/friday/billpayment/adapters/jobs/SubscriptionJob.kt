package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.app.account.AccountConfigurationService
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.SystemActivityService
import ai.friday.billpayment.app.account.SystemActivityType
import ai.friday.billpayment.app.bill.isBusinessDay
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionService
import ai.friday.billpayment.app.job.AbstractJob
import ai.friday.billpayment.app.subscription.SubscribeResult
import ai.friday.billpayment.app.subscription.SubscriptionConfiguration
import ai.friday.billpayment.app.subscription.SubscriptionService
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.log.andAppend
import io.micronaut.context.annotation.Requires
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
@Requires(beans = [SubscriptionJobService::class])
open class CreateSubscriptionRecurrenceJob(
    private val subscriptionJobService: SubscriptionJobService,
) : AbstractJob(
    cron = "0 15 5-10 * *", // NOTE: a Job não deve rodar depois do dia do vencimento da assinatura
    lockAtLeastFor = "10m",
) {
    override fun execute() {
        if (getLocalDate().isBusinessDay() && getLocalDate().dayOfMonth <= 10) {
            subscriptionJobService.createSubscriptions()
        }
    }
}

@Singleton
@Requires(beans = [SubscriptionJobService::class])
open class SynchronizeAllSubscriptionsPaymentStatusJob(
    private val subscriptionJobService: SubscriptionJobService,
) : AbstractJob(
    cron = "0 15 * * *",
    lockAtLeastFor = "10m",
) {
    override fun execute() {
        subscriptionJobService.synchronizeAllSubscriptionsPaymentStatus()
        LOG.info(
            "SynchronizeAllSubscriptionsPaymentStatusJob",
        )
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(SynchronizeAllSubscriptionsPaymentStatusJob::class.java)
    }
}

@Singleton
@Requires(beans = [SubscriptionJobService::class])
open class SynchronizeAllInAppSubscriptionsPaymentStatusJob(
    private val inAppSubscriptionService: InAppSubscriptionService,
) : AbstractJob(
    cron = "0 15 * * *",
    lockAtLeastFor = "10m",
) {
    override fun execute() {
        inAppSubscriptionService.handleSubscriptionExpiration()
        LOG.info(
            "SynchronizeAllInAppSubscriptionsPaymentStatusJob",
        )
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(SynchronizeAllSubscriptionsPaymentStatusJob::class.java)
    }
}

@Singleton
@Requires(beans = [SubscriptionJobService::class])
open class UnscheduleCurrentSubscriptionJob(
    private val subscriptionService: SubscriptionService,
) : AbstractJob(
    cron = "0 4 10-15 * *",
    lockAtLeastFor = "10m",
) {
    override fun execute() {
        subscriptionService.unscheduleCurrentSubscription()
    }
}

@Singleton
open class SubscriptionJobService(
    private val subscriptionService: SubscriptionService,
    private val systemActivityService: SystemActivityService,
    private val walletService: WalletService,
    private val subscriptionConfiguration: SubscriptionConfiguration,
    private val accountConfigurationService: AccountConfigurationService,
    private val accountService: AccountService,
) {
    @NewSpan
    open fun createSubscriptions() {
        val logName = "CreateSubscriptionRecurrenceJob"
        LOG.info(Markers.append("step", "begin"), logName)

        val receivedDDASubscriptionBill =
            systemActivityService.findAccountIdsThat(SystemActivityType.ReceivedDDASubscriptionBill)

        val viewedBillingCarousel =
            systemActivityService.findAccountIdsThat(SystemActivityType.ViewedBillingCarousel)

        val activatedMoreThan45Days =
            systemActivityService.findAccountIdsLowerThan(
                SystemActivityType.AccountActivated,
                calcAccountActivationCutoffDate(getLocalDate(), 45).format(
                    DateTimeFormatter.ISO_DATE,
                ),
            )

        val expiredTrial =
            systemActivityService.findAccountIdsLowerThan(
                SystemActivityType.TrialActivated,
                calcAccountActivationCutoffDate(getLocalDate(), 0).format(
                    DateTimeFormatter.ISO_DATE,
                ),
            )

        val eligibleAccounts =
            receivedDDASubscriptionBill + viewedBillingCarousel.intersect(activatedMoreThan45Days.toSet()) + expiredTrial

        eligibleAccounts
            .filter { it.isEligibleForPixSubscription() }
            .forEach {
                val result: SubscribeResult =
                    subscriptionService.subscribe(
                        accountId = it,
                        amount = subscriptionConfiguration.amount,
                        dayOfMonth = subscriptionConfiguration.dayOfMonth,
                    )

                val markers = Markers.append("result", result).andAppend("accountId", it)

                when (result) {
                    is SubscribeResult.Error ->
                        LOG.error(markers.andAppend("errorMessage", result.message), logName)

                    SubscribeResult.Success ->
                        LOG.info(markers, logName)

                    SubscribeResult.AccountNotFound, SubscribeResult.AccountIsFreeOfFridaySubscription ->
                        LOG.error(markers, logName)

                    SubscribeResult.AccountNotActive, is SubscribeResult.Conflict -> LOG.warn(markers, logName)
                }
            }

        LOG.info(Markers.append("step", "end"), logName)
    }

    open fun synchronizeAllSubscriptionsPaymentStatus() {
        LOG.info(Markers.append("step", "begin"), "SubscriptionJobService#synchronizeAllSubscriptionsPaymentStatus")
        val activePixSubscriptions = subscriptionService.synchronizeAllPixSubscriptionsPaymentStatus()
        val activeInAppSubscriptions = subscriptionService.synchronizeAllInAppSubscriptionStatusActive()
        val expiredInAppSubscriptions = subscriptionService.synchronizeAllInAppSubscriptionStatusExpired()
        LOG.info(
            Markers.append("step", "end")
                .andAppend("activeSubscriptions", activePixSubscriptions)
                .andAppend("activeInAppSubscriptions", activeInAppSubscriptions)
                .andAppend("expiredInAppSubscriptions", expiredInAppSubscriptions),
            "SubscriptionJobService#synchronizeAllSubscriptionsPaymentStatus",
        )
    }

    internal fun calcAccountActivationCutoffDate(
        date: LocalDate,
        days: Long,
    ): LocalDate = date.withDayOfMonth(10).minusDays(days)

    private fun AccountId.isEligibleForPixSubscription(): Boolean {
        try {
            val configuration = accountConfigurationService.find(this)
            if (configuration.freeOfFridaySubscription) {
                return false
            }

            val account = accountService.findAccountById(this)

            if (account.subscriptionType !== SubscriptionType.PIX) {
                return false
            }

            val wallets = walletService.findWallets(this)

            val isParticipant =
                wallets.any { wallet ->
                    wallet.getMember(this).type !== MemberType.FOUNDER
                }

            if (!isParticipant) {
                return true
            }

            return systemActivityService.getBillPaidOnOwnWallet(this) || systemActivityService.getDDABillPaid(this)
        } catch (e: Exception) {
            LOG.error(Markers.append("accountId", this.value).andAppend("ACTION", "VERIFY"), "isFreeOfCharge", e)
            return false
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(SubscriptionJobService::class.java)
    }
}