package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.ScheduleCanceledReason.EXPIRATION
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.ScheduledBillRepository
import ai.friday.billpayment.app.integrations.ScheduledWalletRepository
import ai.friday.billpayment.app.job.AbstractJob
import ai.friday.billpayment.app.payment.ScheduledBill
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.log.andAppend
import io.micronaut.context.annotation.Property
import jakarta.inject.Named
import jakarta.inject.Singleton
import java.time.LocalDate
import java.util.concurrent.ExecutorService
import java.util.concurrent.atomic.AtomicInteger
import kotlinx.coroutines.runBlocking
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import parallelMap
import reactor.core.scheduler.Schedulers

@Singleton
open class ExpireScheduledBillJob(
    private val updateBillService: UpdateBillService,
    private val scheduledBillRepository: ScheduledBillRepository,
    private val scheduledWalletRepository: ScheduledWalletRepository,
    private val billRepository: BillRepository,
    @Property(name = "schedules.expireScheduledBill.cron") cron: String,
    @Named("jobExecutor") val jobExecutor: ExecutorService,
) : AbstractJob(cron = cron) {
    override fun execute() {
        LOG.info("ExpireScheduledBillJob#started")

        val dateLimit = getLocalDate().minusDays(1)

        val walletsToProcess =
            scheduledWalletRepository.findWalletsWithScheduledBillsUntil(dateLimit)
                .publishOn(Schedulers.fromExecutor(jobExecutor))

        val count = AtomicInteger(0)

        walletsToProcess.buffer(10_000)
            .doOnError {
                LOG.error("ExpireScheduledBillJob#error", it)
            }
            .doOnNext {
                runBlocking {
                    it.chunked(10)
                        .parallelMap {
                            count.addAndGet(it.size)
                            it
                                .flatMap { walletId -> getScheduledBillsByWalletId(walletId, dateLimit) }
                                .filter { scheduledBill -> scheduledBill.expires }
                                .filter { scheduledBill -> scheduledBill.isNotProcessing() }
                                .forEach { scheduledBill -> cancelScheduledPayment(scheduledBill) }
                        }
                }
            }.blockLast()

        LOG.info(append("count", count.get()), "ExpireScheduledBillJob#ended")
    }

    private fun ScheduledBill.isNotProcessing(): Boolean {
        val bill = billRepository.findBill(billId, walletId)

        if (bill.status == BillStatus.PROCESSING) {
            val markers = append("billId", billId.value)
                .andAppend("walletId", walletId)

            LOG.warn(markers, "ExpireScheduledBillJob#billIsProcessing")
        }

        return bill.status != BillStatus.PROCESSING
    }

    private fun getScheduledBillsByWalletId(
        walletId: WalletId,
        dateLimit: LocalDate,
    ) =
        scheduledBillRepository.findScheduledBillsByWalletIdAndUntilScheduledDate(
            walletId = walletId,
            scheduledDate = dateLimit,
        )

    private fun cancelScheduledPayment(scheduledBill: ScheduledBill) {
        LOG.info(append("scheduledBill", scheduledBill), "ExpireScheduledBillJob")

        updateBillService.cancelScheduledPayment(
            walletId = scheduledBill.walletId,
            billId = scheduledBill.billId,
            reason = EXPIRATION,
        )
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(ExpireScheduledBillJob::class.java)
    }
}