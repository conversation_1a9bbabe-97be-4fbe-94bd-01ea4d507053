package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.app.account.AccountClosureDetails
import ai.friday.billpayment.app.account.AccountClosureReason
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.CloseAccountService
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.job.AbstractJob
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.dateFormat
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import jakarta.inject.Singleton
import java.util.stream.Collectors
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
open class RemoveInactiveRegisterJob(
    private val accountRegisterRepository: AccountRegisterRepository,
    private val accountRepository: AccountRepository,
    private val closeAccountService: CloseAccountService,
) : AbstractJob(cron = "0 6 * * *") {
    override fun execute() {
        val sixMonthsAgo = getLocalDate().minusMonths(6).atStartOfDay(brazilTimeZone)
        val markers = Markers.append("cutOffDate", sixMonthsAgo.format(dateFormat))

        val incompleteRegisters = accountRepository.findPartialAccountByStatus(
            accountStatus = AccountStatus.REGISTER_INCOMPLETE,
        ).parallelStream().map {
            accountRegisterRepository.findByAccountId(it.id)
        }.collect(Collectors.toUnmodifiableList())
        markers.andAppend("incompleteRegisters", incompleteRegisters.size)

        val oldestIncompleteRegister = incompleteRegisters.minByOrNull {
            it.lastUpdated
        }
        markers.andAppend(
            "oldestIncompleteRegister",
            oldestIncompleteRegister?.lastUpdated?.format(dateFormat),
        )

        val inactiveRegisters = incompleteRegisters.filter {
            it.lastUpdated.isBefore(sixMonthsAgo)
        }.map {
            it.accountId
        }
        markers.andAppend("inactiveRegisters", inactiveRegisters.size)

        LOG.info(markers, "RemoveInactiveRegisterJob")
        inactiveRegisters.forEach {
            it.closePartialAccount()
        }
    }

    private fun AccountId.closePartialAccount() {
        val markers = Markers.append("accountId", this.value)
        closeAccountService.closePartialAccount(
            this,
            AccountClosureDetails.create(
                reason = AccountClosureReason.INACTIVE,
                description = "Cadastro encerrado pela job de contas abandonadas",
            ),
        ).map {
            markers.andAppend("result", it)
            LOG.info(markers, "RemoveInactiveRegisterJob")
        }.getOrElse {
            markers.andAppend("result", it)
            LOG.error(markers, "RemoveInactiveRegisterJob")
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(RemoveInactiveRegisterJob::class.java)
    }
}