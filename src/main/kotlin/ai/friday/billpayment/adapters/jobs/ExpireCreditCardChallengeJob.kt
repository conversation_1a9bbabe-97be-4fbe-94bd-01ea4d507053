package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.adapters.dynamodb.CreditCardChallengeDbRepository
import ai.friday.billpayment.app.account.CreditCardChallengeStatus
import ai.friday.billpayment.app.account.DefaultCreditCardService
import ai.friday.billpayment.app.account.isExpired
import ai.friday.billpayment.app.job.AbstractJob
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Singleton
open class ExpireCreditCardChallengeJob(
    private val creditCardService: DefaultCreditCardService,
    private val creditCardChallengeRepository: CreditCardChallengeDbRepository,
    @Property(name = "schedules.expireCreditCard.cron") cron: String,
) : AbstractJob(
    cron = cron,
    lockAtLeastFor = "1m",
    lockAtMostFor = "10m",
) {
    override fun execute() {
        creditCardChallengeRepository.findAll(CreditCardChallengeStatus.ACTIVE)
            .forEach { challenge ->
                val marker = append("paymentMethodId", challenge.paymentMethodId)
                try {
                    if (challenge.isExpired()) {
                        LOG.info(marker, "ExpireCreditCardChallengeJob")
                        creditCardService.expireChallenge(challenge)
                    }
                } catch (e: Exception) {
                    LOG.error(marker, "ExpireCreditCardChallengeJob", e)
                }
            }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(ExpireCreditCardChallengeJob::class.java)
    }
}