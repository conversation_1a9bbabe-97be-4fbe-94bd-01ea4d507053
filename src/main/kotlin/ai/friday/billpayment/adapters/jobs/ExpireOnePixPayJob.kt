package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.app.job.AbstractJob
import ai.friday.billpayment.app.onepixpay.OnePixPayRepository
import ai.friday.billpayment.app.onepixpay.OnePixPayService
import ai.friday.billpayment.app.onepixpay.OnePixPayStatus
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
open class ExpireOnePixPayJob(
    val onePixPayService: OnePixPayService,
    val onePixPayRepository: OnePixPayRepository,
) : AbstractJob(
    cron = "5 21 * * *",
) {
    override fun execute() {
        val expiredOnePixPayList =
            onePixPayRepository.findAll(getLocalDate().minusDays(1)).filter {
                it.status in listOf(OnePixPayStatus.CREATED, OnePixPayStatus.REQUESTED)
            }

        expiredOnePixPayList.forEach {
            LOG.info(Markers.append("expiredOnePixPay", it), "ExpireOnePixPayJob")
            onePixPayService.save(it.copy(status = OnePixPayStatus.EXPIRED))
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(ExpireOnePixPayJob::class.java)
    }
}