package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.isLegalPerson
import ai.friday.billpayment.app.balance.Balance
import ai.friday.billpayment.app.balance.BalanceService
import ai.friday.billpayment.app.banking.InternalBankService
import ai.friday.billpayment.app.integrations.BillService
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.ScheduledBillRepository
import ai.friday.billpayment.app.integrations.ScheduledWalletRepository
import ai.friday.billpayment.app.job.AbstractJob
import ai.friday.billpayment.app.payment.ScheduledBill
import ai.friday.billpayment.app.payment.sumAmount
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton
import java.util.concurrent.atomic.AtomicInteger
import kotlinx.coroutines.runBlocking
import net.logstash.logback.marker.Markers
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import parallelMap

@Singleton
@Requires(beans = [NotifyInsufficientBalanceTodayService::class])
open class NotifyInsufficientBalanceTodayMorningJob(
    private val service: NotifyInsufficientBalanceTodayService,
) : AbstractJob(cron = "20 11 * * *") {
    override fun execute() {
        service.execute(true)
    }
}

@Singleton
@Requires(beans = [NotifyInsufficientBalanceTodayService::class])
open class NotifyInsufficientBalanceTodayAfternoonJob(
    private val service: NotifyInsufficientBalanceTodayService,
) : AbstractJob(cron = "0 15,19 * * *") {
    override fun execute() {
        service.execute(false)
    }
}

@Singleton
open class NotifyInsufficientBalanceTodayService(
    private val notificationAdapter: NotificationAdapter,
    private val scheduledBillRepository: ScheduledBillRepository,
    private val scheduledWalletRepository: ScheduledWalletRepository,
    private val internalBankService: InternalBankService,
    private val balanceService: BalanceService,
    private val walletService: WalletService,
    private val billService: BillService,
    private val accountService: AccountService,
) {
    fun execute(shouldNotifyIfOnlyHasSubscription: Boolean) {
        LOG.info("NotifyInsufficientBalanceTodayJob#started")

        val wallets = scheduledWalletRepository.findAllWalletsWithScheduledBills()

        val count = AtomicInteger(0)

        wallets.buffer(1_000)
            .doOnError {
                LOG.error("NotifyInsufficientBalanceTodayJob#error", it)
            }
            .doOnNext {
                runBlocking {
                    it.chunked(10)
                        .parallelMap {
                            count.addAndGet(it.size)
                            it.forEach { walletId ->
                                doProcess(walletId, shouldNotifyIfOnlyHasSubscription)
                            }
                        }
                }
            }.blockLast()

        LOG.info(append("count", count.get()), "NotifyInsufficientBalanceTodayJob#ended")
    }

    private fun doProcess(
        walletId: WalletId,
        shouldNotifyIfOnlyHasSubscription: Boolean,
    ) {
        val logName = "NotifyInsufficientBalanceTodayJob#doProcess"
        val markers = Markers.append("walletId", walletId)
        try {
            val wallet = walletService.findWallet(walletId)
            val founderAccount = accountService.findAccountById(wallet.founder.accountId)
            markers.andAppend("founderAccountId", founderAccount.accountId.value)
            if (accountService.getChatbotType(founderAccount).checkHasAIChatbotEnabled()) {
                markers.andAppend("aiChatbotEnabled", true)
                LOG.info(markers, logName)
                return
            }
            markers.andAppend("aiChatbotEnabled", false)

            val scheduledBills =
                scheduledBillRepository.findScheduledBillsByWalletIdAndUntilScheduledDate(
                    walletId,
                    getZonedDateTime().toLocalDate(),
                )

            if (scheduledBills.hasAtLeastOneNonOverdueSubscriptionBill()) {
                val balance =
                    balanceService.getBalanceFrom(
                        wallet.founder.accountId,
                        wallet.paymentMethodId,
                    )

                val scheduledAmountToday = scheduledBills.sumAmount()
                if (balance.amount < scheduledAmountToday) {
                    val now = getLocalDate()
                    val nowPlus7Days = now.plusDays(7)
                    val nowPlus15Days = now.plusDays(15)

                    val (amountTotal7Days, amountTotal15Days) =
                        billService.sumAmountUntilEachDate(
                            walletId,
                            listOf(nowPlus7Days, nowPlus15Days),
                        )

                    if (scheduledBills.hasOnlySubscription()) {
                        if (shouldNotifyIfOnlyHasSubscription) {
                            scheduledBills.firstOrNull {
                                it.scheduledDate == now
                            }?.let { subscriptionDueToday ->
                                notificationAdapter.notifySubscriptionInsufficientBalance(
                                    members =
                                    wallet.getMembersCanViewAll()
                                        .filter { member -> shouldNotifyCurrentMember(member) },
                                    walletId = walletId,
                                    walletName = wallet.name,
                                    amount = subscriptionDueToday.amount,
                                )
                            }
                        }
                    } else {
                        if (founderAccount.isLegalPerson()) {
                            // aqui o certo é termos um registro de quem são os usuários que vão receber as notificações da carteira PJ.
                            val originalFounder =
                                wallet.getMembersCanViewAll().firstOrNull {
                                    it.type == MemberType.COFOUNDER && it.emailAddress.value == founderAccount.emailAddress.value
                                } ?: return

                            val pjFounder = accountService.findAccountById(originalFounder.accountId)
                            if (accountService.getChatbotType(pjFounder).checkHasAIChatbotEnabled()) {
                                markers.andAppend("pjFounderAccountId", pjFounder.accountId.value)
                                LOG.info(markers, logName)
                                notificationAdapter.notifyInsufficientBalanceTodaySecondaryWallet(
                                    members = listOf(originalFounder),
                                    walletId = walletId,
                                    walletName = wallet.name,
                                    pendingScheduledAmountToday = scheduledAmountToday - balance.amount,
                                )
                            } else {
                                notifyInsufficientBalanceToday(wallet, scheduledAmountToday, balance, amountTotal7Days, amountTotal15Days)
                            }
                        } else {
                            notifyInsufficientBalanceToday(wallet, scheduledAmountToday, balance, amountTotal7Days, amountTotal15Days)
                        }
                    }
                }
            }
        } catch (e: Exception) {
            LOG.error(append("walletId", walletId), "NotifyInsufficientBalanceTodayJob", e)
        }
    }

    private fun notifyInsufficientBalanceToday(
        wallet: Wallet,
        scheduledAmountToday: Long,
        balance: Balance,
        amountTotal7Days: Long,
        amountTotal15Days: Long,
    ) {
        notificationAdapter.notifyInsufficientBalanceToday(
            members =
            wallet.getMembersCanViewAll()
                .filter { member -> shouldNotifyCurrentMember(member) },
            pendingScheduledAmountToday = scheduledAmountToday - balance.amount,
            pendingAmountTotal7Days = amountTotal7Days - balance.amount,
            pendingAmountTotal15Days = amountTotal15Days - balance.amount,
            walletId = wallet.id,
            walletName = wallet.name,
            isAfterHours = internalBankService.isAfterHour(getZonedDateTime()),
        )
    }

    private fun shouldNotifyCurrentMember(member: Member): Boolean {
        return member.permissions.viewBalance
    }

    private fun List<ScheduledBill>.hasOnlySubscription() = this.all { !it.expires }

    private fun List<ScheduledBill>.hasAtLeastOneNonOverdueSubscriptionBill() = this.any { !it.isSubscriptionOverdue() }

    private fun ScheduledBill.isSubscriptionOverdue() = !expires && scheduledDate.isBefore(getLocalDate())

    companion object {
        private val LOG = LoggerFactory.getLogger(NotifyInsufficientBalanceTodayService::class.java)
    }
}