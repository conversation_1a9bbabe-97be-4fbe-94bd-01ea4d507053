package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.app.job.AbstractJob
import ai.friday.billpayment.app.utilityaccount.UtilityAccountConnectionStatus
import ai.friday.billpayment.app.utilityaccount.UtilityAccountService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.dateFormat
import ai.friday.morning.log.andAppend
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
open class UtilityAccountLastBillFoundJob(
    private val service: UtilityAccountService,
) : AbstractJob(cron = "40 6 * * *", lockAtMostFor = "30m") {

    override fun execute() {
        val logName = "UtilityAccountLastBillFoundJob#execute"
        try {
            logger.info(Markers.append("action", "start"), logName)
            process()
            logger.info(Markers.append("action", "finish"), logName)
        } catch (ex: Exception) {
            logger.error(logName, ex)
        }
    }

    private fun process() {
        val daysToNotify = 25
        val filtered = service.findAllByStatus(UtilityAccountConnectionStatus.CONNECTED).filter { utilityAccount ->
            val logName = "UtilityAccountLastBillFoundJob#process"
            try {
                val daysSinceLastDueDateFound = utilityAccount.daysSinceLastDueDateFound()
                logger.info(
                    Markers
                        .append("utilityAccountId", utilityAccount.id.value)
                        .andAppend("utility", utilityAccount.utility.name.lowercase())
                        .andAppend("walletId", utilityAccount.walletId.value)
                        .andAppend("connectionMethod", utilityAccount.connectionMethod.name)
                        .andAppend("lastDueDateFound", utilityAccount.lastDueDateFound?.let { dateFormat.format(it) })
                        .andAppend("daysSinceLastDueDateFound", daysSinceLastDueDateFound)
                        .andAppend("createdAt", dateFormat.format(utilityAccount.createdAt.toLocalDate()))
                        .andAppend("scanFailureCount", utilityAccount.scanFailureCount),
                    logName,
                )
                daysSinceLastDueDateFound > daysToNotify
            } catch (ex: Exception) {
                logger.error(logName, ex)
                true
            }
        }
        if (filtered.isNotEmpty()) {
            service.notifyInternal(
                utilityAccountList = filtered.sortedByDescending { it.daysSinceLastDueDateFound() },
                "RESUMO - Usuários com contas vencidas a mais de $daysToNotify dias - ${getLocalDate()}",
            )
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(UtilityAccountLastBillFoundJob::class.java)
    }
}