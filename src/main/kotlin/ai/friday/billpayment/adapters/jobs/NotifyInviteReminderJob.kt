package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.job.AbstractJob
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import java.time.Duration
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
open class NotifyInviteReminderJob(
    private val commCentreAdapter: NotificationAdapter,
    private val walletDbRepository: WalletDbRepository,
    private val accountRepository: AccountRepository,
    @Property(name = "schedules.notifyInviteReminder.timeToRemind") private val timeToRemind: Duration,
    @Property(name = "schedules.notifyInviteReminder.cron") cron: String,
) : AbstractJob(
    cron = cron,
    lockAtLeastFor = "10m",
) {
    override fun execute() {
        walletDbRepository.findPendingInvites()
            .filter { getZonedDateTime().isAfter(it.created.plus(timeToRemind)) }
            .also {
                LOG.info(
                    Markers.append("numberOfInvites", it.size).andAppend("invites", it),
                    "NotifyInviteReminderJob#execute",
                )
            }
            .forEach {
                if (!walletDbRepository.isInviteReminderSent(it)) {
                    val account: Account? = accountRepository.findAccountByDocumentOrNull(it.memberDocument)

                    LOG.info(
                        Markers.append("account", account).andAppend("invite", it),
                        "NotifyInviteReminderJob#execute",
                    )
                    commCentreAdapter.notifyInviteReminder(it, account)
                    walletDbRepository.markInviteReminderAsSent(it)
                }
            }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(NotifyInviteReminderJob::class.java)
    }
}