package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.app.job.AbstractJob
import ai.friday.billpayment.app.utilityaccount.UtilityAccountService
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
open class UtilityAccountPendingConnectionJob(
    private val service: UtilityAccountService,
) : AbstractJob(cron = "0 * * * *", lockAtLeastFor = "30m") {

    override fun execute() {
        logger.info(Markers.append("action", "start"), "UtilityAccountPendingConnectionJob#execute")
        process()
        logger.info(Markers.append("action", "finish"), "UtilityAccountPendingConnectionJob#execute")
    }

    private fun process() {
        try {
            service.getPendingConnectionsForMoreThanOneDay().map {
                logger.info(Markers.append("pendingConnection", it), "UtilityAccountPendingConnectionJob#process")
            }
        } catch (ex: Exception) {
            logger.error("UtilityAccountPendingConnectionJob#process", ex)
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(UtilityAccountPendingConnectionJob::class.java)
    }
}