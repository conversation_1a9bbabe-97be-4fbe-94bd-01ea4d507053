package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.adapters.arbi.ArbiAdapterException
import ai.friday.billpayment.adapters.messaging.SynchronizeBankAccountMessage
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.banking.InternalBankService
import ai.friday.billpayment.app.banking.OmnibusBankAccountConfiguration
import ai.friday.billpayment.app.banking.SynchronizeBankAccountException
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.job.AbstractJob
import ai.friday.billpayment.app.message.QueueMessageBatch
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requirements
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton
import kotlin.random.Random
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Singleton
@Requirements(Requires(beans = [SynchronizeBankAccountService::class]))
open class SynchronizePhysicalBankAccountJob(private val service: SynchronizeBankAccountService) :
    AbstractJob(
        cron = "55 13,21 * * *",
    ) {
    override fun execute() {
        service.executePhysical()
    }
}

@Singleton
@Requirements(Requires(beans = [SynchronizeBankAccountService::class]))
open class SynchronizeOmnibusBankAccountJob(private val service: SynchronizeBankAccountService) :
    AbstractJob(
        cron = "50 * * * *",
    ) {
    override fun execute() {
        service.executeOmnibus()
    }
}

@Singleton
open class SynchronizeBankAccountService(
    private val accountRepository: AccountRepository,
    private val internalBankService: InternalBankService,
    private val omnibusBankAccountConfiguration: OmnibusBankAccountConfiguration,
    private val messagePublisher: MessagePublisher,
    @Property(name = "sqs.queues.synchronizeBankAccount") private val queueName: String,
) {
    open fun executePhysical() {
        val balanceList = accountRepository.findAllPhysicalBalanceAccount()
        val paymentMethodList =
            balanceList.filter { x -> x.status == AccountPaymentMethodStatus.ACTIVE }.map { paymentMethod ->
                val synchronizeBankAccountMessage = SynchronizeBankAccountMessage(
                    accountId = paymentMethod.accountId,
                    paymentMethodId = paymentMethod.id,
                )
                getObjectMapper().writeValueAsString(synchronizeBankAccountMessage)
            }

        messagePublisher.sendMessageBatch(
            messageBatch = QueueMessageBatch(
                queueName = queueName,
                messages = paymentMethodList,
                delaySeconds = Random.nextInt(0, 600),
            ),
        )
    }

    fun synchronizePaymentMethod(paymentMethodId: AccountPaymentMethodId, accountId: AccountId) {
        val paymentMethod = accountRepository.findAccountPaymentMethodByIdAndAccountId(
            accountPaymentMethodId = paymentMethodId,
            accountId = accountId,
        )
        val bankAccount = paymentMethod.method as InternalBankAccount
        try {
            internalBankService.synchronizeBankAccount(
                accountId = paymentMethod.accountId,
                accountPaymentMethodId = paymentMethod.id,
                accountNumber = bankAccount.accountNumber,
                document = bankAccount.document,
                withPixFallBack = false,
            )
        } catch (e: SynchronizeBankAccountException) {
            if (e.cause is ArbiAdapterException) {
                LOG.warn(append("BankAccount", bankAccount), "SynchronizeBankAccountJob", e.cause)
            } else {
                LOG.error(append("BankAccount", bankAccount), "SynchronizeBankAccountJob", e.cause)
            }
        }
    }

    open fun executeOmnibus() {
        try {
            internalBankService.synchronizeOmnibusBankAccount()
        } catch (e: SynchronizeBankAccountException) {
            LOG.error(
                append("OmnibusBankAccount", omnibusBankAccountConfiguration),
                "SynchronizeBankAccountJob",
                e.cause,
            )
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(SynchronizeBankAccountService::class.java)
    }
}