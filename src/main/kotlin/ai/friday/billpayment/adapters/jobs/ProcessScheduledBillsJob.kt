package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.app.job.AbstractJob
import ai.friday.billpayment.app.payment.StartProcessScheduledBillsService
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton

@Singleton
open class ProcessExpirableScheduledBillsJob(
    private val startProcessScheduledBillsService: StartProcessScheduledBillsService,
    @Property(name = "schedules.startProcessScheduledBills.cron") cron: String,
) : AbstractJob(cron = cron) {
    override fun execute() {
        startProcessScheduledBillsService.start(includeSubscription = false)
    }
}

@Singleton
open class ProcessAllScheduledBillsJob(
    private val startProcessScheduledBillsService: StartProcessScheduledBillsService,
) : AbstractJob(
    crons = listOf("50 10 * * *", "10 02 * * *"),
) {
    override fun execute() {
        startProcessScheduledBillsService.start(includeSubscription = true)
    }
}