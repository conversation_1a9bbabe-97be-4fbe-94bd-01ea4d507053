package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.PartialAccount
import ai.friday.billpayment.app.account.RegisterService
import ai.friday.billpayment.app.account.UpgradeStatus
import ai.friday.billpayment.app.account.getCPF
import ai.friday.billpayment.app.account.getName
import ai.friday.billpayment.app.bill.deltaInSeconds
import ai.friday.billpayment.app.feature.FeaturesRepository
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.ExternalAccountRegister
import ai.friday.billpayment.app.job.AbstractJob
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import jakarta.inject.Singleton
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Singleton
open class QueryExternalRegisterStatusJob(
    private val accountRepository: AccountRepository,
    private val accountRegisterRepository: AccountRegisterRepository,
    private val externalAccountRegister: ExternalAccountRegister,
    private val registerService: RegisterService,
    private val featuresRepository: FeaturesRepository,
) : AbstractJob(
    cron = "0/30 12-22 * * *",
    lockAtLeastFor = "10m",
) {
    override fun execute() {
        if (featuresRepository.getAll().maintenanceMode) {
            LOG.info(
                append("jobExcecutionSkipped", "maintenanceMode"),
                "QueryExternalRegisterStatusJob",
            )
            return
        }

        val partialAccountList = accountRepository.findPartialAccountByStatus(AccountStatus.UNDER_EXTERNAL_REVIEW)

        LOG.info(
            append("size", partialAccountList.size),
            "QueryExternalRegisterStatusJob#partialAccount",
        )

        partialAccountList.forEach { partialAccount ->
            processAccount(partialAccount.id, true)
        }

        val upgradeAccountList = accountRepository.findAccountByUpgradeStatus(UpgradeStatus.UNDER_EXTERNAL_REVIEW)

        LOG.info(
            append("size", upgradeAccountList.size),
            "QueryExternalRegisterStatusJob#upgradeAccount",
        )

        upgradeAccountList.forEach { upgradeAccount ->
            processAccount(upgradeAccount.accountId, false)
        }
    }

    private fun processAccount(accountId: AccountId, partialAccount: Boolean) {
        val markers: LogstashMarker =
            append("accountId", accountId.value)
                .andAppend("partialAccount", partialAccount)

        try {
            val accountRegister = accountRegisterRepository.findByAccountId(accountId)
            val document = accountRegister.getCPF()!!

            val externalStatus =
                externalAccountRegister.queryExternalRegisterStatus(
                    accountRegister.getName(),
                    document,
                )
            markers.andAppend("document", document)
                .andAppend("externalStatus", externalStatus)

            externalStatus?.let { status ->
                val updateAccountStatus = registerService.updateAccountStatus(document, status).getOrElse {
                    throw it
                }
                markers.andAppend("result", updateAccountStatus)
            }

            LOG.info(markers, "QueryExternalRegisterStatusJob#processAccount")
        } catch (e: Exception) {
            LOG.error(markers, "QueryExternalRegisterStatusJob#processAccount", e)
        }
    }

    private fun PartialAccount.secondsSinceLastUpdate(): Long {
        if (this.statusUpdated == null) {
            return 0L
        }

        return getZonedDateTime().deltaInSeconds(this.statusUpdated)
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(QueryExternalRegisterStatusJob::class.java)
    }
}