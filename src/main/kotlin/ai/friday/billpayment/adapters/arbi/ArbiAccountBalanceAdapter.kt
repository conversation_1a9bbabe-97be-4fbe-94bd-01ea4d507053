package ai.friday.billpayment.adapters.arbi

import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.integrations.AccountBalanceAdapter
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MediaType
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Singleton
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
class ArbiAccountBalanceAdapter(
    @param:Client(
        id = "arbi",
    ) private val httpClient: RxHttpClient,
    private val configuration: ArbiConfiguration,
    private val authenticationManager: NewArbiAuthenticationManager,
) : AccountBalanceAdapter {
    override fun getBalance(
        accountNo: String,
        document: Document,
        logBody: Boolean,
    ): String {
        val logName = "ArbiBalanceFull"

        val token = authenticationManager.getToken()
        val request =
            CheckingRequestTO(
                contaCorrente =
                ContaCorrenteTO(
                    inscricaoParceiro = configuration.inscricao,
                    tokenUsuario = configuration.userToken,
                    idModulo = "1",
                    idTransacao = "15",
                    dataAgendamento = getZonedDateTime().format(DateTimeFormatter.ISO_LOCAL_DATE),
                    contaOrigem = accountNo,
                ),
            )
        val marker = Markers.append("request", request)
        val httpRequest =
            HttpRequest
                .POST(configuration.checkingV2Path, request)
                .header("client_id", configuration.clientId)
                .header("access_token", token)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON_TYPE)
        val call =
            httpClient.retrieve(
                httpRequest,
                Argument.STRING,
                Argument.STRING,
            )
        try {
            val response = call.firstOrError().blockingGet()
            marker.andAppend("response", response)

            logger.info(marker, logName)

            return response
        } catch (e: HttpClientResponseException) {
            checkUnauthorized(e)

            val responseBody = e.response.getBody(String::class.java).get()

            val invalidAccount =
                e.status == HttpStatus.UNPROCESSABLE_ENTITY &&
                    responseBody
                        .contains("Conta origem não cadastrado e/ou invalido.")

            val accountNotAllowed =
                e.status == HttpStatus.FORBIDDEN &&
                    responseBody
                        .contains("Usuario/Modulo/transacao/Conta sem permissão.")

            val response = e.response.getBody(String::class.java).get()
            if (invalidAccount) {
                logger.warn(
                    marker.andAppend("response", response),
                    logName,
                    e,
                )
                throw ArbiInvalidAccountException()
            } else if (accountNotAllowed) {
                logger.warn(
                    marker.andAppend("response", response),
                    logName,
                    e,
                )
                throw ArbiAccountMissingPermissionsException()
            } else {
                logger.error(
                    marker.andAppend("response", response),
                    logName,
                    e,
                )
                throw ArbiAdapterException()
            }
        } catch (e: Exception) {
            logger.error(marker, logName, e)
            throw ArbiAdapterException()
        }
    }

    private fun checkUnauthorized(e: HttpClientResponseException) {
        if (e.status == HttpStatus.UNAUTHORIZED) {
            logger.warn(Markers.append("error_message", "Token is expired"), "ArbiAccountStatementAdapter")
            authenticationManager.cleanTokens()
            throw ArbiLoginException()
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(ArbiAccountStatementAdapter::class.java)
    }
}