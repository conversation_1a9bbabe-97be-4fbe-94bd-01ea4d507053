package ai.friday.billpayment.adapters.arbi

import ai.friday.billpayment.ByteWrapper
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.and
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.account.AccountRegisterDocument
import ai.friday.billpayment.app.account.ExternalRegisterStatus
import ai.friday.billpayment.app.account.NotifyUpgradeStatusRequest
import ai.friday.billpayment.app.account.NotifyUpgradeStatusResponse
import ai.friday.billpayment.app.account.SendAccountRegisterDocumentsRequest
import ai.friday.billpayment.app.account.SendDocumentsResponse
import ai.friday.billpayment.app.account.SendSimpleSignUpDocumentsRequest
import ai.friday.billpayment.app.account.SendUpgradeDocumentsRequest
import ai.friday.billpayment.app.integrations.ECMProvider
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.log.andAppend
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.nimbusds.jwt.JWTParser
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MediaType
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.uri.UriBuilder
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Singleton
import java.time.Instant
import java.time.LocalDateTime
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

const val AUTHENTICATION_HEADER_NAME = "FEPWEB-JWT-TOKEN"

@ConfigurationProperties("integrations.arbi.fepweb")
class ArbiECMFepWebAdapterConfiguration
@ConfigurationInject
constructor(
    val host: String,
    val username: String,
    val password: String,
    val authorizePath: String,
    val userSearchPath: String,
    val userSavePath: String,
    val formSavePath: String,
    val fillFormPath: String,
    val documentSavePath: String,
    val sendToApprovalPath: String,
    val formTemplateExtCode: String,
    val productExtCode: String,
    val businessUnitExtCode: String,
)

@Singleton
class ArbiECMAFepWebTokenManager(
    private val configuration: ArbiECMFepWebAdapterConfiguration,
    @param:Client(
        value = "\${integrations.arbi.fepweb.host}",
    ) private val httpClient: RxHttpClient,
) {

    private var jwtToken: String? = null
    private var expiresIn: LocalDateTime? = null
    private val expireOffset = 1L

    fun getToken(): String {
        if (isExpired()) {
            jwtToken = getAccessToken()
            val exp = JWTParser.parse(jwtToken).jwtClaimsSet.expirationTime
            expiresIn = if (exp != null) {
                LocalDateTime.ofInstant(Instant.ofEpochMilli(exp.time), brazilTimeZone)
            } else {
                null
            }
        }
        return jwtToken!!
    }

    fun expireToken() {
        expiresIn = null
        jwtToken = null
    }

    private fun isExpired(): Boolean {
        return expiresIn?.minusMinutes(expireOffset)
            ?.isBefore(getZonedDateTime().toLocalDateTime())
            ?: true
    }

    private fun getAccessToken(): String {
        val httpRequest = HttpRequest.POST(
            configuration.authorizePath,
            FepWebLoginRequestTO(
                username = configuration.username,
                password = configuration.password,
            ),
        )
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)
        val call = httpClient.exchange(
            httpRequest,
            Argument.STRING,
            Argument.STRING,
        )
        try {
            val response = call.firstOrError().blockingGet()
            val token = response.headers[AUTHENTICATION_HEADER_NAME]
            if (token == null) {
                logger.error(
                    Markers.append("error", "Unable to find FepWeb API token"),
                    "ArbiECMAFepWebTokenManager",
                )
                throw ArbiLoginException()
            }
            return token
        } catch (e: HttpClientResponseException) {
            logger.error(
                Markers.append("error", "Error authorizing on FepWeb API")
                    .andAppend("status", e.status),
                "ArbiECMAFepWebTokenManager",
                e,
            )
            throw ArbiLoginException()
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(ArbiECMAFepWebTokenManager::class.java)
    }
}

@Singleton
class ArbiECMFepWebAdapter(
    private val tokenManager: ArbiECMAFepWebTokenManager,
    private val configuration: ArbiECMFepWebAdapterConfiguration,
    @param:Client(
        value = "\${integrations.arbi.fepweb.host}",
    ) private val httpClient: RxHttpClient,
) : ECMProvider {
    override fun sendAccountRegisterDocuments(request: SendAccountRegisterDocumentsRequest): SendDocumentsResponse {
        val logName = "ArbiECMFepWebAdapter#sendAccountRegisterDocuments"
        val markers =
            Markers.append("document", request.document).and("email" to request.emailAddress, "accountId" to request.id)

        try {
            val fepWebUserId = callSaveUser(SaveUserDocumentData(request))
            val fepWebFormId = callSaveForm(fepWebUserId)
            callFillForm(fepWebFormId)
            callSaveDocument(fepWebFormId, request.document, request.userContract, FepWebDocumentType.FICHA)
            callSaveDocument(fepWebFormId, request.document, request.userKYC!!, FepWebDocumentType.KYC)
            callSaveDocument(fepWebFormId, request.document, request.userSelfie, FepWebDocumentType.SELFIE)
            callSaveDocument(fepWebFormId, request.document, request.declarationOfResidency, FepWebDocumentType.DECLARATION_OF_RESIDENCY_FULL)
            request.userDocument.forEach {
                callSaveDocument(fepWebFormId, request.document, it, FepWebDocumentType.ID_DOCUMENT_FULL)
            }
            callSendToApproval(fepWebFormId, actionType = FepWebApprovalType.PENDING_FULL)

            logger.info(markers.and("fepWebUserId" to fepWebUserId), logName)

            return SendDocumentsResponse(
                success = true,
                status = "",
            )
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return SendDocumentsResponse(
                success = false,
                status = e.message.orEmpty(),
            )
        }
    }

    override fun sendSimpleSignUpDocuments(request: SendSimpleSignUpDocumentsRequest): SendDocumentsResponse {
        val markers =
            Markers.append("document", request.document).and("email" to request.emailAddress, "accountId" to request.id)

        try {
            val fepWebUserId = callSaveUser(SaveUserDocumentData(request))
            val fepWebFormId = callSaveForm(fepWebUserId)
            callSaveDocument(fepWebFormId, request.document, request.userContract, FepWebDocumentType.FICHA)
            callSaveDocument(fepWebFormId, request.document, request.userKYC!!, FepWebDocumentType.KYC)
            callSaveDocument(fepWebFormId, request.document, request.userSelfie, FepWebDocumentType.SELFIE)
            callSendToApproval(fepWebFormId)

            logger.info(markers.and("fepWebUserId" to fepWebUserId), "ArbiECMFepWebAdapter#sendSimpleSignUpDocuments")

            return SendDocumentsResponse(
                success = true,
                status = "",
            )
        } catch (e: Exception) {
            logger.error(markers, "ArbiECMFepWebAdapter#sendSimpleSignUpDocuments", e)
            return SendDocumentsResponse(
                success = false,
                status = e.message.orEmpty(),
            )
        }
    }

    override fun sendUpgradeDocuments(request: SendUpgradeDocumentsRequest): SendDocumentsResponse {
        val markers =
            Markers.append("document", request.document).and("email" to request.emailAddress, "accountId" to request.id)

        try {
            val fepWebUserId = callSaveUser(SaveUserDocumentData(request))
            val fepWebFormId = callSaveForm(fepWebUserId)
            callSaveDocument(fepWebFormId, request.document, request.userContract, FepWebDocumentType.FICHA)
            callSaveDocument(fepWebFormId, request.document, request.declarationOfResidency, FepWebDocumentType.DECLARATION_OF_RESIDENCY)
            request.userDocument.forEach {
                callSaveDocument(fepWebFormId, request.document, it, FepWebDocumentType.ID_DOCUMENT)
            }

            callSendToApproval(fepWebFormId)

            logger.info(markers.and("fepWebUserId" to fepWebUserId), "ArbiECMFepWebAdapter#sendUpgradeRegisterDocuments")

            return SendDocumentsResponse(
                success = true,
                status = "",
            )
        } catch (e: Exception) {
            logger.error(markers, "ArbiECMFepWebAdapter#sendUpgradeRegisterDocuments", e)
            return SendDocumentsResponse(
                success = false,
                status = e.message.orEmpty(),
            )
        }
    }

    override fun notifyUpgradeStatus(request: NotifyUpgradeStatusRequest): NotifyUpgradeStatusResponse {
        val markers = Markers.append("document", request.document).andAppend("status", request.status)

        return try {
            val fepWebFormId = getUpgradeFormId(request.document)
            callSendToApproval(
                fepWebFormId,
                actionType = if (request.status == ExternalRegisterStatus.APPROVED) FepWebApprovalType.APPROVED else FepWebApprovalType.REJECTED,
            )

            NotifyUpgradeStatusResponse(
                success = true,
                status = "",
            )
        } catch (e: Exception) {
            logger.error(markers, "ArbiECMFepWebAdapter#notifyUpgradeStatus", e)
            NotifyUpgradeStatusResponse(
                success = false,
                status = e.message.orEmpty(),
            )
        }
    }

    private fun getUpgradeFormId(document: String): FepWebFormId {
        val markers = Markers.append("document", document)

        val response = callSearchUser(document)
        logger.info(markers.and("response" to response), "ArbiECMFepWebAdapter#getFebWebUserId")

        val forms = response.formParameters.filter { it.template.extCode == configuration.formTemplateExtCode }

        if (forms.isEmpty()) {
            throw Exception("No form found for document $document")
        }

        if (forms.size != 1) {
            logger.warn(
                markers.andAppend(
                    "reason",
                    "More than one form found for document $document. Forms: ${forms.joinToString { it.form.formId.toString() }}",
                ).andAppend("ACTION", "VERIFY"),
                "ArbiECMFepWebAdapter#getFebWebUserId",
            )
        }

        return FepWebFormId(forms.first().form.formId.toString())
    }

    private fun getFebWebUserId(document: String): FepWebUserId {
        val markers = Markers.append("document", document)

        val response = callSearchUser(document)

        logger.info(markers.and("idFilrUsr" to response.idFilrUsr), "ArbiECMFepWebAdapter#getFebWebUserId")

        return FepWebUserId(response.idFilrUsr)
    }

    private fun callSearchUser(document: String): FepWebUserResponseTO {
        val token = tokenManager.getToken()
        val markers = Markers.append("document", document)

        val httpRequest = HttpRequest.POST(
            configuration.userSearchPath,
            FepWebUserSearchRequestTO(
                ni = document,
            ),
        )
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)
            .header(AUTHENTICATION_HEADER_NAME, token)
        val call = httpClient.retrieve(
            httpRequest,
            Argument.of(FepWebUserResponseTO::class.java),
            Argument.STRING,
        )
        try {
            val response = call.firstOrError().blockingGet()
            logger.info(markers.and("response" to response), "ArbiECMFepWebAdapter#callSearchUser")
            return response
        } catch (e: HttpClientResponseException) {
            checkUnauthorized(e)
            logger.error(
                markers.and("error" to "Error searching FepWeb user", "status" to e.status),
                "ArbiECMFepWebAdapter#callSearchUser",
                e,
            )
            throw ArbiAdapterException()
        }
    }

    private fun callSaveUser(documentsData: SaveUserDocumentData): FepWebUserId {
        val token = tokenManager.getToken()
        val markers = Markers.append("document", documentsData.document)
            .and("email" to documentsData.emailAddress, "accountId" to documentsData.id)

        val httpRequest = HttpRequest.POST(
            configuration.userSavePath,
            FepWebSaveUserRequestTO(
                userFillerDTO = FepWebUserFillerDTO(
                    usrNm = documentsData.name,
                    usrLogn = documentsData.document,
                    email = documentsData.emailAddress.value,
                    cpfCnpjNif = documentsData.document,
                    businessUnitDTO = mapOf("ext_code" to configuration.businessUnitExtCode),
                ),
            ),
        )
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)
            .header(AUTHENTICATION_HEADER_NAME, token)
        val call = httpClient.retrieve(
            httpRequest,
            Argument.of(FepWebUserResponseTO::class.java),
            Argument.STRING,
        )
        try {
            val response = call.firstOrError().blockingGet()
            logger.info(markers.and("idFilrUsr" to response.idFilrUsr), "ArbiECMFepWebAdapter#callSaveUser")
            return FepWebUserId(response.idFilrUsr)
        } catch (e: HttpClientResponseException) {
            checkUnauthorized(e)
            if (e.status == HttpStatus.INTERNAL_SERVER_ERROR && e.message?.contains("Prospect with the same NI already exist") == true) {
                return getFebWebUserId(documentsData.document)
            }
            logger.error(
                markers.and("error" to "Error saving FepWeb user", "status" to e.status),
                "ArbiECMFepWebAdapter#callSaveUser",
                e,
            )
            throw ArbiAdapterException()
        }
    }

    private fun callSaveForm(userId: FepWebUserId): FepWebFormId {
        val token = tokenManager.getToken()
        val markers = Markers.append("userId", userId)

        val fepWebSaveFormRequestTO = FepWebSaveFormRequestTO(
            userFillerId = userId.value,
            form = FepWebFormRequestInternalTO(
                productList = listOf(FormExtCodeTO(extCode = configuration.productExtCode)),
                formParameters = FormParametersRequestTO(
                    template = FormExtCodeTO(extCode = configuration.formTemplateExtCode),
                    workflow = FormExtCodeTO(extCode = configuration.formTemplateExtCode),
                ),
            ),
        )

        val httpRequest = HttpRequest.POST(
            configuration.formSavePath,
            fepWebSaveFormRequestTO,
        )
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)
            .header(AUTHENTICATION_HEADER_NAME, token)
        markers.andAppend("request", getObjectMapper().writeValueAsString(fepWebSaveFormRequestTO))
        val call = httpClient.retrieve(
            httpRequest,
            Argument.of(FepWebFormResponseTO::class.java),
            Argument.STRING,
        )
        try {
            val response = call.firstOrError().blockingGet()
            logger.info(markers.and("formId" to response.formId), "ArbiECMFepWebAdapter#callSaveForm")
            return FepWebFormId(response.formId)
        } catch (e: HttpClientResponseException) {
            checkUnauthorized(e)
            logger.error(
                markers.and("error" to "Error saving FepWeb Form", "status" to e.status),
                "ArbiECMFepWebAdapter#callSaveForm",
                e,
            )
            throw ArbiAdapterException()
        }
    }

    private fun callFillForm(formId: FepWebFormId) {
        val token = tokenManager.getToken()
        val markers = Markers.append("formId", formId)

        val fepWebFillFormRequestTO = FeWebFillFormRequestTO(
            formId = formId.value,
            values = listOf(
                mapOf("integrationId" to "conta_corrente_completa", "value" to "true"),
                mapOf("integrationId" to "conta_corrente_simples", "value" to "false"),
            ),
        )

        val httpRequest = HttpRequest.POST(
            configuration.fillFormPath,
            fepWebFillFormRequestTO,
        )
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)
            .header(AUTHENTICATION_HEADER_NAME, token)
        markers.andAppend("request", getObjectMapper().writeValueAsString(fepWebFillFormRequestTO))
        val call = httpClient.exchange(
            httpRequest,
        )
        try {
            call.firstOrError().blockingGet()
            logger.info(markers, "ArbiECMFepWebAdapter#callFillForm")
        } catch (e: HttpClientResponseException) {
            checkUnauthorized(e)
            logger.error(
                markers.and("error" to "Error filling FepWeb Form", "status" to e.status),
                "ArbiECMFepWebAdapter#callFillForm",
                e,
            )
            throw ArbiAdapterException()
        }
    }

    private fun callSaveDocument(
        formId: FepWebFormId,
        fileNamePrefix: String,
        documentData: AccountRegisterDocument,
        documentType: FepWebDocumentType,
    ): FepWebDocumentId {
        val markers = Markers.append("formId", formId).and("documentType" to documentType)

        val token = tokenManager.getToken()
        val uri = UriBuilder.of(configuration.documentSavePath)
            .expand(mutableMapOf("formId" to formId.value))
            .toString()

        val fepWebSaveDocumentRequestTO = FepWebSaveDocumentRequestTO(
            documentType = FormDocTypeTO(documentType.extCode),
            fileNm = "$fileNamePrefix-${documentType.extCode}.${documentData.extension}",
            fileType = documentData.extension,
            dcmntCntnt = ByteWrapper(documentData.content).getBase64(),
        )
        markers.andAppend(
            "request",
            getObjectMapper().writeValueAsString(fepWebSaveDocumentRequestTO.copy(dcmntCntnt = "****")),
        )
        val httpRequest = HttpRequest.POST(
            uri,
            fepWebSaveDocumentRequestTO,
        )
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)
            .header(AUTHENTICATION_HEADER_NAME, token)
        val call = httpClient.retrieve(
            httpRequest,
            Argument.of(FepWebDocumentResponseTO::class.java),
            Argument.STRING,
        )
        try {
            val response = call.firstOrError().blockingGet()
            logger.info(markers.and("idDcmntMgmt" to response.idDcmntMgmt), "ArbiECMFepWebAdapter#callSaveDocument")
            return FepWebDocumentId(response.idDcmntMgmt)
        } catch (e: HttpClientResponseException) {
            checkUnauthorized(e)
            logger.error(
                markers.and("error" to "Error saving FepWeb Document", "status" to e.status),
                "ArbiECMFepWebAdapter#callSaveDocument",
                e,
            )
            throw ArbiAdapterException()
        }
    }

    private fun callSendToApproval(
        formId: FepWebFormId,
        actionType: FepWebApprovalType = FepWebApprovalType.PENDING,
    ) {
        val markers = Markers.append("formId", formId)

        val token = tokenManager.getToken()

        val httpRequest = HttpRequest.POST(configuration.sendToApprovalPath, FepWebSendToApprovalRequestTO(formId, actionType))
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)
            .header(AUTHENTICATION_HEADER_NAME, token)
        val call = httpClient.exchange(
            httpRequest,
            Argument.STRING,
            Argument.STRING,
        )
        try {
            call.firstOrError().blockingGet()
            logger.info(markers, "ArbiECMFepWebAdapter#callSendToApproval")
        } catch (e: HttpClientResponseException) {
            checkUnauthorized(e)
            logger.error(
                markers.and("error" to "Error approving FepWeb user", "status" to e.status),
                "ArbiECMFepWebAdapter#callSendToApproval",
                e,
            )
            throw ArbiAdapterException()
        }
    }

    private fun checkUnauthorized(e: HttpClientResponseException) {
        if (e.status == HttpStatus.UNAUTHORIZED) {
            logger.warn(Markers.append("error_message", "Token is expired"), "ArbiECMFepWebAdapter")
            tokenManager.expireToken()
            throw ArbiLoginException()
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(ArbiECMFepWebAdapter::class.java)
    }
}

private data class FepWebUserId(val value: String)
private data class FepWebFormId(val value: String)
private data class FepWebDocumentId(val value: String)

private data class FepWebLoginRequestTO(
    val username: String,
    val password: String,
) {
    val loginType: String = "U"
    val loginForBatch: Boolean = false
}

private data class FepWebUserSearchRequestTO(
    val personType: String = "F",
    val ni: String,
)

private data class SaveUserDocumentData(
    val id: String,
    val name: String,
    val document: String,
    val emailAddress: EmailAddress,
) {
    constructor(request: SendSimpleSignUpDocumentsRequest) : this(
        id = request.id,
        name = request.name,
        document = request.document,
        emailAddress = request.emailAddress,
    )

    constructor(request: SendUpgradeDocumentsRequest) : this(
        id = request.id,
        name = request.name,
        document = request.document,
        emailAddress = request.emailAddress,
    )
}

private data class FepWebSaveUserRequestTO(
    val userFillerDTO: FepWebUserFillerDTO,
)

@JsonIgnoreProperties(ignoreUnknown = true)
private data class FepWebUserResponseTO(
    val idFilrUsr: String,
    val formParameters: List<FepWebFormParameterTO>,
)

@JsonIgnoreProperties(ignoreUnknown = true)
private data class FepWebFormParameterTO(
    val form: FepWebFormTO,
    val template: FebWebFormTemplateTO,
)

@JsonIgnoreProperties(ignoreUnknown = true)
private data class FepWebFormTO(
    val formId: Number,
)

@JsonIgnoreProperties(ignoreUnknown = true)
private data class FebWebFormTemplateTO(
    val extCode: String,
)

@JsonIgnoreProperties(ignoreUnknown = true)
private data class FepWebFormResponseTO(
    val formId: String,
)

@JsonIgnoreProperties(ignoreUnknown = true)
private data class FepWebDocumentResponseTO(
    val idDcmntMgmt: String,
)

private data class FepWebUserFillerDTO(
    val usrPhonLocaleIso2: String = "br",
    val usrNm: String,
    val usrLogn: String,
    val email: String,
    val cpfCnpjNif: String,
    val dflt_lang: String = "PT",
    val prsnTyp: String = "F",
    val businessUnitDTO: Map<String, String>,
    val rmUser: Map<String, String?> = mapOf("login" to null),
)

private data class FepWebSaveFormRequestTO(
    val userFillerId: String,
    val form: FepWebFormRequestInternalTO,
)

private data class FeWebFillFormRequestTO(
    val formId: String,
    val overwriteExistingValues: Boolean = true,
    val values: List<Map<String, String>>,
)

private data class FepWebFormRequestInternalTO(
    val productList: List<FormExtCodeTO>,
    val formParameters: FormParametersRequestTO,
)

private data class FormExtCodeTO(
    val extCode: String,
)

private data class FormParametersRequestTO(
    val template: FormExtCodeTO,
    val workflow: FormExtCodeTO,
)

private data class FepWebSaveDocumentRequestTO(
    val documentType: FormDocTypeTO,
    val fileNm: String,
    val fileType: String,
    val dcmntCntnt: String,
)

private data class FormDocTypeTO(
    val ext_code: String,
)

private enum class FepWebApprovalType(val value: String) {
    PENDING("env_analise"),
    PENDING_FULL("env_analise_conta_completa"),
    APPROVED("compliance_aprovado"),
    REJECTED("compliance_reprovado"),
}

private data class FepWebSendToApprovalRequestTO(private val formId: FepWebFormId, private val actionType: FepWebApprovalType) {
    val action = FormExtCodeTO(actionType.value)
    val form = mapOf("formId" to formId.value)
}

private enum class FepWebDocumentType(val extCode: String) {
    KYC("dossie_kyc"),
    FICHA("ficha_abertura"),
    SELFIE("selfie"),
    DECLARATION_OF_RESIDENCY("comp_residencia_rep_legal"),
    DECLARATION_OF_RESIDENCY_FULL("comprovante_de_residencia"),
    ID_DOCUMENT("rep_legal_rg_cnh_outro"),
    ID_DOCUMENT_FULL("doc_identity"),
    USE_TERMS("termos_uso_plataforma"),
}