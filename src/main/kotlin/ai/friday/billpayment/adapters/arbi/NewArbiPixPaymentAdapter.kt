package ai.friday.billpayment.adapters.arbi

import ai.friday.billpayment.andAppendMasked
import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.BankOperationId
import ai.friday.billpayment.app.banking.BankStatementItemFlow
import ai.friday.billpayment.app.banking.BankStatementItemType
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.fingerprint.DeviceId
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.EndToEnd
import ai.friday.billpayment.app.integrations.NewPixCommand
import ai.friday.billpayment.app.integrations.PixCommand
import ai.friday.billpayment.app.integrations.PixKeyManagement
import ai.friday.billpayment.app.integrations.PixPaymentService
import ai.friday.billpayment.app.onepixpay.QrCodeTransactionId
import ai.friday.billpayment.app.payment.PixPaymentResult
import ai.friday.billpayment.app.payment.PixPaymentStatus
import ai.friday.billpayment.app.pix.PixKeyDetails
import ai.friday.billpayment.app.pix.PixKeyError
import ai.friday.billpayment.app.pix.PixStatementItem
import ai.friday.billpayment.app.pix.PixTransactionError
import ai.friday.billpayment.app.pix.PixTransactionError.BusinessDailyLimit
import ai.friday.billpayment.app.pix.PixTransactionError.BusinessInsufficientBalance
import ai.friday.billpayment.app.pix.PixTransactionError.BusinessSameValuePaymentsExceeded
import ai.friday.billpayment.app.pix.PixTransactionError.BusinessSingleTransactionLimit
import ai.friday.billpayment.app.pix.PixTransactionError.PaymentGenericTemporaryError
import ai.friday.billpayment.app.pix.PixTransactionError.SettlementDestinationAccountNotAvailable
import ai.friday.billpayment.app.pix.PixTransactionError.SettlementDestinationAccountNotFound
import ai.friday.billpayment.app.pix.PixTransactionError.SettlementDestinationAccountTypeInvalid
import ai.friday.billpayment.app.pix.PixTransactionError.SettlementDestinationInstitutionNotAllowed
import ai.friday.billpayment.app.pix.PixTransactionError.SettlementGenericPermanentError
import ai.friday.billpayment.app.pix.PixTransactionError.SettlementGenericTemporaryError
import ai.friday.billpayment.app.pix.PixTransactionError.SettlementPaymentRefusedByDestination
import ai.friday.billpayment.app.stripEmojis
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.dateFormat
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpMethod
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MediaType
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.uri.UriBuilder
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Singleton
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

private const val ARBI_ROUTING_NO = "0001"

@ConfigurationProperties("integrations.arbi")
class NewArbiPixPaymentConfiguration {
    lateinit var userToken: String
    lateinit var clientId: String
    lateinit var clientSecret: String
    lateinit var codInstituicaoPagador: String
    lateinit var initPaymentV2Path: String
    lateinit var pixStatementV2Path: String
    lateinit var paymentStatusIdIdempotenteV2Path: String
    lateinit var paymentStatusE2EV2Path: String
}

@Singleton
class NewArbiPixPaymentAdapter(
    @param:Client(
        id = "arbi",
    ) private val httpClient: RxHttpClient,
    private val configuration: NewArbiPixPaymentConfiguration,
    private val authenticationManager: NewArbiAuthenticationManager,
    private val pixKeyManagement: PixKeyManagement,
    private val accountRepository: AccountRepository, // FIXME InternalBankAccount deveria ter o nome do titular da conta
) : PixPaymentService {

    override fun transfer(command: NewPixCommand, deviceId: DeviceId?): PixPaymentResult {
        val logName = "ArbiPixPaymentAdapter#transfer"
        val request = when (command) {
            is NewPixCommand.BankAccountCommand -> buildPixPaymentRequestTO(command).copy(
                codInstituicaoBeneficiario = command.recipientBankAccount.ispb!!,
                codAgenciaBeneficiario = command.recipientBankAccount.routingNo.formatRoutingNo(),
                nroContaBeneficiario = command.recipientBankAccount.buildFullAccountNumber().formatFullAccountNo(),
                tipoContaBeneficiario = command.recipientBankAccount.accountType.toTipoConta(),
                cpfCnpjBeneficiario = command.recipientDocument,
                nomeBeneficiario = command.recipientName,
            )

            is NewPixCommand.PixKeyCommand -> buildPixPaymentRequestTO(command).copy(
                endToEnd = command.endToEnd,
                chaveEnderecamento = command.pixKeyDetails.key.value,
                referenciaInterna = command.pixQrCodeId,
            )
        }

        val markers = append("request", request)
        val httpRequest = createHttpRequest<PixPaymentRequestTO>(HttpMethod.POST, configuration.initPaymentV2Path)
            .body(request)

        deviceId?.let {
            markers.andAppendMasked("deviceId", it.value)
            httpRequest.header("x-device-id", it.value)
        }

        val call = httpClient.retrieve(
            httpRequest,
            Argument.listOf(ArbiPixResponseTO::class.java),
            Argument.listOf(ArbiPixResponseTO::class.java),
        )

        try {
            val response = call.firstOrError().blockingGet()
            val mappedResponse = jacksonObjectMapper().readerFor(ArbiPixPaymentResponseTO::class.java)
                .readValue<ArbiPixPaymentResponseTO>(response[0].response)
            logger.info(markers.and(append("response", mappedResponse)), logName)
            return PixPaymentResult(
                pixKeyDetails = null, // TODO: criar um result específico para quando o pix é por dados bancários
                status = parseStatus(mappedResponse.statusOrdemPagamento),
                idOrdemPagamento = mappedResponse.idOrdemPagamento,
                endToEnd = mappedResponse.endToEnd,
            )
        } catch (e: HttpClientResponseException) {
            return handleCheckPaymentStatusHttpError(command.bankOperationId, e, markers)
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return PixPaymentResult(
                status = PixPaymentStatus.UNKNOWN,
                idOrdemPagamento = "",
                endToEnd = "",
                error = PaymentGenericTemporaryError,
            )
        }
    }

    override fun initPayment(command: PixCommand, bankOperationId: BankOperationId, deviceId: DeviceId?): PixPaymentResult {
        var pixKeyDetailsResult: PixKeyDetails? = null

        val request = buildPixPaymentRequestTO(command, bankOperationId.value).run {
            if (command.recipientBankAccount != null) {
                this.copy(
                    codInstituicaoBeneficiario = command.recipientBankAccount.ispb!!,
                    codAgenciaBeneficiario = command.recipientBankAccount.routingNo.formatRoutingNo(),
                    nroContaBeneficiario = command.recipientBankAccount.buildFullAccountNumber().formatFullAccountNo(),
                    tipoContaBeneficiario = command.recipientBankAccount.accountType.toTipoConta(),
                    cpfCnpjBeneficiario = command.recipientDocument,
                    nomeBeneficiario = command.recipientName,
                )
            } else {
                val (pixKeyDetails, e2e) = pixKeyManagement.findKeyDetails(
                    command.recipientPixKey!!,
                    command.originBankAccount.document,
                ).getOrElse { pixKeyError ->
                    return when (pixKeyError) {
                        PixKeyError.UnknownError, PixKeyError.KeyNotConfirmed -> {
                            PixPaymentResult(
                                status = PixPaymentStatus.REFUSED,
                                idOrdemPagamento = "",
                                endToEnd = "",
                                error = SettlementGenericTemporaryError,
                            )
                        }

                        PixKeyError.SystemUnavailable -> {
                            PixPaymentResult(
                                status = PixPaymentStatus.REFUSED,
                                idOrdemPagamento = "",
                                endToEnd = "",
                                error = PixTransactionError.SystemUnavailable,
                            )
                        }

                        else -> {
                            PixPaymentResult(
                                status = PixPaymentStatus.REFUSED,
                                idOrdemPagamento = "",
                                endToEnd = "",
                                error = PixTransactionError.InvalidPixkey,
                            )
                        }
                    }
                }
                if (command.recipientDocument != pixKeyDetails.owner.document) {
                    logger.error(
                        append("ACTION", "VERIFY")
                            .andAppend("errorMessage", "Chave pix mudou de documento")
                            .andAppend("originalDocument", command.recipientDocument)
                            .andAppend("newPixKeyDocument", pixKeyDetails.owner.document),
                        "InitPayment",
                    )
                }
                pixKeyDetailsResult = pixKeyDetails
                this.copy(
                    endToEnd = e2e,
                    chaveEnderecamento = pixKeyDetails.key.value,
                    referenciaInterna = command.pixQrCodeId,
                )
            }
        }
        val markers = append("request", request)
        val httpRequest = createHttpRequest<PixPaymentRequestTO>(HttpMethod.POST, configuration.initPaymentV2Path)
            .body(request)

        deviceId?.let {
            markers.andAppendMasked("deviceId", it.value)
            httpRequest.header("x-device-id", it.value)
        }

        val call = httpClient.retrieve(
            httpRequest,
            Argument.listOf(ArbiPixResponseTO::class.java),
            Argument.listOf(ArbiPixResponseTO::class.java),
        )

        try {
            val response = call.firstOrError().blockingGet()
            val mappedResponse = jacksonObjectMapper().readerFor(ArbiPixPaymentResponseTO::class.java)
                .readValue<ArbiPixPaymentResponseTO>(response[0].response)
            logger.info(markers.and(append("response", mappedResponse)), "InitPayment")
            return PixPaymentResult(
                pixKeyDetails = pixKeyDetailsResult,
                status = parseStatus(mappedResponse.statusOrdemPagamento),
                idOrdemPagamento = mappedResponse.idOrdemPagamento,
                endToEnd = mappedResponse.endToEnd,
            )
        } catch (e: HttpClientResponseException) {
            return handleCheckPaymentStatusHttpError(bankOperationId, e, markers)
        } catch (e: Exception) {
            logger.error(markers, "InitPayment", e)
            return PixPaymentResult(
                status = PixPaymentStatus.UNKNOWN,
                idOrdemPagamento = "",
                endToEnd = "",
                error = PaymentGenericTemporaryError,
            )
        }
    }

    @Deprecated("Utilize o transfer")
    override fun initPayment(
        bill: Bill,
        paymentMethod: AccountPaymentMethod,
        bankOperationId: BankOperationId,
    ): PixPaymentResult {
        val bankAccount = paymentMethod.method as InternalBankAccount

        val account = accountRepository.findById(paymentMethod.accountId)

        val command = PixCommand(
            payerName = account.name,
            recipientName = bill.recipient!!.name,
            recipientDocument = bill.recipient!!.document!!,
            recipientBankAccount = bill.recipient!!.bankAccount,
            recipientPixKey = bill.recipient!!.pixKeyDetails?.key,
            originBankAccount = bankAccount,
            description = bill.description,
            amount = bill.amountTotal,
            pixQrCodeId = bill.pixQrCodeData?.pixId,
        )

        return initPayment(command, bankOperationId, null)
    }

    override fun checkPaymentStatus(
        bankAccount: InternalBankAccount?,
        bankOperationId: BankOperationId,
    ): PixPaymentResult {
        val idIdempotente = bankOperationId.value
        val uri = UriBuilder.of(configuration.paymentStatusIdIdempotenteV2Path)
            .expand(mutableMapOf("idIdempotente" to idIdempotente)).toString()
        val httpRequest = createHttpRequest<PixPaymentRequestTO>(HttpMethod.GET, uri)

        val call = httpClient.retrieve(
            httpRequest,
            Argument.listOf(ArbiPixResponseTO::class.java),
            Argument.listOf(ArbiPixResponseTO::class.java),
        )

        val marker = append("idIdempotente", idIdempotente)
            .andAppend("bankAccount", bankAccount?.buildFullAccountNumber())

        return try {
            val response = call.firstOrError().blockingGet()

            val mappedResponse = jacksonObjectMapper().readerFor(ArbiPixPaymentResponseTO::class.java)
                .readValue<ArbiPixPaymentResponseTO>(response[0].response)

            logger.info(marker.and(append("response", mappedResponse)), "CheckPaymentStatus")

            PixPaymentResult(
                status = parseStatus(mappedResponse.statusOrdemPagamento),
                idOrdemPagamento = mappedResponse.idOrdemPagamento,
                endToEnd = mappedResponse.endToEnd,
            )
        } catch (e: HttpClientResponseException) {
            handleCheckPaymentStatusHttpError(bankOperationId, e, marker)
        } catch (e: Exception) {
            logger.error(marker, "CheckPaymentStatus", e)
            PixPaymentResult(status = PixPaymentStatus.UNKNOWN, idOrdemPagamento = "", endToEnd = "")
        }
    }

    private fun StatementResponseTO.toPixStatement() = PixStatementItem(
        flow = flow,
        transactionId = referenciaInterna?.let { QrCodeTransactionId.from(it) },
        endToEnd = endToEnd?.let { EndToEnd(it) },
        date = ZonedDateTime.parse(dataMovimento, DateTimeFormatter.ISO_DATE_TIME).withZoneSameInstant(
            brazilTimeZone,
        ).toLocalDate(),
        type = if (indDevolucao) {
            BankStatementItemType.DEVOLUCAO_PIX
        } else {
            BankStatementItemType.PIX
        },
        description = descricao,
        operationNumber = nroMovimento,
        isTemporaryOperationNumber = isTemporaryOperationNumber(nroMovimento),
        amount = convertToLong(valor.toString()),
        counterpartName = nome,
        counterpartDocument = cpfCnpjContraParte,
        counterpartAccountNo = nroContaLancamento,
        counterpartBankName = nomeInstituicaoLancamento,
        documentNumber = cpfCnpj,
        ref = referenciaInterna,
        lastUpdate = getZonedDateTime(),
        metadata = null,
        notificatedAt = null,
    )

    fun getStatementTest(map: Map<String, String>): List<PixStatementItem> {
        val marker = append("request", map)
        val httpRequest = createHttpRequest<Map<String, String>>(HttpMethod.POST, configuration.pixStatementV2Path)
            .body(map)
        val call = httpClient.retrieve(
            httpRequest,
            Argument.listOf(StatementResponseTO::class.java),
            Argument.STRING,
        )
        try {
            val response: List<StatementResponseTO> = call.firstOrError().blockingGet()
            logger.info(marker.andAppend("response", response), "GetPixStatement")
            return response.map { it.toPixStatement() }
        } catch (e: HttpClientResponseException) {
            marker.andAppend("response", e.response.body)
                .andAppend("status", e.status)

            if (e.status == HttpStatus.NOT_FOUND) {
                logger.info(marker, "GetPixStatement")
                return emptyList()
            }

            logger.error(marker, "GetPixStatement", e)
            throw e
        } catch (e: Exception) {
            logger.error(marker, "GetPixStatement", e)
            throw e
        }
    }

    override fun getStatement(accountNumber: AccountNumber): List<PixStatementItem> {
        val statementRequestTO = StatementRequestTO(
            codIspb = configuration.codInstituicaoPagador,
            codAgencia = ARBI_ROUTING_NO,
            nroConta = accountNumber.fullAccountNumber.padStart(10, '0'),
        )

        val marker = append("request", statementRequestTO)
        val httpRequest = createHttpRequest<StatementRequestTO>(HttpMethod.POST, configuration.pixStatementV2Path)
            .body(statementRequestTO)
        val call = httpClient.retrieve(
            httpRequest,
            Argument.listOf(StatementResponseTO::class.java),
            Argument.STRING,
        )

        try {
            val response = call.firstOrError().blockingGet()
            marker.andAppend("response", response)

            logger.info(marker, "GetPixStatement")
            return response.map { it.toPixStatement() }
        } catch (e: HttpClientResponseException) {
            marker.andAppend("response", e.response.body)
                .andAppend("status", e.status)

            if (e.status == HttpStatus.NOT_FOUND) {
                logger.info(marker, "GetPixStatement")
                return emptyList()
            }

            logger.error(marker, "GetPixStatement", e)
            throw e
        } catch (e: Exception) {
            logger.error(marker, "GetPixStatement", e)
            throw e
        }
    }

    override fun checkEndToEndStatus(endToEndCode: String): String {
        val marker = append("endToEndCode", endToEndCode)
        val uri = UriBuilder.of(configuration.paymentStatusE2EV2Path)
            .expand(mutableMapOf("e2e" to endToEndCode)).toString()
        val httpRequest = createHttpRequest<String>(HttpMethod.GET, uri)
        val call = httpClient.retrieve(
            httpRequest,
            Argument.STRING,
            Argument.STRING,
        )

        try {
            val response = call.firstOrError().blockingGet()
            logger.info(marker.andAppend("response", response), "CheckEndToEndStatus")
            return response
        } catch (e: HttpClientResponseException) {
            marker.andAppend("response", e.response.body)
                .andAppend("status", e.status)

            if (e.status == HttpStatus.NOT_FOUND) {
                logger.info(marker, "CheckEndToEndStatus")
                return ""
            }

            logger.error(marker, "CheckEndToEndStatus", e)
            throw e
        } catch (e: Exception) {
            logger.error(marker, "CheckEndToEndStatus", e)
            throw e
        }
    }

    data class PixTransactionErrorCode(val code: String, val additionalErrorCode: String? = null)

    private fun handleCheckPaymentStatusHttpError(
        bankOperationId: BankOperationId,
        e: HttpClientResponseException,
        marker: LogstashMarker,
    ): PixPaymentResult {
        val successRange = 200..299
        if (e.status.code in successRange) {
            return PixPaymentResult(
                status = PixPaymentStatus.UNKNOWN,
                idOrdemPagamento = bankOperationId.value,
                endToEnd = "",
                error = PaymentGenericTemporaryError,
            )
        }

        val optionalBody = e.response.getBody(Argument.listOf(ArbiPixResponseTO::class.java))
        val body = if (optionalBody.isPresent && optionalBody.get().isNotEmpty()) {
            optionalBody.get()
        } else {
            val body = e.response.getBody(String::class.java).orElse("")
            logger.error(
                marker.and<LogstashMarker>(append("status", e.status)).and(append("body", body)),
                "CheckPaymentStatus",
                e,
            )
            return PixPaymentResult(
                status = PixPaymentStatus.UNKNOWN,
                idOrdemPagamento = bankOperationId.value,
                endToEnd = "",
                error = PaymentGenericTemporaryError,
            )
        }

        logger.warn(
            marker.and<LogstashMarker>(append("status", e.response.status)).and(append("response", body)),
            "CheckPaymentStatus",
        )
        if (body.first().status == 412 && body.first().response == "Não Localizado/Inválido") {
            return PixPaymentResult(
                status = PixPaymentStatus.FAILED,
                idOrdemPagamento = bankOperationId.value,
                endToEnd = "",
                error = PaymentGenericTemporaryError,
            )
        }
        val mappedResponse = readArbiPixPaymentResponse(body.first().response) ?: return PixPaymentResult(
            status = PixPaymentStatus.UNKNOWN,
            idOrdemPagamento = bankOperationId.value,
            endToEnd = "",
            error = PaymentGenericTemporaryError,
        )

        return PixPaymentResult(
            status = parseStatus(mappedResponse.statusOrdemPagamento),
            idOrdemPagamento = mappedResponse.idOrdemPagamento,
            endToEnd = mappedResponse.endToEnd,
            error = mapError(mappedResponse.erros),
        )
    }

    private fun Long.formatRoutingNo(): String {
        return toString().padStart(4, '0')
    }

    private fun String.formatFullAccountNo(): String {
        return padStart(10, '0')
    }

    private fun buildPixPaymentRequestTO(command: NewPixCommand) = PixPaymentRequestTO(
        codInstituicaoPagador = configuration.codInstituicaoPagador,
        codAgenciaPagador = command.originBankAccount.routingNo.formatRoutingNo(),
        nroContaPagador = command.originBankAccount.buildFullAccountNumber().formatFullAccountNo(),
        tipoContaPagador = command.originBankAccount.accountType.toTipoConta(),
        cpfCnpjPagador = command.originBankAccount.document,
        valorOperacao = toCurrencyFormat(command.amount),
        codUsuario = command.originBankAccount.document,
        dataPagamento = getZonedDateTime().format(dateFormat),
        nomePagador = command.payerName,
        campoLivre = command.description.stripEmojis(),
        idIdempotente = command.bankOperationId.value,
    )

    private fun buildPixPaymentRequestTO(command: PixCommand, idIdempotente: String) = PixPaymentRequestTO(
        codInstituicaoPagador = configuration.codInstituicaoPagador,
        codAgenciaPagador = command.originBankAccount.routingNo.formatRoutingNo(),
        nroContaPagador = command.originBankAccount.buildFullAccountNumber().formatFullAccountNo(),
        tipoContaPagador = command.originBankAccount.accountType.toTipoConta(),
        cpfCnpjPagador = command.originBankAccount.document,
        valorOperacao = toCurrencyFormat(command.amount),
        codUsuario = command.originBankAccount.document,
        dataPagamento = getZonedDateTime().format(dateFormat),
        nomePagador = command.payerName,
        campoLivre = command.description.stripEmojis(),
        idIdempotente = idIdempotente,
    )

    private fun readArbiPixPaymentResponse(resultado: String): ArbiPixPaymentResponseTO? {
        return try {
            jacksonObjectMapper().readerFor(ArbiPixPaymentResponseTO::class.java)
                .readValue<ArbiPixPaymentResponseTO>(resultado)
        } catch (e: Exception) {
            logger.warn(
                append("result", "invalid JSON"),
                "CheckPaymentStatus",
                e,
            )
            val resultadoFormatted = removeMalFormatedXML(resultado)
            try {
                jacksonObjectMapper().readerFor(ArbiPixPaymentResponseTO::class.java)
                    .readValue<ArbiPixPaymentResponseTO>(resultadoFormatted)
            } catch (e: Exception) {
                logger.error(
                    append("result", "invalid JSON can't fix"),
                    "CheckPaymentStatus",
                    e,
                )
                null
            }
        }
    }

    fun removeMalFormatedXML(value: String, timesCalled: Int = 0): String {
        val startIndex = value.indexOf("\"xml version=\"")
        val finalToken = "</problem>"
        val endIndex = value.indexOf(finalToken)
        if (startIndex > 0 && endIndex > 0 && timesCalled < 4) {
            val newValue = "${value.substring(0, startIndex + 1)}${value.substring(endIndex + finalToken.length)}"
            return removeMalFormatedXML(newValue, timesCalled + 1)
        }
        return value
    }

    private val pixBusinessInsufficientBalanceErrorCodes = listOf(PixTransactionErrorCode("BL00001", "60168"))
    private val pixBusinessSingleTransactionLimitErrorCodes = listOf(PixTransactionErrorCode("AB00002"))
    private val pixBusinessDailyLimitErrorCodes = listOf(PixTransactionErrorCode("AB00003"))
    private val pixBusinessSameValuePaymentsExceededErrorCodes = listOf(PixTransactionErrorCode("AB00004"))

    private val pixSettlementDestinationNotFoundErrorCodes = listOf(
        PixTransactionErrorCode("BL00001", "60743"),
        PixTransactionErrorCode("TI00001", "60743"),
        PixTransactionErrorCode("PIXBE01"),
        PixTransactionErrorCode("AB00015"),
        PixTransactionErrorCode("PIXAC03"),
        PixTransactionErrorCode("PIXCH11"),
    )

    private val pixSettlementDestinationAccountTypeInvalidErrorCodes = listOf(
        PixTransactionErrorCode("PIXAC14"),
    )

    private val pixQrCodeRejectedByRecipient = listOf(
        PixTransactionErrorCode("PIXBE17"),
    )

    private val pixSettlementDestinationAccountNotAvailableErrorCodes = listOf(
        PixTransactionErrorCode("PIXAC06"),
        PixTransactionErrorCode("PIXAC07"),
        PixTransactionErrorCode("AB00303"),
    )

    private val pixSettlementGenericPermanentErrorCodes = listOf(
        PixTransactionErrorCode("AB00006"),
        PixTransactionErrorCode("AB00021"),
        PixTransactionErrorCode("PIXAG03"),
        PixTransactionErrorCode("PIXAM01"),
        PixTransactionErrorCode("PIXAM18"),
        PixTransactionErrorCode("PIXDS0G"),
        PixTransactionErrorCode("PIXRR01"),
        PixTransactionErrorCode("TI00001", "60625"),
    )

    private val pixSettlementGenericTemporaryErrorCodes = listOf(
        PixTransactionErrorCode("AB00001"),
        PixTransactionErrorCode("AB00007"),
        PixTransactionErrorCode("AB00014"),
        PixTransactionErrorCode("AB00016"),
        PixTransactionErrorCode("AB00017"),
        PixTransactionErrorCode("AB00018"),
        PixTransactionErrorCode("AB00019"),
        PixTransactionErrorCode("AB00020"),
        PixTransactionErrorCode("AB99999"),
        PixTransactionErrorCode("AB00023"),
        PixTransactionErrorCode("PIXAB03"),
        PixTransactionErrorCode("PIXAB09"),
        PixTransactionErrorCode("PIXAM04"),
        PixTransactionErrorCode("PIXAM05"),
        PixTransactionErrorCode("PIXCH16"),
        PixTransactionErrorCode("PIXDT02"),
        PixTransactionErrorCode("PIXDU04"),
        PixTransactionErrorCode("PIXED05"),
        PixTransactionErrorCode("PIXFF08"),
        PixTransactionErrorCode("PIXRC09"),
        PixTransactionErrorCode("PIXRC10"),
    )

    private val pixSettlementPaymentRefusedByDestinationErrorCodes = listOf(
        PixTransactionErrorCode("PIXDS04"),
    )

    private val pixSourceAccountLocked = listOf(
        PixTransactionErrorCode("TI00001", "60216"),
        PixTransactionErrorCode("TI00001", "60217"),
        PixTransactionErrorCode("BL00001", "60217"),
    )

    private val pixAccountFundsBlocked = listOf(
        PixTransactionErrorCode("BL00001", "60432"),
    )

    private val pixSettlementDestinationInstitutionNotAllowedErrorCodes = listOf(
        PixTransactionErrorCode("AB00005"),
    )

    private val deviceFingerprintErrorCodes = listOf(PixTransactionErrorCode("9993"))

    private fun mapError(errors: List<ArbiPixErrorResponseTO>?): PixTransactionError? {
        return errors?.map {
            when (PixTransactionErrorCode(it.codigoErro, it.codigoErroComplementar)) {
                in pixBusinessInsufficientBalanceErrorCodes -> BusinessInsufficientBalance
                in pixBusinessSingleTransactionLimitErrorCodes -> BusinessSingleTransactionLimit
                in pixBusinessDailyLimitErrorCodes -> BusinessDailyLimit
                in pixBusinessSameValuePaymentsExceededErrorCodes -> BusinessSameValuePaymentsExceeded
                in pixSettlementDestinationNotFoundErrorCodes -> SettlementDestinationAccountNotFound
                in pixSettlementDestinationAccountNotAvailableErrorCodes -> SettlementDestinationAccountNotAvailable
                in pixSettlementGenericPermanentErrorCodes -> SettlementGenericPermanentError
                in pixSettlementPaymentRefusedByDestinationErrorCodes -> SettlementPaymentRefusedByDestination
                in pixSettlementDestinationInstitutionNotAllowedErrorCodes -> SettlementDestinationInstitutionNotAllowed
                in pixSettlementGenericTemporaryErrorCodes -> {
                    if (it.codigoErro == "AB99999") {
                        logger.error(
                            append("error", it)
                                .andAppend("ACTION", "VERIFY").andAppend("context", "Cliente com limite configurado errado no ARBI. O limite no Arbi deveria ser sempre maior que o nosso. Tem que avisar ao ARBI. Tem acontecido com clientes PJ."),
                            "validacao_de_limite_error",
                        )
                    }
                    SettlementGenericTemporaryError
                }
                in pixSettlementDestinationAccountTypeInvalidErrorCodes -> SettlementDestinationAccountTypeInvalid
                in pixSourceAccountLocked -> PixTransactionError.PaymentSourceAccountLocked
                in pixAccountFundsBlocked -> PixTransactionError.AccountFundsBlocked
                in deviceFingerprintErrorCodes -> {
                    logger.warn(
                        append("error", it)
                            .andAppend("ACTION", "VERIFY"),
                        "device_fingerprint_error",
                    )
                    PixTransactionError.DeviceFingerprintError
                }
                in pixQrCodeRejectedByRecipient -> PixTransactionError.QrCodeRejectedByRecipient

                else -> {
                    logger.warn(append("error", it), "UnmappedError_CheckPaymentStatus")
                    SettlementGenericTemporaryError
                }
            }
        }?.firstOrNull()
    }

    private fun parseStatus(statusOrdemPagamento: String): PixPaymentStatus {
        return when (statusOrdemPagamento) {
            "ENVIADA" -> PixPaymentStatus.ACKNOWLEDGED
            "EFETIVADA" -> PixPaymentStatus.SUCCESS
            "RECUSADA" -> PixPaymentStatus.REFUSED
            "AGENDADA" -> PixPaymentStatus.SCHEDULED
            "ERRO" -> PixPaymentStatus.FAILED
            else -> PixPaymentStatus.UNKNOWN // ICOM
        }
    }

    private fun <T> createHttpRequest(httpMethod: HttpMethod, uri: String) = HttpRequest.create<T>(httpMethod, uri)
        .header("client_id", configuration.clientId)
        .header("access_token", authenticationManager.getToken())
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON_TYPE)

    companion object {
        private val logger = LoggerFactory.getLogger(NewArbiPixPaymentAdapter::class.java)
    }
}

private fun toCurrencyFormat(number: Long) = (number / 100.0).toFloat()

data class ArbiPixResponseTO(val response: String, val status: Int)

@JsonIgnoreProperties(ignoreUnknown = true)
data class ArbiPixPaymentResponseTO(
    val idOrdemPagamento: String,
    val endToEnd: String,
    val statusOrdemPagamento: String,
    val infoOrdemPagamento: String?,
    val erros: List<ArbiPixErrorResponseTO>?,
)

data class ArbiPixErrorResponseTO(
    val codigoErro: String,
    val descricaoErro: String,
    val codigoErroComplementar: String?,
)

@JsonInclude(JsonInclude.Include.NON_NULL)
data class PixPaymentRequestTO(
    val codInstituicaoPagador: String,
    val codAgenciaPagador: String,
    val nroContaPagador: String,
    val tipoContaPagador: String,
    val cpfCnpjPagador: String,
    val valorOperacao: Float,
    val codUsuario: String,
    val dataPagamento: String,
    val nomePagador: String,

    val codInstituicaoBeneficiario: String? = null,
    val codAgenciaBeneficiario: String? = null,
    val nroContaBeneficiario: String? = null,
    val cpfCnpjBeneficiario: String? = null,
    val tipoContaBeneficiario: String? = null,
    val nomeBeneficiario: String? = null,

    val endToEnd: String? = null,
    val chaveEnderecamento: String? = null,

    val campoLivre: String,
    val idIdempotente: String,

    val referenciaInterna: String? = null,
)

data class StatementRequestTO(
    val codIspb: String,
    val codAgencia: String,
    val nroConta: String,
    val qtdDias: Int = 2,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class StatementResponseTO(
    val natureza: String,
    val referenciaInterna: String?,
    val endToEnd: String?,
    val dataMovimento: String,
    val indDevolucao: Boolean,
    val descricao: String,
    val nroMovimento: String,
    val valor: Double,
    val nome: String,
    val cpfCnpjContraParte: String,
    val nroContaLancamento: String?,
    val nomeInstituicaoLancamento: String?,
    val cpfCnpj: String,
    val codIspbLancamento: String?,
    val codAgenciaLancamento: String?,
    val origemMovimento: String?,
    val chave: String?,
) {
    val flow = when (natureza) {
        "C" -> BankStatementItemFlow.CREDIT
        "D" -> BankStatementItemFlow.DEBIT
        else -> throw IllegalStateException("Natureza do movimento não mapeada: $natureza")
    }
}