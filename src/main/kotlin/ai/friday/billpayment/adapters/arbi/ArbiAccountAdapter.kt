package ai.friday.billpayment.adapters.arbi

import ai.friday.billpayment.adapters.parsers.parseListFrom
import ai.friday.billpayment.and
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.ExternalAccount
import ai.friday.billpayment.app.account.ExternalRegisterStatus
import ai.friday.billpayment.app.account.MonthlyIncome
import ai.friday.billpayment.app.account.PoliticallyExposed
import ai.friday.billpayment.app.account.RegisterNaturalPersonResponse
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.integrations.DocumentType
import ai.friday.billpayment.app.integrations.ExternalAccountRegister
import ai.friday.billpayment.app.integrations.NaturalPersonStatus
import ai.friday.billpayment.app.integrations.NaturalPersonStatusChangeReason
import ai.friday.morning.date.dateFormat
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MediaType
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Singleton
import java.text.DecimalFormat
import java.text.NumberFormat
import java.util.Locale
import java.util.UUID
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

const val NEIGHBORHOOD_MAX_LENGTH = 20

@Singleton
class ArbiAccountAdapter(
    @param:Client(
        id = "arbi",
    ) private val newGWhttpClient: RxHttpClient,
    private val newGWAuthenticationManager: NewArbiAuthenticationManager,
    private val configuration: ArbiConfiguration,
) : ExternalAccountRegister {
    override fun notifyAccountRegisterDocumentsSent(
        name: String,
        document: String,
    ): Boolean {
        val request = buildNotifyAccountRegisterDocumentsSentRequest(name, document)

        val markers = append("request", request)

        val token = newGWAuthenticationManager.getToken()
        val httpRequest =
            HttpRequest.POST(configuration.cadastroPFPath, request)
                .header("client_id", configuration.clientId)
                .header("access_token", token)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON_TYPE)

        val call =
            newGWhttpClient.retrieve(
                httpRequest,
                Argument.listOf(ArbiResponseTO::class.java),
                Argument.of(String::class.java),
            )

        return try {
            val response = call.firstOrError().blockingGet().first()
            logger.info(markers.and(append("response", response)), "notifyAccountRegisterDocumentsSent")

            true
        } catch (e: HttpClientResponseException) {
            newCheckUnauthorized(e)
            val response = e.response.getBody(Argument.of(String::class.java)).orElse("")
            logger.error(markers.and("response" to response, "statusCode" to e.status), "notifyAccountRegisterDocumentsSent")

            val isKycOverdue = response.lowercase(Locale.getDefault()).contains("Utilizar transação 5 para renovação de KYC".lowercase(Locale.getDefault()))

            if (isKycOverdue) {
                return renewKYC(name, document)
            }

            response.contains("O cadastro desse proponente ja esta aprovado no KYC")
        } catch (e: Exception) {
            logger.error(markers, "notifyAccountRegisterDocumentsSent", e)
            false
        }
    }

    private fun renewKYC(name: String, document: String): Boolean {
        val logName = "notifyKYCRenew"
        val request = buildNotifyKycRenewRequest(name, document)
        val markers = append("request", request)

        val token = newGWAuthenticationManager.getToken()
        val httpRequest =
            HttpRequest.POST(configuration.cadastroPFPath, request)
                .header("client_id", configuration.clientId)
                .header("access_token", token)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON_TYPE)

        val call =
            newGWhttpClient.retrieve(
                httpRequest,
                Argument.listOf(ArbiResponseTO::class.java),
                Argument.of(String::class.java),
            )

        return try {
            val response = call.firstOrError().blockingGet().first()
            logger.info(markers.and(append("response", response)), logName)

            true
        } catch (e: HttpClientResponseException) {
            newCheckUnauthorized(e)
            val response = e.response.getBody(Argument.of(String::class.java)).orElse("")
            logger.error(markers.and("response" to response, "statusCode" to e.status), logName)

            false
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            false
        }
    }

    override fun registerNaturalPerson(customer: ExternalAccount): RegisterNaturalPersonResponse {
        val request = ArbiAccountRequest(customer.toRegisterNaturalPerson(configuration))

        val (success, result) = handleRequest(request, "registerNaturalPerson")

        if (success) {
            val response =
                jacksonObjectMapper().readerFor(RegisterNaturalPersonResult::class.java)
                    .readValue<RegisterNaturalPersonResult>(result)

            return RegisterNaturalPersonResponse(
                success,
                response.fullAccountNumber?.dropLast(1)?.toLong(),
                response.fullAccountNumber?.takeLast(1),
            )
        } else {
            return RegisterNaturalPersonResponse(success, null, null)
        }
    }

    override fun simpleRegisterNaturalPerson(customer: ExternalAccount): RegisterNaturalPersonResponse {
        val request = ArbiAccountRequest(customer.toSimpleRegisterNaturalPerson(configuration))

        val (success, result) = handleRequest(request, "simpleRegisterNaturalPerson")

        return if (success) {
            val response =
                jacksonObjectMapper().readerFor(RegisterNaturalPersonResult::class.java)
                    .readValue<RegisterNaturalPersonResult>(result)

            return RegisterNaturalPersonResponse(
                success,
                response.fullAccountNumber?.dropLast(1)?.toLong(),
                response.fullAccountNumber?.takeLast(1),
            )
        } else {
            return RegisterNaturalPersonResponse(success, null, null)
        }
    }

    override fun queryExternalRegisterStatus(
        name: String,
        document: String,
    ): ExternalRegisterStatus? {
        val request = buildExternalRegisterStatusQueryRequest(name, document)

        val (success, result) = handleRequest(request, "queryExternalAccountStatus")

        return if (success) {
            val response =
                jacksonObjectMapper().readerFor(ExternalRegisterStatusQueryResponse::class.java)
                    .readValue<ExternalRegisterStatusQueryResponse>(result)

            when {
                response.status.equals("APROVADO", ignoreCase = true) -> ExternalRegisterStatus.APPROVED
                response.status.equals("REPROVADO", ignoreCase = true) -> ExternalRegisterStatus.REJECTED
                else -> null
            }
        } else {
            null
        }
    }

    override fun close(accountNo: AccountNumber): Either<Exception, Unit> {
        return closeWithZero(accountNo.fullAccountNumber)
    }

    override fun getNaturalPersonsStatus(accountNos: List<AccountNumber>): Result<List<Pair<AccountNumber, NaturalPersonStatus>>> {
        val markers = append("accountNos", accountNos)

        val url = "${configuration.accountStatus}?${accountNos.joinToString("&") { "accounts=" + it.fullAccountNumber }}"
        markers.andAppend("url", url)

        val token = newGWAuthenticationManager.getToken()
        val httpRequest =
            HttpRequest.GET<NaturalPersonStatusResponse>(url)
                .header("client_id", configuration.clientId)
                .header("access_token", token)
                .header("X-Client-Request-Id", UUID.randomUUID().toString())
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON_TYPE)

        return try {
            val response = newGWhttpClient.toBlocking()
                .exchange(
                    httpRequest,
                    Argument.listOf(NaturalPersonStatusResponse::class.java),
                    Argument.of(String::class.java),
                )
            logger.info(markers.and(append("response", response)), "getNaturalPersonStatus")

            Result.success(response.body().map { AccountNumber(it.accountNumber) to it.status })
        } catch (e: HttpClientResponseException) {
            newCheckUnauthorized(e)
            val response = e.response.getBody(Argument.of(String::class.java)).orElse("")
            logger.error(markers.and("response" to response, "statusCode" to e.status), "getNaturalPersonStatus")

            Result.failure(e)
        } catch (e: Exception) {
            logger.error(markers, "getNaturalPersonStatus", e)
            Result.failure(e)
        }
    }

    override fun getNaturalPersonStatus(accountNo: AccountNumber): Either<Exception, NaturalPersonStatus> {
        val markers = append("accountNo", accountNo)

        val url = "${configuration.accountStatus}?accounts=${accountNo.fullAccountNumber}"
        markers.andAppend("url", url)

        val token = newGWAuthenticationManager.getToken()
        val httpRequest =
            HttpRequest.GET<NaturalPersonStatusResponse>(url)
                .header("client_id", configuration.clientId)
                .header("access_token", token)
                .header("X-Client-Request-Id", UUID.randomUUID().toString())
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON_TYPE)

        return try {
            val response = newGWhttpClient.toBlocking()
                .exchange(
                    httpRequest,
                    Argument.listOf(NaturalPersonStatusResponse::class.java),
                    Argument.of(String::class.java),
                )
            logger.info(markers.and(append("response", response)), "getNaturalPersonStatus")

            response.body().first().status.right()
        } catch (e: HttpClientResponseException) {
            newCheckUnauthorized(e)
            val response = e.response.getBody(Argument.of(String::class.java)).orElse("")
            logger.error(markers.and("response" to response, "statusCode" to e.status), "getNaturalPersonStatus")

            e.left()
        } catch (e: Exception) {
            logger.error(markers, "getNaturalPersonStatus", e)
            e.left()
        }
    }

    override fun setNaturalPersonStatus(accountNo: AccountNumber, status: NaturalPersonStatus, reason: NaturalPersonStatusChangeReason): Either<Exception, Unit> {
        val markers = append("accountNo", accountNo)
            .andAppend("reason", reason)
            .andAppend("status", status)

        val request = SetNaturalPersonStatusRequest(
            accounts = listOf(accountNo.fullAccountNumber),
            reason = reason,
            newStatus = status,
        )

        val token = newGWAuthenticationManager.getToken()
        val httpRequest =
            HttpRequest.PUT(configuration.accountStatus, request)
                .header("client_id", configuration.clientId)
                .header("access_token", token)
                .header("X-Client-Request-Id", UUID.randomUUID().toString())
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON_TYPE)

        val call = newGWhttpClient.exchange(
            httpRequest,
            Argument.listOf(SetNaturalPersonStatusResponse::class.java),
            Argument.of(String::class.java),
        )

        return try {
            val response = call.firstOrError().blockingGet().body()
            logger.info(markers.and(append("response", response)), "setNaturalPersonStatus")

            val accountResponse = response.firstOrNull()

            if (accountResponse != null) {
                if (accountResponse.code == 1) {
                    Unit.right()
                } else {
                    ArbiSetNaturalPersonStatusException(response.first().result).left()
                }
            } else {
                ArbiSetNaturalPersonStatusException("empty response").left()
            }
        } catch (e: HttpClientResponseException) {
            newCheckUnauthorized(e)
            val response = e.response.getBody(Argument.of(String::class.java)).orElse("")
            logger.error(markers.and("response" to response, "statusCode" to e.status), "setNaturalPersonStatus")

            e.left()
        } catch (e: Exception) {
            logger.error(markers, "setNaturalPersonStatus", e)
            e.left()
        }
    }

    private fun closeWithZero(accountNo: String): Either<Exception, Unit> {
        val token = newGWAuthenticationManager.getToken()
        val request =
            CloseAccountRequest(
                cadastroManuntencao =
                CadastroManutencaoRequest(
                    inscricaoParceiro = configuration.inscricao,
                    contaOrigem = accountNo.padStart(10, '0'),
                    tokenUsuario = configuration.userToken,
                ),
            )
        val markers = append("accountNo", accountNo).andAppend("request", request)
        val httpRequest =
            HttpRequest.POST(configuration.encerrarContaV2Path, request)
                .header("client_id", configuration.clientId)
                .header("access_token", token)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON_TYPE)

        val call =
            newGWhttpClient.retrieve(
                httpRequest,
                Argument.of(String::class.java),
                Argument.of(String::class.java),
            )

        return try {
            val response = call.firstOrError().blockingGet()
            logger.info(markers.and(append("response", response)), "ArbiCloseAccount")
            Unit.right()
        } catch (e: HttpClientResponseException) {
            // TODO mapear cenarios de erro
            newCheckUnauthorized(e)

            val rawResponse = e.response.getBody(String::class.java)
            logger.warn(markers.andAppend("response", rawResponse), "ArbiCloseAccount#raw")

            if (rawResponse.orElse(null) != null) {
                val response = parseListFrom<ArbiResponseTO>(rawResponse.orElseThrow())
                markers.andAppend("response", response).andAppend("status", e.response.status.code)

                return if (response.isNotEmpty() && response.first().descricaoStatus.contains("Conta já foi encerrada anteriormente", ignoreCase = true)) {
                    logger.warn(markers, "ArbiCloseAccount")

                    Unit.right()
                } else {
                    logger.error(markers, "ArbiCloseAccount")

                    val errorMessage = if (response.isNotEmpty()) {
                        with(response.first()) {
                            "$resultado - $descricaoStatus"
                        }
                    } else {
                        ""
                    }
                    ArbiCloseAccountException(errorMessage).left()
                }
            }

            logger.error(markers, "ArbiCloseAccount")

            ArbiCloseAccountException("Erro não tratado").left()
        } catch (e: Exception) {
            logger.error(markers, "ArbiCloseAccount", e)
            e.left()
        }
    }

    private fun handleRequest(
        request: ArbiAccountRequest,
        message: String,
    ): Pair<Boolean, String?> {
        val markers = append("request", request)

        val token = newGWAuthenticationManager.getToken()
        val httpRequest =
            HttpRequest.POST(configuration.cadastroPFPath, request)
                .header("client_id", configuration.clientId)
                .header("access_token", token)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON_TYPE)

        val call =
            newGWhttpClient.retrieve(
                httpRequest,
                Argument.listOf(ArbiResponseTO::class.java),
                Argument.of(String::class.java),
            )

        return try {
            val response = call.firstOrError().blockingGet().first()
            logger.info(markers.and(append("response", response)), message)

            Pair(true, response.resultado)
        } catch (e: HttpClientResponseException) {
            newCheckUnauthorized(e)
            val response = e.response.getBody(Argument.of(String::class.java)).orElse("")
            logger.error(markers.andAppend("response", response).andAppend("status", e.response.status.code), message)

            Pair(false, null)
        } catch (e: Exception) {
            logger.error(markers, message, e)
            Pair(false, null)
        }
    }

    private fun newCheckUnauthorized(e: HttpClientResponseException) {
        if (e.status == HttpStatus.UNAUTHORIZED) {
            logger.warn(append("error_message", "Token is expired"), "ArbiAccountAdapter")
            newGWAuthenticationManager.cleanTokens()
            throw ArbiLoginException()
        }
    }

    private fun buildNotifyKycRenewRequest(
        name: String,
        document: String,
    ): ArbiAccountRequest {
        return ArbiAccountRequest(
            RegisterNaturalPersonRequest(
                inscricaoparceiro = configuration.inscricao,
                tokenusuario = configuration.userToken,
                idtransacao = "5",
                nome = name,
                cpf = document,
            ),
        )
    }

    private fun buildNotifyAccountRegisterDocumentsSentRequest(
        name: String,
        document: String,
    ): ArbiAccountRequest {
        return ArbiAccountRequest(
            RegisterNaturalPersonRequest(
                inscricaoparceiro = configuration.inscricao,
                tokenusuario = configuration.userToken,
                idtransacao = "2",
                nome = name,
                cpf = document,
            ),
        )
    }

    private fun buildExternalRegisterStatusQueryRequest(
        name: String,
        document: String,
    ): ArbiAccountRequest {
        return ArbiAccountRequest(
            RegisterNaturalPersonRequest(
                inscricaoparceiro = configuration.inscricao,
                tokenusuario = configuration.userToken,
                idtransacao = "3",
                nome = name,
                cpf = document,
            ),
        )
    }

    companion object {
        private val logger = LoggerFactory.getLogger(ArbiAccountAdapter::class.java)
    }
}

private fun String.sanitize() = this.replace("'", "")

internal fun ExternalAccount.toRegisterNaturalPerson(configuration: ArbiConfiguration) =
    RegisterNaturalPersonRequest(
        inscricaoparceiro = configuration.inscricao,
        tokenusuario = configuration.userToken,
        idtransacao = "1",
        nome = documentInfo.name.sanitize(),
        cpf = documentInfo.cpf,
        endereco = "${address.streetType.orEmpty()} ${address.streetName.sanitize()}".trim(),
        numero = address.number.toString(),
        complemento = address.complement.orEmpty().sanitize(),
        bairro = address.neighborhood.sanitize().take(NEIGHBORHOOD_MAX_LENGTH),
        cidade = address.city.sanitize(),
        cep = address.zipCode,
        uf = address.state,
        datanascimento =
        documentInfo.birthDate?.format(dateFormat)
            ?: throw IllegalStateException("toRegisterNaturalPerson requires not null birthDate"),
        naturalidade = documentInfo.birthCity.sanitize(),
        tipoident = documentInfo.docType.toRequest(),
        dataident =
        documentInfo.expeditionDate?.format(dateFormat)
            ?: throw IllegalStateException("toRegisterNaturalPerson requires not null expeditionDate"),
        numeroident = if (documentInfo.docType === DocumentType.CNH) documentInfo.cnhNumber.orEmpty() else documentInfo.rg,
        orgaoident = if (documentInfo.docType === DocumentType.CNH) "DETRAN" else if (documentInfo.orgEmission.startsWith("DETRAN", ignoreCase = true)) "DETRAN" else documentInfo.orgEmission,
        uforgaoident = documentInfo.birthState,
        nomemae = documentInfo.motherName.sanitize(),
        nomepai = documentInfo.fatherName.sanitize(),
        email = email,
        ddd = mobilePhone.ddd(),
        telefone = mobilePhone.phoneNumber(),
        ppe = politicallyExposed.toPPE(),
        cargo = "",
        renda = monthlyIncome.toRenda(),
        nacionalidade = "N", // TODO "N" - Nacional e "S" - Estrangeira
        sexo = calculatedGender.name,
        periodo = "",
        observacao = "",
        foto = "",
    )

internal fun ExternalAccount.toSimpleRegisterNaturalPerson(configuration: ArbiConfiguration) =
    RegisterNaturalPersonRequest(
        inscricaoparceiro = configuration.inscricao,
        tokenusuario = configuration.userToken,
        idtransacao = "1",
        nome = documentInfo.name.sanitize(),
        cpf = documentInfo.cpf,
        endereco = "${address.streetType.orEmpty()} ${address.streetName.sanitize()}".trim(),
        numero = address.number.toString(),
        complemento = address.complement.orEmpty().sanitize(),
        bairro = address.neighborhood.sanitize().take(NEIGHBORHOOD_MAX_LENGTH),
        cidade = address.city.sanitize(),
        cep = address.zipCode,
        uf = address.state,
        datanascimento =
        documentInfo.birthDate?.format(dateFormat)
            ?: throw IllegalStateException("toSimpleRegisterNaturalPerson requires not null birthDate"),
        naturalidade = documentInfo.birthCity.sanitize(),
        tipoident = "CPF",
        dataident = "",
        numeroident = documentInfo.cpf,
        orgaoident = "SRF",
        uforgaoident = "",
        nomemae = documentInfo.motherName.sanitize(),
        nomepai = documentInfo.fatherName.sanitize(),
        email = email,
        ddd = mobilePhone.ddd(),
        telefone = mobilePhone.phoneNumber(),
        ppe = politicallyExposed.toPPE(),
        cargo = "",
        renda = monthlyIncome.toRenda(),
        nacionalidade = "N", // TODO "N" - Nacional e "S" - Estrangeira
        sexo = calculatedGender.name,
        periodo = "",
        observacao = "",
        foto = "",
        tipoproduto = "24",
    )

private fun DocumentType.toRequest(): String {
    return when (this) {
        DocumentType.RG, DocumentType.NEWRG -> "RG"
        DocumentType.CNH, DocumentType.CNHV2 -> "CNH"
        DocumentType.CPF -> this.value
        DocumentType.CNPJ -> this.value
        is DocumentType.OTHER -> this.value
    }
}

private fun PoliticallyExposed.toPPE() =
    if (isExposed) {
        "sim"
    } else {
        "não"
    }

private fun MobilePhone.ddd() = msisdn.substring(3, 5)

private fun MobilePhone.phoneNumber() = msisdn.substring(5)

internal fun MonthlyIncome.toRenda(): String {
    return when {
        upperBound == null -> convertLongToString(lowerBound + 1)
        else -> convertLongToString(upperBound)
    }
}

@Deprecated("melhor usar o do ArbiAdapter esse pode ter erro de arredondamento")
fun convertLongToString(value: Long): String {
    val nf = NumberFormat.getNumberInstance(Locale.US) as DecimalFormat
    nf.applyPattern("#.##")
    return nf.format(value.toDouble() / 100.0f)
}

internal data class ArbiAccountRequest(val cadastropf: RegisterNaturalPersonRequest)

@JsonInclude(JsonInclude.Include.ALWAYS)
data class RegisterNaturalPersonRequest(
    val inscricaoparceiro: String,
    val tokenusuario: String,
    val idrequisicao: String = UUID.randomUUID().toString(),
    val idmodulo: String = "4",
    val idtransacao: String,
    val nome: String,
    val cpf: String,
    val endereco: String = "",
    val numero: String = "",
    val complemento: String = "",
    val bairro: String = "",
    val cidade: String = "",
    val cep: String = "",
    val uf: String = "",
    val datanascimento: String = "",
    val naturalidade: String = "",
    val tipoident: String = "",
    val numeroident: String = "",
    val dataident: String = "",
    val orgaoident: String = "",
    val uforgaoident: String = "",
    val nomemae: String = "",
    val nomepai: String = "",
    val email: String = "",
    val ddd: String = "",
    val telefone: String = "",
    val ppe: String = "",
    val cargo: String = "",
    val renda: String = "",
    val nacionalidade: String = "",
    val sexo: String = "",
    val periodo: String = "",
    val observacao: String = "",
    val foto: String = "",
    val tipoproduto: String = "12",
)

data class CloseAccountRequest(
    @JsonProperty("cadastromanutencao") val cadastroManuntencao: CadastroManutencaoRequest,
)

data class CadastroManutencaoRequest(
    @JsonProperty("inscricaoparceiro") val inscricaoParceiro: String,
    @JsonProperty("tokenusuario") val tokenUsuario: String,
    @JsonProperty("idrequisicao") val idRequisicao: String = UUID.randomUUID().toString(),
    @JsonProperty("idmodulo") val idModulo: String = "8",
    @JsonProperty("idtransacao") val idTransacao: String = "5",
    @JsonProperty("bancoorigem") val bancoOrigem: String = "213",
    @JsonProperty("agenciaorigem") val agenciaOrigem: String = "00019",
    @JsonProperty("contaorigem") val contaOrigem: String,
    @JsonProperty("codsituacao") val codSituacao: String = "ENC",
    @JsonProperty("codmotivoenc") val codMotivoEnc: String = "01",
)

@JsonIgnoreProperties(ignoreUnknown = true)
private data class RegisterNaturalPersonResult(
    @JsonProperty("contadepositovista") val fullAccountNumber: String?,
)

@JsonIgnoreProperties(ignoreUnknown = true)
private data class ExternalRegisterStatusQueryResponse(
    @JsonProperty("cpf") val document: String,
    @JsonProperty("statuskyc") val status: String,
)

@JsonIgnoreProperties(ignoreUnknown = true)
private data class NaturalPersonStatusResponse(
    val accountNumber: String,
    val balanceOfDay: String,
    val blockedAmountBacenJud: String,
    val holderRegistration: String,
    val lastChangeDate: String,
    val lastMoveDate: String,
    val previousStatus: NaturalPersonStatus,
    val product: String,
    val status: NaturalPersonStatus,
)

private data class SetNaturalPersonStatusRequest(
    val accounts: List<String>,
    val reason: NaturalPersonStatusChangeReason,
    val newStatus: NaturalPersonStatus,
)

@JsonIgnoreProperties(ignoreUnknown = true)
private data class SetNaturalPersonStatusResponse(
    val account: String,
    val code: Int,
    val result: String,
)