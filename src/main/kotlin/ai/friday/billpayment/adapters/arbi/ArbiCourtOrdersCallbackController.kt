package ai.friday.billpayment.adapters.arbi

import ai.friday.billpayment.app.account.Role
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Secured(Role.Code.ARBI_CALLBACK)
@Controller("/arbi")
class ArbiCourtOrdersCallbackController {

    @Post("/court-orders")
    fun arbiCourtOrdersCallback(@Body courtOrderCallbackTO: CourtOrderCallbackTO): HttpResponse<*> {
        val markers = Markers.append("payload", courtOrderCallbackTO)
        try {
            LOG.info(markers, "arbiCourtOrdersCallback")
            return HttpResponse.noContent<Unit>()
        } catch (e: Exception) {
            LOG.error(markers, "arbiCourtOrdersCallback", e)
            return HttpResponse.serverError<Unit>()
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(ArbiCourtOrdersCallbackController::class.java)
    }
}

data class CourtOrderCallbackTO(
    val dataordem: String,
    val protocolosolicitacao: String,
    val sequencialbloqueio: String,
    val reiteracaobloqueio: String,
    val sequencialsolicitacao: String,
    val reiteracaosolicitacao: String,
    val cpfcnpjenvolvido: String,
    val nomeenvolvido: String,
    val valorsolicitado: String,
    val numprocesso: String,
    val vara: String,
    val tribunal: String,
    val nome: String,
    val tipo: String,
    val conta: String,
    val valoratendido: String,
    val status: String,
    val cancelareiteracao: String,
)