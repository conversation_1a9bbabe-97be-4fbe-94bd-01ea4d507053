package ai.friday.billpayment.adapters.itp

import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.app.itp.ITPService
import ai.friday.billpayment.app.itp.ITPServiceError
import ai.friday.billpayment.app.itp.PaymentIntentId
import ai.friday.billpayment.app.onepixpay.OnePixPayErrors
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import io.micronaut.security.rules.SecurityRule
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Controller("/itp")
@Secured(SecurityRule.IS_ANONYMOUS)
@Version("2")
open class ITPController(private val itpService: ITPService) {
    private val logger = LoggerFactory.getLogger(ITPController::class.java)

    @Get("/payment/{paymentIntentId}")
    fun getPaymentByToken(@PathVariable("paymentIntentId") paymentIntentId: String): HttpResponse<*> {
        val markers = Markers.append("paymentIntentId", paymentIntentId)

        val result = itpService.retrievePaymentRedirectUrl(
            PaymentIntentId(paymentIntentId),
        ).getOrElse { itpError ->
            return itpError.mapToHttpResponse(
                onBadRequest = {
                    logger.info(
                        markers.andAppend("ErrorMessage", itpError.message),
                        "ITPController#getPaymentByToken",
                    )
                },
                onServerError = {
                    logger.error(
                        markers.andAppend("ErrorMessage", itpError.message),
                        "ITPController#getPaymentByToken",
                        it.exception,
                    )
                },
            )
        }
        logger.info(
            markers.andAppend("paymentIntentId", paymentIntentId).andAppend("consentURL", result.url),
            "ITPController#getPaymentByToken",
        )

        return HttpResponse.ok(mapOf("consentPage" to result.url))
    }

    @Post("/payment/callback")
    fun callbackForPaymentInitiationConsent(@Body iTPConsentCallbackTO: ITPConsentCallbackTO): HttpResponse<*> {
        val logName = "ITPController#callbackForPaymentInitiationConsent"
        val markers = Markers.append("callbackPayload", iTPConsentCallbackTO)
        itpService.callbackForPaymentInitiationConsent(
            state = iTPConsentCallbackTO.state,
            code = iTPConsentCallbackTO.code,
            idToken = iTPConsentCallbackTO.idToken,
            error = iTPConsentCallbackTO.error,
            errorDescription = iTPConsentCallbackTO.errorDescription,
        ).getOrElse { itpError ->
            return itpError.mapToHttpResponse(
                onBadRequest = {
                    logger.warn(
                        markers.andAppend("ErrorMessage", itpError.message),
                        logName,
                    )
                },
                onServerError = {
                    logger.error(
                        markers.andAppend("ErrorMessage", itpError.message),
                        logName,
                        it.exception,
                    )
                },
            )
        }
        logger.info(markers, logName)
        return HttpResponse.noContent<Unit>()
    }
}

fun ITPServiceError.mapToHttpResponse(onBadRequest: () -> Unit, onServerError: (iTPErrorWithException: ITPServiceError.ITPErrorWithException) -> Unit): HttpResponse<*> {
    return when (this) {
        is ITPServiceError.AccountNotFound,
        is ITPServiceError.WalletNotFound,
        is ITPServiceError.PaymentIntentNotFound,
        is ITPServiceError.ISPBNotFound,
        -> {
            onBadRequest()
            HttpResponse.badRequest(ResponseTO("4001", "Not found"))
        }

        is ITPServiceError.SufficientBalance -> {
            onBadRequest()
            HttpResponse.badRequest(ResponseTO("4002", "SufficientBalance"))
        }

        is ITPServiceError.NestedOnePixPayError -> {
            onBadRequest()
            return nested.mapToHttpResponse()
        }

        is ITPServiceError.UnknownFinancialInstitution -> {
            onBadRequest()
            HttpResponse.badRequest(ResponseTO("4005", "UnknownFinancialInstitution"))
        }

        is ITPServiceError.ITPErrorWithException.ITPInternalError -> {
            onServerError(this)
            HttpResponse.serverError<Unit>()
        }

        is ITPServiceError.ITPErrorWithException.ITPProviderError -> {
            onServerError(this)
            HttpResponse.serverError<Unit>()
        }
    }
}

fun OnePixPayErrors.mapToHttpResponse(): HttpResponse<ResponseTO> {
    return when (this) {
        OnePixPayErrors.AtLeastOneBillRequired -> HttpResponse.badRequest(ResponseTO("40031", "AtLeastOneBillRequired"))
        OnePixPayErrors.InvalidAmount -> HttpResponse.badRequest(ResponseTO("40032", "SufficientBalance"))
        OnePixPayErrors.OnlyActiveOrScheduledBillsAreAllowed -> HttpResponse.badRequest(ResponseTO("40033", "OnlyActiveOrScheduledBillsAreAllowed"))
        is OnePixPayErrors.SingleWalletRequired -> HttpResponse.badRequest(ResponseTO("40034", "SingleWalletRequired"))
    }
}

data class ITPConsentCallbackTO(
    val state: String,
    val code: String?,
    val idToken: String?,
    val error: String?,
    val errorDescription: String?,
)