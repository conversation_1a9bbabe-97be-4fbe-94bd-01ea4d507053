package ai.friday.billpayment.adapters.msisdnauth

import ai.friday.billpayment.adapters.converters.AccountIdConverter
import ai.friday.billpayment.adapters.converters.MsisdnAuthIdConverter
import ai.friday.billpayment.adapters.dynamodb.AbstractBillPaymentDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_PARTITION_KEY
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_RANGE_KEY
import ai.friday.billpayment.adapters.dynamodb.GlobalSecondaryIndexNames
import ai.friday.billpayment.adapters.dynamodb.INDEX_1
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.integrations.MsisdnAuthRepository
import ai.friday.billpayment.app.msisdnauth.MsisdnAuth
import ai.friday.billpayment.app.msisdnauth.MsisdnAuthId
import jakarta.inject.Singleton
import java.time.ZonedDateTime
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.internal.converter.attribute.ZonedDateTimeAsStringAttributeConverter
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbConvertedBy
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

private const val CREATED_AT = "createdAt"
private const val UPDATED_AT = "updatedAt"
private const val MSISDN_AUTH_PREFIX = "MSISDN_AUTH"

@Singleton
class MsisdnAuthDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<MsisdnAuthEntity>(cli, MsisdnAuthEntity::class.java)

@Singleton
class MsisdnAuthDbRepository(
    private val client: MsisdnAuthDynamoDAO,
) : MsisdnAuthRepository {
    override fun save(msisdnAuth: MsisdnAuth) {
        client.save(
            MsisdnAuthEntity().apply {
                primaryKey = msisdnAuth.id
                scanKey = "$MSISDN_AUTH_PREFIX#${msisdnAuth.accountId.value}"
                accountId = msisdnAuth.accountId
                createdAt = msisdnAuth.createdAt
                updatedAt = msisdnAuth.updatedAt
                index1HashKey = this.scanKey
                index1RangeKey = this.primaryKey.value
            },
        )
    }

    override fun find(msisdnAuthId: MsisdnAuthId): MsisdnAuth? {
        return client.findByPartitionKey(partitionKey = msisdnAuthId.value).singleOrNull()
            ?.toMsisdnAuth()
    }

    override fun findByAccountId(accountId: AccountId): MsisdnAuth? {
        return client.findByPartitionKeyOnIndex(
            GlobalSecondaryIndexNames.GSIndex1,
            "$MSISDN_AUTH_PREFIX#${accountId.value}",
        )
            .singleOrNull()
            ?.toMsisdnAuth()
    }
}

fun MsisdnAuthEntity.toMsisdnAuth(): MsisdnAuth {
    return MsisdnAuth(
        id = this.primaryKey,
        accountId = this.accountId,
        createdAt = this.createdAt,
    )
}

@DynamoDbBean
class MsisdnAuthEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    @get:DynamoDbConvertedBy(value = MsisdnAuthIdConverter::class)
    lateinit var primaryKey: MsisdnAuthId // MSISDN_AUTH-189239-********-91293-9123

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String // ACCOUNT_ID

    @get:DynamoDbConvertedBy(value = ZonedDateTimeAsStringAttributeConverter::class)
    @get:DynamoDbAttribute(value = CREATED_AT)
    lateinit var createdAt: ZonedDateTime

    @get:DynamoDbConvertedBy(value = AccountIdConverter::class)
    @get:DynamoDbAttribute(value = "AccountId")
    lateinit var accountId: AccountId

    @get:DynamoDbConvertedBy(value = ZonedDateTimeAsStringAttributeConverter::class)
    @get:DynamoDbAttribute(value = UPDATED_AT)
    lateinit var updatedAt: ZonedDateTime

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    lateinit var index1HashKey: String // MSISDN_AUTH#ACCOUNT_ID

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    lateinit var index1RangeKey: String
}