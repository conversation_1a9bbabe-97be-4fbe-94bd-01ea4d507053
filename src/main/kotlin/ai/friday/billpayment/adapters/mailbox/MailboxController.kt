package ai.friday.billpayment.adapters.mailbox

import ai.friday.billpayment.adapters.parsers.parseListFrom
import ai.friday.billpayment.app.account.Role.Code.BACKOFFICE
import ai.friday.billpayment.app.integrations.MailboxListsService
import ai.friday.billpayment.app.mailbox.MailboxListType
import ai.friday.billpayment.app.mailbox.RefreshMailboxGlobalData
import ai.friday.billpayment.log
import ai.friday.billpayment.measure
import io.micronaut.http.HttpResponse
import io.micronaut.http.MediaType
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Delete
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.http.multipart.CompletedFileUpload
import io.micronaut.security.annotation.Secured
import jakarta.inject.Singleton
import org.slf4j.LoggerFactory

@Secured(BACKOFFICE)
@Controller("/mailbox")
@Singleton
class MailboxController(
    private val mailboxListsService: MailboxListsService,
    private val refreshMailboxGlobalData: RefreshMailboxGlobalData,
) {
    private val logger = LoggerFactory.getLogger(MailboxController::class.java)

    @Post("/refresh-global-data")
    fun refreshGlobalData(): HttpResponse<*> {
        return try {
            val (_, elapsed) = measure {
                refreshMailboxGlobalData.execute()
            }
            logger.info(log("elapsed_time" to elapsed), "MailboxController#refreshGlobalData")
            HttpResponse.ok<Any>()
        } catch (ex: Exception) {
            logger.error("MailboxController#refreshGlobalData", ex)
            HttpResponse.serverError("Server Error")
        }
    }

    @Get("/allowed-list")
    fun allowedList() = findGlobal(MailboxListType.ALLOWED)

    @Post(value = "/allowed-list", consumes = [MediaType.MULTIPART_FORM_DATA], produces = [MediaType.APPLICATION_JSON])
    fun saveAllowedList(document: CompletedFileUpload) = saveGlobal(document, MailboxListType.ALLOWED)

    @Delete("/allowed-list/{email}")
    fun deleteAllowedList(@PathVariable("email") email: String) = deleteGlobal(email, MailboxListType.ALLOWED)

    @Get("/block-list")
    fun blockList() = findGlobal(MailboxListType.BLOCKED)

    @Post(value = "/block-list", consumes = [MediaType.MULTIPART_FORM_DATA], produces = [MediaType.APPLICATION_JSON])
    fun saveBlockList(document: CompletedFileUpload) = saveGlobal(document, MailboxListType.BLOCKED)

    @Delete("/block-list/{email}")
    fun deleteBlockList(@PathVariable("email") email: String) = deleteGlobal(email, MailboxListType.BLOCKED)

    @Get("/dnd-list")
    fun dndList() = findGlobal(MailboxListType.DO_NOT_DISTURB)

    @Post(value = "/dnd-list", consumes = [MediaType.MULTIPART_FORM_DATA], produces = [MediaType.APPLICATION_JSON])
    fun saveDndList(document: CompletedFileUpload) = saveGlobal(document, MailboxListType.DO_NOT_DISTURB)

    @Delete("/dnd-list/{email}")
    fun deleteDndList(@PathVariable("email") email: String) = deleteGlobal(email, MailboxListType.DO_NOT_DISTURB)

    private fun findGlobal(type: MailboxListType): HttpResponse<*> {
        return try {
            val (allowedList, elapsed) = measure { mailboxListsService.findGlobal(type) }

            logger.info(log("elapsed_time" to elapsed), "MailboxController#find#${type.name}")

            HttpResponse.ok<Any>(allowedList)
        } catch (ex: Exception) {
            logger.error("MailboxController#find#${type.name}", ex)
            HttpResponse.serverError("Server Error")
        }
    }

    private fun saveGlobal(document: CompletedFileUpload, type: MailboxListType): HttpResponse<*> {
        return try {
            val items = parseListFrom<String>(document.inputStream.readBytes().toString(Charsets.UTF_8))
            mailboxListsService.putGlobal(type, items)
            logger.info(log("items_size" to items.size), "MailboxController#save#${type.name}")
            HttpResponse.created(items).also {
                refreshGlobalData()
            }
        } catch (ex: Exception) {
            logger.error("MailboxController#save#${type.name}", ex)
            HttpResponse.serverError<Any>()
        }
    }

    private fun deleteGlobal(email: String, type: MailboxListType): HttpResponse<*> {
        val markers = log("email" to email)
        return try {
            mailboxListsService.deleteGlobal(type, email)
            logger.info(markers, "MailboxController#delete#${type.name}")
            HttpResponse.noContent<Any>().also {
                refreshGlobalData()
            }
        } catch (ex: Exception) {
            logger.error(markers, "MailboxController#delete#${type.name}", ex)
            HttpResponse.serverError<Any>()
        }
    }
}