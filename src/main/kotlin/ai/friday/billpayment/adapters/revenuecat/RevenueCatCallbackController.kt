package ai.friday.billpayment.adapters.revenuecat

import ai.friday.billpayment.adapters.messaging.SQSMessageHandlerConfiguration
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.morning.log.andAppend
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Secured(Role.Code.REVENUE_CAT_CALLBACK)
@Controller("/revenue-cat")
class RevenueCatCallbackController(
    private val publisher: MessagePublisher,
    private val messageHandlerConfiguration: SQSMessageHandlerConfiguration,
) {
    private val logger = LoggerFactory.getLogger(RevenueCatCallbackController::class.java)

    @Post("/webhooks")
    fun revenueCatCallbackController(
        @Body body: String,
    ): HttpResponse<*> {
        val map = parseObjectFrom<Map<String, Any>>(body)
        logger.info(
            Markers
                .append("request", body)
                .andAppend("parsedRequest", map),
            "RevenueCatCallbackController",
        )

        publisher.sendMessage(messageHandlerConfiguration.revenueCatQueueName, map)
        return HttpResponse.ok<Unit>()
    }
}