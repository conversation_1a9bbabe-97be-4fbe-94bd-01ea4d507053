package ai.friday.billpayment.adapters.pdf

import ai.friday.billpayment.app.integrations.PDFConverter
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import jakarta.inject.Singleton
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileOutputStream
import java.math.BigDecimal
import java.math.RoundingMode
import javax.xml.parsers.DocumentBuilder
import javax.xml.parsers.DocumentBuilderFactory
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import org.xhtmlrenderer.pdf.ITextFontResolver
import org.xhtmlrenderer.pdf.ITextRenderer

@Singleton
class FlyingSaucerPDFConverter(val configuration: PDFConfiguration) : PDFConverter {

    init {
        configuration.fontFiles.forEach { resourceName ->
            extractFontFile(resourceName)
        }
    }

    override fun convert(html: String, cropToFit: Boolean): ByteArray {
        val htmlWithPageSize = if (cropToFit) {
            val (originalWidth, originalHeight) = calculateDocumentPageSize(html)
            val divisor = configuration.dotsPerPixel.toBigDecimal()
            html.replace(
                "</head>",
                """
                <style type="text/css">
                    @page { size: ${BigDecimal(originalWidth).divide(divisor, RoundingMode.UP)}px ${BigDecimal(originalHeight).divide(divisor, RoundingMode.UP)}px; }
               </style>
                </head>
                """.trimIndent(),
            )
        } else { html }

        val renderer = createDocumentRenderer(htmlWithPageSize)

        val bos = ByteArrayOutputStream()
        renderer.createPDF(bos)
        renderer.finishPDF()
        return bos.toByteArray()
    }

    private fun calculateDocumentPageSize(html: String): Pair<Int, Int> {
        val renderer = createDocumentRenderer(html)
        return renderer.rootBox.width to renderer.rootBox.height
    }

    private fun createDocumentRenderer(html: String): ITextRenderer {
        val doc = getDocumentBuilder().parse(ByteArrayInputStream(html.toByteArray(charset("UTF-8"))))

        val renderer = ITextRenderer(configuration.dotsPerPoint, configuration.dotsPerPixel)

        val fontResolver: ITextFontResolver = renderer.getFontResolver()
        addAllFonts(fontResolver)

        renderer.setDocument(doc, null)
        renderer.layout()
        return renderer
    }

    // we use this method to create a DocumentBuild not so fanatic about XHTML
    private fun getDocumentBuilder(): DocumentBuilder {
        val fac = DocumentBuilderFactory.newInstance()
        fac.isNamespaceAware = false
        fac.isValidating = false
        fac.setFeature("http://xml.org/sax/features/namespaces", false)
        fac.setFeature("http://xml.org/sax/features/validation", false)
        fac.setFeature("http://apache.org/xml/features/nonvalidating/load-dtd-grammar", false)
        fac.setFeature("http://apache.org/xml/features/nonvalidating/load-external-dtd", false)
        return fac.newDocumentBuilder()
    }

    private fun addAllFonts(fontResolver: ITextFontResolver) {
        configuration.fontFiles.forEach { resourceName ->
            addFont(fontResolver, resourceName)
        }
    }

    private fun addFont(fontResolver: ITextFontResolver, resourceName: String) {
        try {
            val file = File("${configuration.folderName}/$resourceName")
            fontResolver.addFont(file.canonicalPath, "ISO-8859-1", true)
        } catch (e: Exception) {
            LOG.warn(Markers.append("resourceName", resourceName), "AddFont", e)
        }
    }

    private fun extractFontFile(resourceName: String) {
        try {
            val file = File("${configuration.folderName}/$resourceName")
            file.parentFile.mkdirs()
            val fontStream = Thread.currentThread().contextClassLoader.getResourceAsStream(resourceName)
            if (fontStream != null) {
                FileOutputStream(file).write(fontStream.readAllBytes())
            } else {
                LOG.error(Markers.append("resourceName", resourceName), "ExtractFontFileNotFound")
            }
        } catch (e: Exception) {
            LOG.error(Markers.append("resourceName", resourceName), "ExtractFontFile", e)
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(FlyingSaucerPDFConverter::class.java)
    }
}

@ConfigurationProperties("pdfConverter")
open class PDFConfiguration
@ConfigurationInject constructor(
    val fontFiles: List<String>,
    val folderName: String,
    val dotsPerPoint: Float,
    val dotsPerPixel: Int,
)