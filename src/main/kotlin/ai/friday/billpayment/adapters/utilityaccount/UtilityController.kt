package ai.friday.billpayment.adapters.utilityaccount

import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.utilityaccount.Utility
import ai.friday.billpayment.app.utilityaccount.UtilityAccountFormData
import ai.friday.billpayment.app.utilityaccount.UtilityAccountService
import ai.friday.billpayment.app.utilityaccount.UtilityAccountWithFormData
import ai.friday.billpayment.app.utilityaccount.UtilityConnectionMethod
import ai.friday.billpayment.app.utilityaccount.UtilityWarning
import com.fasterxml.jackson.annotation.JsonInclude
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.security.annotation.Secured

@Secured(Role.Code.OWNER)
@Controller("/utility")
@Version("2")
class UtilityController(private val utilityAccountService: UtilityAccountService) {
    @Get("/v2") // TODO - aparentemente ninguem está chamando esse cara
    fun listUtilityObjects(): HttpResponse<*> {
        // TODO: deve pegar os valores do banco e não do enum
        val utilities = Utility.entries.filter {
            it != Utility.UNKNOWN && it.enabled
        }.map {
            UtilityTO(
                utility = it.name,
                manual = it.method == UtilityConnectionMethod.MANUAL,
            )
        }

        return HttpResponse.ok(utilities)
    }

    @Get("/list")
    fun listUtilitiesDynamic(): HttpResponse<*> {
        val utilities = utilityAccountService.listAllEnabledUtilities().map {
            it.toUtilityAccountWithDynamicFormTO()
        }
        return HttpResponse.ok(utilities)
    }

    @Get
    fun listUtilities(): HttpResponse<*> {
        // TODO: deve pegar os valores do banco e não do enum
        return HttpResponse.ok(
            Utility.entries.filter {
                it != Utility.UNKNOWN && it.enabled
            },
        )
    }
}

private fun UtilityAccountWithFormData.toUtilityAccountWithDynamicFormTO(): UtilityAccountWithDynamicFormTO {
    return UtilityAccountWithDynamicFormTO(
        utility = this.utility.name,
        name = this.utility.viewName,
        category = this.category,
        method = this.utility.method.toTO(),
        form = this.form,
        warnings = this.utility.warnings.map { it.toWarningTO() },
    )
}

data class UtilityTO(
    val utility: String,
    val manual: Boolean,
)

data class UtilityAccountWithDynamicFormTO(
    val utility: String,
    val name: String,
    val category: String,
    val method: Char,
    val form: List<UtilityAccountFormData>, // TODO - poderia ter um TO para esse cara em algum momento
    @JsonInclude(JsonInclude.Include.ALWAYS)
    val warnings: List<UtilityWarningTO>,
)

data class UtilityWarningTO(
    val level: String,
    val title: String,
    val description: String,
)

fun UtilityWarning.toWarningTO(): UtilityWarningTO {
    return UtilityWarningTO(
        level = when (level) {
            UtilityWarning.Level.WARNING -> "warn"
        },
        title = title,
        description = description,
    )
}