package ai.friday.billpayment.adapters.utilityaccount

import ai.friday.billpayment.adapters.api.getWallet
import ai.friday.billpayment.adapters.api.toWalletId
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.integrations.UserEvent
import ai.friday.billpayment.app.integrations.UserEventService
import ai.friday.billpayment.app.utilityaccount.Utility
import ai.friday.billpayment.app.utilityaccount.UtilityAccount
import ai.friday.billpayment.app.utilityaccount.UtilityAccountConnectionDetails
import ai.friday.billpayment.app.utilityaccount.UtilityAccountConnectionStatus
import ai.friday.billpayment.app.utilityaccount.UtilityAccountError
import ai.friday.billpayment.app.utilityaccount.UtilityAccountId
import ai.friday.billpayment.app.utilityaccount.UtilityAccountService
import ai.friday.billpayment.app.utilityaccount.UtilityConnectionMethod
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Delete
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import kotlin.reflect.KClass
import kotlin.reflect.full.memberProperties
import net.logstash.logback.marker.Markers
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Secured(Role.Code.OWNER)
@Controller("/utilityAccount")
@Version("2")
class UtilityAccountController(
    private val service: UtilityAccountService,
    private val userEventService: UserEventService,
) {

    private val logger = LoggerFactory.getLogger(UtilityAccountController::class.java)

    @Post("/connect")
    fun connect(authentication: Authentication, @Body body: UtilityAccountConnectionRequestTO): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val walletId = authentication.toWalletId()
        val logName = "UtilityAccountController#connect"
        val markers = append("accountId", accountId)
            .andAppend("walletId", walletId.value)
            .andAppend("body", body)
            .andAppend("utility", body.utility)

        val utility = Utility.of(body.utility)

        if (utility == null) {
            val errorResponseTO = ErrorResponseTO(
                code = "4000",
                message = "A concessionária não existe",
            )
            logger.error(
                markers,
                logName,
            )
            return HttpResponse.badRequest(errorResponseTO)
        }

        userEventService.save(UserEvent(accountId, "ua_connection_requested", metadata = mapOf("utility" to body.utility)))

        val connectionDetails = try {
            UtilityAccountConnectionDetails.create(utility, body.connectionDetails)
        } catch (ex: IllegalArgumentException) {
            val errorResponseTO = ErrorResponseTO(
                code = "4000",
                message = ex.message ?: "Não é possível criar uma conta de consumo com esses dados",
            )
            logger.error(
                markers,
                logName,
            )
            return HttpResponse.badRequest(errorResponseTO)
        }

        val wallet = authentication.getWallet()

        val httpResponse = service.create(
            connectionDetails = connectionDetails,
            walletFounderAccountId = wallet.founder.accountId,
            walletId = wallet.id,
            utility = utility,
            connectionMethod = utility.method,
            currentAccountId = accountId,
        )
            .fold(
                ifLeft = { error ->
                    markers.andAppend("error", error)
                    when (error) {
                        is UtilityAccountError.AccountAlreadyCreated -> {
                            val errorResponseTO = ErrorResponseTO(
                                code = "4002",
                                message = "Uma conta já existe para essa concessionária com esses dados de login",
                            )
                            logger.warn(
                                markers,
                                logName,
                            )
                            HttpResponse.badRequest(errorResponseTO)
                        }

                        is UtilityAccountError.CannotEncryptPassword -> {
                            val errorResponseTO = ErrorResponseTO(
                                code = "5000",
                                message = "Não foi possível ler a senha",
                            )
                            logger.error(
                                markers,
                                logName,
                            )
                            HttpResponse.serverError(errorResponseTO)
                        }

                        else -> {
                            val errorResponseTO = ErrorResponseTO(
                                code = "5000",
                                message = "Um erro inexperado aconteceu",
                            )
                            logger.error(
                                markers,
                                logName,
                            )
                            HttpResponse.serverError(errorResponseTO)
                        }
                    }
                },
                ifRight = { utilityAccount ->
                    val response = ConnectUtilityResponseTO(status = utilityAccount.status)
                    logger.info(markers.andAppend("response", response), logName)
                    HttpResponse.created(response)
                },
            )
        return httpResponse
    }

    @Post("/reconnect/{utilityAccountId}")
    fun reconnect(
        authentication: Authentication,
        @PathVariable utilityAccountId: String,
        @Body body: UtilityAccountReconnectionRequestTO,
    ): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val walletId = authentication.toWalletId()
        val logName = "UtilityAccountController#reconnect"
        val markers = append("accountId", accountId)
            .andAppend("walletId", walletId.value)
            .andAppend("utilityAccountId", utilityAccountId)
            .andAppend("body", body)

        userEventService.save(UserEvent(accountId, "ua_reconnection_requested", metadata = mapOf("ua_id" to utilityAccountId)))

        val httpResponse = service.reconnect(
            utilityAccountId = UtilityAccountId(utilityAccountId),
            currentAccountId = accountId,
            wallet = authentication.getWallet(),
            connectionDetailsMap = body.connectionDetails,
        )
            .fold(
                ifLeft = { error ->
                    markers.andAppend("error", error)
                    when (error) {
                        is UtilityAccountError.AccountAlreadyCreated -> {
                            val errorResponseTO = ErrorResponseTO(
                                code = "4002",
                                message = "Uma conta já existe para essa concessionária com esses dados de login",
                            )
                            logger.warn(
                                markers,
                                logName,
                            )
                            HttpResponse.badRequest(errorResponseTO)
                        }

                        is UtilityAccountError.CannotEncryptPassword -> {
                            val errorResponseTO = ErrorResponseTO(
                                code = "5000",
                                message = "Não foi possível ler a senha",
                            )
                            logger.error(
                                markers,
                                logName,
                            )
                            HttpResponse.serverError(errorResponseTO)
                        }

                        is UtilityAccountError.AccountNotFound -> {
                            val errorResponseTO = ErrorResponseTO(
                                code = ConnectUtilityErrorResponse.UTILITY_ACCOUNT_NOT_FOUND.name,
                                message = "Conta de consumo não encontrada",
                            )
                            logger.error(
                                markers,
                                logName,
                            )
                            HttpResponse.notFound(errorResponseTO)
                        }

                        else -> {
                            val errorResponseTO = ErrorResponseTO(
                                code = "5000",
                                message = "Um erro inexperado aconteceu",
                            )
                            logger.error(
                                markers,
                                logName,
                            )
                            HttpResponse.serverError(errorResponseTO)
                        }
                    }
                },
                ifRight = { utilityAccount ->
                    val response = ConnectUtilityResponseTO(status = utilityAccount.status)
                    logger.info(markers.andAppend("response", response), logName)
                    HttpResponse.created(response)
                },
            )
        return httpResponse
    }

    @Get("/list")
    fun listAccountsWithCategory(authentication: Authentication): HttpResponse<*> {
        val list = service.listAllAccounts(authentication.getWallet().id)
        val markers = append("accountId", authentication.toAccountId())
        val utilities = service.listAllUtilities()
        val accounts = list.map {
            val (loginInfo, _) = it.connectionDetails.loginInfo()
            UtilityAccountResponseTO(
                id = it.id.value,
                login = loginInfo,
                status = it.status,
                statusMessage = it.statusMessage,
                utility = UtilityResponseTO(
                    utility = it.utility.name,
                    name = it.utility.viewName,
                    category = utilities.first { current -> current.utility == it.utility }.category,
                ),
                utilityConnectionMethod = it.connectionMethod.toTO(),
            )
        }
        logger.info(markers.andAppend("accounts", accounts), "UtilityAccountController#listAccountsWithCategory")
        return HttpResponse.ok(accounts)
    }

    @Delete("/{utilityAccountId}")
    fun disconnectAccount(authentication: Authentication, @PathVariable utilityAccountId: String): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val markers = append("accountId", accountId).andAppend("utilityAccountId", utilityAccountId)
        val result = service.disconnectAccount(UtilityAccountId(utilityAccountId), authentication.getWallet())

        userEventService.save(UserEvent(accountId, "ua_disconnection_requested", metadata = mapOf("ua_id" to utilityAccountId)))

        result.getOrElse {
            when (it) {
                UtilityAccountError.AccountNotFound -> {
                    return HttpResponse.notFound(
                        ErrorResponseTO(
                            code = ConnectUtilityErrorResponse.UTILITY_ACCOUNT_NOT_FOUND.name,
                            message = "Conta de consumo não encontrada",
                        ),
                    )
                }

                UtilityAccountError.CannotDisconnectUtilityAccount -> {
                    return HttpResponse.badRequest(
                        ErrorResponseTO(
                            code = ConnectUtilityErrorResponse.CANNOT_DISCONNECT_ACCOUNT.name,
                            message = "Não é possível desconectar uma conta de consumo com este status",
                        ),
                    )
                }

                else -> {
                    return HttpResponse.serverError(Unit)
                }
            }
        }
        logger.info(markers, "UtilityAccountController#disconnect")
        return HttpResponse.noContent<Unit>()
    }

    @Get("/{utilityAccountId}")
    fun find(authentication: Authentication, @PathVariable utilityAccountId: String): HttpResponse<*> {
        val markers = Markers
            .append("utilityAccountId", utilityAccountId)
            .andAppend("accountId", authentication.toAccountId().value)
            .andAppend("walletId", authentication.toWalletId().value)

        val utilityAccount = try {
            service.find(authentication.toWalletId(), UtilityAccountId(utilityAccountId))
        } catch (e: Exception) {
            logger.error(markers, "UtilityAccountController#find", e)
            return HttpResponse.serverError<Unit>()
        }

        if (utilityAccount == null) {
            logger.warn(markers, "UtilityAccountController#find")
            return HttpResponse.notFound<Unit>()
        }

        logger.info(markers, "UtilityAccountController#find")

        return HttpResponse.ok(utilityAccount.toFindResponseTO())
    }

    @Post("/requestConnection")
    fun requestConnection(
        authentication: Authentication,
        @Body body: UtilityAccountRequestConnectionRequestTO,
    ): HttpResponse<*> {
        val markers = append("accountId", authentication.toAccountId().value)
            .andAppend("name", body.name)
            .andAppend("uf", body.uf)

        logger.info(markers, "UtilityAccountController#requestConnection")

        return HttpResponse.ok(Unit)
    }
}

data class UtilityAccountConnectionRequestTO(
    val utility: String,
    val connectionDetails: Map<String, String>,
)

data class UtilityAccountReconnectionRequestTO(
    val connectionDetails: Map<String, String>,
)

data class UtilityAccountRequestConnectionRequestTO(
    val name: String,
    val uf: String,
)

data class ConnectUtilityResponseTO(
    val status: UtilityAccountConnectionStatus,
)

data class UtilityAccountResponseTO(
    val id: String,
    val login: String,
    val status: UtilityAccountConnectionStatus,
    val statusMessage: String?,
    val utility: UtilityResponseTO,
    val utilityConnectionMethod: Char,
)

data class FindUtiltiyAccountResponseTO(
    val id: String,
    val utility: Utility,
    val status: UtilityAccountConnectionStatus,
    val connectionDetails: Map<String, String>,
)

data class UtilityResponseTO(
    val utility: String,
    val name: String,
    val category: String,
)

fun UtilityConnectionMethod.toTO(): Char {
    return when (this) {
        UtilityConnectionMethod.FLOW -> 'F'
        UtilityConnectionMethod.SCRAPING -> 'S'
        UtilityConnectionMethod.MANUAL -> 'M'
        UtilityConnectionMethod.USER -> 'U'
    }
}

fun UtilityAccount.toFindResponseTO(): FindUtiltiyAccountResponseTO {
    return FindUtiltiyAccountResponseTO(
        id = this.id.value,
        utility = this.utility,
        status = this.status,
        connectionDetails = toConnectionDetailsMap(this.connectionDetails),
    )
}

private fun <T : Any> toConnectionDetailsMap(obj: T): Map<String, String> {
    return (obj::class as KClass<T>).memberProperties
        .filter { it.name != "password" }
        .associate { prop ->
            prop.name to (prop.get(obj)?.let { it.toString() } ?: "")
        }
}

data class ErrorResponseTO(
    val code: String,
    val message: String,
)

enum class ConnectUtilityErrorResponse(val code: String) {
    UTILITY_ACCOUNT_NOT_FOUND("4004"),
    CANNOT_DISCONNECT_ACCOUNT("4006"),
    SERVER_ERROR("5000"),
}