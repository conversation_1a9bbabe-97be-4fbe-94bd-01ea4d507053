package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.integrations.OnboardingTestPixRepository
import ai.friday.billpayment.app.onboarding.OnboardingTestPix
import ai.friday.billpayment.app.onboarding.OnboardingTestPixStatus
import ai.friday.morning.date.dateTimeFormat
import jakarta.inject.Singleton
import java.time.ZonedDateTime
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.internal.converter.attribute.ZonedDateTimeAsStringAttributeConverter
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbConvertedBy
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

const val ONBOARDING_TEST_PIX_PARTITION_KEY = "ONBOARDING_TEST_PIX"

@Singleton
class OnboardingTestPixDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<OnboardingTestPixEntity>(cli, OnboardingTestPixEntity::class.java)

@Singleton
class OnboardingTestPixDbRepository(private val client: OnboardingTestPixDynamoDAO) : OnboardingTestPixRepository {
    override fun save(onboardingTestPix: OnboardingTestPix) {
        val onboardingTestPixEntity = OnboardingTestPixEntity().apply {
            primaryKey = ONBOARDING_TEST_PIX_PARTITION_KEY
            scanKey = onboardingTestPix.accountId.value
            createdAt = onboardingTestPix.createdAt
            updatedAt = onboardingTestPix.updatedAt
            remindedAt = onboardingTestPix.remindedAt?.format(dateTimeFormat)
            billIds = onboardingTestPix.billIds.map { it.value }
            status = onboardingTestPix.status
            billCreatedCounter = onboardingTestPix.billIds.size
            billPaidCounter = onboardingTestPix.billPaidCounter
            index1HashKey = ONBOARDING_TEST_PIX_PARTITION_KEY
            index1RangeKey = onboardingTestPix.status
        }

        client.save(onboardingTestPixEntity)
    }

    override fun findByStatus(onboardingTestPixStatus: OnboardingTestPixStatus): List<OnboardingTestPix> {
        return client.findByPartitionKeyAndScanKeyOnIndex(GlobalSecondaryIndexNames.GSIndex1, ONBOARDING_TEST_PIX_PARTITION_KEY, onboardingTestPixStatus.name)
            .map { it.toOnboardingTestPix() }
    }

    override fun findByAccountId(accountId: AccountId): OnboardingTestPix? {
        return client.findByPrimaryKey(ONBOARDING_TEST_PIX_PARTITION_KEY, accountId.value)?.toOnboardingTestPix()
    }
}

@DynamoDbBean
class OnboardingTestPixEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String // ONBOARDING_TEST_PIX

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String // ACCOUNT_ID

    @get:DynamoDbConvertedBy(value = ZonedDateTimeAsStringAttributeConverter::class)
    @get:DynamoDbAttribute(value = "CreatedAt")
    lateinit var createdAt: ZonedDateTime

    @get:DynamoDbConvertedBy(value = ZonedDateTimeAsStringAttributeConverter::class)
    @get:DynamoDbAttribute(value = "UpdatedAt")
    lateinit var updatedAt: ZonedDateTime

    @get:DynamoDbAttribute(value = "RemindedAt")
    var remindedAt: String? = null

    @get:DynamoDbAttribute(value = "BillIds")
    lateinit var billIds: List<String>

    @get:DynamoDbAttribute(value = "Status")
    var status = OnboardingTestPixStatus.IN_PROGRESS

    @get:DynamoDbAttribute(value = "BillCreatedCounter")
    var billCreatedCounter: Int = 0

    @get:DynamoDbAttribute(value = "BillPaidCounter")
    var billPaidCounter: Int = 0

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    lateinit var index1HashKey: String // ONBOARDING_TEST_PIX

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    lateinit var index1RangeKey: OnboardingTestPixStatus // Status
}

fun OnboardingTestPixEntity.toOnboardingTestPix() = OnboardingTestPix(
    accountId = AccountId(scanKey),
    createdAt = createdAt,
    updatedAt = updatedAt,
    remindedAt = remindedAt?.let { ZonedDateTime.parse(it, dateTimeFormat) },
    billIds = billIds.map { BillId(it) },
    billPaidCounter = billPaidCounter,
    status = status,
)