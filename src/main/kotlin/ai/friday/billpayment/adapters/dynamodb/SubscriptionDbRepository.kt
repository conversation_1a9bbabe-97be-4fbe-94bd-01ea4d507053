package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.integrations.SubscriptionRepository
import ai.friday.billpayment.app.recurrence.RecurrenceId
import ai.friday.billpayment.app.subscription.Subscription
import ai.friday.billpayment.app.subscription.SubscriptionPaymentStatus
import ai.friday.billpayment.app.subscription.SubscriptionStatus
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.date.dateFormat
import jakarta.inject.Singleton
import java.time.LocalDate
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

private const val SubscriptionPartitionKey = "SUBSCRIPTION"

@Singleton
class SubscriptionDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<SubscriptionEntity>(cli, SubscriptionEntity::class.java)

@Singleton
class SubscriptionDbRepository(private val client: SubscriptionDynamoDAO) : SubscriptionRepository {
    override fun save(subscription: Subscription) {
        val entity = SubscriptionEntity().apply {
            partitionKey = SubscriptionPartitionKey
            sortKey = subscription.accountId.value
            gSIndex1PartitionKey = SubscriptionPartitionKey
            gSIndex1SortKey = subscription.buildIndex1ScanKey()
            document = subscription.document.value
            status = subscription.status
            amount = subscription.amount
            dayOfMonth = subscription.dayOfMonth
            recurrenceId = subscription.recurrenceId.value
            paymentStatus = subscription.paymentStatus
            walletId = subscription.walletId.value
            nextEffectiveDueDate = subscription.nextEffectiveDueDate.format(dateFormat)
        }
        client.save(entity)
    }

    override fun find(accountId: AccountId): Subscription? {
        return client.findByPrimaryKey(
            partitionKey = SubscriptionPartitionKey,
            sortKey = accountId.value,
        )?.toSubscription()
    }

    override fun findAll(): List<Subscription> {
        return client.findByPartitionKey(
            partitionKey = SubscriptionPartitionKey,
        ).map { it.toSubscription() }
    }

    override fun find(status: SubscriptionStatus): List<Subscription> {
        return client.findBeginsWithOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex1,
            partitionKey = SubscriptionPartitionKey,
            sortKey = status.name,
        ).map { it.toSubscription() }
    }

    override fun find(
        subscriptionStatus: SubscriptionStatus,
        paymentStatus: SubscriptionPaymentStatus,
    ): List<Subscription> {
        return client.findByPartitionKeyAndScanKeyOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex1,
            partitionKey = SubscriptionPartitionKey,
            sortKey = buildIndex1ScanKey(subscriptionStatus, paymentStatus),
        ).map { it.toSubscription() }
    }
}

@DynamoDbBean
class SubscriptionEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var partitionKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var sortKey: String

    @get:DynamoDbAttribute(value = "Status")
    lateinit var status: SubscriptionStatus

    @get:DynamoDbAttribute(value = "Document")
    lateinit var document: String

    @get:DynamoDbAttribute(value = "Amount")
    var amount: Long = 0

    @get:DynamoDbAttribute(value = "DayOfMonth")
    var dayOfMonth: Int = 0

    @get:DynamoDbAttribute(value = "RecurrenceId")
    lateinit var recurrenceId: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    lateinit var gSIndex1PartitionKey: String

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    lateinit var gSIndex1SortKey: String

    @get:DynamoDbAttribute(value = "PaymentStatus")
    lateinit var paymentStatus: SubscriptionPaymentStatus

    @get:DynamoDbAttribute(value = "WalletId")
    lateinit var walletId: String

    @get:DynamoDbAttribute(value = "NextEffectiveDueDate")
    lateinit var nextEffectiveDueDate: String
}

private fun SubscriptionEntity.toSubscription() = Subscription(
    accountId = AccountId(sortKey),
    document = Document(document),
    status = status,
    amount = amount,
    dayOfMonth = dayOfMonth,
    recurrenceId = RecurrenceId(recurrenceId),
    paymentStatus = paymentStatus,
    walletId = WalletId(walletId),
    nextEffectiveDueDate = LocalDate.parse(nextEffectiveDueDate, dateFormat),
)

private fun Subscription.buildIndex1ScanKey() = buildIndex1ScanKey(status, paymentStatus)
private fun buildIndex1ScanKey(status: SubscriptionStatus, paymentStatus: SubscriptionPaymentStatus) =
    "$status#$paymentStatus"