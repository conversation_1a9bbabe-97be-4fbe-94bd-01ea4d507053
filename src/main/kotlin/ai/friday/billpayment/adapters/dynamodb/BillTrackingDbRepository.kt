package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.Sequencer
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillTrackingCalculateOptions
import ai.friday.billpayment.app.bill.BillTrackingCalculateOptions.CALCULATE
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.TrackableBill
import ai.friday.billpayment.app.bill.calculateOrQuery
import ai.friday.billpayment.app.bill.tracking.BillTrackingPropertiesBinder
import ai.friday.billpayment.app.integrations.BillTrackingRepository
import ai.friday.billpayment.app.payment.AmountCalculationModel
import ai.friday.billpayment.app.payment.DiscountData
import ai.friday.billpayment.app.payment.DiscountType
import ai.friday.billpayment.app.payment.FineData
import ai.friday.billpayment.app.payment.FineType
import ai.friday.billpayment.app.payment.InterestData
import ai.friday.billpayment.app.payment.InterestType
import ai.friday.billpayment.log
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormat
import jakarta.inject.Singleton
import java.math.BigDecimal
import java.time.LocalDate
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import org.slf4j.LoggerFactory
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@Singleton
class BillTrackingDynamoDAO(
    cli: DynamoDbEnhancedAsyncClient,
) : AbstractBillPaymentDynamoDAOAsync<BillTrackingEntity>(cli, BillTrackingEntity::class.java)

@Singleton
class BillTrackingDbRepository(
    private val client: BillTrackingDynamoDAO,
    properties: BillTrackingPropertiesBinder,
) : BillTrackingRepository {
    private val logger = LoggerFactory.getLogger(BillTrackingDbRepository::class.java)

    private val calculateSequencer = Sequencer(properties.calculate.partitions)
    private val querySequencer = Sequencer(properties.query.partitions)

    private enum class TrackableBillSource {
        DDA,
        OTHERS,
    }

    override fun create(
        trackableBill: TrackableBill,
        processingDate: LocalDate,
    ) {
        val calculateModel = trackableBill.calculateOrQuery()
        val partition = if (calculateModel == CALCULATE) calculateSequencer::next else querySequencer::next
        save(trackableBill, calculateModel, partition = partition.invoke(), processingDate = processingDate)
    }

    override fun update(
        trackableBill: TrackableBill,
        processingDate: LocalDate,
    ) {
        update(trackableBill, trackableBill.calculateOrQuery(), processingDate)
    }

    override fun update(
        trackableBill: TrackableBill,
        billTrackingType: BillTrackingCalculateOptions,
        processingDate: LocalDate,
    ) {
        val partition = if (billTrackingType == CALCULATE) calculateSequencer::next else querySequencer::next
        save(trackableBill, billTrackingType, partition = partition.invoke(), processingDate = processingDate)
    }

    override fun save(
        trackableBill: TrackableBill,
        billTrackingType: BillTrackingCalculateOptions,
        processingDate: LocalDate,
        partition: Int?,
    ) {
        val isDda = if (trackableBill.addedByDda) TrackableBillSource.DDA else TrackableBillSource.OTHERS

        val entity =
            BillTrackingEntity().apply {
                primaryKey = trackableBill.billId.value
                scanKey = BillTrackingIndexPartitionKey
                billId = trackableBill.billId.value
                created = trackableBill.created.format(DateTimeFormatter.ISO_DATE_TIME)
                lastUpdated = getZonedDateTime().format(DateTimeFormatter.ISO_DATE_TIME)
                this.source = isDda.name
                type = trackableBill.billType
                dueDate = trackableBill.dueDate.format(dateFormat)
                digitableBarcode = trackableBill.barCode.digitable
                amount = trackableBill.amount
                amountCalculationModel = trackableBill.amountCalculationModel
                fine =
                    FineDataEntity2(
                        type = trackableBill.fineData.type,
                        value = trackableBill.fineData.value,
                        date = trackableBill.fineData.date?.format(dateFormat),
                    )
                interest =
                    InterestDataEntity2(
                        type = trackableBill.interestData.type,
                        value = trackableBill.interestData.value,
                        date = trackableBill.interestData.date?.format(dateFormat),
                    )
                discount =
                    DiscountDataEntity2(
                        type = trackableBill.discountData.type,
                        value1 = trackableBill.discountData.value1,
                        date1 = trackableBill.discountData.date1?.format(dateFormat),
                        value2 = trackableBill.discountData.value2,
                        date2 = trackableBill.discountData.date2?.format(dateFormat),
                        value3 = trackableBill.discountData.value3,
                        date3 = trackableBill.discountData.date3?.format(dateFormat),
                    )
                abatement = trackableBill.abatement
                index1RangeKey = "$billTrackingType#${(processingDate)}"
            }

        if (partition != null) entity.index1HashKey = "$BillTrackingIndexPartitionKey#$partition"

        logger.info(
            log(
                "bill_id" to entity.primaryKey,
                "hash_key_1" to entity.index1HashKey,
                "range_key_1" to entity.index1RangeKey,
            ),
            "BillTrackingDbRepository#save",
        )

        client.save(entity, true).block()
    }

    override fun findById(billId: BillId): Mono<TrackableBill> =
        client.findByPrimaryKey(billId.value, BillTrackingIndexPartitionKey).map { it.toTrackableBill() }

    override fun findBillsByPartition(
        calculateOptions: BillTrackingCalculateOptions,
        date: LocalDate,
        partition: Int,
    ): Flux<TrackableBill> =
        client
            .findByPartitionKeyAndBetweenSortKeysOnIndex(
                GlobalSecondaryIndexNames.GSIndex1,
                "$BillTrackingIndexPartitionKey#$partition",
                "$calculateOptions#${date.minusDays(60)}",
                "$calculateOptions#$date",
            ).map { it.toTrackableBill() }

    override fun remove(billId: BillId) {
        client.delete(billId.value, BillTrackingIndexPartitionKey).block()
    }

    private fun BillTrackingEntity.toTrackableBill() =
        TrackableBill(
            billId = BillId(this.billId),
            created = ZonedDateTime.parse(this.created, DateTimeFormatter.ISO_DATE_TIME),
            lastUpdated = ZonedDateTime.parse(this.lastUpdated, DateTimeFormatter.ISO_DATE_TIME),
            barCode = BarCode.ofDigitable(this.digitableBarcode),
            addedByDda = this.source == TrackableBillSource.DDA.name,
            billType = this.type,
            dueDate = LocalDate.parse(this.dueDate, dateFormat),
            amount = this.amount,
            amountCalculationModel = this.amountCalculationModel,
            fineData =
            FineData(
                type = this.fine?.type,
                value = this.fine?.value,
                date = this.fine?.date?.let { LocalDate.parse(it, dateFormat) },
            ),
            interestData =
            InterestData(
                type = this.interest?.type,
                value = this.interest?.value,
                date = this.interest?.date?.let { LocalDate.parse(it, dateFormat) },
            ),
            discountData =
            DiscountData(
                type = this.discount?.type,
                value1 = this.discount?.value1,
                date1 = this.discount?.date1?.let { LocalDate.parse(it, dateFormat) },
                value2 = this.discount?.value2,
                date2 = this.discount?.date2?.let { LocalDate.parse(it, dateFormat) },
                value3 = this.discount?.value3,
                date3 = this.discount?.date3?.let { LocalDate.parse(it, dateFormat) },
            ),
            abatement = this.abatement ?: BigDecimal.ZERO,
            isActive = true,
        )
}

private const val BillTrackingIndexPartitionKey = "BILL_TRACKING_REGISTER"

@DynamoDbBean
class BillTrackingEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String // BILL_ID

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String // BILL_TRACKING_REGISTER

    @get:DynamoDbAttribute(value = "BillId")
    lateinit var billId: String

    @get:DynamoDbAttribute(value = "DigitableBarcode")
    lateinit var digitableBarcode: String

    @get:DynamoDbAttribute(value = "Created")
    lateinit var created: String

    @get:DynamoDbAttribute(value = "LastUpdated")
    lateinit var lastUpdated: String

    @get:DynamoDbAttribute(value = "Source")
    lateinit var source: String

    @get:DynamoDbAttribute(value = "Type")
    lateinit var type: BillType

    @get:DynamoDbAttribute(value = "DueDate")
    lateinit var dueDate: String

    @get:DynamoDbAttribute(value = "Amount")
    var amount: Long = 0

    @get:DynamoDbAttribute(value = "AmountCalculationModel")
    var amountCalculationModel: AmountCalculationModel? = null

    @get:DynamoDbAttribute(value = "Fine")
    var fine: FineDataEntity2? = null

    @get:DynamoDbAttribute(value = "Interest")
    var interest: InterestDataEntity2? = null

    @get:DynamoDbAttribute(value = "Discount")
    var discount: DiscountDataEntity2? = null

    @get:DynamoDbAttribute(value = "Abatement")
    var abatement: BigDecimal? = null

    @get:DynamoDbSecondaryPartitionKey(indexNames = ["GSIndex1"])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    var index1HashKey: String? = null // BILL_TRACKING_REGISTER#partition

    @get:DynamoDbSecondarySortKey(indexNames = ["GSIndex1"])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    lateinit var index1RangeKey: String
}

@DynamoDbBean
data class FineDataEntity2(
    var type: FineType? = null,
    var value: BigDecimal? = null,
    var date: String? = null,
)

@DynamoDbBean
data class InterestDataEntity2(
    var type: InterestType? = null,
    var value: BigDecimal? = null,
    var date: String? = null,
)

@DynamoDbBean
data class DiscountDataEntity2(
    var type: DiscountType? = null,
    var value1: BigDecimal? = null,
    var date1: String? = null,
    var value2: BigDecimal? = null,
    var date2: String? = null,
    var value3: BigDecimal? = null,
    var date3: String? = null,
)