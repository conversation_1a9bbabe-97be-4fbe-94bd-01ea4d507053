package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.integrations.LimitRepository
import ai.friday.billpayment.app.limit.Limit
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import jakarta.inject.Singleton
import java.time.Duration
import java.time.format.DateTimeFormatter
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.extensions.annotations.DynamoDbAtomicCounter
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

const val LIMIT_PREFIX = "LIMIT"

@Singleton
class LimitDynamoDAO(cli: DynamoDbEnhancedClient) : AbstractBillPaymentDynamoDAO<LimitEntity>(cli, LimitEntity::class.java)

@Singleton
class LimitDbRepository(
    private val client: LimitDynamoDAO,
) : LimitRepository {
    override fun get(accountId: AccountId, key: String): Limit? {
        return client.findByPrimaryKey(accountId.value, generateScan(key))
            ?.takeIf { it.expirationTtl > getZonedDateTime().toInstant().epochSecond }
            ?.let { Limit(count = it.count, maxCount = it.maxCount) }
    }

    override fun create(accountId: AccountId, key: String, maxCount: Int, expiration: Duration) {
        client.save(
            LimitEntity().apply {
                partitionKey = accountId.value
                scanKey = generateScan(key)
                createdAt = getZonedDateTime().format(DateTimeFormatter.ISO_DATE_TIME)
                expirationTtl = getZonedDateTime().plus(expiration).toInstant().epochSecond
                count = 0
                this.maxCount = maxCount
            },
        )
    }

    override fun increment(accountId: AccountId, key: String) {
        client.findByPrimaryKey(accountId.value, generateScan(key))
            ?.let(client::update)
    }

    private fun generateScan(key: String) = "$LIMIT_PREFIX#$key"
}

@DynamoDbBean
class LimitEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var partitionKey: String // ACCOUNT_ID

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String // PREFIX#KEY

    @get:DynamoDbAttribute(value = "CreatedAt")
    lateinit var createdAt: String

    @get:DynamoDbAttribute(value = "ExpirationTTL")
    var expirationTtl: Long = 0

    @get:DynamoDbAtomicCounter(startValue = 0, delta = 1)
    @get:DynamoDbAttribute(value = "Count")
    var count: Int = 0

    @get:DynamoDbAttribute(value = "MaxCount")
    var maxCount: Int = 0
}