package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.integrations.UserDDANotificationTrackingRepository
import jakarta.inject.Singleton
import java.time.LocalDate
import reactor.core.publisher.Flux
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@Singleton
class UserDDANotificationTrackingDynamoDAO(cli: DynamoDbEnhancedAsyncClient) :
    AbstractBillPaymentDynamoDAOAsync<UserDDANotificationTrackingEntity>(cli, UserDDANotificationTrackingEntity::class.java)

@Singleton
class UserDDANotificationTrackingDbRepository(
    private val client: UserDDANotificationTrackingDynamoDAO,
) : UserDDANotificationTrackingRepository {

    override fun save(accountId: AccountId, date: LocalDate) {
        val entity = UserDDANotificationTrackingEntity().apply {
            primaryKey = accountId.value
            index1RangeKey = date.toString()
            index1HashKey = UserDDANotificationIndexPartitionKey
            scanKey = UserDDANotificationIndexPartitionKey
        }

        client.save(entity, true).block()
    }

    override fun findUsersToActivateNotification(date: LocalDate): Flux<String> =
        client.findByPrimaryKeyOnIndex(
            GlobalSecondaryIndexNames.GSIndex1,
            UserDDANotificationIndexPartitionKey,
            date.toString(),
            ConditionalType.LESS_THAN,
        ).map { it.primaryKey }

    override fun remove(accountId: AccountId) {
        client.delete(accountId.value, UserDDANotificationIndexPartitionKey).block()
    }
}

private const val UserDDANotificationIndexPartitionKey = "USER_DDA_NOTIFICATION"

@DynamoDbBean
class UserDDANotificationTrackingEntity {

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String // user ACCOUNT_ID

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String // USER_DDA_NOTIFICATION

    @get:DynamoDbSecondaryPartitionKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    lateinit var index1HashKey: String // USER_DDA_NOTIFICATION

    @get:DynamoDbSecondarySortKey(indexNames = [INDEX_1])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    lateinit var index1RangeKey: String // LocalDate
}