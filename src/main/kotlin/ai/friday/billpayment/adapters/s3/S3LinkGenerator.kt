package ai.friday.billpayment.adapters.s3

import ai.friday.billpayment.app.account.StoredObject
import ai.friday.billpayment.app.utils.PublicHttpLinkGenerator
import ai.friday.billpayment.app.utils.PublicHttpLinkGeneratorRetryConfiguration
import ai.friday.morning.log.andAppend
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import java.time.Duration
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.GetObjectRequest
import software.amazon.awssdk.services.s3.presigner.S3Presigner
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest

@Singleton
open class S3LinkGenerator(
    private val s3Client: S3Client,
    @Property(name = "aws.region") private val awsRegion: String,
) : PublicHttpLinkGenerator {

    override fun generate(storedObject: StoredObject, duration: Duration, contentType: String?, retryConfiguration: PublicHttpLinkGeneratorRetryConfiguration?): String? {
        val logName = "S3LinkGenerator#generate"
        if (retryConfiguration == null) {
            return doGenerate(storedObject, duration, contentType)
        }

        var attempt = 1
        var delayMillis = retryConfiguration.initialDelay.toMillis()

        while (attempt <= retryConfiguration.maxRetries) {
            val markers = append("attempt", attempt)
                .andAppend("retryConfiguration", retryConfiguration)
                .andAppend("storedObject", storedObject)
            try {
                val result = doGenerate(storedObject, duration, contentType)
                if (result != null) {
                    return result
                }
                logger.warn(markers, logName)
            } catch (e: Exception) {
                if (attempt == retryConfiguration.maxRetries - 1) {
                    logger.error(markers, logName, e)
                    throw e
                }
                logger.warn(markers, logName, e)
            }

            try {
                Thread.sleep(delayMillis)
            } catch (interrupted: InterruptedException) {
                Thread.currentThread().interrupt()
                logger.error(markers, logName, interrupted)
                throw interrupted
            }

            delayMillis = (delayMillis * 2).coerceAtMost(retryConfiguration.maxDelay.toMillis())
            attempt++
        }
        return null
    }

    private fun doGenerate(storedObject: StoredObject, duration: Duration, contentType: String?): String? {
        return runCatching {
            if (!objectExists(storedObject)) {
                return null
            }

            generatePresignedUrl(storedObject, duration, contentType)
        }.getOrElse { e ->
            // FIXME se RegisterController precisa tratar a exceção  e assim nao precisaria nem do log de do try/catch
            //  outra opção seria retornar um either
            logger.error("S3LinkGenerator", e)
            throw e
        }
    }

    private fun objectExists(storedObject: StoredObject): Boolean {
        return runCatching {
            s3Client.headObject {
                it.bucket(storedObject.bucket).key(storedObject.key)
            }
            true
        }.getOrDefault(false)
    }

    private fun generatePresignedUrl(storedObject: StoredObject, duration: Duration, contentType: String?): String {
        val region = Region.of(storedObject.region ?: awsRegion)

        return S3Presigner.builder()
            .region(region)
            .build().use { presigner ->
                val getObjectRequest = GetObjectRequest.builder()
                    .bucket(storedObject.bucket)
                    .key(storedObject.key)
                    .apply {
                        contentType?.let { responseContentType(it) }
                    }
                    .build()

                val presignRequest = GetObjectPresignRequest.builder()
                    .signatureDuration(duration)
                    .getObjectRequest(getObjectRequest)
                    .build()

                presigner.presignGetObject(presignRequest).url().toString()
            }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(S3LinkGenerator::class.java)
    }
}