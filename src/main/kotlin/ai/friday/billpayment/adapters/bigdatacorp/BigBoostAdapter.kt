package ai.friday.billpayment.adapters.bigdatacorp

import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.account.Address
import ai.friday.billpayment.app.account.Gender
import ai.friday.billpayment.app.account.PepQuery
import ai.friday.billpayment.app.account.PepQueryResult
import ai.friday.billpayment.app.integrations.AddressNotFoundException
import ai.friday.billpayment.app.integrations.BigDataServerException
import ai.friday.billpayment.app.integrations.BigDataService
import ai.friday.billpayment.app.integrations.DocumentBelongsToAMinorException
import ai.friday.billpayment.app.integrations.DocumentNotFoundException
import ai.friday.billpayment.app.integrations.PersonBasicInfo
import ai.friday.billpayment.app.integrations.PersonReceitaFederalInfo
import ai.friday.billpayment.app.register.kyc.KycDossier
import ai.friday.billpayment.app.register.kyc.KycDossierEmail
import ai.friday.billpayment.app.register.kyc.KycDossierMepQuery
import ai.friday.billpayment.app.register.kyc.KycDossierOfficialDocumentData
import ai.friday.billpayment.app.register.kyc.KycDossierPhone
import ai.friday.billpayment.app.register.kyc.KycDossierSanctionType
import ai.friday.billpayment.app.register.kyc.KycDossierSanctions
import ai.friday.billpayment.app.register.kyc.KycDossierTaxIdRegion
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.parseToZonedDateTime
import ai.friday.morning.date.dateFormat
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.left
import arrow.core.leftIfNull
import arrow.core.right
import com.fasterxml.jackson.annotation.JsonFormat
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.MediaType
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.client.HttpClientConfiguration
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.ApplicationConfiguration
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.reactivex.Flowable
import jakarta.inject.Singleton
import java.net.URL
import java.time.Duration
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.Markers
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@ConfigurationProperties("integrations.bigdatacorp")
class BigBoostConfiguration {
    lateinit var bigBoostHost: String
    lateinit var accessToken: String
    lateinit var peoplePath: String
    lateinit var companyPath: String
    lateinit var addressPath: String
}

@Singleton
class BigBoostHttpConfiguration(
    applicationConfiguration: ApplicationConfiguration,
    val configuration: HttpClientConfiguration,
) : HttpClientConfiguration(applicationConfiguration) {
    init {
        setReadTimeout(Duration.ofSeconds(90))
    }

    override fun getConnectionPoolConfiguration(): ConnectionPoolConfiguration =
        configuration.connectionPoolConfiguration
}

@Singleton
class BigBoostAdapter(
    httpConfiguration: BigBoostHttpConfiguration,
    private val configuration: BigBoostConfiguration,
) : BigDataService {

    private val MINOR_MESSAGE = "THIS CPF BELONGS TO A MINOR. DATE OF BIRTH IS NEEDED TO PROCESS REQUEST"

    private val httpClient = RxHttpClient.create(
        URL(configuration.bigBoostHost),
        httpConfiguration.apply {
            setConnectionPoolIdleTimeout(Duration.ofSeconds(10))
        },
    )

    private val httpClientLongTimeout = RxHttpClient.create(
        URL(configuration.bigBoostHost),
        httpConfiguration.apply {
            setReadTimeout(Duration.ofMinutes(5))
            setConnectionPoolIdleTimeout(Duration.ofSeconds(10))
        },
    )

    override fun getPersonName(cpf: String): Either<Exception, String> {
        val httpRequest = buildGetPeopleRequest(cpf)
        val call = httpClient.retrieve(httpRequest, Argument.of(String::class.java))
        val response = fetch(call)
        return extractPersonName(response)
    }

    override fun getPersonBasicInfo(cpf: String): Either<Exception, PersonBasicInfo> {
        val httpRequest = buildGetPeopleRequest(cpf)
        val call = httpClient.retrieve(httpRequest, Argument.of(PersonTO::class.java))
        val response = fetch(call)
        return extractPersonBasicInfo(response)
    }

    override fun getCompanyName(cnpj: String): Either<Exception, String> {
        val httpRequest = buildGetCompanyRequest(cnpj)
        val call = httpClient.retrieve(httpRequest, Argument.of(CompanyTO::class.java))
        val response = fetch(call)
        return extractCompanyName(response)
    }

    override fun getKycDossier(cpf: String): Either<Exception, KycDossier> {
        return try {
            val request = buildGetKycDossierTORequest(cpf)
            val call = httpClientLongTimeout.retrieve(request, Argument.STRING)
            val response = fetch(call)

            response.map {
                return parseBigBoostTO(it, cpf)
            }
        } catch (e: Exception) {
            Either.Left(e)
        }
    }

    fun getKycDossierRawData(
        cpf: String,
    ): Either<Exception, String> {
        val httpRequest = buildGetKycDossierTORequest(cpf)
        val call = httpClientLongTimeout.retrieve(httpRequest, Argument.of(String::class.java))
        return fetch(call)
    }

    override fun getAddress(zipCode: String): Either<Exception, Address> {
        val httpRequest = buildGetAddressRequest(zipCode)
        val call = httpClient.retrieve(httpRequest, Argument.of(AddressResponseTO::class.java))
        val response = fetch(call)
        return extractAddress(response)
    }

    private fun buildGetAddressRequest(zipCode: String): MutableHttpRequest<Map<String, String>> {
        val requestBody =
            mapOf("Datasets" to "basic_data", "q" to "zipcode{$zipCode}", "AccessToken" to configuration.accessToken)
        return HttpRequest.POST(configuration.addressPath, requestBody)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)
    }

    private fun buildGetPeopleRequest(cpf: String): MutableHttpRequest<Map<String, String>> {
        val requestBody =
            mapOf("Datasets" to "basic_data", "q" to "doc{$cpf}", "AccessToken" to configuration.accessToken)
        return HttpRequest.POST(configuration.peoplePath, requestBody)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)
    }

    private fun buildGetKycDossierTORequest(
        cpf: String,
        birthDate: LocalDate? = null,
    ): MutableHttpRequest<Map<String, String>> {
        val requestBody =
            mapOf(
                "Datasets" to "ondemand_rf_status,basic_data,phones,emails,kyc,media_profile_and_exposure",
                "q" to "doc{$cpf}${birthDate?.let { ",birthdate{${it.format(dateFormat)}" }}},dateformat{yyyy-MM-dd}",
                "AccessToken" to configuration.accessToken,
            )
        return HttpRequest.POST(configuration.peoplePath, requestBody)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)
    }

    private inline fun <reified T : Any> fetch(call: Flowable<T>): Either<Exception, T> {
        return try {
            Either.Right(call.firstOrError().blockingGet())
        } catch (e: HttpClientResponseException) {
            Either.Left<Exception>(BigDataServerException(e))
        }.log<T>()
    }

    private fun extractAddress(response: Either<Exception, AddressResponseTO>): Either<Exception, Address> {
        return response.map { address ->
            address.results[0].basicData.zipCode?.let {
                address.results[0].basicData.toAddress()
            }
        }.leftIfNull { AddressNotFoundException() }
    }

    private fun extractPersonBasicInfo(response: Either<Exception, PersonTO>): Either<Exception, PersonBasicInfo> {
        return response.map {
            PersonBasicInfo(
                name = it.results[0].basicData.name,
                birthDate = it.results[0].basicData.birthDate,
                motherName = it.results[0].basicData.motherName,
                fatherName = it.results[0].basicData.fatherName,
                gender = it.results[0].basicData.gender,
            )
        }.leftIfNull { DocumentNotFoundException() }
    }

    internal fun extractPersonName(response: Either<Exception, String>): Either<Exception, String> {
        return response.map { json ->
            val personTO = parseObjectFrom<PersonTO>(json)
            if (personTO.status.dateOfBirthValidation != null && personTO.status.dateOfBirthValidation.any { status ->
                status.message.uppercase().contains(MINOR_MESSAGE)
            }
            ) {
                return DocumentBelongsToAMinorException().left()
            }
            personTO.results.firstOrNull()?.basicData?.name
        }.leftIfNull { DocumentNotFoundException() }
    }

    private inline fun <reified T : Any> Either<Exception, T>.log(): Either<Exception, T> {
        val logName = "BigDataCorpGet${T::class.simpleName}"
        this.fold(ifLeft = { logger.error(logName, it) }, ifRight = { logger.info(append("response", it), logName) })
        return this
    }

    private fun buildGetCompanyRequest(cnpj: String): MutableHttpRequest<Map<String, String>> {
        val requestBody =
            mapOf("Datasets" to "basic_data", "q" to "doc{$cnpj}", "AccessToken" to configuration.accessToken)
        return HttpRequest.POST(configuration.companyPath, requestBody)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)
    }

    private fun extractCompanyName(response: Either<Exception, CompanyTO>): Either<Exception, String> {
        return response.map {
            it.results[0].basicData.officialName
        }.leftIfNull { DocumentNotFoundException() }
    }

    private fun KycDossierTO.toKycDossier(cpf: String): KycDossier {
        val officialData = onlineCertificates?.toKycDossierOfficialDocumentData()
            ?: fallbackOfficialData(basicData)
            ?: throw IllegalStateException("Document official data is mandatory for a KYC")

        return KycDossier(
            taxId = KycDossierTaxIdRegion(
                region = basicData.taxIdFiscalRegion,
            ),

            gender = basicData.gender?.let { if (it == "M") Gender.M else Gender.F },
            motherName = basicData.motherName?.ifEmpty { null },
            phones = phones.map { it.toKycDossierPhone() },
            emails = emails.map { it.toKycDossierEmail() },
            mep = mediaProfileAndExposure?.toKycDossierMepQuery() ?: KycDossierMepQuery.empty(),
            pep = kycData.toPepQuery(),
            officialData = officialData,
            sanctions = kycData.toKycDossierSanctionsList(),
            globalSanctionsList = getGlobalSanctionsList(),
            fatherName = basicData.fatherName?.ifEmpty { null },
        )
    }

    fun parseBigBoostTO(json: String, cpf: String): Either<Exception, KycDossier> {
        val logName = "BigBoostAdapter#parseBigBoostTO"
        val markers = Markers.append("json", json).andAppend("cpf", cpf)
        return try {
            val parsedData: KycDossierQueryTO = parseObjectFrom(json)
            parsedData.results.first().toKycDossier(cpf).right()
        } catch (e: Exception) {
            // TODO: montar KYC especial para casos de menor idade
            val containsMinor = json.uppercase().contains(MINOR_MESSAGE)
            markers.andAppend("isUserMinor", containsMinor)
            if (containsMinor) {
                logger.warn(markers, logName, e)
                DocumentBelongsToAMinorException().left()
            } else {
                logger.error(markers, logName, e)
                Either.Left(e)
            }
        }
    }

    private fun fallbackOfficialData(basicData: PersonBasicDataTO): KycDossierOfficialDocumentData? {
        logger.warn(
            append("reason", "Fallback para dados oficiais do BigDataCorp").andAppend("basicData", basicData),
            "BigDataCorpGet",
        )

        if (basicData.taxIdStatus?.uppercase() == "CPF DOES NOT EXIST IN RECEITA FEDERAL DATABASE") {
            return KycDossierOfficialDocumentData(
                provider = "BigData",
                name = "CPF NÃO EXISTE NA BASE DA RECEITA FEDERAL",
                birthDate = LocalDate.EPOCH,
                socialName = null,
                hasObitIndication = false,
                deathYear = null,
                regular = false,
                status = basicData.taxIdStatus,
            )
        }

        return KycDossierOfficialDocumentData(
            provider = "BigData",
            name = basicData.name?.ifEmpty { throw IllegalStateException("fallback official data name is mandatory") }
                ?: return null,
            birthDate = basicData.birthDate ?: return null,
            socialName = null,
            hasObitIndication = basicData.hasObitIndication ?: return null,
            deathYear = null,
            regular = basicData.taxIdStatus?.let { it.uppercase() == "REGULAR" } ?: return null,
            status = basicData.taxIdStatus.ifEmpty { throw IllegalStateException("fallback official data status is mandatory") },
        )
    }

    companion object {
        private val logger = LoggerFactory.getLogger(BigBoostAdapter::class.java)
    }
}

private data class AddressResponseTO(
    @JsonProperty("Result") val results: List<AddressResultTO>,
    @JsonProperty("QueryId") val queryId: String,
    @JsonProperty("ElapsedMilliseconds") val elapsedMilliseconds: Int,
    @JsonProperty("Status") val status: StatusTO,
)

private data class AddressResultTO(
    @JsonProperty("MatchKeys") val matchKeys: String,
    @JsonProperty("BasicData") val basicData: AddressBasicDataTO,
)

@JsonInclude(JsonInclude.Include.NON_EMPTY)
data class AddressBasicDataTO(
    @JsonProperty("Typology") val typology: String? = null,
    @JsonProperty("ECTStandardizedTypology") val ectStandardizedTypology: String? = null,
    @JsonProperty("Title") val title: String? = null,
    @JsonProperty("ECTStandardizedTitle") val ectStandardizedTitle: String? = null,
    @JsonProperty("AddressMain") val addressMain: String? = null,
    @JsonProperty("ECTStandardizedAddressMain") val ectStandardizedAddressMain: String? = null,
    @JsonProperty("ECTAddressMainCode") val ectAddressMainCode: String? = null,
    @JsonProperty("NumberRange") val numberRange: String? = null,
    @JsonProperty("Neighborhood") val neighborhood: String? = null,
    @JsonProperty("ECTStandardizedNeighborhood") val ectStandardizedNeighborhood: String? = null,
    @JsonProperty("ZipCode") val zipCode: String? = null,
    @JsonProperty("ZipCodeType") val zipCodeType: String? = null,
    @JsonProperty("City") val city: String? = null,
    @JsonProperty("IBGECityCode") val ibgeCityCode: String? = null,
    @JsonProperty("State") val state: String? = null,
    @JsonProperty("Country") val country: String? = null,
)

private data class KycDossierTO(
    @JsonProperty("BasicData") val basicData: PersonBasicDataTO,
    @JsonProperty("Emails") val emails: List<PersonEmailTO>,
    @JsonProperty("Phones") val phones: List<PersonPhoneTO>,
    @JsonProperty("KycData") val kycData: KycDataTO,
    @JsonProperty("MediaProfileAndExposure") val mediaProfileAndExposure: MediaProfileAndExposureTO?,
    @JsonProperty("OnlineCertificates") val onlineCertificates: List<ReceitaFederalOnDemandOnlineCertificatesTO>?,
)

private data class KycDataTO(
    @JsonProperty("PEPHistory") val pepHistory: List<PEPHistoryTO>,
    @JsonProperty("IsCurrentlyPEP") val isCurrentlyPEP: Boolean,
    @JsonProperty("SanctionsHistory") val sanctionsHistory: List<SanctionsHistoryTO>,
    @JsonProperty("IsCurrentlySanctioned") val isCurrentlySanctioned: Boolean,
)

private data class SanctionsHistoryTO(
    @JsonProperty("Source") val source: String,
    @JsonProperty("StandardizedSanctionType") val standardizedSanctionType: String,
    @JsonProperty("MatchRate") val matchRate: String,
    @JsonProperty("NameUniquenessScore") val nameUniquenessScore: String,
)

@JsonIgnoreProperties(ignoreUnknown = true)
private data class PEPHistoryTO(
    @JsonProperty("Level") val level: String,
    @JsonProperty("JobTitle") val jobTitle: String,
    @JsonProperty("Department") val department: String,
    @JsonProperty("Motive") val motive: String,
    @JsonProperty("Source") val source: String,
    @JsonProperty("TaxId") val taxId: String,
    @JsonProperty("StartDate") val startDate: String,
    @JsonProperty("EndDate") val endDate: String,
)

private data class MediaProfileAndExposureTO(
    @JsonProperty("MediaExposureLevel") val mediaExposureLevel: String?,
    @JsonProperty("CelebrityLevel") val celebrityLevel: String?,
    @JsonProperty("UnpopularityLevel") val unpopularityLevel: String?,
)

data class PersonEmailTO(
    @JsonProperty("EmailAddress") val emailAddress: String,
    @JsonProperty("CreationDate")
    @JsonFormat(timezone = "UTC")
    val creationDate: LocalDate,
    @JsonProperty("Priority") val priority: Long,
)

private data class PersonPhoneTO(
    @JsonProperty("Number") val number: String,
    @JsonProperty("AreaCode") val areaCode: String,
    @JsonProperty("CountryCode") val countryCode: String,
    @JsonProperty("CreationDate")
    @JsonFormat(timezone = "UTC")
    val creationDate: LocalDate,
    @JsonProperty("Priority") val priority: Long,
)

private data class KycDossierQueryTO(
    @JsonProperty("Result") val results: List<KycDossierTO>,
    @JsonProperty("QueryId") val queryId: String,
    @JsonProperty("ElapsedMilliseconds") val elapsedMilliseconds: Int,
    @JsonProperty("Status") val status: KycDossierQueryStatusTO,
)

private data class KycDossierQueryStatusTO(
    @JsonProperty("basic_data") val basicData: List<BasicDataStatusTO>,
    @JsonProperty("phones") val phones: List<BasicDataStatusTO>,
    @JsonProperty("emails") val emails: List<BasicDataStatusTO>,
)

private data class PersonTO(
    @JsonProperty("Result") val results: List<PersonResultTO>,
    @JsonProperty("QueryId") val queryId: String,
    @JsonProperty("ElapsedMilliseconds") val elapsedMilliseconds: Int,
    @JsonProperty("Status") val status: StatusTO,
)

private data class StatusTO(
    @JsonProperty("basic_data") val basicData: List<BasicDataStatusTO>?,
    @JsonProperty("date_of_birth_validation") val dateOfBirthValidation: List<BasicDataStatusTO>?,
)

private data class BasicDataStatusTO(
    @JsonProperty("Code") val code: Int,
    @JsonProperty("Message") val message: String,
)

private data class PersonResultTO(
    @JsonProperty("MatchKeys") val matchKeys: String,
    @JsonProperty("BasicData") val basicData: PersonBasicDataTO,
)

private data class PersonBasicDataTO(
    @JsonProperty("Name") val name: String?,
    @JsonProperty("TaxIdNumber") val taxIdNumber: String?,
    @JsonProperty("TaxIdCountry") val taxIdCountry: String?,
    @JsonProperty("TaxIdFiscalRegion") val taxIdFiscalRegion: String?,
    @JsonProperty("AlternativeIdNumbers") val alternativeIdNumbers: AlternativeIdNumbersTO?,
    @JsonProperty("ExtendedDocumentInformation") val extendedDocumentInformation: ExtendedDocumentInformationTO?,
    @JsonProperty("Gender") val gender: String?,
    @JsonProperty("NameUniquenessScore") val nameUniquenessScore: Double?,
    @JsonProperty("BirthDate")
    @JsonFormat(timezone = "UTC")
    val birthDate: LocalDate?,
    @JsonProperty("BirthCountry") val birthCountry: String?,
    @JsonProperty("MotherName") val motherName: String?,
    @JsonProperty("FatherName") val fatherName: String?,
    @JsonProperty("MaritalStatusData") val maritalStatusData: MaritalStatusDataTO?,
    @JsonProperty("TaxIdStatus") val taxIdStatus: String?,
    @JsonProperty("HasObitIndication") val hasObitIndication: Boolean?,
    @JsonProperty("TaxIdStatusDate")
    @JsonFormat(timezone = "UTC")
    val taxIdStatusDate: LocalDate?,
)

private data class MaritalStatusDataTO(
    @JsonProperty("MaritalStatus") val maritalStatus: String?,
)

private data class ExtendedDocumentInformationTO(
    @JsonProperty("CNH") val cnh: ExtendedDocumentInformationDataTO?,
)

private data class ExtendedDocumentInformationDataTO(
    @JsonProperty("DocumentLast4Digits") val documentLast4Digits: String,
    @JsonProperty("CreationDate")
    @JsonFormat(timezone = "UTC")
    val creationDate: LocalDate,
)

private data class AlternativeIdNumbersTO(
    @JsonProperty("SocialSecurityNumber") val socialSecurityNumber: String?,
)

private data class CompanyTO(
    @JsonProperty("Result") val results: List<CompanyResultTO>,
    @JsonProperty("QueryId") val queryId: String,
    @JsonProperty("ElapsedMilliseconds") val elapsedMilliseconds: Int,
    @JsonProperty("Status") val status: StatusTO,
)

private data class CompanyResultTO(
    @JsonProperty("MatchKeys") val matchKeys: String,
    @JsonProperty("BasicData") val basicData: CompanyBasicDataTO,
)

private data class CompanyBasicDataTO(
    @JsonProperty("OfficialName") val officialName: String?,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class ReceitaFederalOnDemandTO(
    @JsonProperty("Result") val result: List<ReceitaFederalOnDemandResultTO>,
) {
    fun toDomain(): PersonReceitaFederalInfo? {
        return result[0].onlineCertificates[0].additionalOutputData?.let { data ->
            PersonReceitaFederalInfo(
                name = data.name,
                birthDate = parseOutputDataBirthDate(data.birthdate),
                socialName = if (data.socialName.isNullOrBlank()) {
                    null
                } else {
                    data.socialName
                },
                hasObitIndication = data.isDead,
                obitYear = if (data.isDead) {
                    data.deathYear
                } else {
                    null
                },
            )
        }
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class ReceitaFederalOnDemandResultTO(
    @JsonProperty("OnlineCertificates") val onlineCertificates: List<ReceitaFederalOnDemandOnlineCertificatesTO>,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class ReceitaFederalOnDemandOnlineCertificatesTO(
    @JsonProperty("BaseStatus") val baseStatus: String?,
    @JsonProperty("AdditionalOutputData") val additionalOutputData: ReceitaFederalOnDemandAdditionalOutputDataTO?,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class ReceitaFederalOnDemandAdditionalOutputDataTO(
    @JsonProperty("Name") val name: String,
    @JsonProperty("Birthdate")
    val birthdate: String,
    @JsonProperty("SocialName") val socialName: String?,
    @JsonProperty("IsDead") val isDead: Boolean,
    @JsonProperty("DeathYear") val deathYear: String?,
)

private fun MediaProfileAndExposureTO.toKycDossierMepQuery() = KycDossierMepQuery(
    exposureLevel = mediaExposureLevel,
    celebrityLevel = celebrityLevel,
    unpopularityLevel = unpopularityLevel,
)

private fun KycDataTO.toPepQuery(): PepQuery {
    val now = getZonedDateTime()

    if (!isCurrentlyPEP) {
        return PepQuery(at = now, result = PepQueryResult.NOT_PEP)
    }

    val result = pepHistory.filter {
        val startDate = parseToZonedDateTime(it.startDate)
        val endDate = parseToZonedDateTime(it.endDate)

        now.isAfter(startDate) && now.isBefore(endDate)
    }.map {
        when (it.level.toIntOrNull()) {
            1 -> PepQueryResult.DIRECT_PEP
            null -> throw IllegalStateException("Expected PEP level to be an integer")
            else -> PepQueryResult.RELATED_TO_PEP
        }
    }.let {
        if (it.contains(PepQueryResult.DIRECT_PEP)) {
            PepQueryResult.DIRECT_PEP
        } else if (it.contains(PepQueryResult.RELATED_TO_PEP)) {
            PepQueryResult.RELATED_TO_PEP
        } else {
            throw IllegalStateException("Expected at least one PEP level")
        }
    }

    return PepQuery(
        at = now,
        result = result,
    )
}

private fun KycDataTO.toKycDossierSanctionsList(): List<KycDossierSanctions> {
    return this.sanctionsHistory.map {
        KycDossierSanctions(
            source = it.source,
            type = KycDossierSanctionType.of(it.standardizedSanctionType),
            matchRate = it.matchRate.toIntOrNull() ?: 0,
            nameUniquenessScore = it.nameUniquenessScore.toDoubleOrNull() ?: 0.0,
        )
    }.filter {
        it.matchRate >= 95
    }
}

private fun List<ReceitaFederalOnDemandOnlineCertificatesTO>.toKycDossierOfficialDocumentData(): KycDossierOfficialDocumentData? {
    if (this.isEmpty()) return null
    val additionalOutputData = this[0].additionalOutputData ?: return null
    val baseStatus = this[0].baseStatus ?: return null

    return KycDossierOfficialDocumentData(
        provider = "Receita Federal",
        name = additionalOutputData.name.ifEmpty { throw IllegalStateException("Expected name to be non-empty") },
        birthDate = parseOutputDataBirthDate(additionalOutputData.birthdate),
        socialName = additionalOutputData.socialName?.ifEmpty { null },
        hasObitIndication = additionalOutputData.isDead,
        deathYear = additionalOutputData.deathYear?.ifEmpty { null },
        regular = baseStatus.uppercase() == "REGULAR",
        status = baseStatus.ifEmpty { throw IllegalStateException("Expected base status to be non-empty") },
    )
}

// TODO: remover quando o BigDataCorp voltar a retornar a data de nascimento no formato yyyy-MM-dd
internal fun parseOutputDataBirthDate(birthdate: String): LocalDate {
    try {
        return LocalDate.parse(birthdate, dateFormat)
    } catch (e: Exception) {
        try {
            return LocalDate.parse(birthdate.replace("-", "").padStart(8, '0'), DateTimeFormatter.ofPattern("ddMMyyyy"))
        } catch (e: Exception) {
            throw e
        }
    }
}

fun PersonEmailTO.toKycDossierEmail(): KycDossierEmail {
    return KycDossierEmail(
        value = EmailAddress(email = emailAddress),
        createdAt = creationDate,
    )
}

private fun PersonPhoneTO.toKycDossierPhone(): KycDossierPhone {
    return KycDossierPhone(
        number = number,
        areaCode = areaCode,
        countryCode = countryCode,
        createdAt = creationDate,
    )
}

fun AddressBasicDataTO.toAddress(): Address {
    val streetMain = buildString {
        ectStandardizedTitle?.let {
            append(it.trim())
            append(" ")
        }
        append(ectStandardizedAddressMain.orEmpty().trim())
    }

    return Address(
        streetType = typology.orEmpty().trim(),
        city = city.orEmpty().trim(),
        complement = "",
        number = "",
        neighborhood = ectStandardizedNeighborhood.orEmpty().trim(),
        state = state.orEmpty().trim(),
        streetName = streetMain.trim(),
        zipCode = zipCode.orEmpty(),
    )
}

private fun getGlobalSanctionsList() = listOf(
    "CVM - Alerta Suspensão",
    "CVM - Penalidade Temporaria",
    "CVM - Termo Compromisso",
    "Banco Central - Inabilitados",
    "Embargos do Ibama",
    "OFAC",
    "COAF",
    "EU",
    "GOVUK",
    "FBI",
    "INTERPOL",
    "UNSC",
    "CEAF",
    "CNEP",
    "MTE (Trabalho Escravo)",
    "Conselho Nacional de Justiça",
    "CEIS",
    "CEPIM",
    "Inidôneos TCU (Tribunal de Contas da União)",
    "Acordos de Leniência (Controladoria-Geral da União)",
    "Processo Administrativo Disciplinar (BSM Supervisão)",
    "Impedidos de Licitar e Contratar Banco",
    "Tribunal de Contas do Estado de São Paulo",
)