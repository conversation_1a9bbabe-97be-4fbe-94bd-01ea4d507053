package ai.friday.billpayment.adapters.caf

import ai.friday.billpayment.and
import ai.friday.billpayment.app.caf.CafAdapterInterface
import ai.friday.billpayment.app.caf.CreateTransactionResponse
import ai.friday.billpayment.app.caf.FileData
import ai.friday.billpayment.app.caf.GetTransactionResponse
import ai.friday.billpayment.app.caf.ListTransactionsResponse
import ai.friday.billpayment.get
import arrow.core.Either
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.MediaType
import io.micronaut.http.client.annotation.Client
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@ConfigurationProperties("integrations.caf")
data class CafConfiguration @ConfigurationInject constructor(
    val apiToken: String,
    val mobileToken: String,
    val transactionPath: String,
)

@Singleton
open class CafAdapter(
    @Client("\${integrations.caf.host}")
    private val httpClient: RxHttpClient,
    private val configuration: CafConfiguration,
) : CafAdapterInterface {
    override fun createTransaction(templateId: String, files: List<FileData>): Either<Exception, CreateTransactionResponse> {
        val markers = append("templateId", templateId)

        val httpRequest = HttpRequest.POST(
            configuration.transactionPath,
            CreateTransactionTO(
                files = files,
                templateId = templateId,
            ),
        ).contentType(MediaType.APPLICATION_JSON).accept(MediaType.APPLICATION_JSON_TYPE)

        val call = httpClient.retrieve(
            httpRequest,
            Argument.of(CreateTransactionResponse::class.java),
            Argument.STRING,
        )

        return try {
            val response = call.firstOrError().blockingGet()
            logger.info(markers.and("response" to response), "ArbiECMFepWebAdapter#callSearchUser")
            Either.Right(response)
        } catch (err: Exception) {
            logger.error(markers, "CafAdapter#createTransaction", err)
            Either.Left(err)
        }
    }

    override fun listTransactions(): Either<Exception, ListTransactionsResponse> {
        val httpRequest = HttpRequest.GET<ListTransactionsResponse>(configuration.transactionPath)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)

        val call = httpClient.retrieve(
            httpRequest,
            Argument.of(ListTransactionsResponse::class.java),
            Argument.STRING,
        )

        return try {
            val response = call.firstOrError().blockingGet()
            logger.info(append("totalItems", response.totalItems).and("response" to response), "CafAdapter#listTransactions")
            Either.Right(response)
        } catch (err: Exception) {
            logger.error("CafAdapter#listTransactions", err)
            Either.Left(err)
        }
    }

    override fun getTransaction(transactionId: String): Either<Exception, GetTransactionResponse> {
        val markers = append("transactionId", transactionId)

        val httpRequest = HttpRequest.GET<GetTransactionResponse>("${configuration.transactionPath}/$transactionId")
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)

        val call = httpClient.retrieve(
            httpRequest,
            Argument.of(GetTransactionResponse::class.java),
            Argument.STRING,
        )

        return try {
            val response = call.firstOrError().blockingGet()
            logger.info(markers.and("response" to response), "CafAdapter#getTransaction")
            Either.Right(response)
        } catch (err: Exception) {
            logger.error(markers, "CafAdapter#getTransaction", err)
            Either.Left(err)
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(CafAdapter::class.java)
    }
}

data class CreateTransactionTO(
    val files: List<FileData>,
    val templateId: String,
)