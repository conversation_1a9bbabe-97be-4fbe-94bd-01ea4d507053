package ai.friday.billpayment.adapters.serpro

import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.account.DocumentInfo
import ai.friday.billpayment.app.integrations.DocumentValidationResponse
import ai.friday.billpayment.app.integrations.DocumentValidationService
import ai.friday.billpayment.bearer
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.right
import com.fasterxml.jackson.annotation.JsonProperty
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.HttpClientConfiguration
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.ApplicationConfiguration
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Singleton
import java.io.InputStream
import java.net.URL
import java.time.Duration
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.Base64
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
class SerproHttpConfiguration(
    applicationConfiguration: ApplicationConfiguration,
    val configuration: HttpClientConfiguration,
) : HttpClientConfiguration(applicationConfiguration) {
    init {
        setReadTimeout(Duration.ofMinutes(1))
    }

    override fun getConnectionPoolConfiguration(): ConnectionPoolConfiguration =
        configuration.connectionPoolConfiguration
}

@Singleton
class SerproAdapter(
    private val serproAuthenticationManager: SerproAuthenticationManager,
    serproHttpConfiguration: SerproHttpConfiguration,
    configuration: SerproAdapterConfiguration,
) : DocumentValidationService {
    private val httpClient = RxHttpClient.create(URL(configuration.host), serproHttpConfiguration)

    override fun validateWithSelfie(
        document: Document,
        name: String,
        selfieImageStream: InputStream,
    ): Either<Exception, DocumentValidationResponse> {
        val selfie = selfieImageStream.use {
            Base64.getEncoder().encodeToString(it.readAllBytes())
        }

        val body = SerproSelfieCheckTO(
            key = SerproKeyTO(cpf = document.value),
            answer = SerproAnswerTO(
                biometriaFace = SerproBiometriaFaceTO(
                    base64 = selfie,
                ),
                nome = name,
                dataNascimento = null,
                situacaoCpf = "regular",
                cnh = null,
                filiacao = null,
                documento = null,
            ),
        )

        return callSerpro(body)
    }

    override fun validateWithSelfie(
        documentInfo: DocumentInfo,
        selfieImageStream: InputStream,
    ): Either<Exception, DocumentValidationResponse> {
        val selfie = selfieImageStream.use {
            Base64.getEncoder().encodeToString(it.readAllBytes())
        }

        val body = SerproSelfieCheckTO(
            key = SerproKeyTO(cpf = documentInfo.cpf),
            answer = SerproAnswerTO(
                biometriaFace = SerproBiometriaFaceTO(
                    base64 = selfie,
                ),
                nome = documentInfo.name,
                dataNascimento = documentInfo.birthDate?.format(DateTimeFormatter.ISO_DATE),
                situacaoCpf = "regular",
                cnh = documentInfo.cnhNumber?.let {
                    SerproCnhTO(numeroRegistro = it)
                },
                filiacao = SerproFiliacaoTO(
                    nomeMae = documentInfo.motherName,
                    nomePai = documentInfo.fatherName,
                ),
                documento = SerproDocumentoTO(
                    tipo = 1,
                    numero = documentInfo.rg,
                    orgaoExpedidor = if (documentInfo.cnhNumber == null) {
                        documentInfo.orgEmission
                    } else {
                        null
                    },
                ),
            ),
        )

        return callSerpro(body)
    }

    private fun callSerpro(
        body: SerproSelfieCheckTO,
    ): Either<Exception, DocumentValidationResponse> {
        val markers =
            Markers.append("document", body.key.cpf).andAppend("base64Size", body.answer.biometriaFace?.base64?.length)
        return try {
            val url = "/datavalid/v3/validate/pf-facial"
            val accessToken = serproAuthenticationManager.getToken()

            val request = HttpRequest.POST(url, body).bearer(accessToken)
            val call = httpClient.retrieve(request, Argument.of(String::class.java))

            val rawResponse = call.firstOrError().blockingGet()
            markers.andAppend("rawResponse", rawResponse)

            val response = parseObjectFrom<SerproBiometriaFaceResponseTO>(rawResponse)

            logger.info(markers.andAppend("result", response), "SerproAdapter#selfieCheck")

            response.right()
        } catch (e: HttpClientResponseException) {
            markers.andAppend("status", e.status).andAppend("body", e.response.getBody(String::class.java))
            if (e.status in listOf(HttpStatus.UNPROCESSABLE_ENTITY, HttpStatus.NOT_FOUND)) {
                logger.warn(markers, "SerproAdapter#selfieCheck")
                return SerproBiometriaFaceResponseTO(
                    cpfDisponivel = false,
                    cnhDisponivel = false,
                    biometriaFace = BiometriaTO(
                        disponivel = false,
                        probabilidade = null,
                        similaridade = null,
                    ),
                ).right()
            }
            logger.error(markers, "SerproAdapter#selfieCheck")
            return Either.Left(e)
        } catch (e: Exception) {
            logger.error(markers, "SerproAdapter#selfieCheck")
            return Either.Left(e)
        }
    }

    private val logger = LoggerFactory.getLogger(SerproAdapter::class.java)
}

@Singleton
class SerproAuthenticationManager(
    private val configuration: SerproAdapterConfiguration,
) {
    private val httpClient = RxHttpClient.create(URL(configuration.host))

    private var token: String? = null
    private var expiresIn: LocalDateTime? = null
    private val expireOffset = 1L

    fun getToken(): String {
        return expiresIn?.let {
            if (it.minusMinutes(expireOffset).isAfter(getZonedDateTime().toLocalDateTime())
            ) {
                token?.let { token -> return token } ?: authenticate()
            } else {
                authenticate()
            }
        } ?: authenticate()
    }

    private fun authenticate(): String {
        val url = "/token"

        val request = HttpRequest.POST(url, mapOf("grant_type" to "client_credentials"))
            .header("Content-Type", "application/x-www-form-urlencoded")
            .basicAuth(configuration.username, configuration.password)

        val call = httpClient.retrieve(request, Argument.of(SerproAuthResponseTO::class.java), Argument.STRING)
        try {
            val response = call.firstOrError().blockingGet()

            expiresIn =
                getZonedDateTime().toLocalDateTime().plusSeconds(response.expiresIn)
            token = response.accessToken
            return response.accessToken
        } catch (e: Exception) {
            logger.error("SerproAuthenticationManager", e)
            throw e
        }
    }

    private val logger = LoggerFactory.getLogger(SerproAuthenticationManager::class.java)
}

@ConfigurationProperties("integrations.serpro")
class SerproAdapterConfiguration
@ConfigurationInject
constructor(
    val host: String,
    val username: String,
    val password: String,
)

data class SerproAuthResponseTO(
    @JsonProperty("access_token") val accessToken: String,
    @JsonProperty("expires_in") val expiresIn: Long,
)

data class SerproSelfieCheckTO(
    val key: SerproKeyTO,
    val answer: SerproAnswerTO,
)

data class SerproKeyTO(val cpf: String)
data class SerproAnswerTO(
    @JsonProperty("biometria_face") val biometriaFace: SerproBiometriaFaceTO? = null,
    val nome: String? = null,
    @JsonProperty("data_nascimento") val dataNascimento: String? = null, // pattern: yyyy-MM-dd
    @JsonProperty("situacao_cpf") val situacaoCpf: String? = null, // regular, suspensa, titular falecido, pendente de regularização, cancelada por multiplicidade, nula, cancelada de oficio
    val sexo: SerproSexo? = null,
    val nacionalidade: Int? = null, // 1 - brasileiro, 2 - brasileiro naturalizado, 3 - estrangeiro, 4 - brasileiro nascido no exterior
    val cnh: SerproCnhTO? = null,
    val filiacao: SerproFiliacaoTO? = null,
    val documento: SerproDocumentoTO? = null,
)

enum class SerproSexo {
    F, M
}

data class SerproBiometriaFaceTO(
    val formato: String = "jpg",
    val base64: String,
)

data class SerproCnhTO(
    @JsonProperty("numero_registro") val numeroRegistro: String,
    val categoria: String? = null,
    @JsonProperty("codigo_situacao") val codigoSituacao: String? = null, // 2 - em emissão, 3 - emitida, A - cancelada
    @JsonProperty("data_ultima_emissao") val dataUltimaEmissao: String? = null, // pattern: yyyy-MM-dd
    @JsonProperty("data_validade") val dataValidade: String? = null, // pattern: yyyy-MM-dd
    @JsonProperty("data_primeira_habilitacao") val dataPrimeiraHabilitacao: String? = null, // pattern: yyyy-MM-dd
    @JsonProperty("registro_nacional_estrangeiro") val registroNacionalEstrangeiro: String? = null,
    @JsonProperty("possui_impedimento") val possuiImpedimento: Boolean = true,
    val observacoes: String? = null,
)

data class SerproFiliacaoTO(
    @JsonProperty("nome_mae") val nomeMae: String? = null,
    @JsonProperty("nome_pai") val nomePai: String? = null,
)

data class SerproDocumentoTO(
    val tipo: Int, // 1 - carteira de identidade, 2 - carteira profissional, 3 - passaporte, 4 - carteira de reservista
    val numero: String,
    @JsonProperty("orgao_expedidor") val orgaoExpedidor: String? = null,
    @JsonProperty("uf_expedidor") val ufExpedidor: String? = null,
)

data class SerproBiometriaFaceResponseTO(
    @JsonProperty("cpf_disponivel") val cpfDisponivel: Boolean,
    @JsonProperty("cnh_disponivel") val cnhDisponivel: Boolean,
    @JsonProperty("biometria_face") val biometriaFace: BiometriaTO,
) : DocumentValidationResponse {
    override fun selfieMatchesDocument(): Boolean? {
        return when (biometriaFace.probabilidade) {
            SerproBiometriaProbability.HIGH -> true
            SerproBiometriaProbability.VERY_HIGH -> true
            SerproBiometriaProbability.LOW -> false
            SerproBiometriaProbability.VERY_LOW -> false
            null -> null
        }
    }

    override fun getPercentage(): Double? {
        return biometriaFace.similaridade
    }
}

data class BiometriaTO(
    val disponivel: Boolean,
    val probabilidade: SerproBiometriaProbability?,
    val similaridade: Double?,
)

enum class SerproBiometriaProbability() {
    @JsonProperty("Altíssima probabilidade")
    VERY_HIGH,

    @JsonProperty("Alta probabilidade")
    HIGH,

    @JsonProperty("Baixa probabilidade")
    LOW,

    @JsonProperty("Baixíssima probabilidade")
    VERY_LOW,
}