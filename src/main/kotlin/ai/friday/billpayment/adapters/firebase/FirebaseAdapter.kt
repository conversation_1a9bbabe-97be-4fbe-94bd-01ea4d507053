package ai.friday.billpayment.adapters.firebase

import ai.friday.billpayment.app.account.AccountId
import io.micronaut.context.annotation.Property
import io.micronaut.http.HttpRequest
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.uri.UriBuilder
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
class FirebaseAdapter(
    @param:Client(value = "\${integrations.firebase.host}") private val httpClient: RxHttpClient,
) {
    @field:Property(name = "integrations.firebase.measurementId")
    lateinit var measurementId: String

    fun publishEvent(accountId: AccountId, eventName: String, customParams: Map<String, Any?> = mapOf()) {
        val markers = Markers.append("eventName", eventName)
        try {
            val uriBuilder = UriBuilder.of("/g/collect")
                .queryParam("tid", measurementId)
                .queryParam("cid", accountId.value)
                .queryParam("v", "2")
                .queryParam("en", eventName)

            customParams.entries.forEach { (k, v) ->
                if (v != null) {
                    uriBuilder.queryParam("ep.$k", v)
                }
            }

            val request = HttpRequest.POST(uriBuilder.build(), "")
            val call = httpClient.exchange(request)

            call.firstOrError().blockingGet()
            LOG.info(markers, "sendEventToFirebase")
        } catch (e: HttpClientResponseException) {
            val httpStatus = e.response.status().code
            val errorResponse = e.response.getBody(String::class.java).get()
            LOG.error(
                markers
                    .and(
                        Markers.append("httpStatus", httpStatus)
                            .and(Markers.append("errorResponse", errorResponse)),
                    ),
                "sendEventToFirebase",
            )
        } catch (e: Exception) {
            LOG.error(markers, "sendEventToFirebase", e)
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(FirebaseAdapter::class.java)
    }
}