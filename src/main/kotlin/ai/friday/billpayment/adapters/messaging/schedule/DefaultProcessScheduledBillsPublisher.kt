package ai.friday.billpayment.adapters.messaging.schedule

import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.ProcessScheduledBillsPublisher
import ai.friday.billpayment.app.message.QueueMessageBatch
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.log
import ai.friday.morning.date.dateFormat
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import java.time.LocalDate
import org.slf4j.LoggerFactory

@Singleton
class DefaultProcessScheduledBillsPublisher(
    private val publisher: MessagePublisher,
    @Property(name = "sqs.payment.process-scheduled-bills") val queue: String,
) : ProcessScheduledBillsPublisher {

    private val logger = LoggerFactory.getLogger(DefaultProcessScheduledBillsPublisher::class.java)

    override fun publish(walletId: WalletId, scheduleDate: LocalDate, includeSubscription: Boolean) {
        val markers = log(
            "walletId" to walletId.value,
            "scheduleDate" to dateFormat.format(scheduleDate),
            "includeSubscription" to includeSubscription,
        )

        try {
            publisher.sendMessage(
                queue,
                ProcessScheduledBillsCommand(
                    walletId = walletId,
                    scheduleDate = scheduleDate,
                    includeSubscription = includeSubscription,
                ),
            )
            logger.info(markers, "ProcessScheduledBillsPublisher")
        } catch (ex: Exception) {
            logger.error(markers, "ProcessScheduledBillsPublisher#error", ex)
        }
    }

    override fun publish(walletIdList: List<WalletId>, scheduleDate: LocalDate, includeSubscription: Boolean) {
        val markers = log(
            "size" to walletIdList.size,
            "scheduleDate" to dateFormat.format(scheduleDate),
            "includeSubscription" to includeSubscription,
        )

        try {
            val mapper = getObjectMapper()

            val commands = walletIdList.map {
                mapper.writeValueAsString(
                    ProcessScheduledBillsCommand(
                        walletId = it,
                        scheduleDate = scheduleDate,
                        includeSubscription = includeSubscription,
                    ),
                )
            }

            publisher.sendMessageBatch(
                QueueMessageBatch(
                    queueName = queue,
                    messages = commands,
                ),
            )

            logger.info(markers, "ProcessScheduledBillsPublisher")
        } catch (ex: Exception) {
            logger.error(markers, "ProcessScheduledBillsPublisher#error", ex)
        }
    }
}