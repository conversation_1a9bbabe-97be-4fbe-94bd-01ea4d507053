package ai.friday.billpayment.adapters.btg

import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.itp.ConsentId
import ai.friday.billpayment.app.itp.ITPService
import ai.friday.billpayment.app.itp.PaymentIntentId
import ai.friday.billpayment.app.itp.PaymentIntentStatus
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import io.micronaut.security.rules.SecurityRule
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Controller("/btg")
@Secured(SecurityRule.IS_ANONYMOUS)
class BTGCallbackController(private val itpService: ITPService) {
    @Post("/webhooks")
    fun receiveCallback(
        @Body body: String,
        request: HttpRequest<*>,
    ): HttpResponse<*> {
        val logName = "BTGCallbackController#receiveCallback"
        val markers = Markers.append("request", parseObjectFrom<Map<String, Any>>(body))
        markers.andAppend("headers", request.headers.asMap())
        logger.info(markers, logName)

        try {
            val callbackTO = parseObjectFrom<BTGCallbackTO>(body)

            if (callbackTO.entity == "PaymentInitiationPix") {
                val paymentIntentId = extractPaymentIntentId(callbackTO.clientRequestId)
                val status = convertToPaymentIntentStatus(callbackTO.status)
                markers.andAppend("paymentIntentId", paymentIntentId)
                    .andAppend("status", status)
                    .andAppend("pactualId", callbackTO.pactualId)
                    .andAppend("institutionName", resolveInstitutionName(callbackTO))
                itpService.processPaymentIntentStatusChanged(paymentIntentId, status).map {
                    logger.info(
                        markers.andAppend("accountId", it.accountId.value).andAppend("walletId", it.walletId.value),
                        logName,
                    )
                }.getOrElse { e ->
                    logger.error(markers, logName, e)
                }
            } else {
                logger.info(markers, logName)
            }
        } catch (e: Exception) {
            logger.error(markers, logName, e)
        }

        return HttpResponse.ok<Unit>()
    }

    @Post("/paymentInitiation")
    fun receivePaymentInitiationCallback(
        @Body body: String,
        request: HttpRequest<*>,
    ): HttpResponse<*> {
        val logName = "BTGCallbackController#receivePaymentInitiationCallback"
        val markers = Markers.append("request", parseObjectFrom<Map<String, Any>>(body))
        markers.andAppend("headers", request.headers.asMap())
        logger.info(markers, logName)

        try {
            val callbackTO = parseObjectFrom<BTGCallbackTO>(body)
            val paymentIntentId = extractPaymentIntentId(callbackTO.clientRequestId)
            val status = convertToPaymentIntentStatus(callbackTO.status)
            markers.andAppend("paymentIntentId", paymentIntentId)
                .andAppend("status", status)
                .andAppend("pactualId", callbackTO.pactualId)
                .andAppend("institutionName", resolveInstitutionName(callbackTO))
            itpService.processPaymentIntentStatusChanged(ConsentId(callbackTO.body!!.consentPactualId), status).map {
                logger.info(
                    markers.andAppend("accountId", it.accountId.value).andAppend("walletId", it.walletId.value),
                    logName,
                )
            }.getOrElse { e ->
                logger.error(markers, logName, e)
            }
        } catch (e: Exception) {
            logger.error(markers, logName, e)
        }

        return HttpResponse.ok<Unit>()
    }

    @Post("/paymentConsent")
    fun receivePaymentConsentCallback(
        @Body body: String,
        request: HttpRequest<*>,
    ): HttpResponse<*> {
        val logName = "BTGCallbackController#receivePaymentConsentCallback"
        val markers = Markers.append("request", parseObjectFrom<Map<String, Any>>(body))
        markers.andAppend("headers", request.headers.asMap())
        logger.info(markers, logName)
        return HttpResponse.ok<Unit>()
    }

    private fun resolveInstitutionName(callbackTO: BTGCallbackTO): String {
        val institution = itpService.listAllStaticInstitutions()
        val authorizationServerId = callbackTO.body?.authorizationServerId.orEmpty()
        return institution[authorizationServerId]?.title ?: authorizationServerId
    }

    companion object {
        private val logger = LoggerFactory.getLogger(BTGCallbackController::class.java)
    }
}

private fun extractPaymentIntentId(clientRequestId: String) = PaymentIntentId(clientRequestId.split("#")[0])

private fun convertToPaymentIntentStatus(status: String): PaymentIntentStatus {
    return when (status) {
        "CONSENT_REJECTED", "PAYMENT_REJECTED", "ERROR" -> PaymentIntentStatus.FAILED
        "SETTLEMENT_COMPLETED" -> PaymentIntentStatus.SUCCESS
        else -> PaymentIntentStatus.ONGOING
    }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class BTGCallbackTO(
    val clientRequestId: String,
    val entity: String,
    val pactualId: String,
    val status: String,
    val body: BTGCallbackBodyTO?,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class BTGCallbackBodyTO(
    val authorizationServerId: String,
    val consentPactualId: String,
)