package ai.friday.billpayment.adapters.cielo

import ai.friday.billpayment.app.Acquirer
import ai.friday.billpayment.app.IntegrationError
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.CreditCard
import ai.friday.billpayment.app.account.CreditCardBinDetails
import ai.friday.billpayment.app.account.CreditCardBrand
import ai.friday.billpayment.app.account.CreditCardPaymentStatus
import ai.friday.billpayment.app.account.CreditCardToken
import ai.friday.billpayment.app.bill.and
import ai.friday.billpayment.app.feature.RequiresCielo
import ai.friday.billpayment.app.integrations.AcquirerService
import ai.friday.billpayment.app.integrations.CreditCardInformationService
import ai.friday.billpayment.app.payment.CreditCardAuthorization
import ai.friday.billpayment.app.payment.PaymentNotCanceled
import ai.friday.billpayment.app.payment.PaymentNotCaptured
import ai.friday.billpayment.app.payment.PaymentNotEnabledToBeCanceled
import ai.friday.billpayment.app.payment.PaymentNotFound
import ai.friday.billpayment.app.payment.UnknownCreditCardPaymentException
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.context.annotation.Property
import io.micronaut.core.annotation.Nullable
import io.micronaut.core.convert.format.MapFormat
import io.micronaut.core.naming.conventions.StringConvention
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MediaType
import io.micronaut.http.client.HttpClientConfiguration
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.uri.UriBuilder
import io.micronaut.runtime.ApplicationConfiguration
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Singleton
import java.time.Duration
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import net.logstash.logback.marker.Markers.append
import org.apache.commons.lang3.StringUtils
import org.slf4j.LoggerFactory

@Singleton
class CieloHttpConfiguration(
    applicationConfiguration: ApplicationConfiguration,
    val configuration: HttpClientConfiguration,
) : HttpClientConfiguration(applicationConfiguration) {
    init {
        setReadTimeout(Duration.ofMinutes(2))
    }

    override fun getConnectionPoolConfiguration(): ConnectionPoolConfiguration =
        configuration.connectionPoolConfiguration
}

@ConfigurationProperties("integrations.cielo")
class CieloConfiguration {
    lateinit var authorizePath: String
    lateinit var capturePath: String
    lateinit var cancelPath: String
    lateinit var checkStatusByMerchantOrderIdPath: String
    lateinit var checkStatusByPaymentIdPath: String
    lateinit var tokenizePath: String
    lateinit var zeroAuthPath: String
    lateinit var checkStatusHost: String
    lateinit var cardBinHost: String
}

// FIXME separando esse endpoint para que funcione em qualquer caso. O ideial seria não precisar dele em todo projeto
@Singleton
class CieloCreditCardInformationAdapter(
    @param:Client(
        value = "\${integrations.cielo.host}",
        configuration = CieloHttpConfiguration::class,
    ) private val httpClient: RxHttpClient,
    private val configuration: CieloConfiguration,
    @MapFormat(keyFormat = StringConvention.CAMEL_CASE_CAPITALIZED)
    @Property(name = "integrations.cielo.credentials")
    private val headers: MutableMap<CharSequence, CharSequence>,
) : CreditCardInformationService {
    private val logger = LoggerFactory.getLogger(CieloCreditCardInformationAdapter::class.java)

    override fun retrieveBinDetails(bin: String): CreditCardBinDetails? {
        val requestMap = mutableMapOf<String, Any>("bin" to bin.take(6))
        val uri = UriBuilder.of(configuration.checkStatusHost + configuration.cardBinHost)
            .expand(requestMap)
            .toString()
        val call = httpClient.exchange(
            HttpRequest.GET<CreditCardDetailsTO>(uri)
                .contentType(MediaType.APPLICATION_JSON_TYPE)
                .headers(headers),
            Argument.of(CreditCardDetailsTO::class.java),
            Argument.of(Array<CieloErrorResponse>::class.java),
        )
        return try {
            val response = call.blockingSingle()
            response.body()!!.toCreditCardDetails()
        } catch (e: HttpClientResponseException) {
            logger.error(
                append("status", e.status).and(
                    append(
                        "body",
                        e.response.getBody(Array<CieloErrorResponse>::class.java),
                    ),
                ),
                "RetrieveBinDetails",
            )
            null
        }
    }
}

@Singleton
@RequiresCielo
class CieloAdapter(
    @param:Client(
        value = "\${integrations.cielo.host}",
        configuration = CieloHttpConfiguration::class,
    ) private val httpClient: RxHttpClient,
    private val configuration: CieloConfiguration,
    @MapFormat(keyFormat = StringConvention.CAMEL_CASE_CAPITALIZED)
    @Property(name = "integrations.cielo.credentials")
    private val headers: MutableMap<CharSequence, CharSequence>,
) : AcquirerService {

    override fun authorize(
        accountId: AccountId,
        orderId: String,
        amount: Long,
        creditCard: CreditCard,
        softDescriptor: String,
        installments: Int,
    ): CreditCardAuthorization {
        return doAuthorizeWithLog(accountId, orderId, amount, creditCard, softDescriptor, false, installments)
    }

    override fun authorizeAndCapture(
        accountId: AccountId,
        orderId: String,
        amount: Long,
        creditCard: CreditCard,
        softDescriptor: String,
        installments: Int,
    ): CreditCardAuthorization {
        return doAuthorizeWithLog(accountId, orderId, amount, creditCard, softDescriptor, true, installments)
    }

    private fun doAuthorizeWithLog(
        accountId: AccountId,
        orderId: String,
        amount: Long,
        creditCard: CreditCard,
        softDescriptor: String,
        capture: Boolean,
        installments: Int,
    ): CreditCardAuthorization {
        return doAuthorize(accountId, orderId, amount, creditCard, softDescriptor, capture, installments)
            .also {
                logger.info(
                    Markers.empty().and(
                        "accountId" to accountId.value,
                        "orderId" to orderId,
                        "amount" to amount,
                        "creditCard" to creditCard,
                        "softDescriptor" to softDescriptor.take(13),
                        "creditCardAuthorization" to it,
                    ),
                    "AcquirerService#authorizeAndCapture",
                )
            }
    }

    private fun doAuthorize(
        accountId: AccountId,
        orderId: String,
        amount: Long,
        creditCard: CreditCard,
        softDescriptor: String,
        capture: Boolean,
        installments: Int,
    ): CreditCardAuthorization {
        val requestTO = buildPaymentRequestTO(orderId, amount, creditCard, softDescriptor, capture, installments)
        val marker = append("accountId", accountId.value).andAppend("url", configuration.authorizePath)
            .andAppend("requestBody", requestTO)
        val call = httpClient.exchange(
            HttpRequest.POST(configuration.authorizePath, requestTO)
                .contentType(MediaType.APPLICATION_JSON_TYPE)
                .headers(headers),
            Argument.of(CieloPaymentResponseTO::class.java),
            Argument.of(Array<CieloErrorResponse>::class.java),
        )
        return try {
            val response = call.blockingSingle()
            val responseTO = response.body()
            val responseMarker = marker.and<LogstashMarker>(append("statusCode", response.status))
                .and<LogstashMarker>(append("responseBody", responseTO))

            // Alarmar erro de velocity
            val acquirerReturnMessage =
                if (responseTO?.payment?.returnCode in PaymentStatus.DENIED_VELOCITY.returnCodes) {
                    logger.error(responseMarker, "CieloPayment")
                    PaymentStatus.DENIED_VELOCITY.message
                } else {
                    val paymentStatus = PaymentStatus.getPaymentStatusByStatusCode(responseTO?.payment?.status!!)
                    if (isUnknownUnknownStatusCode(paymentStatus, responseTO)) {
                        logger.error(responseMarker.andAppend("ACTION", "VERIFY"), "CieloAdapter#UnknownUnknownStatusCode")
                    } else {
                        logger.info(responseMarker, "CieloPayment")
                    }
                    paymentStatus.message
                }
            CreditCardAuthorization(
                acquirer = Acquirer.CIELO,
                transactionId = orderId,
                acquirerTid = responseTO!!.payment.paymentId.takeIf { responseTO.payment.status == 1 }
                    ?: responseTO.payment.paymentId,
                status = toCreditCardPaymentStatus(responseTO.payment.status),
                amount = responseTO.payment.amount!!,
                acquirerReturnCode = responseTO.payment.returnCode,
                acquirerReturnMessage = acquirerReturnMessage,
                tid = responseTO.payment.tid,
                authorizationCode = responseTO.payment.authorizationCode,
            )
        } catch (e: HttpClientResponseException) {
            val response = e.response.getBody(Array<CieloErrorResponse>::class.java)
            if (response.isPresent) {
                val error = response.get().firstOrNull()
                logger.warn(marker.and(append("response", error)), "CieloPayment")
                CreditCardAuthorization(
                    acquirer = Acquirer.CIELO,
                    transactionId = orderId,
                    status = CreditCardPaymentStatus.DENIED,
                    acquirerReturnMessage = error?.message.orEmpty(),
                )
            } else {
                logger.error(marker, "CieloPayment", e)
                CreditCardAuthorization(
                    acquirer = Acquirer.CIELO,
                    transactionId = orderId,
                    status = CreditCardPaymentStatus.ABORTED,
                    acquirerReturnMessage = PaymentStatus.UNKNOWN.message,
                )
            }
        } catch (e: Exception) {
            logger.error(marker, "CieloPayment", e)
            CreditCardAuthorization(
                acquirer = Acquirer.CIELO,
                transactionId = orderId,
                status = CreditCardPaymentStatus.ABORTED,
                acquirerReturnMessage = PaymentStatus.UNKNOWN.message,
            )
        }
    }

    private fun isUnknownUnknownStatusCode(
        paymentStatus: PaymentStatus,
        responseTO: @Nullable CieloPaymentResponseTO,
    ) = paymentStatus.statusCode == PaymentStatus.UNKNOWN.statusCode && responseTO.payment.returnCode != "-1"

    private fun toCreditCardPaymentStatus(status: Int?): CreditCardPaymentStatus {
        return when (status) {
            PaymentStatus.AUTHORIZED.statusCode -> CreditCardPaymentStatus.AUTHORIZED
            PaymentStatus.PAYMENT_CONFIRMED.statusCode -> CreditCardPaymentStatus.PAYMENT_CONFIRMED
            else -> CreditCardPaymentStatus.DENIED
        }
    }

    override fun capture(acquirerTid: String) {
        val uri = configuration.capturePath.replace("{PaymentId}", acquirerTid)
        val call = httpClient.exchange(
            HttpRequest.PUT(uri, "")
                .contentType(MediaType.APPLICATION_JSON_TYPE)
                .headers(headers),
            Argument.of(CieloCaptureResponseTO::class.java),
            Argument.of(CieloErrorResponse::class.java),
        )
        try {
            val responseTO = call.firstOrError().blockingGet().body()
            if (responseTO!!.status != PaymentStatus.PAYMENT_CONFIRMED.statusCode) {
                logger.warn(append("url", uri).and<LogstashMarker>(append("responseBody", responseTO)), "CieloCapture")
                throw PaymentNotCaptured(responseTO.returnCode, responseTO.returnMessage)
            }
            logger.info(append("url", uri).and<LogstashMarker>(append("responseBody", responseTO)), "CieloCapture")
        } catch (e: HttpClientResponseException) {
            val marker = append("url", uri).and<LogstashMarker>(append("HttpStatus", e.response.status()))
            val optCieloErrorResponse = e.response.getBody(CieloErrorResponse::class.java)
            if (optCieloErrorResponse.isPresent) {
                val cieloErrorResponse = optCieloErrorResponse.get()
                logger.warn(marker.and<LogstashMarker>(append("responseBody", cieloErrorResponse)), "CieloCapture")
                if (CieloErrorCode.TRANSACTION_NOT_AVAILABLE_TO_CAPTURE.code == cieloErrorResponse.code) {
                    return
                }
            } else {
                logger.warn(marker, "CieloCapture")
            }
            throw UnknownCreditCardPaymentException(e) // FIXME retornar UnknownPaymentStatus e fazer catch de exception
        }
    }

    override fun cancel(orderId: String) {
        val uri = configuration.cancelPath.replace("{MerchantOrderId}", orderId)
        val call = httpClient.exchange(
            HttpRequest.PUT(uri, "")
                .contentType(MediaType.APPLICATION_JSON_TYPE)
                .headers(headers),
            Argument.of(CieloCancelResponseTO::class.java),
            Argument.of(CieloErrorResponse::class.java),
        )
        try {
            val responseTO = call.firstOrError().blockingGet().body()
            if (responseTO!!.status != PaymentStatus.VOIDED.statusCode && responseTO.status != PaymentStatus.REFUNDED.statusCode) {
                logger.warn(append("url", uri).and<LogstashMarker>(append("responseBody", responseTO)), "CieloCancel")
                throw PaymentNotCanceled(responseTO.returnCode, responseTO.returnMessage)
            }
            logger.info(append("url", uri).and<LogstashMarker>(append("responseBody", responseTO)), "CieloCancel")
        } catch (e: HttpClientResponseException) {
            val body = e.response.getBody(CieloErrorResponse::class.java)
            if (body.isPresent) {
                logger.warn(
                    append("url", uri).and<LogstashMarker>(
                        append("HttpResponse", e.response.status()).and(
                            append("responseBody", body.get()),
                        ),
                    ),
                    "CieloCancel",
                )
                if (CieloErrorCode.TRANSACTION_NOT_AVAILABLE_TO_VOID.code == body.get().code) {
                    throw PaymentNotEnabledToBeCanceled(body.get().code, body.get().message)
                }
            } else {
                logger.warn(
                    append("url", uri).and<LogstashMarker>(append("HttpResponse", e.response.status())),
                    "CieloCancel",
                )
            }
            if (e.response.status() == HttpStatus.NOT_FOUND) {
                throw PaymentNotFound(e.message)
            }
            throw e
        }
    }

    override fun checkStatus(orderId: String): CreditCardAuthorization {
        return when (val paymentIdResult = findPaymentId(orderId)) {
            is PaymentIdResult.Success -> checkPaymentStatusByPaymentId(paymentIdResult.paymentId, orderId)
            PaymentIdResult.NotFound -> CreditCardAuthorization(
                acquirer = Acquirer.CIELO,
                transactionId = orderId,
                status = CreditCardPaymentStatus.DENIED,
                acquirerTid = null,
                amount = null,
                acquirerReturnCode = null,
                acquirerReturnMessage = "",
            )

            PaymentIdResult.Error -> CreditCardAuthorization(
                acquirer = Acquirer.CIELO,
                transactionId = orderId,
                status = CreditCardPaymentStatus.ABORTED,
                acquirerTid = null,
                amount = null,
                acquirerReturnCode = null,
                acquirerReturnMessage = PaymentStatus.UNKNOWN.message,
            )
        }
    }

    override fun tokenize(
        cardNumber: String,
        expirationDate: String,
        brand: CreditCardBrand,
        holderName: String,
        uid: String,
    ): Either<IntegrationError, CreditCardToken> {
        val requestTO = TokenizeRequestTO(
            customerName = holderName,
            cardNumber = cardNumber,
            holder = holderName,
            expirationDate = expirationDate,
            brand = brand.toCieloCreditCardBrandName(),
        )

        val call = httpClient.exchange(
            HttpRequest.POST(configuration.tokenizePath, requestTO)
                .contentType(MediaType.APPLICATION_JSON_TYPE)
                .headers(headers),
            Argument.of(TokenizeResponseTO::class.java),
            Argument.of(Array<CieloErrorResponse>::class.java),
        )

        try {
            val response = call.blockingSingle()
            val responseTO = response.body()
            return responseTO?.cardToken?.let {
                CreditCardToken(it).right()
            } ?: Either.Left(IntegrationError.ServerError())
        } catch (e: HttpClientResponseException) {
            val markers = append("request", requestTO)
                .and<LogstashMarker>(append("response", e.response.getBody(String::class.java).orElse("empty")))
                .and<LogstashMarker>(append("status", e.status))
                .and<LogstashMarker>(append("bin", cardNumber.take(6)))

            return when (e.status.code) {
                in (400..499) -> {
                    logger.warn(markers, "CieloTokenize")
                    IntegrationError.ClientError()
                }

                else -> {
                    logger.error(markers, "CieloTokenize")
                    IntegrationError.ServerError()
                }
            }.left()
        } catch (e: Exception) {
            logger.error("CieloTokenize", e)
            return IntegrationError.ServerError(e).left()
        }
    }

    override fun validate(token: CreditCardToken, cvv: String): Either<IntegrationError, Boolean> {
        val request = CieloZeroAuthRequest(
            cardToken = token.value,
            cvv = cvv,
        )
        val call = httpClient.exchange(
            HttpRequest.POST(configuration.zeroAuthPath, request)
                .contentType(MediaType.APPLICATION_JSON_TYPE)
                .headers(headers),
            Argument.of(CieloZeroAuthResponse::class.java),
            Argument.of(Array<CieloErrorResponse>::class.java),
        )

        return try {
            val response = call.blockingSingle()
            val responseTO = response.body()
            logger.info(append("status", response.status).and(append("body", responseTO)), "CieloZeroAuth")
            Either.Right(responseTO?.valid ?: false)
        } catch (e: HttpClientResponseException) {
            logger.error(
                append("status", e.status).and(
                    append(
                        "body",
                        e.response.getBody(Array<CieloErrorResponse>::class.java),
                    ),
                ),
                "CieloZeroAuth",
            )
            Either.Left(IntegrationError.ClientError())
        } catch (e: Exception) {
            logger.error("CieloZeroAuth", e)
            Either.Left(IntegrationError.ServerError(e))
        }
    }

    private fun checkPaymentStatusByPaymentId(paymentId: String, orderId: String): CreditCardAuthorization {
        val requestMap = mutableMapOf<String, Any>("PaymentId" to paymentId)
        val uri = UriBuilder.of(configuration.checkStatusHost + configuration.checkStatusByPaymentIdPath)
            .expand(requestMap)
            .toString()
        val call = httpClient.exchange(
            HttpRequest.GET<Unit>(uri)
                .contentType(MediaType.APPLICATION_JSON_TYPE)
                .headers(headers),
            Argument.of(CieloPaymentResponseTO::class.java),
            Argument.of(CieloErrorResponse::class.java),
        )
        try {
            call.firstOrError().blockingGet().body()?.let { response ->
                val acquirerReturnMessage =
                    if (response.payment.returnCode in PaymentStatus.DENIED_VELOCITY.returnCodes) {
                        PaymentStatus.DENIED_VELOCITY.message
                    } else {
                        val paymentStatus = PaymentStatus.getPaymentStatusByStatusCode(response.payment?.status!!)
                        if (isUnknownUnknownStatusCode(paymentStatus, response)) {
                            logger.error(append("paymentId", paymentId).andAppend("response", response).andAppend("ACTION", "VERIFY"), "CieloAdapter#UnknownUnknownStatusCode")
                        }
                        paymentStatus.message
                    }
                return CreditCardAuthorization(
                    acquirer = Acquirer.CIELO,
                    transactionId = response.merchantOrderId!!,
                    acquirerTid = response.payment.paymentId.takeIf { response.payment.status == 1 }
                        ?: response.payment.paymentId,
                    status = toCreditCardPaymentStatus(response.payment.status),
                    amount = response.payment.amount!!,
                    acquirerReturnCode = response.payment.returnCode,
                    acquirerReturnMessage = acquirerReturnMessage,
                    tid = response.payment.tid,
                    authorizationCode = response.payment.authorizationCode,
                )
            }
            logger.error(append("paymentId", paymentId), "CieloCheckPaymentStatusByPaymentId")
            return CreditCardAuthorization(
                acquirer = Acquirer.CIELO,
                transactionId = orderId,
                status = CreditCardPaymentStatus.ABORTED,
                acquirerReturnMessage = PaymentStatus.UNKNOWN.message,
            )
        } catch (e: Exception) {
            logger.error(append("paymentId", paymentId), "CieloCheckPaymentStatusByPaymentId", e)
            return CreditCardAuthorization(
                acquirer = Acquirer.CIELO,
                transactionId = orderId,
                status = CreditCardPaymentStatus.ABORTED,
                acquirerReturnMessage = PaymentStatus.UNKNOWN.message,
            )
        }
    }

    private fun findPaymentId(orderId: String): PaymentIdResult {
        val uri = UriBuilder.of(configuration.checkStatusHost + configuration.checkStatusByMerchantOrderIdPath)
            .queryParam("merchantOrderId", orderId)
            .toString()
        val call = httpClient.exchange(
            HttpRequest.GET<Unit>(uri)
                .contentType(MediaType.APPLICATION_JSON_TYPE)
                .headers(headers),
            Argument.of(CieloCheckStatusResponse::class.java),
            Argument.of(String::class.java),
        )
        return try {
            call.firstOrError().blockingGet().body()?.let { responseTO ->
                logger.info(append("orderId", orderId).and(append("response", responseTO)), "CieloFindPaymentId")
                val paymentId = responseTO.payments
                    .ifEmpty { return PaymentIdResult.NotFound }
                    .first().paymentId
                return PaymentIdResult.Success(paymentId)
            }
            logger.error(append("orderId", orderId), "CieloFindPaymentId")
            return PaymentIdResult.Error
        } catch (e: HttpClientResponseException) {
            if (e.status == HttpStatus.NOT_FOUND) {
                logger.info(append("orderId", orderId).and(append("status", e.status)), "CieloFindPaymentId")
                return PaymentIdResult.NotFound
            }
            logger.error(
                append("orderId", orderId).and(append("response", e.response.getBody(String::class.java))),
                "CieloFindPaymentId",
            )
            PaymentIdResult.Error
        } catch (e: Exception) {
            logger.error("CieloFindPaymentId", e)
            PaymentIdResult.Error
        }
    }

    private sealed class PaymentIdResult {
        class Success(val paymentId: String) : PaymentIdResult()
        data object NotFound : PaymentIdResult()
        data object Error : PaymentIdResult()
    }

    private fun buildPaymentRequestTO(
        orderId: String,
        amount: Long,
        creditCard: CreditCard,
        softDescriptor: String,
        capture: Boolean,
        installments: Int,
    ): CieloPaymentRequestTO {
        val cardOnFileTO = CieloCardOnFileTO().apply {
            usage = "First"
            reason = "Unscheduled"
        }
        val creditCardTO = CieloCreditCardTO().apply {
            if (creditCard.token != null) {
                cardToken = creditCard.token.value
            } else {
                brand = creditCard.brand.toCieloCreditCardBrandName()
                cardNumber = creditCard.pan
                expirationDate = creditCard.expiryDate
                securityCode = creditCard.securityCode
                cardOnFile = cardOnFileTO
            }
        }
        val paymentoTO = CieloPaymentTO().apply {
            this.amount = amount
            type = "CreditCard"
            this.installments = installments
            this.softDescriptor =
                StringUtils.substring(softDescriptor, 0, 13).replace("[^a-zA-Z0-9]".toRegex(), "") // max 13 chars
            this.creditCard = creditCardTO
            this.isCapture = capture
        }
        return CieloPaymentRequestTO().apply {
            merchantOrderId = orderId
            payment = paymentoTO
            isCryptoCurrencyNegotiation = false
        }
    }

    fun getBinByAcquirerTid(acquirerTid: String): String? {
        val uri =
            UriBuilder.of(configuration.checkStatusHost + configuration.checkStatusByMerchantOrderIdPath + "/$acquirerTid")
                .toString()
        val call = httpClient.exchange(
            HttpRequest.GET<Unit>(uri)
                .contentType(MediaType.APPLICATION_JSON_TYPE)
                .headers(headers),
            Argument.of(CieloGetBinResponseTO::class.java),
            Argument.of(String::class.java),
        )

        try {
            return call.blockingFirst().body.get().payment.creditCard.cardNumber.take(6)
                .takeIf { bin -> bin.all { it.isDigit() } }
        } catch (e: HttpClientResponseException) {
            logger.error(
                append("acquirerTid", acquirerTid)
                    .andAppend("status", e.status)
                    .and(append("response", e.response.getBody(String::class.java))),
                "CieloGetBin",
                e,
            )
            return null
        } catch (e: Exception) {
            logger.error(
                append("acquirerTid", acquirerTid),
                "CieloGetBin",
                e,
            )
            return null
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(CieloAdapter::class.java)
    }
}

private fun CreditCardBrand.toCieloCreditCardBrandName(): String {
    return when (this) {
        CreditCardBrand.AMEX -> "Amex"
        CreditCardBrand.AURA -> "Aura"
        CreditCardBrand.DINERS -> "Diners"
        CreditCardBrand.DISCOVER -> "Discover"
        CreditCardBrand.ELO -> "Elo"
        CreditCardBrand.JCB -> "JCB"
        CreditCardBrand.HIPERCARD -> "Hipercard"
        CreditCardBrand.MASTERCARD -> "Master"
        CreditCardBrand.VISA -> "Visa"
    }
}

private fun CreditCardDetailsTO.toCreditCardDetails() = CreditCardBinDetails(
    provider = this.provider,
    cardType = this.cardType,
    foreignCard = this.foreignCard,
    corporateCard = this.corporateCard,
    issuer = this.issuer,
    issuerCode = this.issuerCode,
    prepaid = this.prepaid,
    status = this.status,
)

@JsonIgnoreProperties(ignoreUnknown = true)
private data class CreditCardDetailsTO(
    @JsonProperty("Provider") val provider: String,
    @JsonProperty("CardType") val cardType: String,
    @JsonProperty("ForeignCard") val foreignCard: Boolean,
    @JsonProperty("CorporateCard") val corporateCard: String,
    @JsonProperty("Issuer") val issuer: String,
    @JsonProperty("IssuerCode") val issuerCode: String,
    @JsonProperty("Prepaid") val prepaid: String,
    @JsonProperty("Status") val status: String,
)

@JsonInclude(JsonInclude.Include.NON_NULL)
class TokenizeRequestTO(
    @JsonProperty("CustomerName")
    val customerName: String,

    @JsonProperty("CardNumber")
    val cardNumber: String,

    @JsonProperty("Holder")
    val holder: String,

    @JsonProperty("ExpirationDate")
    val expirationDate: String,

    @JsonProperty("Brand")
    val brand: String,
)

@JsonIgnoreProperties(ignoreUnknown = true)
class TokenizeResponseTO(
    @JsonProperty("CardToken")
    val cardToken: String,
)

class CieloCancelResponseTO : CieloConfirmationResponseTO()

class CieloCaptureResponseTO : CieloConfirmationResponseTO()

@JsonInclude(JsonInclude.Include.NON_NULL)
class CieloCardOnFileTO {
    @JsonProperty("Usage")
    var usage: String? = null

    @JsonProperty("Reason")
    var reason: String? = null
}

abstract class CieloConfirmationResponseTO {
    @JsonProperty("Status")
    var status: Int = 0

    @JsonProperty("Tid")
    var tid: String? = null

    @JsonProperty("ProofOfSale")
    var proofOfSale: String? = null

    @JsonProperty("AuthorizationCode")
    var authorizationCode: String? = null

    @JsonProperty("ReturnCode")
    var returnCode: String? = null

    @JsonProperty("ReturnMessage")
    var returnMessage: String? = null

    @JsonProperty("Links")
    var links: List<CieloLinkTO>? = null
}

@JsonInclude(JsonInclude.Include.NON_DEFAULT)
class CieloCreditCardTO {
    @JsonProperty("CardNumber")
    var cardNumber: String? = null

    @JsonProperty("Holder")
    var holder: String? = null

    @JsonProperty("ExpirationDate")
    var expirationDate: String? = null

    @JsonProperty("SecurityCode")
    var securityCode: String? = null

    @JsonProperty("Brand")
    var brand: String? = null

    @JsonProperty("SaveCard")
    var isSaveCard: Boolean? = // response only
        null

    @JsonProperty("CardOnFile")
    var cardOnFile: CieloCardOnFileTO? = null

    @JsonProperty("PaymentAccountReference")
    var paymentAccountReference: String? = null

    @JsonProperty("CardToken")
    var cardToken: String? = null
}

class CieloCustomerTO {
    @JsonProperty("Name")
    var name: String? = null
}

enum class CieloErrorCode(val code: Int) {
    TRANSACTION_NOT_AVAILABLE_TO_CAPTURE(308),
    TRANSACTION_NOT_AVAILABLE_TO_VOID(309),
}

class CieloErrorResponse {
    @JsonProperty("Code")
    var code = 0

    @JsonProperty("Message")
    var message: String? = null
}

class CieloLinkTO {
    @JsonProperty("Method")
    var method: String? = null

    @JsonProperty("Rel")
    var rel: String? = null

    @JsonProperty("Href")
    var href: String? = null
}

data class CieloGetBinResponseTO(
    @JsonProperty("Payment") val payment: PaymentCieloGetBinResponseTO,
)

data class PaymentCieloGetBinResponseTO(
    @JsonProperty("CreditCard") val creditCard: CreditCardPaymentCieloGetBinResponseTO,
)

data class CreditCardPaymentCieloGetBinResponseTO(
    @JsonProperty("CardNumber") val cardNumber: String,
)

class CieloPaymentRequestTO {
    @JsonProperty("MerchantOrderId")
    var merchantOrderId: String? = null

    @JsonProperty("Customer")
    var customer: CieloCustomerTO? = null

    @JsonProperty("Payment")
    var payment: CieloPaymentTO? = null

    @JsonProperty("IsCryptoCurrencyNegotiation")
    var isCryptoCurrencyNegotiation = false
}

class CieloPaymentResponseTO {
    @JsonProperty("MerchantOrderId")
    var merchantOrderId: String? = null

    @JsonProperty("Customer")
    var customer: CieloCustomerTO? = null

    @JsonProperty("Payment")
    lateinit var payment: CieloPaymentTO
}

@JsonInclude(JsonInclude.Include.NON_NULL)
class CieloPaymentTO {
    @JsonProperty("Type")
    var type: String? = null

    @JsonProperty("Amount")
    var amount: Long? = null

    @JsonProperty("Installments")
    var installments: Int? = null

    @JsonProperty("SoftDescriptor")
    var softDescriptor: String? = // only request
        null

    @JsonProperty("CreditCard")
    var creditCard: CieloCreditCardTO? = null

    @JsonProperty("Provider")
    var provider: String? = null

    // -- only response fields
    @JsonProperty("ServiceTaxAmount")
    var serviceTaxAmount: Long? = null

    @JsonProperty("Interest")
    var interest: String? = null

    @JsonProperty("Capture")
    var isCapture: Boolean? = null

    @JsonProperty("Authenticate")
    var isAuthenticate: Boolean? = null

    @JsonProperty("IsCryptoCurrencyNegotiation")
    var isCryptoCurrencyNegotiation: Boolean? = null

    @JsonProperty("tryautomaticcancellation")
    var isTryautomaticcancellation: Boolean? = null

    @JsonProperty("ProofOfSale")
    var proofOfSale: String? = null

    @JsonProperty("Tid")
    var tid: String? = null

    @JsonProperty("AuthorizationCode")
    var authorizationCode: String? = null

    @JsonProperty("PaymentId")
    var paymentId: String? = null

    @JsonProperty("Currency")
    var currency: String? = null

    @JsonProperty("Country")
    var country: String? = null

    @JsonProperty("ExtraDataCollection")
    var extraDataCollection: List<String>? = null

    @JsonProperty("Status")
    var status: Int? = null

    @JsonProperty("ReturnCode")
    var returnCode: String? = null

    @JsonProperty("ReturnMessage")
    var returnMessage: String? = null

    @JsonProperty("Links")
    var links: List<CieloLinkTO>? = null
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class CieloCheckStatusResponse(
    @JsonProperty("Payments")
    val payments: List<CieloCheckStatusPaymentResponse>,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class CieloCheckStatusPaymentResponse(
    @JsonProperty("PaymentId") val paymentId: String,
    @JsonProperty("ReceveidDate") val receveidDate: String,
)

data class CieloZeroAuthRequest(
    @JsonProperty("CardToken") val cardToken: String,
    @JsonProperty("SecurityCode") val cvv: String,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class CieloZeroAuthResponse(
    @JsonProperty("Valid") val valid: Boolean,
    @JsonProperty("ReturnCode") val returnCode: String,
    @JsonProperty("ReturnMessage") val returnMessage: String,
)

enum class PaymentStatus(val statusCode: Int, val returnCodes: List<String>, val message: String) {
    NOT_FINISHED(0, listOf(), "Ocorreu um erro ao realizar o pagamento"),
    AUTHORIZED(1, listOf(), "Pagamento autorizado"),
    PAYMENT_CONFIRMED(2, listOf(), "Pagamento confirmado e finalizado"),
    DENIED(
        3,
        listOf(),
        "O pagamento foi negado pelo seu cartão de crédito. Por favor, entre em contato com o seu banco",
    ),
    DENIED_VELOCITY(
        3,
        listOf("GA", "GD"),
        "O pagamento foi negado pelo seu cartão de crédito. Por favor, entre em contato com o atendimento",
    ),
    VOIDED(10, listOf(), "Pagamento cancelado"),
    REFUNDED(11, listOf(), "Pagamento cancelado após 23:59 do dia de autorização"),
    PENDING(12, listOf(), "Aguardando Status de instituição financeira"),
    ABORTED(13, listOf(), "Pagamento cancelado por falha no processamento"),
    SCHEDULED(20, listOf(), "Recorrência agendada"),
    UNKNOWN(-1, listOf(), "Ocorreu um erro ao realizar o pagamento"),
    ;

    companion object {
        fun getPaymentStatusByStatusCode(statusCode: Int, returnCode: String? = null): PaymentStatus {
            values().forEach {
                when {
                    it.statusCode == statusCode && !returnCode.isNullOrEmpty() && (returnCode in it.returnCodes) -> return it
                    it.statusCode == statusCode && returnCode.isNullOrEmpty() -> return it
                }
            }
            return UNKNOWN
        }
    }
}