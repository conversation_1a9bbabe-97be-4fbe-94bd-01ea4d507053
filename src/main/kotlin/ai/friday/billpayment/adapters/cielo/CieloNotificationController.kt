package ai.friday.billpayment.adapters.cielo

import com.fasterxml.jackson.annotation.JsonProperty
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Post
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Controller("/cielo")
class CieloNotificationController {
    @Post("/notifications")
    fun notifications(@Body notificationTO: CieloNotificationTO?): HttpStatus {
        logger.info(
            Markers.append("CieloNotificationTO", notificationTO)
                .and<LogstashMarker>(Markers.append("responseStatus", HttpStatus.OK)),
            "CieloNotificationReceived",
        )
        return HttpStatus.OK
    }

    companion object {
        private val logger = LoggerFactory.getLogger(CieloNotificationController::class.java)
    }
}

/*
1	Pendente	Para todos os meios de pagamento	Indica que o pagamento ainda está sendo processado; OBS: Boleto - Indica que o boleto não teve o status alterado pelo lojista
2	Pago	Para todos os meios de pagamento	Transação capturada e o dinheiro será depositado em conta.
3	Negado	Somente para Cartão Crédito	Transação não autorizada pelo responsável do meio de pagamento
4	Expirado	Cartões de Crédito e Boleto	Transação deixa de ser válida para captura - 15 dias pós Autorização
5	Cancelado	Para cartões de crédito	Transação foi cancelada pelo lojista
6	Não Finalizado	Todos os meios de pagamento	Pagamento esperando Status - Pode indicar erro ou falha de processamento. Entre em contato com o Suporte cielo
7	Autorizado	somente para Cartão de Crédito	Transação autorizada pelo emissor do cartão. Deve ser capturada para que o dinheiro seja depositado em conta
8	Chargeback	somente para Cartão de Crédito	Transação cancelada pelo consumidor junto ao emissor do cartão. O Dinheiro não será depositado em conta.

VALOR	DESCRIÇÃO
1	Cartão de Crédito
2	Boleto Bancário
3	Débito Online
4	Cartão de Débito
 */

class CieloNotificationTO {
    @JsonProperty("order_number")
    var orderNumber: String? = null
    var amount: Long = 0

    @JsonProperty("discount_amount")
    var discountAmount: Long = 0

    @JsonProperty("checkout_cielo_order_number")
    var checkoutCieloOrderNumber: String? = null

    @JsonProperty("created_date")
    var createdDate: String? = null

    @JsonProperty("customer_name")
    var customerName: String? = null

    @JsonProperty("customer_phone")
    var customerPhone: String? = null

    @JsonProperty("customer_identity")
    var customerIdentity: String? = null

    @JsonProperty("customer_email")
    var customerEmail: String? = null

    @JsonProperty("shipping_type")
    var shippingType: Long = 0

    @JsonProperty("shipping_name")
    var shippingName: String? = null

    @JsonProperty("shipping_price")
    var shippingPrice: Long = 0

    @JsonProperty("payment_method_type")
    var paymentMethodType: Long = 0

    @JsonProperty("payment_method_brand")
    var paymentMethodBrand: Long = 0

    @JsonProperty("payment_maskedcreditcard")
    var paymentMaskedCreditcard: String? = null

    @JsonProperty("payment_installments")
    var paymentInstallments: Long = 0

    @JsonProperty("payment_status")
    var paymentStatus: Long = 0

    @JsonProperty("tid")
    var tid: String? = null

    @JsonProperty("test_transaction")
    var testTransaction: String? = null
}