package ai.friday.billpayment.adapters.handlebar

import ai.friday.billpayment.app.CompiledHtml
import ai.friday.billpayment.app.banking.FinancialInstitution
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.CONCESSIONARIA_DIGITABLE_LINE_SIZE
import ai.friday.billpayment.app.integrations.ReceiptTemplateCompiler
import ai.friday.billpayment.app.isValidCpf
import ai.friday.billpayment.app.notification.EmailTemplatesConfiguration
import ai.friday.billpayment.app.payment.AutomaticPixReceiptData
import ai.friday.billpayment.app.payment.BoletoReceiptData
import ai.friday.billpayment.app.payment.InvestmentReceiptData
import ai.friday.billpayment.app.payment.InvoiceReceiptData
import ai.friday.billpayment.app.payment.PixReceiptData
import ai.friday.billpayment.app.payment.ReceiptData
import ai.friday.billpayment.app.payment.buildFormattedAmount
import ai.friday.billpayment.app.payment.buildFormattedHeaderDateTime
import ai.friday.billpayment.app.payment.formatAccountNumber
import ai.friday.billpayment.app.payment.formatAccountType
import ai.friday.billpayment.app.payment.formatBankData
import ai.friday.billpayment.app.payment.formatDocument
import ai.friday.billpayment.app.payment.formatRoutingNumber
import ai.friday.billpayment.app.payment.getDocumentType
import ai.friday.billpayment.app.payment.maskDocument
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.utils.TemplateHelper
import ai.friday.billpayment.app.wallet.Wallet
import jakarta.inject.Singleton
import java.time.format.DateTimeFormatter

@Singleton
class ReceiptHandlebarCompiler(private val emailTemplatesConfiguration: EmailTemplatesConfiguration) : ReceiptTemplateCompiler {
    override fun canBuildFor(receiptData: ReceiptData): Boolean {
        return when (receiptData) {
            is BoletoReceiptData,
            is InvoiceReceiptData,
            is PixReceiptData,
            is AutomaticPixReceiptData,
            -> true

            is InvestmentReceiptData -> false
        }
    }

    override fun buildReceiptHtml(receiptData: ReceiptData, wallet: Wallet): CompiledHtml {
        return when (receiptData) {
            is InvoiceReceiptData -> buildInvoiceReceiptHtml(receiptData)
            is PixReceiptData -> buildPixReceiptHtml(receiptData)
            is AutomaticPixReceiptData -> buildAutomaticPixReceiptHtml(receiptData)
            is BoletoReceiptData -> buildBoletoReceiptHtml(receiptData, wallet)
            is InvestmentReceiptData -> throw IllegalStateException("Investment receipt data not supported")
        }
    }

    override fun buildReceiptMailHtml(receiptData: ReceiptData): CompiledHtml {
        return when (receiptData) {
            is InvoiceReceiptData -> buildInvoiceReceiptMailHtml(receiptData)
            is PixReceiptData -> buildPixReceiptMailHtml(receiptData)
            is AutomaticPixReceiptData -> buildAutomaticPixReceiptMailHtml(receiptData)
            is BoletoReceiptData -> buildBoletoReceiptMailHtml(receiptData)
            is InvestmentReceiptData -> throw IllegalStateException("Investment receipt data not supported")
        }
    }

    private fun buildPixReceiptHtml(receiptData: PixReceiptData): CompiledHtml {
        val map = buildPixReceiptMap(receiptData) + mapOf(
            "typeLine1" to "Pix",
            "typeLine2" to "enviado!",
        )
        return CompiledHtml(TemplateHelper.applyTemplate(emailTemplatesConfiguration.local.pixReceipt, map))
    }

    private fun buildInvoiceReceiptHtml(receiptData: InvoiceReceiptData): CompiledHtml {
        val extras: List<ReceiptExtras> = listOfNotNull(
            if (receiptData.purpose.isNullOrEmpty()) {
                null
            } else {
                ReceiptExtras("Finalidade: ${receiptData.purpose}")
            },
        )

        val map = mapOf(
            "title" to "Comprovante",
            "dateTime" to buildFormattedHeaderDateTime(receiptData.dateTime),
            "isInvoice" to true,
            "typeLine1" to "TED",
            "typeLine2" to "realizada!",
            "totalAmount" to buildFormattedAmount(receiptData.totalAmount),
            "isPayerAPerson" to (receiptData.payer.document.isValidCpf()),
            "payerName" to receiptData.payer.name?.uppercase(),
            "payerDetails" to listOf(
                maskDocument(receiptData.payer.document),
                "Banco Arbi S.A.",
            ),
            "isRecipientAPerson" to (receiptData.recipient.document?.isValidCpf() == true),
            "recipientName" to receiptData.recipient.name.uppercase(),
            "recipientDetails" to listOfNotNull(
                maskDocument(receiptData.recipient.document),
                formatBankData(
                    FinancialInstitution(name = receiptData.payeeBank, ispb = receiptData.recipient.bankAccount?.ispb, compe = receiptData.recipient.bankAccount?.bankNo),
                ),
                if (receiptData.recipient.bankAccount != null) {
                    "Agência: ${formatRoutingNumber(receiptData.recipient.bankAccount.routingNo.toString())}"
                } else {
                    null
                },
                if (receiptData.recipient.bankAccount != null) {
                    "Conta ${formatAccountType(receiptData.recipient.bankAccount.accountType)}: ${formatAccountNumber(receiptData.recipient.bankAccount.accountNo, receiptData.recipient.bankAccount.accountDv)}"
                } else {
                    null
                },
            ),
            "extras" to extras,
            "footer" to listOf(
                ReceiptExtras("Id: ${receiptData.authentication}"),
                ReceiptExtras("Pago via Banco Arbi - CNPJ ${paymentPartnerCNPJ("arbi")}"),
            ),
        )
        return CompiledHtml(TemplateHelper.applyTemplate(emailTemplatesConfiguration.local.invoiceReceipt, map))
    }

    private fun buildBoletoReceiptHtml(receiptData: BoletoReceiptData, wallet: Wallet): CompiledHtml {
        val fixedPartnerName = fixPartnerName(receiptData.paymentPartnerName)

        val map = mapOf(
            "title" to "Comprovante",
            "dateTime" to buildFormattedHeaderDateTime(receiptData.dateTime),
            "isBarcode" to true,
            "typeLine1" to "Boleto",
            "typeLine2" to "pago!",
            "totalAmount" to buildFormattedAmount(receiptData.totalAmount),
            "isPayerAPerson" to (wallet.founder.document.isValidCpf()),
            "payerName" to wallet.founder.name.uppercase(),
            "payerDetails" to listOf(
                maskDocument(wallet.founder.document),
                "Banco Arbi S.A.",
            ),
            "isRecipientAPerson" to (receiptData.recipient?.document?.isValidCpf() == true),
            "recipientName" to receiptData.recipientName.uppercase(),
            "recipientDetails" to listOfNotNull(
                receiptData.recipient?.let { "${getDocumentType(receiptData.recipient.document)}: ${maskDocument(receiptData.recipient.document)}" },
                if (receiptData.recipient?.name != null) {
                    receiptData.assignor
                } else {
                    null
                },
            ),
            "extras" to listOf(
                ReceiptExtras("Vencimento: ${receiptData.dueDate.format(DateTimeFormatter.ofPattern("dd/MM/yyyy"))}", true),
                /*ReceiptExtras("Desconto: ???"),
                ReceiptExtras("Juros: ???"),
                ReceiptExtras("Multa: ???"),
                ReceiptExtras("Descrição: ???"),*/
                if (!receiptData.barcode.isConcessionaria()) {
                    receiptData.payer?.name?.let { ReceiptExtras("Emitido para: ${receiptData.payer.name} (${getDocumentType(receiptData.payer.document)}: ${maskDocument(receiptData.payer.document)})") }
                } else {
                    receiptData.paymentPartnerName?.let { ReceiptExtras("Através de: ${receiptData.paymentPartnerName}") }
                },
                ReceiptExtras("Código de barras: ${receiptData.barcode.formattedDigitable()}"),
            ),

            "footer" to listOf(
                ReceiptExtras("Id: ${receiptData.authentication}"),
                ReceiptExtras("Pago via $fixedPartnerName - CNPJ ${paymentPartnerCNPJ(fixedPartnerName)}"),
            ),
        )

        return CompiledHtml(TemplateHelper.applyTemplate(emailTemplatesConfiguration.local.barcodeBillReceipt, map))
    }

    private fun buildPixReceiptMailHtml(receiptData: PixReceiptData): CompiledHtml {
        val map = mapOf(
            "totalAmount" to buildFormattedAmount(receiptData.totalAmount),
            "date" to buildFormattedHeaderDateTime(receiptData.dateTime),
            "recipientName" to receiptData.recipient.name,
            "recipientDocument" to maskDocument(receiptData.recipient.document),
            "billTypeLabel" to "PIX",
            "authentication" to receiptData.authentication,
        )
        return CompiledHtml(TemplateHelper.applyTemplate(emailTemplatesConfiguration.local.mailReceipt, map))
    }

    private fun buildAutomaticPixReceiptMailHtml(receiptData: AutomaticPixReceiptData): CompiledHtml {
        val map = mapOf(
            "totalAmount" to buildFormattedAmount(receiptData.totalAmount),
            "date" to buildFormattedHeaderDateTime(receiptData.dateTime),
            "recipientName" to receiptData.pixReceiptData.recipient.name,
            "recipientDocument" to maskDocument(receiptData.pixReceiptData.recipient.document),
            "billTypeLabel" to "PIX AUTOMÁTICO",
            "authentication" to receiptData.pixReceiptData.authentication,
        )
        return CompiledHtml(TemplateHelper.applyTemplate(emailTemplatesConfiguration.local.mailReceipt, map))
    }

    private fun buildInvoiceReceiptMailHtml(receiptData: InvoiceReceiptData): CompiledHtml {
        val map = mapOf(
            "totalAmount" to buildFormattedAmount(receiptData.totalAmount),
            "date" to buildFormattedHeaderDateTime(receiptData.dateTime),
            "recipientName" to receiptData.recipient.name,
            "recipientDocument" to maskDocument(receiptData.recipient.document),
            "billTypeLabel" to "TED",
            "authentication" to receiptData.authentication,
        )
        return CompiledHtml(TemplateHelper.applyTemplate(emailTemplatesConfiguration.local.mailReceipt, map))
    }

    private fun buildBoletoReceiptMailHtml(receiptData: BoletoReceiptData): CompiledHtml {
        val map = mapOf(
            "totalAmount" to buildFormattedAmount(receiptData.totalAmount),
            "date" to buildFormattedHeaderDateTime(receiptData.dateTime),
            "recipientName" to (receiptData.recipient?.name ?: receiptData.assignor),
            "recipientDocument" to maskDocument(receiptData.recipient?.document),
            "typeableLine" to receiptData.barcode.formattedDigitable(),
            "authentication" to receiptData.authentication,
        )
        return CompiledHtml(TemplateHelper.applyTemplate(emailTemplatesConfiguration.local.mailReceipt, map))
    }

    private fun buildAutomaticPixReceiptHtml(receiptData: AutomaticPixReceiptData): CompiledHtml {
        val pixReceiptMapData = buildPixReceiptMap(
            receiptData.pixReceiptData,
            listOfNotNull(
                receiptData.automaticPixDescription?.let { ReceiptExtras(it) },
                receiptData.contractNumber?.let { ReceiptExtras("Contrato: $it") },
            ),
        )

        return CompiledHtml(TemplateHelper.applyTemplate(emailTemplatesConfiguration.local.pixReceipt, pixReceiptMapData))
    }

    private fun buildPixReceiptMap(receiptData: PixReceiptData, extras: List<ReceiptExtras> = emptyList()): Map<String, Any?> {
        val finalExtras: List<ReceiptExtras> = extras + listOfNotNull(
            if (receiptData.description.isNullOrEmpty()) {
                null
            } else {
                ReceiptExtras("Descrição: ${receiptData.description}")
            },
            receiptData.recipient.pixQrCodeData?.pixId?.let { ReceiptExtras(it) },
        ) + (
            receiptData.recipient.pixQrCodeData?.additionalInfo?.toList()?.map {
                ReceiptExtras("${it.first}: ${it.second}")
            } ?: emptyList()
            )

        return mapOf(
            "title" to "Comprovante",
            "dateTime" to buildFormattedHeaderDateTime(receiptData.dateTime),
            "isPix" to true,
            "totalAmount" to buildFormattedAmount(receiptData.totalAmount),
            "isPayerAPerson" to (receiptData.payer.document.isValidCpf()),
            "payerName" to receiptData.payer.name?.uppercase(),
            "payerDetails" to listOf(
                maskDocument(receiptData.payer.document),
                "Banco Arbi S.A.",
            ),
            "isRecipientAPerson" to (receiptData.recipient.document?.isValidCpf() == true),
            "recipientName" to receiptData.recipient.name.uppercase(),
            "recipientDetails" to listOfNotNull(
                maskDocument(receiptData.recipient.document),
                formatBankData(receiptData.payeeFinancialInstitution),
                if (receiptData.recipient.pixQrCodeData == null) {
                    receiptData.recipient.pixKeyDetails?.key?.let { "Chave Pix: ${receiptData.recipient.pixKeyDetails.key.let { formatPixKey(it) }}" }
                } else {
                    null
                },
                if (receiptData.recipient.bankAccount != null) {
                    "Agência: ${formatRoutingNumber(receiptData.recipient.bankAccount.routingNo.toString())}"
                } else {
                    null
                },
                if (receiptData.recipient.bankAccount != null) {
                    "Conta ${formatAccountType(receiptData.payeeAccountType)}: ${formatAccountNumber(receiptData.payeeAccountNo, receiptData.payeeAccountDv)}"
                } else {
                    null
                },
                if (receiptData.recipient.pspInformation != null) {
                    "Instituição do recebedor: ${listOfNotNull(receiptData.recipient.pspInformation.code, receiptData.recipient.pspInformation.name).joinToString(" - ")}"
                } else {
                    null
                },
            ),
            "extras" to finalExtras,
            "footer" to listOf(
                ReceiptExtras("Id: ${receiptData.authentication}"),
                ReceiptExtras("Pago via Banco Arbi - CNPJ ${paymentPartnerCNPJ("arbi")}"),
            ),
        )
    }

    private fun formatPixKey(pixKey: PixKey?): String {
        return pixKey?.let {
            when (it.type) {
                PixKeyType.CNPJ, PixKeyType.CPF -> formatDocument(it.value)
                PixKeyType.PHONE -> formatPhone(it.value)
                else -> it.value
            }
        } ?: ""
    }

    private fun formatPhone(value: String): String {
        return "(${value.substring(3, 5)}) ${value.substring(5, 6)} ${value.substring(6, 10)}-${value.substring(10)}"
    }

    private fun BarCode.isConcessionaria() = when (digitable.length) {
        CONCESSIONARIA_DIGITABLE_LINE_SIZE -> true
        else -> false
    }
}

private data class ReceiptExtras(
    val content: String,
    val highlight: Boolean = false,
)

private fun paymentPartnerCNPJ(name: String?): String {
    if (name != null) {
        if (name.lowercase().contains("arbi")) {
            return "54.403.563/0001-50"
        } else if (name.lowercase().contains("celcoin")) {
            return "13.935.893/0003-70"
        }
    }
    return ""
}

private fun fixPartnerName(paymentPartnerName: String?): String {
    if (paymentPartnerName == null || paymentPartnerName.lowercase().contains("arbi")) {
        return "Banco Arbi"
    }
    return "Celcoin"
}