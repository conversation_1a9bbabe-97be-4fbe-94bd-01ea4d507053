package ai.friday.billpayment.adapters.handlebar

import ai.friday.billpayment.app.CompiledHtml
import ai.friday.billpayment.app.DeclarationOfResidencyForm
import ai.friday.billpayment.app.integrations.TemplateCompiler
import ai.friday.billpayment.app.integrations.TemplateForm
import ai.friday.billpayment.app.notification.EmailTemplatesConfiguration
import ai.friday.billpayment.app.register.kyc.KycDossierForm
import ai.friday.billpayment.app.utils.TemplateHelper
import jakarta.inject.Singleton

@Singleton
class HandlebarTemplateCompiler(
    private val emailTemplatesConfiguration: EmailTemplatesConfiguration,
) : TemplateCompiler {

    override fun buildHtml(form: TemplateForm): CompiledHtml {
        val template = when (form) {
            is KycDossierForm -> emailTemplatesConfiguration.local.kycDossierFormPath
            is DeclarationOfResidencyForm -> emailTemplatesConfiguration.local.declarationOfResidencyFormPath
            else -> throw IllegalStateException()
        }

        return CompiledHtml(TemplateHelper.applyTemplate(template, form))
    }
}