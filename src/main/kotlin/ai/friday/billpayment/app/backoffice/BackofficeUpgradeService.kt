package ai.friday.billpayment.app.backoffice

import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.RegisterService
import ai.friday.billpayment.app.account.UpgradeStatus
import ai.friday.billpayment.app.integrations.AccountRepository
import arrow.core.getOrElse
import jakarta.inject.Singleton
import java.time.ZonedDateTime
import org.slf4j.LoggerFactory

@Singleton
class BackofficeUpgradeService(
    private val accountRepository: AccountRepository,
    private val registerService: RegisterService,
) {
    fun getAll(): List<UserAccount> {
        val underReviewAccounts = accountRepository.findAccountByUpgradeStatus(UpgradeStatus.UNDER_REVIEW)
        val underExternalReviewAccounts = accountRepository.findAccountByUpgradeStatus(UpgradeStatus.UNDER_EXTERNAL_REVIEW)
        val externalDeniedAccounts = accountRepository.findAccountByUpgradeStatus(UpgradeStatus.EXTERNAL_DENIED)

        val accounts = underReviewAccounts + underExternalReviewAccounts + externalDeniedAccounts

        return accounts.map { account ->
            val register = registerService.findByAccountId(account.accountId).getOrElse { throw it }

            account.toUserAccount(register.lastUpdated)
        }
    }

    fun get(accountId: AccountId): UserAccount {
        val account = accountRepository.findById(accountId)
        val register = registerService.findByAccountId(account.accountId).getOrElse { throw it }

        return account.toUserAccount(register.lastUpdated)
    }

    private fun Account.toUserAccount(lastUpdated: ZonedDateTime): UserAccount {
        return UserAccount(
            accountId = this.accountId,
            name = this.name,
            document = this.document,
            email = this.emailAddress,
            phone = this.mobilePhone,
            status = this.status,
            upgradeStatus = this.upgradeStatus,
            updated = lastUpdated,
        )
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(BackofficeUpgradeService::class.java)
    }
}

data class UserAccount(
    val accountId: AccountId,
    val name: String,
    val document: String,
    val email: EmailAddress,
    val phone: String,
    val status: AccountStatus,
    val upgradeStatus: UpgradeStatus?,
    val updated: ZonedDateTime,
)