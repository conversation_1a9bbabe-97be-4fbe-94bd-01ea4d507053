package ai.friday.billpayment.app.backoffice

import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountClosureDetails
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountIsNotUnderReview
import ai.friday.billpayment.app.account.AccountNotFoundException
import ai.friday.billpayment.app.account.AccountRegisterData
import ai.friday.billpayment.app.account.AccountRegisterService
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.Address
import ai.friday.billpayment.app.account.CloseAccountService
import ai.friday.billpayment.app.account.DocumentInfo
import ai.friday.billpayment.app.account.MonthlyIncome
import ai.friday.billpayment.app.account.PartialAccount
import ai.friday.billpayment.app.account.PoliticallyExposed
import ai.friday.billpayment.app.account.RegistrationType
import ai.friday.billpayment.app.account.UpgradeIsNotUnderReview
import ai.friday.billpayment.app.account.UpgradeStatus
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.account.getCPF
import ai.friday.billpayment.app.account.getName
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.RegisterInstrumentationService
import ai.friday.billpayment.app.liveness.LivenessId
import ai.friday.billpayment.app.liveness.LivenessService
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import java.time.ZonedDateTime
import java.util.Base64
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
class BackofficeRegisterService(
    private val accountRepository: AccountRepository,
    private val accountRegisterRepository: AccountRegisterRepository,
    private val accountService: AccountService,
    private val closeAccountService: CloseAccountService,
    private val registerInstrumentationService: RegisterInstrumentationService,
    private val notificationAdapter: NotificationAdapter,
    private val livenessService: LivenessService,
    private val accountRegisterService: AccountRegisterService,
    @Property(name = "deeplink-url") private val deeplinkUrl: String,
) {
    fun getPending(): List<UserAccountRegister> {
        val internalReview = accountRepository.findPartialAccountByStatus(AccountStatus.UNDER_REVIEW)
            .map { it.toRegister(checkOpenAccount = true) }

        val externalReview = accountRepository.findPartialAccountByStatus(AccountStatus.UNDER_EXTERNAL_REVIEW)
            .map { it.toRegister(checkOpenAccount = true) }

        val approved = accountRepository.findPartialAccountByStatus(AccountStatus.APPROVED)
            .map { it.toRegister(checkOpenAccount = true) }

        return (internalReview + externalReview + approved).distinctBy { it.accountId } // TODO: não é claro o motivo do distinct
    }

    fun get(accountId: AccountId): UserAccountRegister {
        return try {
            accountRepository.findPartialAccountById(accountId).toRegister()
        } catch (e: AccountNotFoundException) {
            accountRepository.findById(accountId).toUserAccountRegister()
        }
    }

    fun updateDocumentOrgEmission(accountId: AccountId, newOrg: String, oldOrg: String) {
        val accountRegisterData = accountRegisterRepository.findByAccountId(accountId)

        if (accountRegisterData.documentInfo == null || accountRegisterData.documentInfo.orgEmission != oldOrg) {
            throw IllegalStateException()
        }

        accountRegisterRepository.save(
            accountRegisterData.copy(
                documentInfo = accountRegisterData.documentInfo.copy(
                    orgEmission = newOrg,
                ),
            ),
        )
    }

    fun updateCity(accountId: AccountId, newCity: String, oldCity: String) {
        val accountRegisterData = accountRegisterRepository.findByAccountId(accountId)

        if (accountRegisterData.address == null || accountRegisterData.address.city != oldCity) {
            throw IllegalStateException()
        }

        accountRegisterRepository.save(
            accountRegisterData.copy(
                address = accountRegisterData.address.copy(
                    city = newCity,
                ),
            ),
        )
    }

    fun createAccountRegister(accountRegisterRequest: CreateAccountRegisterRequest): Either<Exception, Unit> {
        return try {
            val account = accountRepository.findById(accountRegisterRequest.accountId)

            val accountRegisterData = accountRegisterRequest.toAccountRegisterData(account)

            accountRegisterRepository.save(accountRegisterData)
            Unit.right()
        } catch (e: Exception) {
            e.left()
        }
    }

    fun denyAccount(accountId: AccountId, closureDetails: AccountClosureDetails): Either<Exception, Unit> {
        return try {
            val partialAccount = accountService.findPartialAccountById(accountId)
            if (partialAccount.status != AccountStatus.UNDER_REVIEW) {
                return Either.Left(AccountIsNotUnderReview(partialAccount.status))
            }
            val accountRegisterData = accountRegisterRepository.findByAccountId(accountId)
            accountService.updatePartialAccountStatus(accountId, AccountStatus.DENIED)

            closeAccountService.deactivateAccountRegister(accountId, closureDetails).getOrElse {
                return it.left()
            }
            registerInstrumentationService.rejected(
                partialAccount.id,
                partialAccount.registrationType,
                emptyList(),
            )
            notificationAdapter.notifyRegisterDenied(accountId = accountId, accountRegisterData.mobilePhone!!)
            Either.Right(Unit)
        } catch (e: Exception) {
            Either.Left(e)
        }
    }

    fun denyUpgrade(accountId: AccountId, reason: String): Either<Exception, Unit> {
        return try {
            val account = accountService.findAccountById(accountId)
            if (account.upgradeStatus != UpgradeStatus.UNDER_REVIEW) {
                return Either.Left(UpgradeIsNotUnderReview(account.upgradeStatus))
            }

            accountService.updateUpgradeStatus(accountId, UpgradeStatus.DENIED)

            Either.Right(Unit)
        } catch (e: Exception) {
            Either.Left(e)
        }
    }

    fun reviewAccount(accountId: AccountId): Either<Exception, Unit> {
        return try {
            val partialAccount = accountService.findPartialAccountById(accountId)
            if (partialAccount.status != AccountStatus.UNDER_REVIEW) {
                return Either.Left(AccountIsNotUnderReview(partialAccount.status))
            }
            accountService.updatePartialAccountStatus(accountId, AccountStatus.REGISTER_INCOMPLETE)
            val accountRegisterUpdated = accountRegisterRepository.findByAccountId(accountId)
                .copy(agreementData = null, openForUserReview = true, openedForUserReviewAt = getZonedDateTime())
            accountRegisterRepository.save(accountRegisterUpdated)
            registerInstrumentationService.reopened(
                partialAccount.id,
                accountRegisterUpdated.registrationType,
                emptyList(),
            )
            Either.Right(Unit)
        } catch (e: Exception) {
            Either.Left(e)
        }
    }

    fun reopenUpgrade(accountId: AccountId): Either<Exception, Unit> {
        try {
            val account = accountService.findAccountById(accountId)

            if (account.upgradeStatus != UpgradeStatus.UNDER_REVIEW) {
                return Either.Left(UpgradeIsNotUnderReview(account.upgradeStatus))
            }

            accountService.updateUpgradeStatus(accountId, UpgradeStatus.INCOMPLETE)

            val accountRegister = accountRegisterRepository.findByAccountId(accountId)

            accountRegisterRepository.save(
                accountRegister.copy(
                    openForUserReview = true,
                    openedForUserReviewAt = getZonedDateTime(),
                    upgradeAgreementData = null,
                ),
            )

            return Either.Right(Unit)
        } catch (e: Exception) {
            return e.left()
        }
    }

    fun getUrlToUpdateLiveness(accountId: AccountId): String {
        val response = livenessService.match(accountId).getOrElse {
            throw IllegalStateException("Error matching liveness for account $accountId")
        }

        val livenessIdBase64 = Base64.getEncoder().encodeToString(response.value.toByteArray())

        return "${deeplinkUrl}liveness/cadastrar/$livenessIdBase64"
    }

    fun updateStoredSelfie(accountId: AccountId, livenessId: LivenessId) {
        val accountRegister = accountRegisterRepository.findByAccountId(accountId)

        val markers = Markers.append("accountId", accountId.value)
            .andAppend("livenessId", livenessId.value)

        val storedObject = accountRegisterService.processSelfie(accountId, livenessId).getOrElse {
            LOG.error(markers, "AccountRegisterData#captureSelfie", it)
            throw IllegalStateException("Error processing selfie")
        }

        markers.andAppend("storedObject", storedObject)

        accountRegisterRepository.save(accountRegister.copy(uploadedSelfie = storedObject))

        LOG.info(markers, "BackofficeRegisterService#updateStoredSelfie")
    }

    private fun PartialAccount.toRegister(checkOpenAccount: Boolean = true): UserAccountRegister {
        val accountRegisterData = accountRegisterRepository.findByAccountId(
            accountId = id,
            checkOpenAccount = checkOpenAccount,
        )

        return accountRegisterData.toUserAccountRegister(
            status = status,
            updated = statusUpdated,
            subscriptionType = subscriptionType,
        )
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(BackofficeRegisterService::class.java)
    }
}

data class UserAccountRegister(
    val accountId: AccountId,
    val name: String,
    val document: String,
    val email: EmailAddress,
    val phone: MobilePhone,
    val status: AccountStatus,
    val updated: ZonedDateTime?,
    val groups: List<AccountGroup>,
    val userAccountType: UserAccountType?,
    val subscriptionType: SubscriptionType,
)

data class CreateAccountRegisterRequest(
    val address: Address,
    val documentInfo: DocumentInfo,
    val monthlyIncome: MonthlyIncome,
    val accountId: AccountId,
)

class CancelInAppSubscriptionException(val accountId: AccountId) : Exception()

private fun AccountRegisterData.toUserAccountRegister(
    status: AccountStatus,
    updated: ZonedDateTime?,
    subscriptionType: SubscriptionType,
) =
    UserAccountRegister(
        accountId = accountId, // OK
        name = getName(), //
        updated = updated,
        document = getCPF()!!,
        email = emailAddress,
        phone = mobilePhone!!,
        status = status,
        groups = emptyList(),
        userAccountType = null,
        subscriptionType = subscriptionType,
    )

fun Account.toUserAccountRegister() = UserAccountRegister(
    accountId = accountId,
    name = name,
    document = document,
    email = emailAddress,
    phone = MobilePhone(mobilePhone),
    status = status,
    updated = created,
    groups = configuration.groups,
    userAccountType = type,
    subscriptionType = subscriptionType,
)

private fun CreateAccountRegisterRequest.toAccountRegisterData(account: Account): AccountRegisterData {
    return AccountRegisterData(
        accountId = this.accountId,
        emailAddress = account.emailAddress,
        nickname = account.name,
        mobilePhone = MobilePhone(account.mobilePhone),
        mobilePhoneVerified = true,
        mobilePhoneTokenExpiration = null,
        created = getZonedDateTime(),
        lastUpdated = getZonedDateTime(),
        documentInfo = this.documentInfo,
        calculatedGender = null,
        isDocumentEdited = false,
        uploadedCNH = null,
        uploadedDocument = null,
        livenessId = null,
        uploadedSelfie = null,
        address = this.address,
        monthlyIncome = this.monthlyIncome,
        politicallyExposed = PoliticallyExposed(false, null),
        agreementData = null,
        kycFile = null,
        openForUserReview = false,
        openedForUserReviewAt = null,
        externalId = null,
        riskAnalysisFailedReasons = null,
        riskAnalysisFailedReasonsHistory = null,
        registrationType = RegistrationType.FULL,
        fraudListMatch = false,
    )
}