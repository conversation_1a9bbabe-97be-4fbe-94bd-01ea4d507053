package ai.friday.billpayment.app.backoffice

import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.CreditCardConfiguration
import ai.friday.billpayment.app.account.LegacyAccountConfiguration
import ai.friday.billpayment.app.account.NotificationGateways
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.dda.DDAProvider
import ai.friday.billpayment.app.dda.DDAService
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.CrmService
import ai.friday.billpayment.app.integrations.DocumentType
import ai.friday.billpayment.app.integrations.EventPublisher
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.payment.isCPF
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.pix.RegisterPixKeyCommand
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.app.wallet.BillPermission
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.MemberAdded
import ai.friday.billpayment.app.wallet.MemberPermissions
import ai.friday.billpayment.app.wallet.MemberStatus
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.app.wallet.WalletStatus
import ai.friday.billpayment.app.wallet.WalletType
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import java.util.UUID
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
class BackofficeCNPJService(
    private val accountRepository: AccountRepository,
    private val ddaService: DDAService,
    private val messagePublisher: MessagePublisher,
    private val walletRepository: WalletRepository,
    private val walletService: WalletService,
    private val crmService: CrmService,
    private val walletBillCategoryService: PFMWalletCategoryService,
    private val eventPublisher: EventPublisher,
    @Property(name = "features.inAppSubscription", defaultValue = "0") private val inAppSubscription: Float,
    @Property(name = "sqs.registerPixKeyQueueName") private val registerPixKeyQueueName: String,
) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    data class CreateCNPJAccountResult(
        val accountId: AccountId,
        val pixKeyCreated: Boolean,
        val evpPixKeyCreated: Boolean,
        val crmCreatedPJ: Boolean,
        val crmUpdatedPF: Boolean,
    )

    fun createCNPJAccount(accountId: AccountId, cnpjData: CNPJCreateData): CreateCNPJAccountResult {
        val logName = "BackofficeCNPJService#createCNPJAccount"
        val markers = Markers.append("accountIdPF", accountId.value)
        val accountPF = accountRepository.findById(accountId)

        val accountIdPj = AccountId()
        markers.andAppend("accountIdPJ", accountIdPj.value)

        val walletIdPj = WalletId(accountIdPj.value)
        val paymentMethodIdPj = AccountPaymentMethodId(UUID.randomUUID().toString())
        val bankAccountPj = BankAccount(
            accountType = AccountType.CHECKING,
            bankNo = 213,
            routingNo = 1,
            accountNo = cnpjData.accountNo.toBigInteger(),
            accountDv = cnpjData.accountDv,
            document = cnpjData.cnpj,
            ispb = null,
        )

        val accountPJ = Account(
            accountId = accountIdPj,
            name = cnpjData.businessName,
            emailAddress = accountPF.emailAddress,
            document = cnpjData.cnpj,
            documentType = DocumentType.CNPJ.value,
            mobilePhone = accountPF.mobilePhone,
            activated = getZonedDateTime(),
            status = AccountStatus.ACTIVE,
            upgradeStatus = null,
            configuration = LegacyAccountConfiguration(
                accountId = null,
                creditCardConfiguration = CreditCardConfiguration(quota = 200000),
                defaultWalletId = WalletId(accountIdPj.value),
                receiveDDANotification = false,
                receiveNotification = false,
                accessToken = null,
                refreshToken = null,
                externalId = null,
                groups = listOf(AccountGroup.CREDIT_CARD_STANDARD_PLAN, AccountGroup.SUBSCRIPTION_STANDARD_PLAN),
                notificationGateway = NotificationGateways.WHATSAPP,
            ),
            firstLoginAsOwner = null,
            type = UserAccountType.FULL_ACCOUNT,
            channel = null,
            created = getZonedDateTime(),
            updated = getZonedDateTime(),
            imageUrlSmall = null,
            imageUrlLarge = null,
            subscriptionType = SubscriptionType.random(inAppSubscription),
        )
        markers.andAppend("account", accountPJ)
        accountRepository.create(
            accountPJ,
        )

        accountRepository.createAccountPaymentMethod(accountId = accountIdPj, bankAccount = bankAccountPj, position = 1, paymentMethodId = paymentMethodIdPj)

        val permissions = MemberPermissions(
            viewBills = BillPermission.ALL_BILLS,
            scheduleBills = BillPermission.ALL_BILLS,
            founderContactsEnabled = true,
            manageMembers = true,
            viewBalance = true,
            notification = true,
        )

        val founderMember = Member(
            permissions = permissions,
            accountId = accountIdPj,
            document = cnpjData.cnpj,
            name = cnpjData.businessName,
            emailAddress = accountPF.emailAddress,
            type = MemberType.FOUNDER,
            status = MemberStatus.ACTIVE,
            created = getZonedDateTime(),
            updated = getZonedDateTime(),
        )

        walletRepository.createMember(
            walletId = walletIdPj,
            member = founderMember,
        )

        val coFounderMember = Member(
            permissions = permissions,
            accountId = accountPF.accountId,
            document = accountPF.document,
            name = accountPF.name,
            emailAddress = accountPF.emailAddress,
            type = MemberType.COFOUNDER,
            status = MemberStatus.ACTIVE,
            created = getZonedDateTime(),
            updated = getZonedDateTime(),
        )

        walletRepository.createMember(
            walletId = walletIdPj,
            member = coFounderMember,
        )

        eventPublisher.publish(
            event = MemberAdded(
                walletId = walletIdPj,
                member = coFounderMember.accountId,
                actionSource = ActionSource.System,
            ),
        )

        val wallet = Wallet(id = walletIdPj, name = cnpjData.businessName, members = listOf(founderMember, coFounderMember), maxOpenInvitations = 10, status = WalletStatus.ACTIVE, type = WalletType.PRIMARY, paymentMethodId = paymentMethodIdPj)
        walletRepository.save(wallet)

        walletBillCategoryService.createDefaultWalletCategories(walletIdPj)

        ddaService.register(accountId = accountIdPj, document = cnpjData.cnpj, ddaProvider = DDAProvider.ARBI)

        val pjAccountNumber = AccountNumber(bankAccountPj.accountNo, bankAccountPj.accountDv)

        val pixKeyCreated = try {
            messagePublisher.sendMessage(
                registerPixKeyQueueName,
                RegisterPixKeyCommand(
                    accountNo = pjAccountNumber,
                    key = walletService.walletPixKey(wallet = wallet),
                    document = cnpjData.cnpj,
                    name = cnpjData.businessName,
                    walletId = wallet.id,
                ),
            )
            true
        } catch (e: Exception) {
            logger.error(markers.andAppend("context", "error registering pix key"), logName, e)
            false
        }

        val evpPixKeyCreated = try {
            messagePublisher.sendMessage(
                registerPixKeyQueueName,
                RegisterPixKeyCommand(
                    accountNo = pjAccountNumber,
                    key = PixKey(value = "", type = PixKeyType.EVP),
                    document = cnpjData.cnpj,
                    name = cnpjData.businessName,
                    walletId = wallet.id,
                ),
            )
            true
        } catch (e: Exception) {
            logger.error(markers.andAppend("context", "error registering evp pix key"), logName, e)
            false
        }

        val createdOnCrmPJ = upsertOnCRM(accountPJ, markers, logName)
        val updatedOnCrmPF = upsertOnCRM(accountPF, markers, logName)

        return CreateCNPJAccountResult(accountId = accountIdPj, pixKeyCreated = pixKeyCreated, evpPixKeyCreated = evpPixKeyCreated, crmCreatedPJ = createdOnCrmPJ, crmUpdatedPF = updatedOnCrmPF).also {
            logger.info(markers.andAppend("result", it), logName)
        }
    }

    fun associateAccountIdsToWalletId(walletId: WalletId, accountIds: List<AccountId>, memberType: MemberType): Either<AssociateError, Unit> {
        val logName = "BackofficeCNPJService#associateAccountIdsToWalletId"
        val wallet = walletRepository.findWallet(walletId)

        if (isCPF(wallet.founder.document)) {
            return AssociateError.CPFNotAllowed.left()
        }

        if (memberType == MemberType.FOUNDER) {
            return AssociateError.FounderNotAllowed.left()
        }

        accountIds.forEach { accountId ->
            val accountPF = accountRepository.findById(accountId)

            walletRepository.createMember(
                walletId = walletId,
                member = Member(
                    accountId = accountId,
                    document = accountPF.document,
                    name = accountPF.name,
                    emailAddress = accountPF.emailAddress,
                    type = memberType,
                    permissions = MemberPermissions.of(memberType),
                    status = MemberStatus.ACTIVE,
                    created = getZonedDateTime(),
                    updated = getZonedDateTime(),
                ),
            )

            eventPublisher.publish(
                event = MemberAdded(
                    walletId = walletId,
                    member = accountId,
                    actionSource = ActionSource.System,
                ),
            )

            upsertOnCRM(accountPF, markers = Markers.append("walletId", walletId.value).andAppend("accountId", accountId.value), logName)
        }

        val accountPJ = accountRepository.findById(wallet.founder.accountId)
        upsertOnCRM(accountPJ, Markers.append("walletId", walletId.value).andAppend("accountIds", accountIds.map { it.value }), logName)

        return Unit.right()
    }

    fun dissociateAccountIdsFromWalletId(walletId: WalletId, accountIds: List<AccountId>): Either<DissociateError, Unit> {
        val logName = "BackofficeCNPJService#dissociateAccountIdsFromWalletId"

        val wallet = walletRepository.findWallet(walletId)

        if (isCPF(wallet.founder.document)) {
            return DissociateError.CPFNotAllowed.left()
        }

        accountIds.forEach { accountId ->
            walletService.removeMember(walletId, source = ActionSource.System, targetAccountId = accountId).getOrElse { error ->
                return DissociateError.RemoveMemberError(error.toString()).left()
            }
            val accountPF = accountRepository.findById(accountId)
            upsertOnCRM(accountPF, markers = Markers.append("walletId", walletId.value).andAppend("accountId", accountId.value), logName)
        }

        val accountPJ = accountRepository.findById(wallet.founder.accountId)
        upsertOnCRM(accountPJ, Markers.append("walletId", walletId.value).andAppend("accountIds", accountIds.map { it.value }), logName)

        return Unit.right()
    }

    private fun upsertOnCRM(account: Account, markers: LogstashMarker, logName: String): Boolean {
        return try {
            crmService.upsertContact(account)
            true
        } catch (e: Exception) {
            logger.error(markers.andAppend("context", "error upserting crm"), logName, e)
            false
        }
    }
}

sealed class AssociateError() {
    data object CPFNotAllowed : AssociateError()
    data object FounderNotAllowed : AssociateError()
}

sealed class DissociateError() {
    data object CPFNotAllowed : DissociateError()
    class RemoveMemberError(val message: String) : DissociateError()
}

data class CNPJCreateData(
    val cnpj: String,
    val businessName: String,
    val accountNo: String,
    val accountDv: String,
)