package ai.friday.billpayment.app.payment

import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.BillPaid
import ai.friday.billpayment.app.bill.PaymentFailed
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.conciliation.BoletoOccurrence
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.TransactionRepository
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.payment.captureFunds.CaptureFundsLocator
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import jakarta.inject.Singleton
import java.time.Instant
import java.time.ZonedDateTime
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
class RefundBoletoService(
    private val walletRepository: WalletRepository,
    private val billEventRepository: BillEventRepository,
    private val updateBillService: UpdateBillService,
    private val transactionRepository: TransactionRepository,
    private val captureFundsLocator: CaptureFundsLocator,
) {

    fun refund(walletId: WalletId, barCode: BarCode): RefundBoletoResponse {
        val markers = Markers.append("walletId", walletId).andAppend("barCode", barCode)
        try {
            val wallet = walletRepository.findWalletOrNull(walletId) ?: return RefundBoletoResponse.WalletNotFound
            val bill =
                billEventRepository.findLastBill(barCode = barCode, walletId = wallet.id).getOrElse {
                    return when (it) {
                        is ItemNotFoundException -> RefundBoletoResponse.BoletoNotFound
                        else -> RefundBoletoResponse.GenericError(it)
                    }
                }
            if (bill.isRefunded()) {
                return RefundBoletoResponse.BoletoAlreadyRefunded
            }
            if (!bill.isPaid()) {
                return RefundBoletoResponse.InvalidBoletoStatus
            }
            val paidDate = bill.paidDate
            val transactionId = bill.history.filterIsInstance<BillPaid>().last().transactionId!!
            val transaction = transactionRepository.findById(transactionId)

            markers.andAppend("billId", bill.billId.value).andAppend("transactionId", transaction.id.value)
                .andAppend("paymentData", transaction.paymentData)

            val refundOperation = captureFundsLocator.undoCaptureFunds(transaction)
            markers.andAppend("refundOperation", refundOperation)

            if (refundOperation == null || transaction.paymentData.status() != PaymentStatus.REFUNDED) {
                logger.error(markers, "RefundBoletoService")
                return RefundBoletoResponse.PaymentRefundError
            }

            transactionRepository.save(
                transaction.apply {
                    status = TransactionStatus.UNDONE
                },
            )

            updateBillService.publishEvent(
                bill,
                PaymentFailed(
                    billId = bill.billId,
                    walletId = bill.walletId,
                    retryable = true,
                    errorSource = ErrorSource.SETTLEMENT_CELCOIN,
                    errorDescription = BoletoOccurrence.DEFAULT_DESCRIPTION,
                    transactionId = transactionId,
                    actionSource = ActionSource.System,
                ),
            )

            updateBillService.synchronizeBill(bill)
            updateBillService.refund(
                walletId = bill.walletId,
                billId = bill.billId,
                transactionId = transactionId,
                type = bill.billType,
                amount = transaction.paymentData.netAmountTotal(),
                originalPaidDate = ZonedDateTime.ofInstant(Instant.ofEpochMilli(paidDate!!), brazilTimeZone),
            )
            return RefundBoletoResponse.Success
        } catch (e: Exception) {
            when (e) {
                is IllegalStateException -> {
                    markers.andAppend("ACTION", "VERIFY")
                    logger.error(markers, "RefundBoletoService")
                    return RefundBoletoResponse.PaymentRefundError
                }

                is CreditCardPaymentException -> {
                    logger.error(markers, "RefundBoletoService")
                    return RefundBoletoResponse.PaymentRefundError
                }

                else -> {
                    logger.error(markers, "RefundBoletoService")
                    return RefundBoletoResponse.GenericError(e)
                }
            }
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(RefundBoletoService::class.java)
    }
}

sealed class RefundBoletoResponse {
    data object Success : RefundBoletoResponse()
    data object WalletNotFound : RefundBoletoResponse()
    data object BoletoNotFound : RefundBoletoResponse()
    data object InvalidBoletoStatus : RefundBoletoResponse()
    data object BoletoAlreadyRefunded : RefundBoletoResponse()
    data object PaymentRefundError : RefundBoletoResponse()
    class GenericError(val e: Exception) : RefundBoletoResponse()
}