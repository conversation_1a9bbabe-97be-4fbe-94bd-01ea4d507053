package ai.friday.billpayment.app.payment

import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.balance.BalanceService
import ai.friday.billpayment.app.banking.BankStatementItemType
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.ScheduledBillRepository
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import jakarta.inject.Singleton

@Singleton
open class NotifyDepositService(
    private val scheduledBillRepository: ScheduledBillRepository,
    private val balanceService: BalanceService,
    private val notificationAdapter: NotificationAdapter,
    private val walletService: WalletService,
) {
    fun notifyDeposit(
        walletId: WalletId,
        accountPaymentMethodId: AccountPaymentMethodId,
        amount: Long,
        senderName: String,
        senderDocument: String,
        senderAccountNo: String? = null,
        senderBankName: String? = null,
        type: BankStatementItemType,
    ) {
        val wallet =
            walletService.findWallet(walletId)
        if (wallet.paymentMethodId != accountPaymentMethodId) {
            throw IllegalStateException("Payment method ${accountPaymentMethodId.value} does not match wallet ${wallet.id.value} payment method ${wallet.paymentMethodId.value}")
        }

        val scheduledToday = scheduledBillRepository.findScheduledBillsByWalletIdAndUntilScheduledDate(
            wallet.id,
            getZonedDateTime().toLocalDate(),
        )
        val members = wallet.getMembersToNotifyCashIn()
        if (scheduledToday.isEmpty()) {
            notificationAdapter.notifyCashIn(
                members = members,
                walletId = wallet.id,
                walletName = wallet.name,
                senderName = senderName,
                senderDocument = senderDocument,
                amount = amount,
            )
        } else {
            val scheduledAmountToday = scheduledToday.sumAmount()
            val balance =
                balanceService.getBalanceFrom(
                    wallet.founder.accountId,
                    accountPaymentMethodId,
                ).amount
            if (balance >= scheduledAmountToday) {
                // notificacao diferente se owner fez cashin para ele mesmo
                if (type == BankStatementItemType.PIX &&
                    wallet.founder.document == senderDocument
                ) {
                    val (founderList, nonFounderList) = members.partition { it.type == MemberType.FOUNDER }
                    // so o owner recebe a notificacao diferente
                    notificationAdapter.notifyFounderSelfCashInPaymentSufficientBalanceToday(
                        members = founderList,
                        walletId = wallet.id,
                        walletName = wallet.name,
                        senderName = senderName,
                        senderDocument = senderDocument,
                        amount = amount,
                        senderAccountNo = senderAccountNo,
                        senderBankName = senderBankName,
                    )
                    // os demais membros da carteira continuam recebendo a notificacao antiga
                    notificationAdapter.notifyCashInPaymentSufficientBalanceToday(
                        members = nonFounderList,
                        walletId = wallet.id,
                        walletName = wallet.name,
                        senderName = senderName,
                        senderDocument = senderDocument,
                        amount = amount,
                    )
                } else {
                    notificationAdapter.notifyCashInPaymentSufficientBalanceToday(
                        members = members,
                        walletId = wallet.id,
                        walletName = wallet.name,
                        senderName = senderName,
                        senderDocument = senderDocument,
                        amount = amount,
                    )
                }
            } else {
                notificationAdapter.notifyCashInPaymentInsufficientBalanceToday(
                    members = members,
                    walletId = wallet.id,
                    walletName = wallet.name,
                    pendingAmountToday = scheduledAmountToday - balance,
                    senderName = senderName,
                    senderDocument = senderDocument,
                    amount = amount,
                )
            }
        }
    }
}

private fun Wallet.getMembersToNotifyCashIn(): List<Member> =
    (getMembersCanScheduleAny() + getMembersCanViewBalance())
        .filter { it.permissions.notification }
        .distinct()