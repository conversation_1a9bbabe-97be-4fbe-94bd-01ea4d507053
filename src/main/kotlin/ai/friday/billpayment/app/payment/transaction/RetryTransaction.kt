package ai.friday.billpayment.app.payment.transaction

import ai.friday.billpayment.adapters.lock.transactionLockProvider
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.UpdateablePaymentStatus
import ai.friday.billpayment.app.payment.CheckoutLocator
import ai.friday.billpayment.app.payment.MultiplePaymentData
import ai.friday.billpayment.app.payment.NoPaymentData
import ai.friday.billpayment.app.payment.PaymentStatus
import ai.friday.billpayment.app.payment.RetrySettlementStatus
import ai.friday.billpayment.app.payment.SettlementRetry
import ai.friday.billpayment.app.payment.SinglePaymentData
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.app.payment.TransactionService
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.app.payment.TransactionType
import ai.friday.billpayment.withEphemeralLock
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import jakarta.inject.Named
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
open class RetryTransaction(
    private val transactionService: TransactionService,
    private val checkoutLocator: CheckoutLocator,
    private val settlementRetry: SettlementRetry,
    private val rollbackTransaction: RollbackTransaction,
    @Named(transactionLockProvider) private val lockProvider: InternalLock,
) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    fun retryTransaction(transactionId: TransactionId): Result<Unit> {
        val logName = "RetryTransaction#retryTransaction"

        return lockProvider.withEphemeralLock(transactionId.value) {
            val transaction = transactionService.findTransactionById(transactionId)

            val markers = Markers.append("accountId", transaction.payer.accountId.value)
                .andAppend("transactionId", transaction.id.value)
                .andAppend("amount", transaction.settlementData.totalAmount)
                .andAppend("type", transaction.type)

            if (transaction.status != TransactionStatus.PROCESSING) {
                markers.andAppend("status", transaction.status).andAppend("nothingToDo", true)
                logger.warn(markers, logName)
            } else {
                transaction.retry()
            }
        }
    }

    private fun Transaction.retry() {
        if (shouldCheckPaymentStatus()) {
            updatePaymentData()
        }

        if (shouldCheckSettlementStatus()) {
            settlementRetry
                .resolve(this)
                .map {
                    when (it) {
                        RetrySettlementStatus.COMPLETED -> return
                        RetrySettlementStatus.PROCESSING -> throw TransactionProcessingException()
                    }
                }.getOrElse { rollbackTransaction.rollback(this) }
        } else {
            rollbackTransaction.rollback(this)
        }
    }

    private fun Transaction.shouldCheckPaymentStatus(): Boolean {
        return when (this.type) {
            TransactionType.DIRECT_INVOICE, TransactionType.GOAL_INVESTMENT, TransactionType.GOAL_REDEMPTION -> false
            TransactionType.BOLETO_PAYMENT,
            TransactionType.INVOICE_PAYMENT,
            TransactionType.CASH_IN,
            -> {
                when (this.paymentData) {
                    is MultiplePaymentData -> throw IllegalStateException("Pagamento multiplo ainda não está disponível")
                    is SinglePaymentData -> this.paymentData.payment!!.status() == PaymentStatus.UNKNOWN
                    NoPaymentData -> false
                }
            }

            TransactionType.AUTOMATIC_PIX -> throw IllegalStateException("Transação automática PIX não deve ser reprocessada. Aguardar chegada do callback.")
        }
    }

    private fun Transaction.updatePaymentData() {
        when (val checkout = checkoutLocator.getCheckout(this)) {
            is UpdateablePaymentStatus -> checkout.updatePaymentStatus(this)

            else -> throw IllegalStateException("Checkout can not check payment status")
        }
    }
}

class TransactionProcessingException() : Exception()