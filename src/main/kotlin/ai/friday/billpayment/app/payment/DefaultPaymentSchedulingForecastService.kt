package ai.friday.billpayment.app.payment

import ai.friday.billpayment.app.bill.BillPaymentScheduleCanceled
import ai.friday.billpayment.app.bill.BillPaymentScheduled
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.PaymentSchedulingForecastService
import ai.friday.billpayment.app.integrations.ScheduleForecastRepository
import jakarta.inject.Singleton
import java.time.LocalDate

@Singleton
class DefaultPaymentSchedulingForecastService(
    private val billEventRepository: BillEventRepository,
    private val schedulingForecastRepository: ScheduleForecastRepository,
) : PaymentSchedulingForecastService {

    override fun provision(event: BillPaymentScheduled) {
        event.infoData.toSchedulePaymentDetails().internalSettlement().takeIf { it }?.let {
            schedulingForecastRepository.processSchedule(
                date = event.scheduledDate,
                amount = event.amount,
            )
        }
    }

    override fun deprovision(event: BillPaymentScheduleCanceled) {
        val bill = billEventRepository.getBillById(event.billId).getOrNull() ?: run {
            throw IllegalStateException("Bill could not be found")
        }

        val scheduleEvent = bill.history.findLast { it is BillPaymentScheduled } as BillPaymentScheduled

        scheduleEvent.infoData.toSchedulePaymentDetails().internalSettlement().takeIf { it }?.let {
            when (bill.status) {
                BillStatus.PAID -> schedulingForecastRepository.processPayment(
                    date = scheduleEvent.scheduledDate,
                    amount = scheduleEvent.amount,
                )

                else -> schedulingForecastRepository.processCancelment(
                    date = scheduleEvent.scheduledDate,
                    amount = scheduleEvent.amount,
                )
            }
        }
    }

    override fun find(refDate: LocalDate) = schedulingForecastRepository.find(refDate)
}