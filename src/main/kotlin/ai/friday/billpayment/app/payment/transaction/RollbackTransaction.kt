package ai.friday.billpayment.app.payment.transaction

import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.PaymentFailed
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.payment.CheckoutLocator
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionRollbackException
import ai.friday.billpayment.app.payment.TransactionService
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.app.payment.TransactionType
import jakarta.inject.Singleton

@Singleton
open class RollbackTransaction(
    private val transactionService: TransactionService,
    private val billEventPublisher: BillEventPublisher,
    private val checkoutLocator: CheckoutLocator,
    private val paymentFailedScheduleResolver: PaymentFailedScheduleResolver,
) {

    fun rollback(transaction: Transaction) {
        val checkout = checkoutLocator.getCheckout(transaction)
        checkout.rollbackTransaction(transaction)
        transactionService.save(transaction)
        if (transaction.status in listOf(TransactionStatus.PROCESSING, TransactionStatus.COMPLETED)) {
            throw TransactionRollbackException()
        }
        if (transaction.type == TransactionType.GOAL_REDEMPTION) {
            return
        }
        billEventPublisher.publish(
            transaction.settlementData.getTarget(),
            PaymentFailed(
                billId = transaction.settlementData.getTarget<Bill>().billId,
                walletId = transaction.settlementData.getTarget<Bill>().walletId,
                retryable = transaction.isRetryable(),
                errorDescription = transaction.getErrorDescription(),
                actionSource = transaction.actionSource,
                errorSource = transaction.getErrorSource(),
                transactionId = transaction.id,
            ),
        )
        paymentFailedScheduleResolver.resolvePostPaymentFailedEvent(transaction)
        transactionService.notify(transaction)
    }
}