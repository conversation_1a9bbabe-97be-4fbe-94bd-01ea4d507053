package ai.friday.billpayment.app.payment

import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.BillType.CONCESSIONARIA
import ai.friday.billpayment.app.bill.BillType.FICHA_COMPENSACAO
import ai.friday.billpayment.app.bill.BillType.INVOICE
import ai.friday.billpayment.app.bill.BillType.PIX
import ai.friday.billpayment.app.integrations.FeatureConfiguration
import ai.friday.billpayment.app.payment.checkout.AsyncSettlementBoletoCheckout
import ai.friday.billpayment.app.payment.checkout.SyncBoletoCheckout
import jakarta.inject.Singleton

interface CheckoutLocator {
    fun getCheckout(transaction: Transaction): Checkout
}

@Singleton
class DefaultCheckoutLocator(
    private val balanceInvoiceCheckout: BalanceInvoiceCheckout,
    private val balancePixCheckout: BalancePixCheckout,
    private val balanceDirectInvoiceCheckout: BalanceDirectInvoiceCheckout,
    private val boletoCheckout: SyncBoletoCheckout,
    private val asyncSettlementBoletoCheckout: AsyncSettlementBoletoCheckout,
    private val featureConfiguration: FeatureConfiguration,
    private val investmentCheckout: InvestmentCheckout?,
) : CheckoutLocator {

    override fun getCheckout(transaction: Transaction): Checkout {
        return when (val billType = transaction.settlementData.getTarget<Bill>().billType) {
            INVOICE -> {
                if (transaction.type == TransactionType.DIRECT_INVOICE) {
                    balanceDirectInvoiceCheckout
                } else {
                    balanceInvoiceCheckout
                }
            }

            PIX -> balancePixCheckout

            CONCESSIONARIA, FICHA_COMPENSACAO -> {
                if (featureConfiguration.asyncSettlement) {
                    asyncSettlementBoletoCheckout
                } else {
                    boletoCheckout
                }
            }

            BillType.INVESTMENT -> investmentCheckout ?: throw IllegalStateException("No checkout found for $billType")
            BillType.AUTOMATIC_PIX -> throw IllegalStateException("Checkout is not allowed for $billType")
            BillType.OTHERS -> throw IllegalStateException("No checkout found for $billType")
        }
    }
}