package ai.friday.billpayment.app.payment

import ai.friday.billpayment.app.account.hasEarlyAccess
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillPaymentScheduleCanceled
import ai.friday.billpayment.app.bill.BillPaymentScheduleUpdated
import ai.friday.billpayment.app.bill.ScheduleCanceledReason
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.bill.UpdatedScheduleData
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.integrations.FeatureConfiguration
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.ScheduledBillPaymentService
import ai.friday.billpayment.app.integrations.ScheduledBillRepository
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import jakarta.inject.Named
import jakarta.inject.Singleton
import kotlin.contracts.ExperimentalContracts
import kotlin.contracts.contract
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
class PaymentSchedulingEventService(
    private val scheduledBillRepository: ScheduledBillRepository,
    private val scheduledBillPaymentService: ScheduledBillPaymentService,
    private val billEventPublisher: BillEventPublisher,
    @Named("bill") private val lockProvider: InternalLock,
    private val updateBillService: UpdateBillService,
    private val featureConfiguration: FeatureConfiguration,
) {
    fun updateAmount(
        walletId: WalletId,
        billId: BillId,
        actionSource: ActionSource,
        updatedAmount: Long,
        shouldProcessScheduledBills: Boolean,
    ) {
        val targetBill =
            scheduledBillRepository.findScheduledBillById(billId = billId).ifEmpty { return }
                .single()

        val markers = Markers.append("billId", billId)
            .andAppend("walletId", walletId)
            .andAppend("updateScheduleOnAmountLowered", featureConfiguration.updateScheduleOnAmountLowered)
            .andAppend("hasEarlyAccess", walletId.hasEarlyAccess())
            .andAppend("updatedAmount", updatedAmount)
            .andAppend("targetBill.amount", targetBill.amount)
            .andAppend("shouldProcessScheduledBills", shouldProcessScheduledBills)
        logger.info(markers, "PaymentSchedulingEventService#updateAmount")
        if (featureConfiguration.updateScheduleOnAmountLowered) {
            val amountLowered = updatedAmount < targetBill.amount
            if (updatedAmount > 0 && amountLowered && targetBill.paymentMethodsDetail is PaymentMethodsDetailWithBalance) {
                logger.info(markers, "PaymentSchedulingEventService#scheduledBillRepository_save")
                scheduledBillRepository.save(
                    targetBill.copy(
                        amount = updatedAmount,
                        paymentMethodsDetail = targetBill.paymentMethodsDetail.copy(amount = updatedAmount),
                    ),
                )

                if (shouldProcessScheduledBills) {
                    if (targetBill.scheduledDate <= getLocalDate()) {
                        scheduledBillPaymentService.process(
                            walletId = walletId,
                            scheduledDate = targetBill.scheduledDate,
                        )
                    }
                }
                return
            }
        }

        if (shouldUpdateSchedule(actionSource, targetBill.paymentMethodsDetail)) {
            scheduledBillRepository.save(
                targetBill.copy(
                    amount = updatedAmount,
                    paymentMethodsDetail = targetBill.paymentMethodsDetail.copy(amount = updatedAmount),
                ),
            )

            if (shouldProcessScheduledBills) {
                if (targetBill.scheduledDate <= getLocalDate()) {
                    scheduledBillPaymentService.process(
                        walletId = walletId,
                        scheduledDate = targetBill.scheduledDate,
                    )
                }
            }
        } else {
            val lock = lockProvider.acquireLock(billId.value) ?: throw IllegalStateException("Lock should be present")

            val bill = updateBillService.getBill(
                billId = billId,
                walletId = walletId,
            ).getOrElse {
                throw it
            }

            if (bill.isProcessing()) {
                lock.unlock()
                throw IllegalStateException("Bill should not be processing")
            }

            val scheduleCancelEvent = BillPaymentScheduleCanceled(
                billId = billId,
                walletId = walletId,
                actionSource = ActionSource.System,
                reason = ScheduleCanceledReason.BILL_AMOUNT_CHANGED,
                batchSchedulingId = bill.schedule!!.batchSchedulingId,
            )
            billEventPublisher.publish(
                bill,
                scheduleCancelEvent,
            )

            lock.unlock()
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(PaymentSchedulingEventService::class.java)
    }

    fun updateCalculationId(event: BillPaymentScheduleUpdated) {
        val scheduledBills =
            scheduledBillRepository.findScheduledBillById(billId = event.billId)

        if (scheduledBills.isEmpty()) return

        val scheduleBill = scheduledBills.single()

        if (scheduleBill.paymentMethodsDetail !is PaymentMethodsDetailWithCreditCard) return

        val updatedPaymentMethod = when (val v = event.updatedScheduleData) {
            is UpdatedScheduleData.NewCalculationId -> scheduleBill.paymentMethodsDetail.copy(calculationId = v.calculationId)
        }

        scheduledBillRepository.save(scheduledBill = scheduleBill.copy(paymentMethodsDetail = updatedPaymentMethod))
    }
}

@OptIn(ExperimentalContracts::class)
private fun shouldUpdateSchedule(
    actionSource: ActionSource,
    paymentMethodsDetail: PaymentMethodsDetail,
): Boolean {
    contract {
        returns(true) implies (paymentMethodsDetail is PaymentMethodsDetailWithBalance)
    }
    return actionSource is ActionSource.Api && paymentMethodsDetail is PaymentMethodsDetailWithBalance
}