package ai.friday.billpayment.app.payment.transaction

import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.PaymentFailed
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionService
import jakarta.inject.Singleton

@Singleton
class FailTransaction(
    private val transactionService: TransactionService,
    private val billEventPublisher: BillEventPublisher,
    private val paymentFailedScheduleResolver: PaymentFailedScheduleResolver,
) {

    fun execute(transaction: Transaction) {
        val billEvent = PaymentFailed(
            billId = transaction.settlementData.getTarget<Bill>().billId,
            walletId = transaction.settlementData.getTarget<Bill>().walletId,
            retryable = transaction.isRetryable(),
            errorDescription = transaction.getErrorDescription(),
            actionSource = transaction.actionSource,
            errorSource = transaction.getErrorSource(),
            transactionId = transaction.id,
        )
        billEventPublisher.publish(transaction.settlementData.getTarget(), billEvent)
        transactionService.notify(transaction)
        paymentFailedScheduleResolver.resolvePostPaymentFailedEvent(transaction)
    }
}