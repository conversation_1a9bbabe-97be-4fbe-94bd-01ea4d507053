package ai.friday.billpayment.app.payment.receipt.builder

import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillPaid
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.payment.ReceiptData
import ai.friday.billpayment.app.wallet.Wallet
import jakarta.inject.Singleton

interface ReceiptDataBuilder {
    val buildFor: List<BillType>
    fun build(bill: Bill, billPaid: BillPaid, wallet: Wallet): ReceiptData
}

@Singleton
class ReceiptDataBuilderService(
    receiptDataBuilder: List<ReceiptDataBuilder>,
) {
    private val builders: Map<BillType, ReceiptDataBuilder> = BillType.entries.mapNotNull { billType ->
        val builders = receiptDataBuilder.filter { it.buildFor.contains(billType) }
        when (builders.size) {
            0 -> null
            1 -> billType to builders.single()
            else -> throw IllegalStateException("More than one builder found for $billType")
        }
    }.toMap()

    fun getReceiptData(bill: Bill, billPaid: BillPaid, wallet: Wallet): ReceiptData {
        return when (bill.billType) {
            BillType.INVOICE,
            BillType.PIX,
            BillType.AUTOMATIC_PIX,
            BillType.FICHA_COMPENSACAO,
            BillType.CONCESSIONARIA,
            BillType.INVESTMENT,
            -> builders[bill.billType]?.build(bill, billPaid, wallet) ?: throw IllegalStateException("No builder found for ${bill.billType}")
            BillType.OTHERS -> throw IllegalStateException("BillType should not be $bill.billType")
        }
    }
}