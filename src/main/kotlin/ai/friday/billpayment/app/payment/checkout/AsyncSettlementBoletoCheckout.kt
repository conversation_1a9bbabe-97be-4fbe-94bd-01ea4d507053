package ai.friday.billpayment.app.payment.checkout

import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.integrations.CheckableSettlementStatus
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.SettlementPaymentService
import ai.friday.billpayment.app.integrations.UpdateablePaymentStatus
import ai.friday.billpayment.app.payment.AsyncSettlementCheckout
import ai.friday.billpayment.app.payment.BoletoSettlementResult
import ai.friday.billpayment.app.payment.BoletoSettlementStatus
import ai.friday.billpayment.app.payment.MultiplePaymentData
import ai.friday.billpayment.app.payment.PaymentStatus
import ai.friday.billpayment.app.payment.SettlementOperation
import ai.friday.billpayment.app.payment.SettlementStatus
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionService
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.app.payment.captureFunds.CaptureFundsLocator
import ai.friday.morning.log.andAppend
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.model.InvalidMessageContentsException

@Singleton
open class AsyncSettlementBoletoCheckout(
    private val captureFundsLocator: CaptureFundsLocator,
    private val messagePublisher: MessagePublisher,
    private val transactionService: TransactionService,
    private val configuration: AsyncBoletoCheckoutConfiguration,
    @Property(name = "integrations.settlement.requestProtocol") private val requestProtocol: RequestProtocol,
    private val settlementPaymentService: SettlementPaymentService,
) : AsyncSettlementCheckout, UpdateablePaymentStatus, CheckableSettlementStatus {
    private val logger = LoggerFactory.getLogger(AsyncSettlementBoletoCheckout::class.java)

    override fun execute(transaction: Transaction): Transaction {
        if (transaction.paymentData is MultiplePaymentData) {
            throw IllegalStateException("MultiplePaymentData is not supported")
        }
        val markers = append("transactionId", transaction.id.value)
        captureFundsLocator.checkOnFraudPrevention(transaction)?.let {
            markers.andAppend("checkOnFraudPrevention", it)
            logger.warn(markers, "AsyncSettlementBoletoCheckout#execute")
            transaction.status = TransactionStatus.FAILED
            transactionService.save(transaction)
            return transaction
        }
        val captureFundsOperation = captureFundsLocator.captureFunds(transaction)
        transactionService.save(transaction)
        markers.andAppend("captureFundsStatus", captureFundsOperation.status())

        when (val status = captureFundsOperation.status()) {
            PaymentStatus.SUCCESS -> try {
                publishSettlementRequest(transaction)
            } catch (e: Exception) {
                logger.error(markers, "AsyncSettlementBoletoCheckout#execute", e)

                if (e is UnsupportedOperationException || e is InvalidMessageContentsException) {
                    val paymentOperation = captureFundsLocator.undoCaptureFunds(transaction)

                    if (paymentOperation?.status() == PaymentStatus.SUCCESS) {
                        transaction.status = TransactionStatus.FAILED
                    }
                } else {
                    transaction.settlementData.settlementOperation = BoletoSettlementResult(
                        gateway = FinancialServiceGateway.FRIDAY,
                        status = BoletoSettlementStatus.UNKNOWN,
                        bankTransactionId = transaction.id.value,
                        externalNsu = 0,
                        externalTerminal = "",
                        errorCode = "",
                        errorDescription = null,
                    )
                }
            }

            PaymentStatus.ERROR -> transaction.status = TransactionStatus.FAILED
            PaymentStatus.UNKNOWN -> {
                val undoCaptureFundsOperation = captureFundsLocator.undoCaptureFunds(transaction)
                markers.andAppend("undoCaptureFundsStatus", undoCaptureFundsOperation?.status())

                if (undoCaptureFundsOperation?.status() == PaymentStatus.SUCCESS) {
                    transaction.status = TransactionStatus.FAILED
                }
            }

            PaymentStatus.REFUNDED, PaymentStatus.AUTHORIZED -> {
                logger.error(markers, "AsyncSettlementBoletoCheckout#execute")
                throw IllegalStateException("captureFunds status should not be ${status.name}")
            }
        }
        transactionService.save(transaction)

        logger.info(markers, "AsyncSettlementBoletoCheckout#execute")
        return transaction
    }

    override fun continueSettlement(
        transaction: Transaction,
        settlementOperation: SettlementOperation,
    ): Transaction {
        val markers = append("transactionId", transaction.id.value)
            .andAppend("settlementOperation", settlementOperation)

        if (settlementOperation !is BoletoSettlementResult) {
            throw IllegalStateException("Settlement operation must be of type BoletoSettlementResult: ${settlementOperation::class.java.simpleName}")
        }

        val transactionStatus = when (settlementOperation.status) {
            BoletoSettlementStatus.CONFIRMED -> TransactionStatus.COMPLETED
            BoletoSettlementStatus.UNAUTHORIZED, BoletoSettlementStatus.VOIDED -> TransactionStatus.FAILED
            BoletoSettlementStatus.AUTHORIZED, BoletoSettlementStatus.UNKNOWN, BoletoSettlementStatus.WAITING_CONFIRMATION -> throw IllegalStateException(
                "Settlement operation status should not be ${settlementOperation.status}",
            )
        }
        markers.andAppend("transactionStatus", transactionStatus)

        transaction.settlementData.settlementOperation = settlementOperation
        transaction.status = transactionStatus

        transactionService.save(transaction)

        logger.info(markers, "AsyncSettlementBoletoCheckout#continueSettlement")
        return transaction
    }

    override fun rollbackTransaction(transaction: Transaction): Transaction {
        val markers = append("transactionId", transaction.id.value)
            .andAppend("paymentStatus", transaction.paymentData.status())
            .andAppend("settlementStatus", transaction.getBoletoSettlementResultStatus())

        if (transaction.getBoletoSettlementResultStatus() in listOf(
                BoletoSettlementStatus.WAITING_CONFIRMATION,
                BoletoSettlementStatus.UNKNOWN,
            )
        ) {
            logger.error(markers, "AsyncSettlementBoletoCheckout#rollbackTransaction")
            throw IllegalStateException("Cannot rollback a transaction waiting settlement confirmation")
        }

        if (transaction.paymentData.status() == PaymentStatus.SUCCESS) {
            val undoCaptureFundsOperation = captureFundsLocator.undoCaptureFunds(transaction)
            markers.andAppend("undoCaptureFundsStatus", undoCaptureFundsOperation?.status())

            if (undoCaptureFundsOperation == null || undoCaptureFundsOperation.status() !in listOf(PaymentStatus.SUCCESS, PaymentStatus.REFUNDED)) {
                logger.error(markers, "AsyncSettlementBoletoCheckout#rollbackTransaction")
                throw IllegalStateException("Unable to rollback transaction due to an error in the undoCaptureFunds operation")
            }
        }

        transaction.rollback()
        transactionService.save(transaction)

        logger.info(markers, "AsyncSettlementBoletoCheckout#rollbackTransaction")
        return transaction
    }

    override fun updatePaymentStatus(transaction: Transaction) = captureFundsLocator.updatePaymentStatus(transaction)

    override fun checkSettlementStatus(transaction: Transaction): SettlementStatus {
        val markers = append("transactionId", transaction.id.value)

        return when (transaction.paymentData.status()) {
            PaymentStatus.ERROR -> SettlementStatus.Failure("Pagamento falhou.")
            PaymentStatus.REFUNDED -> SettlementStatus.Failure("Pagamento estornado.")
            PaymentStatus.UNKNOWN, PaymentStatus.AUTHORIZED -> SettlementStatus.Processing
            PaymentStatus.SUCCESS -> handlePaymentSuccess(transaction, markers)
            null -> SettlementStatus.Failure("Sem informação do pagamento.")
        }
    }

    private fun handlePaymentSuccess(transaction: Transaction, markers: LogstashMarker): SettlementStatus {
        val settlementResultStatus = transaction.getBoletoSettlementResultStatus()
        markers.andAppend("settlementResultStatus", settlementResultStatus)

        logger.info(markers, "AsyncSettlementBoletoCheckout#checkSettlementStatus")
        return when (settlementResultStatus) {
            BoletoSettlementStatus.CONFIRMED,
            ->
                SettlementStatus.AlreadyCompleted

            BoletoSettlementStatus.AUTHORIZED, BoletoSettlementStatus.UNKNOWN -> {
                publishSettlementRequest(transaction)
                transactionService.save(transaction)
                SettlementStatus.Processing
            }

            BoletoSettlementStatus.VOIDED,
            BoletoSettlementStatus.UNAUTHORIZED,
            ->
                SettlementStatus.Failure(settlementResultStatus.name)

            BoletoSettlementStatus.WAITING_CONFIRMATION, null -> SettlementStatus.Processing
        }
    }

    private fun publishSettlementRequest(transaction: Transaction) {
        val markers = append("transactionId", transaction.id.value)

        val bill = transaction.settlementData.getTarget<Bill>()

        val settlementRequest = SettlementClientRequestTO(
            transactionId = transaction.id.value,
            amount = transaction.settlementData.totalAmount,
            barcode = bill.barcode!!.number,
            finalPayer = PersonTO(
                name = transaction.payer.name,
                commercialName = null,
                documentNumber = transaction.payer.document,
            ),
        )
        markers.andAppend("settlementRequest", settlementRequest)

        val status = if (requestProtocol == RequestProtocol.HTTP) {
            val result = settlementPaymentService.request(settlementRequest)

            if (result.isSuccess && result.getOrNull() == true) {
                logger.info(markers, "AsyncSettlementBoletoCheckout#publishSettlementRequest")
                BoletoSettlementStatus.WAITING_CONFIRMATION
            } else {
                logger.error(markers, "AsyncSettlementBoletoCheckout#publishSettlementRequest")
                BoletoSettlementStatus.UNAUTHORIZED
            }
        } else {
            messagePublisher.sendMessage(
                queueName = configuration.settlementRequestQueueName,
                body = settlementRequest,
            )
            BoletoSettlementStatus.WAITING_CONFIRMATION
        }

        transaction.settlementData.settlementOperation = BoletoSettlementResult(
            gateway = FinancialServiceGateway.FRIDAY,
            status = status,
            bankTransactionId = transaction.id.value,
            externalNsu = 0,
            externalTerminal = "",
            errorCode = "",
            errorDescription = null,
        )

        logger.info(markers, "AsyncSettlementBoletoCheckout#publishSettlementRequest")
    }

    private fun Transaction.getBoletoSettlementResultStatus(): BoletoSettlementStatus? {
        if (settlementData.settlementOperation == null) {
            return null
        }

        return settlementData.getOperation<BoletoSettlementResult>().status
    }
}

@ConfigurationProperties("sqs")
interface AsyncBoletoCheckoutConfiguration {
    val settlementRequestQueueName: String
}

enum class RequestProtocol {
    HTTP, QUEUE
}

data class SettlementClientRequestTO(
    val transactionId: String,
    val amount: Long,
    val barcode: String,
    val finalPayer: PersonTO,
)

data class PersonTO(
    val name: String,
    val commercialName: String?,
    val documentNumber: String,
)