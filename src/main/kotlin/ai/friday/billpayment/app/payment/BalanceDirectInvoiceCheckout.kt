package ai.friday.billpayment.app.payment

import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.integrations.CheckableSettlementStatus
import ai.friday.billpayment.app.integrations.TEDService
import jakarta.inject.Singleton
import org.slf4j.LoggerFactory

@Singleton
class BalanceDirectInvoiceCheckout(private val tedService: TEDService) :
    SyncCheckout,
    CheckableSettlementStatus by tedService {
    override fun execute(transaction: Transaction): Transaction {
        val result = tedService.transfer(
            originAccountNo = (transaction.paymentData.toSingle().accountPaymentMethod.method as InternalBankAccount).buildFullAccountNumber(),
            recipient = transaction.settlementData.getTarget<Bill>().recipient!!,
            totalAmount = transaction.settlementData.totalAmount,
        )

        val (settlementOperationStatus, transactionStatusResult, errorDescription) = when (result.status) {
            DirectTEDStatus.Error -> Triple(BankOperationStatus.ERROR, TransactionStatus.PROCESSING, "")
            DirectTEDStatus.Failure.AFTER_HOURS -> Triple(
                BankOperationStatus.ERROR,
                TransactionStatus.FAILED,
                "After hours",
            )

            DirectTEDStatus.Failure.INSUFFICIENT_FUNDS -> Triple(
                BankOperationStatus.INSUFFICIENT_FUNDS,
                TransactionStatus.FAILED,
                "Insufficient funds",
            )

            DirectTEDStatus.Failure.INVALID_BANK_DATA, DirectTEDStatus.Failure.NOT_ALLOWED -> Triple(
                BankOperationStatus.INVALID_DATA,
                TransactionStatus.FAILED,
                "Invalid bank data",
            )

            DirectTEDStatus.Success -> Triple(BankOperationStatus.SUCCESS, TransactionStatus.COMPLETED, "")
            DirectTEDStatus.Unknown -> Triple(BankOperationStatus.UNKNOWN, TransactionStatus.PROCESSING, "")
        }

        transaction.apply {
            status = transactionStatusResult
            settlementData.settlementOperation = BankTransfer(
                operationId = result.operationId,
                gateway = result.gateway,
                status = settlementOperationStatus,
                amount = result.amount,
                authentication = result.authentication.orEmpty(),
                errorDescription = errorDescription,
            )
        }

        return transaction
    }

    override fun rollbackTransaction(transaction: Transaction): Transaction {
        transaction.rollback()
        return transaction
    }

    override fun checkSettlementStatus(transaction: Transaction): SettlementStatus {
        val result = tedService.checkSettlementStatus(transaction)

        return when (result) {
            is SettlementStatus.Failure, is SettlementStatus.InvalidData -> {
                // Arbi pode ter executado o TED mesmo quando diz que nao reconhece a operacao
                SettlementStatus.Error
            }

            else -> result
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(BalanceDirectInvoiceCheckout::class.java)
    }
}