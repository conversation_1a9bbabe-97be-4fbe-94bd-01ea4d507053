package ai.friday.billpayment.app.payment.captureFunds

import ai.friday.billpayment.app.account.CreditCard
import ai.friday.billpayment.app.account.CreditCardPaymentStatus
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.cashIn.CreditCardFraudPreventionService
import ai.friday.billpayment.app.integrations.AcquirerService
import ai.friday.billpayment.app.integrations.FundProvider
import ai.friday.billpayment.app.metrics.AbstractSummary
import ai.friday.billpayment.app.metrics.push
import ai.friday.billpayment.app.payment.BalanceAuthorization
import ai.friday.billpayment.app.payment.BankTransfer
import ai.friday.billpayment.app.payment.CreditCardAuthorization
import ai.friday.billpayment.app.payment.FraudPreventionPaymentOperationDenied
import ai.friday.billpayment.app.payment.MultiplePaymentData
import ai.friday.billpayment.app.payment.NoPaymentData
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithBalance
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithCreditCard
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithExternalPayment
import ai.friday.billpayment.app.payment.PaymentOperation
import ai.friday.billpayment.app.payment.PaymentStatus
import ai.friday.billpayment.app.payment.SinglePaymentData
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.app.payment.paymentMethodType
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.context.annotation.Property
import jakarta.inject.Named
import jakarta.inject.Singleton
import java.time.temporal.ChronoUnit
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
class FridayCaptureFundsLocator(
    @Named("InternalBankService") private val fundProvider: FundProvider,
    private val acquirerService: AcquirerService,
    private val creditCardFraudPreventionService: CreditCardFraudPreventionService,
) : CaptureFundsLocator {
    @field:Property(name = "cashIn.softDescriptor")
    lateinit var softDescriptor: String
    override fun checkOnFraudPrevention(transaction: Transaction): FraudPreventionPaymentOperationDenied? {
        return when (transaction.paymentData) {
            is MultiplePaymentData -> throw IllegalStateException("MultiplePayment não está disponível")
            NoPaymentData -> throw IllegalStateException("NoPaymentData não pode ser usado aqui")
            is SinglePaymentData -> {
                when (transaction.paymentData.details) {
                    is PaymentMethodsDetailWithBalance -> null
                    is PaymentMethodsDetailWithCreditCard -> {
                        creditCardFraudPreventionService.check(
                            paymentMethod = transaction.paymentData.accountPaymentMethod,
                            netAmount = transaction.paymentData.details.netAmount,
                            feeAmount = transaction.paymentData.details.feeAmount,
                            installments = transaction.paymentData.details.installments,
                        ).map {
                            return null
                        }.getOrElse {
                            val paymentOperation = FraudPreventionPaymentOperationDenied(it)
                            transaction.paymentData.payment = paymentOperation
                            paymentOperation
                        }
                    }

                    is PaymentMethodsDetailWithExternalPayment -> throw IllegalStateException("pagamento externo não pode ser liquidado")
                }
            }
        }
    }

    override fun captureFunds(transaction: Transaction): PaymentOperation {
        return when (transaction.paymentData) {
            is MultiplePaymentData -> throw IllegalStateException("MultiplePayment não está disponível")
            NoPaymentData -> throw IllegalStateException("NoPaymentData não pode ser usado aqui")
            is SinglePaymentData -> {
                val paymentOperation = when (transaction.paymentData.details) {
                    is PaymentMethodsDetailWithBalance -> captureFundsWithBalance(transaction.paymentData)
                    is PaymentMethodsDetailWithCreditCard -> captureFundsWithCreditCard(
                        transaction.paymentData,
                        transaction.id,
                        transaction.settlementData.getTarget<Bill>().getPayee(),
                    )

                    is PaymentMethodsDetailWithExternalPayment -> throw IllegalStateException("pagamento externo não pode ser liquidado")
                }
                transaction.paymentData.payment = paymentOperation
                paymentOperation
            }
        }.also {
            if (!it.hasError()) {
                pushMetrics(transaction)
            }
        }
    }

    override fun undoCaptureFunds(transaction: Transaction): PaymentOperation? {
        return when (transaction.paymentData) {
            is MultiplePaymentData -> throw IllegalStateException("MultiplePayment não está disponível")
            NoPaymentData -> throw IllegalStateException("NoPaymentData não pode ser usado aqui")
            is SinglePaymentData -> {
                transaction.paymentData.payment?.let { originalPaymentOperation ->
                    if (originalPaymentOperation.status() == PaymentStatus.SUCCESS) {
                        val paymentOperation = when (transaction.paymentData.details) {
                            is PaymentMethodsDetailWithBalance -> undoCaptureFundsWithBalance(transaction.paymentData)
                            is PaymentMethodsDetailWithCreditCard -> {
                                undoCaptureFundsWithCreditCard(transaction.paymentData, transaction.id)
                            }

                            is PaymentMethodsDetailWithExternalPayment -> {
                                throw IllegalStateException("pagamento externo não pode ser desfeito")
                            }
                        }
                        paymentOperation
                    } else {
                        null
                    }
                }
            }
        }.also {
            if (it != null && !it.hasError()) {
                UndoCaptureFundsSummary.push(
                    tags = mapOf(
                        "method_type" to transaction.paymentMethodType().name,
                        "payment_type" to transaction.settlementData.getTarget<Bill>().billType.name,
                    ),
                    value = transaction.paymentData.netAmountTotal(),
                )
            }
        }
    }

    override fun updatePaymentStatus(transaction: Transaction) {
        return when (transaction.paymentData) {
            is MultiplePaymentData -> throw IllegalStateException("MultiplePayment não está disponível")
            NoPaymentData -> throw IllegalStateException("NoPaymentData não pode ser usado aqui")
            is SinglePaymentData -> {
                when (transaction.paymentData.details) {
                    is PaymentMethodsDetailWithBalance -> {
                        val payment = transaction.paymentData.payment as BalanceAuthorization
                        val bankOperationStatus = if (fundProvider.checkCaptureFunds(
                                accountId = transaction.paymentData.accountPaymentMethod.accountId,
                                paymentMethod = transaction.paymentData.accountPaymentMethod.method,
                                operationId = payment.operationId,
                                operationDate = transaction.created.toLocalDate(),
                            )
                        ) {
                            pushMetrics(transaction)
                            BankOperationStatus.SUCCESS
                        } else {
                            if (ChronoUnit.MINUTES.between(
                                    transaction.created,
                                    getZonedDateTime(),
                                ) > 15 // Disclaimer: o Arbi demora um tempo para que extrato seja atualizado. Por esse motivo devemos esperar alguns minutos (15 é um número mágico) para consolidar a resposta
                            ) {
                                BankOperationStatus.ERROR
                            } else {
                                BankOperationStatus.UNKNOWN
                            }
                        }
                        transaction.paymentData.payment = payment.apply {
                            status = bankOperationStatus
                        }
                    }

                    is PaymentMethodsDetailWithCreditCard -> {
                        val currentAuthorization = acquirerService.checkStatus(transaction.id.value)
                        if (!currentAuthorization.hasError()) {
                            pushMetrics(transaction)
                        }
                        transaction.paymentData.payment = currentAuthorization
                    }

                    is PaymentMethodsDetailWithExternalPayment -> throw IllegalStateException("pagamento externo não pode ser consultado")
                }
            }
        }
    }

    private fun pushMetrics(transaction: Transaction) {
        CaptureFundsSummary.push(
            tags = mapOf(
                "method_type" to transaction.paymentMethodType().name,
                "payment_type" to transaction.settlementData.getTarget<Bill>().billType.name,
            ),
            value = transaction.paymentData.netAmountTotal(),
        )
    }

    private fun captureFundsWithBalance(paymentData: SinglePaymentData): BalanceAuthorization {
        val settlementOperation = fundProvider.captureFunds(
            accountId = paymentData.accountPaymentMethod.accountId,
            accountPaymentMethodId = paymentData.accountPaymentMethod.id,
            paymentMethod = paymentData.accountPaymentMethod.method,
            amount = (paymentData.details as PaymentMethodsDetailWithBalance).amount,
        )
        return settlementOperation.toBalanceAuthorization()
    }

    private fun undoCaptureFundsWithBalance(paymentData: SinglePaymentData): BalanceAuthorization {
        val balanceAuthorization = paymentData.payment as BalanceAuthorization
        val settlementOperation = fundProvider.undoCaptureFunds(
            accountId = paymentData.accountPaymentMethod.accountId,
            accountPaymentMethodId = paymentData.accountPaymentMethod.id,
            paymentMethod = paymentData.accountPaymentMethod.method,
            operationId = balanceAuthorization.operationId,
            amount = balanceAuthorization.amount,
            ongoingRefundOperationId = balanceAuthorization.ongoingRefundOperationId,
        )

        if (settlementOperation.status != BankOperationStatus.SUCCESS) {
            paymentData.payment =
                (paymentData.payment as BalanceAuthorization).copy(
                    ongoingRefundOperationId = settlementOperation.operationId,
                )
        } else {
            balanceAuthorization.refund()
        }
        return settlementOperation.toBalanceAuthorization()
    }

    private fun undoCaptureFundsWithCreditCard(
        paymentData: SinglePaymentData,
        transactionId: TransactionId,
    ): CreditCardAuthorization {
        val creditCardAuthorization = paymentData.payment as CreditCardAuthorization
        try {
            acquirerService.cancel(transactionId.value)
            paymentData.payment = creditCardAuthorization.copy(
                status = CreditCardPaymentStatus.REFUNDED,
            )
        } catch (e: Exception) {
            logger.error(
                Markers.append("ACTION", "VERIFY").andAppend("transactionId", transactionId.value)
                    .andAppend("paymentOperation", paymentData.payment),
                "undoCaptureFundsWithCreditCard",
                e,
            )
            throw e
        }
        return paymentData.payment as CreditCardAuthorization
    }

    private fun captureFundsWithCreditCard(
        paymentData: SinglePaymentData,
        transactionId: TransactionId,
        softDescriptor: String,
    ): CreditCardAuthorization {
        return acquirerService.authorizeAndCapture(
            accountId = paymentData.accountPaymentMethod.accountId,
            orderId = transactionId.value,
            amount = (paymentData.details as PaymentMethodsDetailWithCreditCard).totalAmount,
            creditCard = paymentData.accountPaymentMethod.method as CreditCard,
            softDescriptor = softDescriptor,
            installments = paymentData.details.installments,
        )
    }

    private fun BankTransfer.toBalanceAuthorization() = when (this.status) {
        BankOperationStatus.SUCCESS -> BalanceAuthorization(
            operationId = this.operationId,
            status = this.status,
            amount = this.amount,
            paymentGateway = this.gateway,
        )

        BankOperationStatus.UNKNOWN,
        BankOperationStatus.TIMEOUT,
        -> BalanceAuthorization(
            operationId = this.operationId,
            status = this.status,
            amount = this.amount,
            errorDescription = this.errorDescription,
            timeout = true,
            paymentGateway = this.gateway,
        )

        else -> BalanceAuthorization(
            operationId = this.operationId,
            status = this.status,
            amount = this.amount,
            errorDescription = this.errorDescription,
            paymentGateway = this.gateway,
        )
    }

    companion object {
        private val logger = LoggerFactory.getLogger(FridayCaptureFundsLocator::class.java)
    }
}

object CaptureFundsSummary : AbstractSummary()
object UndoCaptureFundsSummary : AbstractSummary()