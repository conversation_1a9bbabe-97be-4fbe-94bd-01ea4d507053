package ai.friday.billpayment.app.payment.transaction

import ai.friday.billpayment.app.balance.BalanceService
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillPaid
import ai.friday.billpayment.app.bill.BillPaymentScheduleCanceled
import ai.friday.billpayment.app.bill.BillPaymentScheduleStarted
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.ScheduleCanceledReason
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.integrations.BillInstrumentationService
import ai.friday.billpayment.app.metrics.AbstractSummary
import ai.friday.billpayment.app.metrics.push
import ai.friday.billpayment.app.onboarding.instrumentation.OnboardingInstrumentationService
import ai.friday.billpayment.app.payment.BankTransfer
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.receipt.NotifyReceiptService
import jakarta.inject.Singleton

@Singleton
class CompleteTransaction(
    private val billEventPublisher: BillEventPublisher,
    private val balanceService: BalanceService,
    private val billInstrumentationService: BillInstrumentationService,
    private val onboardingInstrumentationService: OnboardingInstrumentationService,
    private val notifyReceiptService: NotifyReceiptService,
) {

    fun execute(transaction: Transaction) {
        balanceService.invalidate(transaction.paymentData.toSingle().accountPaymentMethod.id)

        val bill = transaction.settlementData.getTarget<Bill>()

        if (bill.isOnboardingTestPix()) {
            onboardingInstrumentationService.singlePixPaid(bill)
        } else {
            billInstrumentationService.paid(bill)
        }

        val billPaidEvent = BillPaid(
            billId = bill.billId,
            walletId = bill.walletId,
            actionSource = transaction.actionSource,
            transactionId = transaction.id,
            pixKeyDetails = transaction.getPixKeyDetails(),
            syncReceipt = true,
        )
        billEventPublisher.publish(
            bill,
            billPaidEvent,
        )

        if (bill.billType != BillType.AUTOMATIC_PIX) {
            billEventPublisher.publish(
                bill,
                BillPaymentScheduleCanceled(
                    billId = bill.billId,
                    walletId = bill.walletId,
                    actionSource = transaction.actionSource,
                    reason = ScheduleCanceledReason.EXECUTED,
                    batchSchedulingId = bill.schedule?.batchSchedulingId,
                ),
            )
        }

        notifyReceiptService.notifyWithAsyncRetry(billPaidEvent)

        pushPaymentMetrics(bill, billPaidEvent)
    }

    private fun Transaction.getPixKeyDetails() = if (settlementData.settlementOperation is BankTransfer) {
        settlementData.getOperation<BankTransfer>().pixKeyDetails
    } else {
        null
    }
}

fun pushPaymentMetrics(bill: Bill, billPaid: BillPaid) {
    val paymentStartedTimestamp = bill.history.lastOrNull() { it is BillPaymentScheduleStarted }?.created ?: return
    val billPaidTimestamp = billPaid.created

    BillPaidFromScheduleStartedElapsedTimeSummaryMetric.push(
        tags = mapOf(
            "type" to bill.billType.name,
        ),
        billPaidTimestamp - paymentStartedTimestamp,
    )
}

object BillPaidFromScheduleStartedElapsedTimeSummaryMetric : AbstractSummary()