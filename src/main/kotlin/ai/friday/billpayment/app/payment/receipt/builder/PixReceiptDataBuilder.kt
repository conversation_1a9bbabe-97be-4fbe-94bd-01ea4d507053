package ai.friday.billpayment.app.payment.receipt.builder

import ai.friday.billpayment.app.FinancialInstitutionGlobalData
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillPaid
import ai.friday.billpayment.app.bill.BillPayer
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.TransactionRepository
import ai.friday.billpayment.app.payment.BankTransfer
import ai.friday.billpayment.app.payment.INVOICE_PURPOSE
import ai.friday.billpayment.app.payment.PixReceiptData
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.simpleName
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.log.andAppend
import jakarta.inject.Singleton
import java.math.BigInteger
import java.time.Instant
import java.time.ZonedDateTime
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
class PixReceiptDataBuilder(
    private val accountRepository: AccountRepository,
    private val transactionRepository: TransactionRepository,
) : ReceiptDataBuilder {
    override val buildFor: List<BillType> = listOf(BillType.PIX)

    override fun build(bill: Bill, billPaid: BillPaid, wallet: Wallet): PixReceiptData {
        val markers = Markers.append("walletId", wallet.id).andAppend("billId", bill.billId)
        try {
            val transaction = transactionRepository.findById(billPaid.transactionId!!)

            val paymentMethods = accountRepository.findAccountPaymentMethodsByAccountIdAndStatus(
                wallet.founder.accountId,
                AccountPaymentMethodStatus.ACTIVE,
            )
            val bankAccount = paymentMethods.single {
                transaction.paymentData.toSingle().accountPaymentMethod.id == it.id
            }.method as InternalBankAccount
            val account = accountRepository.findById(wallet.founder.accountId)

            val (payeeIspb, payeeRoutingNo, payeeAccountNo, payeeAccountDv, payeeAccountType) = when {
                bill.recipient!!.bankAccount != null -> {
                    with(bill.recipient!!.bankAccount!!) {
                        PayeeBankDetails(ispb, routingNo, accountNo, accountDv, accountType)
                    }
                }
                bill.recipient!!.pixKeyDetails != null -> {
                    with(bill.recipient!!.pixKeyDetails!!.holder) {
                        PayeeBankDetails(ispb, routingNo, accountNo, accountDv, accountType)
                    }
                }

                bill.recipient!!.pspInformation != null -> {
                    with(bill.recipient!!.pspInformation!!) {
                        PayeeBankDetails(code, 0, BigInteger.ZERO, "0", AccountType.CHECKING) // TODO - definir esses dados no fluxo do pix automatico
                    }
                }

                else -> throw IllegalStateException("Recipient com dados faltando para construir o recibo do PIX")
            }

            markers.andAppend("payeeIspb", payeeIspb)
            val payeeFinancialInstitution = FinancialInstitutionGlobalData.pixParticipants.first { it.ispb == payeeIspb }
            markers.andAppend("bankNo", bankAccount.bankNo)
            val payerFinancialInstitution =
                FinancialInstitutionGlobalData.pixParticipants.first { it.compe == bankAccount.bankNo }
            val scheduler = wallet.getScheduler(bill)
            return PixReceiptData(
                authentication = transaction.settlementData.getOperation<BankTransfer>().authentication,
                dateTime = ZonedDateTime.ofInstant(Instant.ofEpochMilli(bill.paidDate!!), brazilTimeZone),
                recipient = bill.recipient!!,
                totalAmount = transaction.settlementData.totalAmount,
                billId = bill.billId,
                walletId = bill.paymentWalletId ?: bill.walletId,
                payer = BillPayer(document = account.document, name = account.name),
                purpose = INVOICE_PURPOSE,
                source = bill.source,
                payeeFinancialInstitution = payeeFinancialInstitution,
                payerFinancialInstitution = payerFinancialInstitution,
                payeeRoutingNo = payeeRoutingNo,
                payeeAccountNo = payeeAccountNo,
                payeeAccountDv = payeeAccountDv,
                payeeAccountType = payeeAccountType,
                payerBankAccount = bankAccount,
                walletName = wallet.name,
                scheduledBy = scheduler.simpleName(),
                transactionId = transaction.id,
                payments = transaction.toPaymentList(),
                description = bill.description,
            )
        } catch (e: Exception) {
            LOGGER.error(markers, "PixReceiptDataBuilder", e)
            throw e
        }
    }

    companion object {
        private val LOGGER = LoggerFactory.getLogger(PixReceiptDataBuilder::class.java)
    }
}