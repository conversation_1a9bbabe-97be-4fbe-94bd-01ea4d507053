package ai.friday.billpayment.app.payment.receipt.builder

import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillPaid
import ai.friday.billpayment.app.bill.BillPayer
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.integrations.TransactionRepository
import ai.friday.billpayment.app.payment.BoletoReceiptData
import ai.friday.billpayment.app.payment.BoletoSettlementResult
import ai.friday.billpayment.app.settlement.receipt.BoletoSettlementReceiptService
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.simpleName
import ai.friday.morning.date.brazilTimeZone
import jakarta.inject.Singleton
import java.time.Instant
import java.time.ZonedDateTime

@Singleton
class BoletoReceiptDataBuilder(
    private val transactionRepository: TransactionRepository,
    private val boletoSettlementReceiptService: BoletoSettlementReceiptService,
) : ReceiptDataBuilder {
    override val buildFor: List<BillType> = listOf(BillType.FICHA_COMPENSACAO, BillType.CONCESSIONARIA)

    override fun build(bill: Bill, billPaid: BillPaid, wallet: Wallet): BoletoReceiptData {
        val transaction = transactionRepository.findById(billPaid.transactionId!!)

        val boletoSettlementResult = transaction.settlementData.getOperation<BoletoSettlementResult>()

        val (authentication, paymentPartnerName) = if (boletoSettlementResult.authentication != null) {
            Pair(boletoSettlementResult.authentication!!, boletoSettlementResult.paymentPartnerName)
        } else {
            val receipt = boletoSettlementReceiptService.getReceipt(transaction)

            Pair(receipt.authentication, receipt.paymentPartnerName)
        }

        val scheduler = wallet.getScheduler(bill)

        return BoletoReceiptData(
            authentication = authentication,
            dateTime = ZonedDateTime.ofInstant(Instant.ofEpochMilli(bill.paidDate!!), brazilTimeZone),
            assignor = bill.assignor!!,
            recipient = bill.recipient,
            totalAmount = bill.amountTotal,
            billId = bill.billId,
            walletId = bill.paymentWalletId ?: bill.walletId,
            payer = bill.payer ?: BillPayer(document = wallet.founder.document, name = wallet.founder.name, alias = null),
            dueDate = bill.dueDate,
            barcode = bill.barcode!!,
            source = bill.source,
            walletName = wallet.name,
            scheduledBy = scheduler.simpleName(),
            paymentPartnerName = paymentPartnerName,
            transactionId = transaction.id,
            payments = transaction.toPaymentList(),
        )
    }
}