package ai.friday.billpayment.app.payment

import ai.friday.billpayment.app.integrations.ProcessScheduledBillsPublisher
import ai.friday.billpayment.app.integrations.ScheduledWalletRepository
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.measure
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.log.andAppend
import jakarta.inject.Named
import jakarta.inject.Singleton
import java.time.Duration
import java.time.LocalDate
import java.util.concurrent.ExecutorService
import java.util.concurrent.atomic.AtomicInteger
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.runBlocking
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import parallelMap
import reactor.core.scheduler.Schedulers

interface StartProcessScheduledBillsService {
    fun start(includeSubscription: Boolean)
}

@Singleton
class StartProcessScheduledBillsAsyncService(
    private val scheduledWalletRepository: ScheduledWalletRepository,
    private val processScheduledBillsPublisher: ProcessScheduledBillsPublisher,
    @Named("jobExecutor") val jobExecutor: ExecutorService,
) : StartProcessScheduledBillsService {

    private val logger = LoggerFactory.getLogger(StartProcessScheduledBillsAsyncService::class.java)

    override fun start(includeSubscription: Boolean) {
        val scheduledDate = getLocalDate()

        val markers = Markers.append("scheduledDate", scheduledDate)
            .andAppend("includeSubscription", includeSubscription)

        logger.info(markers, "StartProcessScheduledBillsAsyncService#start")

        val walletsToProcess = scheduledWalletRepository.findWalletsWithScheduledBillsBetween(scheduledDate.minusMonths(3), scheduledDate)
            .publishOn(Schedulers.fromExecutor(jobExecutor))

        val count = AtomicInteger(0)

        measure {
            walletsToProcess
                .buffer(10_000)
                .doOnError {
                    logger.error(markers, "StartProcessScheduledBillsAsyncService#error", it)
                }
                .doOnNext {
                    runBlocking(jobExecutor.asCoroutineDispatcher()) {
                        it.chunked(10)
                            .parallelMap {
                                count.addAndGet(it.size)
                                sendToProcess(it, scheduledDate, includeSubscription)
                            }
                    }
                }.blockLast()
        }.also { (_, elapsed) ->
            Duration.ofMillis(elapsed)
                .also { markers.andAppend("elapsedTime", "${it.toMinutesPart()}m ${it.toSecondsPart()}s") }
        }

        logger.info(markers.andAppend("count", count), "StartProcessScheduledBillsAsyncService#ended")
    }

    private fun sendToProcess(
        walletIdList: List<WalletId>,
        scheduledDate: LocalDate,
        includeSubscription: Boolean,
    ) {
        try {
            processScheduledBillsPublisher.publish(
                walletIdList = walletIdList,
                scheduleDate = scheduledDate,
                includeSubscription = includeSubscription,
            )
        } catch (e: Exception) {
            logger.error(
                Markers.append("size", walletIdList.size),
                "StartProcessScheduledBillsAsyncService",
                e,
            )
        }
    }
}