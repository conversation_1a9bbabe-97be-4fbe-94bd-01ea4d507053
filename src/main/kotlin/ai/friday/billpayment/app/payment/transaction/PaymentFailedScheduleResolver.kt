package ai.friday.billpayment.app.payment.transaction

import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillPaymentScheduleCanceled
import ai.friday.billpayment.app.bill.BillSchedulePostponed
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.ScheduleCanceledReason
import ai.friday.billpayment.app.bill.SchedulePostponedReason
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.payment.ErrorSource
import ai.friday.billpayment.app.payment.SettlementOperation
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.pix.PixTransactionError
import jakarta.inject.Singleton

@Singleton
open class PaymentFailedScheduleResolver(
    private val billEventPublisher: BillEventPublisher,
) {

    fun resolvePostPaymentFailedEvent(transaction: Transaction) {
        val bill = transaction.settlementData.getTarget<Bill>()
        val operation = transaction.settlementData.settlementOperation

        when {
            !bill.isPaymentScheduled() -> null

            !transaction.isRetryable() -> {
                BillPaymentScheduleCanceled(
                    billId = bill.billId,
                    walletId = bill.walletId,
                    actionSource = transaction.actionSource,
                    reason = ScheduleCanceledReason.BILL_NOT_PAYABLE, // TODO - Falha de confirmação de TEF acaba gerando um VOID na celcoin. Com isso a conta fica não pagavel. O certo seria identificar essa falha e ter um outro reason
                    batchSchedulingId = bill.schedule!!.batchSchedulingId,
                )
            }

            isPostponedPix(bill, operation) -> BillSchedulePostponed(
                billId = transaction.settlementData.getTarget<Bill>().billId,
                walletId = transaction.settlementData.getTarget<Bill>().walletId,
                actionSource = ActionSource.System,
                reason = when (operation?.getErrorMessage()) {
                    PixTransactionError.BusinessDailyLimit.code -> SchedulePostponedReason.LIMIT_REACHED
                    PixTransactionError.BusinessInsufficientBalance.code -> SchedulePostponedReason.INSUFFICIENT_FUNDS
                    else -> throw IllegalStateException("Unknown PixTransactionError on BillSchedulePostponed")
                },
            )

            isCreditCardDenied(transaction) -> BillPaymentScheduleCanceled(
                billId = bill.billId,
                walletId = bill.walletId,
                actionSource = ActionSource.System,
                reason = ScheduleCanceledReason.CANT_PAY_WITH_CURRENT_CREDIT_CARD,
                batchSchedulingId = bill.schedule!!.batchSchedulingId,
            )

            else -> null
        }?.let { postPaymentFailedEvent ->
            billEventPublisher.publish(transaction.settlementData.getTarget(), postPaymentFailedEvent)
        }
    }

    private fun isPostponedPix(bill: Bill, operation: SettlementOperation?): Boolean =
        bill.billType == BillType.PIX && operation?.getErrorMessage() in listOf(
            PixTransactionError.BusinessDailyLimit.code,
            PixTransactionError.BusinessInsufficientBalance.code,
        )

    private fun isCreditCardDenied(transaction: Transaction): Boolean {
        return transaction.getErrorSource() == ErrorSource.FRAUD_PREVENTION ||
            transaction.getErrorSource() == ErrorSource.ACQUIRER
    }
}