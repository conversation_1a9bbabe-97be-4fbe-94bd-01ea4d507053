package ai.friday.billpayment.app.payment.receipt.builder

import ai.friday.billpayment.adapters.celcoin.payerBank
import ai.friday.billpayment.adapters.celcoin.payerDocument
import ai.friday.billpayment.app.FinancialInstitutionGlobalData
import ai.friday.billpayment.app.banking.FinancialInstitution
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.banking.getDescription
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillPaid
import ai.friday.billpayment.app.bill.BillPayer
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.TransactionRepository
import ai.friday.billpayment.app.payment.BankTransfer
import ai.friday.billpayment.app.payment.INVOICE_PURPOSE
import ai.friday.billpayment.app.payment.InvoiceReceiptData
import ai.friday.billpayment.app.payment.TransactionType
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.simpleName
import ai.friday.morning.date.brazilTimeZone
import jakarta.inject.Singleton
import java.time.Instant
import java.time.ZonedDateTime

@Singleton
class InvoiceReceiptDataBuilder(
    private val accountRepository: AccountRepository,
    private val transactionRepository: TransactionRepository,
) : ReceiptDataBuilder {
    override val buildFor: List<BillType> = listOf(BillType.INVOICE)

    private val directInvoiceBank = FinancialInstitution(name = "Banco Arbi S.A.", ispb = null, compe = 213)

    override fun build(bill: Bill, billPaid: BillPaid, wallet: Wallet): InvoiceReceiptData {
        val payeeBank =
            FinancialInstitutionGlobalData.bankList.firstOrNull { it.number == bill.getBankNo() }
                ?.getDescription() ?: bill.getBankNo().toString()

        val account = accountRepository.findById(wallet.founder.accountId)

        val transaction = transactionRepository.findById(billPaid.transactionId!!)

        val scheduler = wallet.getScheduler(bill)

        val payerFinancialInfo = if (transaction.type == TransactionType.DIRECT_INVOICE) {
            PayerFinancialInfo(
                transaction.paymentData.toSingle().accountPaymentMethod.method as InternalBankAccount,
                directInvoiceBank,
                null,
                null,
            )
        } else {
            PayerFinancialInfo(null, null, payerBank, payerDocument)
        }

        return InvoiceReceiptData(
            authentication = transaction.settlementData.getOperation<BankTransfer>().authentication,
            dateTime = ZonedDateTime.ofInstant(Instant.ofEpochMilli(bill.paidDate!!), brazilTimeZone),
            recipient = bill.recipient!!,
            totalAmount = bill.amountTotal,
            purpose = INVOICE_PURPOSE,
            billId = bill.billId,
            walletId = bill.paymentWalletId ?: bill.walletId,
            payer = BillPayer(document = account.document, name = account.name),
            source = bill.source,
            payeeBank = payeeBank,
            paymentPartnerName = payerFinancialInfo.paymentPartnerName,
            paymentPartnerDocument = payerFinancialInfo.paymentPartnerDocument,
            walletName = wallet.name,
            scheduledBy = scheduler.simpleName(),
            payerBankAccount = payerFinancialInfo.payerBankAccount,
            payerFinancialInstitution = payerFinancialInfo.payerFinancialInstitution,
            transactionId = transaction.id,
            payments = transaction.toPaymentList(),
        )
    }

    private fun Bill.getBankNo() =
        recipient?.bankAccount?.bankNo ?: throw IllegalStateException("Invoice without recipient")
}