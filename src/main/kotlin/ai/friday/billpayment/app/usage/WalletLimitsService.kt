package ai.friday.billpayment.app.usage

import ai.friday.billpayment.adapters.lock.walletDailyPaymentLimitLockProvider
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.payment.PaymentSchedulingService
import ai.friday.billpayment.app.wallet.DailyPaymentLimit
import ai.friday.billpayment.app.wallet.DailyPaymentLimitType
import ai.friday.billpayment.app.wallet.GetLimitsErrors
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.MonthlyLimitUsage
import ai.friday.billpayment.app.wallet.UpdateLimitsErrors
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletLimitsUsage
import ai.friday.billpayment.app.wallet.WalletPaymentLimits
import ai.friday.billpayment.app.wallet.WalletPaymentLimitsWithUsage
import ai.friday.billpayment.app.wallet.WalletPersonType
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import jakarta.inject.Named
import jakarta.inject.Singleton
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZonedDateTime
import net.logstash.logback.marker.Markers
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

private const val DEFAULT_WHATSAPP_PAYMENT_LIMIT_AMOUNT = 50_00L
private const val DEFAULT_DAILY_PAYMENT_LIMIT_AMOUNT = 25_000_00L
private const val DEFAULT_NIGHTTIME_PAYMENT_LIMIT_AMOUNT = 1_000_00L

private const val DEFAULT_BASIC_DAILY_PAYMENT_LIMIT_AMOUNT = 500_00L
private const val DEFAULT_BASIC_NIGHTTIME_PAYMENT_LIMIT_AMOUNT = 500_00L

const val MAX_DAILY_PAYMENT_LIMIT_AMOUNT = 100_000_00L
const val MAX_NIGHTTIME_PAYMENT_LIMIT_AMOUNT = 10_000_00L
const val MAX_WHATSAPP_PAYMENT_LIMIT_AMOUNT = 1_000_00L

const val MAX_MONTHLY_PAYMENT_LIMIT_BASIC_ACCOUNT = 10_000_00L

@Singleton
open class WalletLimitsService(
    private val billRepository: BillRepository,
    private val walletRepository: WalletRepository,
    private val accountService: AccountService,
    private val paymentSchedulingService: PaymentSchedulingService,
    @Named(walletDailyPaymentLimitLockProvider) private val internalLock: InternalLock,
) {
    private val logger = LoggerFactory.getLogger(WalletLimitsService::class.java)

    private val nighttimeLimitStart = LocalTime.of(20, 0, 0, 0)
    private val nighttimeLimitEnd = LocalTime.of(5, 59, 59, 999)
    private val daytimeLimitStart = LocalTime.of(6, 0, 0, 0)

    open fun getDailyLimit(walletId: WalletId): Long {
        return findWalletPaymentLimitsOrException(walletId).daily.activeAmount
    }

    private fun validateUpdateRequestAndLock(
        accountId: AccountId,
        walletId: WalletId,
        type: DailyPaymentLimitType,
        amount: Long,
        source: ActionSource,
    ): Either<UpdateLimitsErrors, Unit> {
        when {
            amount < 0 -> return UpdateLimitsErrors.InvalidAmount.left()
            type == DailyPaymentLimitType.DAILY && amount > MAX_DAILY_PAYMENT_LIMIT_AMOUNT -> return UpdateLimitsErrors.InvalidAmount.left()
            type == DailyPaymentLimitType.NIGHTTIME && amount > MAX_NIGHTTIME_PAYMENT_LIMIT_AMOUNT -> return UpdateLimitsErrors.InvalidAmount.left()
            type == DailyPaymentLimitType.WHATSAPP_PAYMENT && amount > MAX_WHATSAPP_PAYMENT_LIMIT_AMOUNT -> return UpdateLimitsErrors.InvalidAmount.left()
        }

        try {
            val wallet = walletRepository.findWalletOrNull(walletId) ?: return UpdateLimitsErrors.ItemNotFound.left()

            if (!wallet.hasActiveMember(accountId)) {
                return UpdateLimitsErrors.ItemNotFound.left()
            }

            if (!checkLimitIsEditable(wallet, source)) {
                return UpdateLimitsErrors.UpdateLimitNotAllowed.left()
            }

            return Unit.right()
        } catch (e: Exception) {
            return UpdateLimitsErrors.UnknownError(e).left()
        }
    }

    fun updateLimit(
        accountId: AccountId,
        walletId: WalletId,
        type: DailyPaymentLimitType,
        amount: Long,
        source: ActionSource,
    ): Either<UpdateLimitsErrors, DailyPaymentLimit> {
        val markers = Markers.append("accountId", accountId.value)
            .andAppend("walletId", walletId.value)
            .andAppend("type", type)
            .andAppend("amount", amount)
            .andAppend("source", source)

        val lock = internalLock.acquireLock(walletId.value)

        if (lock == null) {
            LOG.warn(markers.andAppend("updateLimitsError", UpdateLimitsErrors.PendingLimitChange), "WalletLimitsService#updateLimit")

            return UpdateLimitsErrors.PendingLimitChange.left()
        }

        try {
            validateUpdateRequestAndLock(
                accountId = accountId,
                walletId = walletId,
                type = type,
                amount = amount,
                source = source,
            ).getOrElse {
                markers.andAppend("updateLimitsError", it)
                logger.warn(markers, "WalletLimitsService#updateLimit")
                return it.left()
            }

            if (type == DailyPaymentLimitType.MONTHLY) {
                logger.info(markers, "WalletLimitsService#updateLimit")
                return walletRepository.saveLimit(walletId, type, amount, null).right()
            }

            val defaults = getDefaultLimits(walletId)

            if (type == DailyPaymentLimitType.NIGHTTIME) {
                val dailyLimit = findWalletLimitOrNull(walletId, DailyPaymentLimitType.DAILY) ?: defaults.daily

                markers.andAppend("dailyLimit", dailyLimit)

                if ((dailyLimit.nextAmount ?: dailyLimit.activeAmount) < amount) {
                    logger.warn(markers.andAppend("updateLimitsError", "NighttimeLimitGreaterThanDaily"), "WalletLimitsService#updateLimit")
                    return UpdateLimitsErrors.NighttimeLimitGreaterThanDaily.left()
                }
            }

            val whatsAppPaymentLimit = findWalletLimitOrNull(walletId, DailyPaymentLimitType.WHATSAPP_PAYMENT) ?: defaults.whatsAppPayment
            val whatsAppLimitIsLowerThanAmount = amount < whatsAppPaymentLimit.activeAmount || whatsAppPaymentLimit.nextAmount?.let { amount < it } == true

            if (type == DailyPaymentLimitType.DAILY && whatsAppLimitIsLowerThanAmount) {
                walletRepository.saveLimit(walletId, DailyPaymentLimitType.WHATSAPP_PAYMENT, amount, null, withTimestamp = false)
                logger.info(markers.andAppend("whatsAppDailyLimit", amount), "WalletLimitsService#updateLimit")
                return walletRepository.saveLimit(walletId, type, amount, null).right()
            }

            if (type == DailyPaymentLimitType.WHATSAPP_PAYMENT) {
                val dailyLimit = findWalletLimitOrNull(walletId, DailyPaymentLimitType.DAILY) ?: defaults.daily
                val whatsAppLimit = findWalletLimitOrNull(walletId, DailyPaymentLimitType.WHATSAPP_PAYMENT) ?: whatsAppPaymentLimit
                markers.andAppend("dailyLimit", dailyLimit)

                if ((dailyLimit.nextAmount ?: dailyLimit.activeAmount) < amount) {
                    logger.warn(markers.andAppend("updateLimitsError", "WhatsAppPaymentLimitGreaterThanDaily"), "WalletLimitsService#updateLimit")
                    return UpdateLimitsErrors.WhatsAppPaymentLimitGreaterThanDaily.left()
                }

                logger.info(markers.andAppend("whatsAppDailyLimit", whatsAppPaymentLimit), "WalletLimitsService#updateLimit")
                return walletRepository.saveLimit(walletId = walletId, type = type, currentAmount = amount, lastAmount = whatsAppLimit.activeAmount, withTimestamp = false).right()
            }

            val currentLimit = findWalletLimitOrNull(walletId, type)
            markers.andAppend("currentLimit", currentLimit)

            if (currentLimit != null) {
                if (currentLimit.activeAmount == amount) {
                    logger.info(markers, "WalletLimitsService#updateLimit")
                    return currentLimit.right()
                }

                if (currentLimit.isPending) {
                    logger.warn(markers.andAppend("updateLimitsError", "PendingLimitChange"), "WalletLimitsService#updateLimit")
                    return UpdateLimitsErrors.PendingLimitChange.left()
                }
            }

            val targetLimit = currentLimit ?: defaults.get(type) ?: throw IllegalStateException("Expected to have a valid limit")
            val lastAmount = targetLimit.activeAmount
            markers.andAppend("lastAmount", lastAmount)

            val savedLimit = walletRepository.saveLimit(walletId, type, amount, lastAmount)
            markers.andAppend("savedLimit", savedLimit)

            if (type == DailyPaymentLimitType.DAILY && amount < lastAmount) {
                val nightTimeLimit = findWalletLimitOrNull(walletId, DailyPaymentLimitType.NIGHTTIME) ?: defaults.nighttime
                markers.andAppend("nightTimeLimit", nightTimeLimit)

                when {
                    nightTimeLimit activeAmountIsHigherThan amount -> walletRepository.saveLimit(
                        walletId,
                        DailyPaymentLimitType.NIGHTTIME,
                        amount,
                        nightTimeLimit.activeAmount,
                    )

                    nightTimeLimit nextAmountIsHigherThan amount -> walletRepository.saveLimit(
                        walletId,
                        DailyPaymentLimitType.NIGHTTIME,
                        nightTimeLimit.activeAmount,
                        null,
                    )
                }
            }

            logger.info(markers, "WalletLimitsService#updateLimit")
            return savedLimit.right()
        } catch (e: Exception) {
            logger.error(markers, "WalletLimitsService#updateLimit", e)
            return UpdateLimitsErrors.UnknownError(e).left()
        } finally {
            lock.unlock()
        }
    }

    open fun getWhatsAppPaymentLimit(walletId: WalletId): DailyPaymentLimit {
        return findWalletLimitOrNull(walletId, DailyPaymentLimitType.WHATSAPP_PAYMENT)
            ?: getDefaultLimits(walletId).whatsAppPayment
    }

    open fun getAvailableLimit(
        walletId: WalletId,
        date: LocalDate = getLocalDate(),
        countScheduledBankTransfer: Boolean = false,
    ): Long {
        val dueDateIsToday = getZonedDateTime().toLocalDate().equals(date)

        val totalScheduled = if (countScheduledBankTransfer) {
            paymentSchedulingService.getScheduledBillsTotalAmount(
                walletId = walletId,
                startDate = null,
                endDate = date,
            ) { bill ->
                bill.shouldCountOnTransferLimits()
            }
        } else {
            0
        }

        val availableLimit = if (dueDateIsToday) {
            getCurrentWindowAvailableLimit(walletId = walletId)
        } else {
            getDailyLimit(walletId = walletId)
        }

        return availableLimit - totalScheduled
    }

    open fun getAvailableMonthlyLimit(
        walletId: WalletId,
        date: LocalDate = getLocalDate(),
        countScheduled: Boolean = false,
    ): Long? {
        val monthlyLimit = getMonthlyLimit(walletId) ?: return null

        val monthBegin = date.withDayOfMonth(1).atStartOfDay(brazilTimeZone)
        val monthEnd = monthBegin.plusMonths(1).minusNanos(1)

        val totalScheduled = if (countScheduled) {
            paymentSchedulingService.getScheduledBillsTotalAmount(
                walletId = walletId,
                startDate = monthBegin.toLocalDate(),
                endDate = monthEnd.toLocalDate(),
            ) {
                it.shouldCountOnTransactionalLimits()
            }
        } else {
            0
        }

        val monthlyLimitUsage = billRepository.getTotalPaid(
            walletId = walletId,
            startDate = monthBegin,
            endDate = monthEnd,
            types = BillType.entries.filter { it.shouldCountOnTransactionalLimits() },
        )

        val availableMonthlyLimit = monthlyLimit - monthlyLimitUsage
        return availableMonthlyLimit - totalScheduled
    }

    open fun getAvailableAssistantLimit(walletId: WalletId, accountId: AccountId): Long {
        val assistantUsage = billRepository.getTotalPaidBillsByScheduleSourceType(
            walletId,
            startDate = LocalDate.now().atStartOfDay(brazilTimeZone),
            endDate = LocalDate.now().atTime(LocalTime.MAX).atZone(brazilTimeZone),
            actionSource = ActionSource.VirtualAssistant(accountId),
        )
        val assistantPaymentLimit = findWalletLimitOrNull(walletId, DailyPaymentLimitType.WHATSAPP_PAYMENT) ?: getDefaultLimits(walletId).whatsAppPayment
        val limit = minOf(assistantPaymentLimit.activeAmount, MAX_WHATSAPP_PAYMENT_LIMIT_AMOUNT)

        return limit - assistantUsage
    }

    open fun getAllWalletLimitsWithUsage(
        walletId: WalletId,
        accountId: AccountId,
    ): Either<GetLimitsErrors, WalletPaymentLimitsWithUsage> {
        val wallet = walletRepository.findWalletOrNull(walletId) ?: return GetLimitsErrors.WalletNotFound.left()
        val walletLimits = findWalletPaymentLimits(walletId = walletId).getOrElse { return it.left() }

        val availableMonthlyLimit = getAvailableMonthlyLimit(walletId = walletId)
        val availableForecastedMonthlyLimit = getAvailableMonthlyLimit(walletId = walletId, countScheduled = true)
        val availableDailyLimit = getAvailableDailyLimit(walletId = walletId, dailyPaymentLimits = walletLimits)
        val nightTimeLimit = getAvailableNightTimeLimit(walletId = walletId, dailyPaymentLimits = walletLimits)

        return WalletPaymentLimitsWithUsage(
            limits = walletLimits,
            usage = WalletLimitsUsage(
                daily = walletLimits.daily.activeAmount - availableDailyLimit,
                nighttime = walletLimits.nighttime.activeAmount - nightTimeLimit,
                monthly = walletLimits.monthly?.let {
                    MonthlyLimitUsage(
                        used = it.activeAmount - (availableMonthlyLimit ?: 0L),
                        forecasted = it.activeAmount - (availableForecastedMonthlyLimit ?: 0L),
                    )
                },
                automaticPix = 0L, // TODO: Ainda precisa implementar o cálculo de uso do limite de pix automático
            ),
            editable = checkLimitIsEditable(wallet = wallet, ActionSource.Api(accountId = accountId)),
        ).right()
    }

    private fun checkLimitIsEditable(wallet: Wallet, source: ActionSource): Boolean {
        return when (source) {
            ActionSource.System -> true
            is ActionSource.WalletActionSource -> when (wallet.getPersonType()) {
                WalletPersonType.PHYSICAL_PERSON -> wallet.founder.accountId == source.accountId
                WalletPersonType.LEGAL_PERSON -> wallet.getActiveMemberOrNull(source.accountId!!)?.type == MemberType.COFOUNDER
            }

            else -> throw IllegalStateException("Action source not supported $source")
        }
    }

    private fun getMonthlyLimit(walletId: WalletId): Long? {
        return findWalletPaymentLimitsOrException(walletId).monthly?.activeAmount
    }

    fun findWalletLimitOrNull(walletId: WalletId, type: DailyPaymentLimitType): DailyPaymentLimit? {
        return walletRepository.findLimitOrNull(walletId, type)
    }

    private fun getCurrentWindowAvailableLimit(walletId: WalletId): Long {
        val markers = append("walletId", walletId.value)

        val dailyPaymentLimits = findWalletPaymentLimitsOrException(walletId)
        markers.andAppend("dailyActiveAmount", dailyPaymentLimits.daily.activeAmount)
            .andAppend("nighttimeActiveAmount", dailyPaymentLimits.nighttime.activeAmount)

        val now = getZonedDateTime()

        val isNighttime =
            now.toLocalTime().isAfter(nighttimeLimitStart) || now.toLocalTime().isBefore(daytimeLimitStart)

        val isAnotherDayInLimit =
            now.toLocalTime().isAfter(LocalTime.MIDNIGHT) && now.toLocalTime().isBefore(daytimeLimitStart)

        val updatedTime = if (isAnotherDayInLimit) {
            now.minusDays(1)
        } else {
            now
        }
        markers.andAppend("isNighttime", isNighttime)
            .andAppend("isAnotherDayInLimit", isAnotherDayInLimit)
            .andAppend("updatedTime", updatedTime)

        val dailyAvailableLimit = getAvailableDailyLimit(
            walletId = walletId,
            dailyPaymentLimits = dailyPaymentLimits,
            date = updatedTime,
        )
        markers.andAppend("dailyAvailableLimit", dailyAvailableLimit)

        if (!isNighttime) {
            LOG.debug(markers.andAppend("windowAvailableLimit", dailyAvailableLimit), "getAvailableLimit")
            return dailyAvailableLimit
        }

        val nightTimeAvailableLimit = getAvailableNightTimeLimit(
            walletId = walletId,
            dailyPaymentLimits = dailyPaymentLimits,
            date = updatedTime,
        )
        markers.andAppend("nightTimeAvailableLimit", nightTimeAvailableLimit)

        val windowAvailableLimit = minOf(dailyAvailableLimit, nightTimeAvailableLimit)

        markers.andAppend("windowAvailableLimit", windowAvailableLimit)

        LOG.debug(markers, "getAvailableLimit")
        return windowAvailableLimit
    }

    private fun getAvailableDailyLimit(
        walletId: WalletId,
        dailyPaymentLimits: WalletPaymentLimits,
        date: ZonedDateTime = getZonedDateTime(),
    ): Long {
        val billTypes = listOf(BillType.PIX, BillType.INVOICE)
        val dailyLimitUsage = billRepository.getTotalPaid(
            walletId = walletId,
            date = date.toLocalDate(),
            types = billTypes,
        )

        return dailyPaymentLimits.daily.activeAmount - dailyLimitUsage
    }

    private fun getAvailableNightTimeLimit(
        walletId: WalletId,
        dailyPaymentLimits: WalletPaymentLimits,
        date: ZonedDateTime = getZonedDateTime(),
    ): Long {
        val billTypes = listOf(BillType.PIX, BillType.INVOICE)
        val now = getZonedDateTime()
        val isNighttime =
            now.toLocalTime().isAfter(nighttimeLimitStart) || now.toLocalTime().isBefore(daytimeLimitStart)

        if (!isNighttime) {
            return dailyPaymentLimits.nighttime.activeAmount
        }

        val nighttimeLimitUsage = billRepository.getTotalPaid(
            walletId = walletId,
            startDate = date.with(nighttimeLimitStart),
            endDate = date.plusDays(1).with(nighttimeLimitEnd),
            types = billTypes,
        )

        return dailyPaymentLimits.nighttime.activeAmount - nighttimeLimitUsage
    }

    private fun findWalletPaymentLimitsOrException(walletId: WalletId): WalletPaymentLimits {
        return findWalletPaymentLimits(walletId).getOrElse {
            when (it) {
                is GetLimitsErrors.WalletNotFound -> throw ItemNotFoundException(walletId)
                is GetLimitsErrors.UnknownError -> throw it.error
            }
        }
    }

    open fun findWalletPaymentLimits(walletId: WalletId): Either<GetLimitsErrors, WalletPaymentLimits> {
        return try {
            val storedLimits = walletRepository.findAllLimits(walletId)
            val defaults = getDefaultLimits(walletId)

            WalletPaymentLimits(
                automaticPix = storedLimits.automaticPix ?: defaults.automaticPix,
                daily = storedLimits.daily ?: defaults.daily,
                nighttime = storedLimits.nighttime ?: defaults.nighttime,
                monthly = storedLimits.monthly ?: defaults.monthly,
                whatsAppPayment = storedLimits.whatsAppPayment ?: defaults.whatsAppPayment,
            ).right()
        } catch (e: Exception) {
            LOG.error(
                append("walletId", walletId.value),
                "GetAvailableLimit",
                e,
            )
            GetLimitsErrors.UnknownError(e).left()
        }
    }

    fun setDefaultFullAccountLimits(accountId: AccountId) {
        val wallet = walletRepository.findWallets(accountId).single { it.founder.accountId == accountId }

        walletRepository.saveLimit(
            wallet.id,
            DailyPaymentLimitType.DAILY,
            DEFAULT_DAILY_PAYMENT_LIMIT_AMOUNT,
            null,
        )
        walletRepository.saveLimit(
            wallet.id,
            DailyPaymentLimitType.NIGHTTIME,
            DEFAULT_NIGHTTIME_PAYMENT_LIMIT_AMOUNT,
            null,
        )

        walletRepository.deleteMonthlyLimit(wallet.id)
    }

    private fun getDefaultLimits(walletId: WalletId): WalletPaymentLimits {
        val wallet = walletRepository.findWallet(walletId)
        val founderAccount = accountService.findAccountById(wallet.founder.accountId)

        val automaticPix = DailyPaymentLimit(
            type = DailyPaymentLimitType.AUTOMATIC_PIX,
            amount = DEFAULT_DAILY_PAYMENT_LIMIT_AMOUNT,
        )

        val dailyPaymentLimit = DailyPaymentLimit(
            type = DailyPaymentLimitType.DAILY,
            amount = if (founderAccount.type == UserAccountType.BASIC_ACCOUNT) DEFAULT_BASIC_DAILY_PAYMENT_LIMIT_AMOUNT else DEFAULT_DAILY_PAYMENT_LIMIT_AMOUNT,
        )

        val nighttimePaymentLimit = DailyPaymentLimit(
            type = DailyPaymentLimitType.NIGHTTIME,
            amount = if (founderAccount.type == UserAccountType.BASIC_ACCOUNT) DEFAULT_BASIC_NIGHTTIME_PAYMENT_LIMIT_AMOUNT else DEFAULT_NIGHTTIME_PAYMENT_LIMIT_AMOUNT,
        )

        val monthlyPaymentLimit = if (founderAccount.type == UserAccountType.BASIC_ACCOUNT) {
            DailyPaymentLimit(
                type = DailyPaymentLimitType.MONTHLY,
                amount = MAX_MONTHLY_PAYMENT_LIMIT_BASIC_ACCOUNT,
            )
        } else {
            null
        }

        val whatsAppPaymentLimit = DailyPaymentLimit(
            type = DailyPaymentLimitType.WHATSAPP_PAYMENT,
            amount = DEFAULT_WHATSAPP_PAYMENT_LIMIT_AMOUNT,
        )

        return WalletPaymentLimits(
            automaticPix = automaticPix,
            daily = dailyPaymentLimit,
            nighttime = nighttimePaymentLimit,
            monthly = monthlyPaymentLimit,
            whatsAppPayment = whatsAppPaymentLimit,
        )
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(WalletLimitsService::class.java)
    }
}

private infix fun DailyPaymentLimit.activeAmountIsHigherThan(amount: Long) = activeAmount > amount

private infix fun DailyPaymentLimit.nextAmountIsHigherThan(amount: Long) = nextAmount !== null && nextAmount > amount