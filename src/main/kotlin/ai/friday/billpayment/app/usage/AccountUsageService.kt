package ai.friday.billpayment.app.usage

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.wallet.GetLimitsErrors
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletPaymentLimitsWithUsage
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import jakarta.inject.Singleton

@Singleton
open class AccountUsageService(
    private val creditCardUsageService: CreditCardUsageService,
    private val walletLimitsService: WalletLimitsService,
) {
    open fun calculateUsage(accountId: AccountId, walletId: WalletId): Either<GetLimitsErrors, Usage> {
        val creditCardUsage = creditCardUsageService.calculateCreditCardUsage(accountId)
        val walletUsage =
            walletLimitsService.getAllWalletLimitsWithUsage(walletId, accountId).getOrElse { return it.left() }

        return Usage(
            wallet = walletUsage,
            creditCard = creditCardUsage,
        ).right()
    }
}

data class Usage(
    val wallet: WalletPaymentLimitsWithUsage,
    val creditCard: CreditCardUsage?,
)