package ai.friday.billpayment.app.subscription

import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.and
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.FinancialInstitutionGlobalData
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountClosureDetails
import ai.friday.billpayment.app.account.AccountClosureReason
import ai.friday.billpayment.app.account.AccountConfigurationService
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentStatus
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.CloseAccountService
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillRecipientUpdated
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.chatbot.ChatbotMessagePublisher
import ai.friday.billpayment.app.dda.DDAService
import ai.friday.billpayment.app.dda.DDAStatus
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionReason
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionService
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionStatus
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.BillRecurrenceRepository
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.CrmRepository
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.SubscriptionRepository
import ai.friday.billpayment.app.recurrence.BillRecurrence
import ai.friday.billpayment.app.recurrence.BillRecurrenceService
import ai.friday.billpayment.app.recurrence.CancelSubscriptionResult
import ai.friday.billpayment.app.recurrence.RecurrenceCreationError
import ai.friday.billpayment.app.recurrence.RecurrenceFrequency
import ai.friday.billpayment.app.recurrence.RecurrenceId
import ai.friday.billpayment.app.recurrence.RecurrenceRule
import ai.friday.billpayment.app.recurrence.RecurrenceStatus
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.context.annotation.Property
import jakarta.inject.Provider
import jakarta.inject.Singleton
import java.time.Duration
import java.time.LocalDate
import java.time.ZonedDateTime
import kotlin.random.Random
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

interface SubscritionDiscountInterface {
    fun checkAndApplyFirstInstallmentDiscount(account: Account, newSubscription: Subscription): Boolean
}

@Singleton
open class SubscriptionService(
    private val accountRepository: AccountRepository,
    private val subscriptionRepository: SubscriptionRepository,
    private val inAppSubscriptionService: InAppSubscriptionService,
    private val recurrenceService: BillRecurrenceService,
    private val billRepository: BillRepository,
    private val subscriptionConfiguration: SubscriptionConfiguration,
    private val notificationAdapter: NotificationAdapter,
    private val accountConfigurationService: AccountConfigurationService,
    private val ddaService: DDAService,
    private val closeAccountServiceProvider: Provider<CloseAccountService>,
    private val intercomAdapter: CrmRepository,
    private val chatbotMessagePublisher: ChatbotMessagePublisher,
    private val subscritionDiscountInterfaceProvider: Provider<SubscritionDiscountInterface>?,
    private val recurrenceRepository: BillRecurrenceRepository,
    private val billEventRepository: BillEventRepository,
    @Property(name = "features.disableOverdueAccountDDA", defaultValue = "false") private val disableOverdueAccountDDA: Boolean,
    @Property(name = "features.closeOverdueAccount", defaultValue = "false") private val closeOverdueAccount: Boolean,
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    fun find(accountId: AccountId): Subscription {
        return subscriptionRepository.find(accountId) ?: throw ItemNotFoundException(accountId.value)
    }

    open fun findOrNull(accountId: AccountId): Subscription? {
        return subscriptionRepository.find(accountId)
    }

    fun handleSubscriptionPayment(accountId: AccountId): Either<SubscriptionPaymentError, Unit> {
        val markers = Markers.append("accountId", accountId.value)
        val logName = "SubscriptionService#handleSubscriptionPayment"

        val subscription = subscriptionRepository.find(accountId) ?: return SubscriptionPaymentError.SubscriptionNotFound.left()
        val account = accountRepository.findByIdOrNull(accountId) ?: return SubscriptionPaymentError.AccountNotFound.left()

        if (subscription.paymentStatus == SubscriptionPaymentStatus.PAID && account.status == AccountStatus.ACTIVE && account.paymentStatus == AccountPaymentStatus.UpToDate) {
            LOG.info(markers.andAppend("reason", "already up to date"), logName)
            return Unit.right()
        }

        val ddaRegister = ddaService.findDDARegister(Document(account.document))

        if (ddaRegister == null || ddaRegister.status in listOf(DDAStatus.CLOSING, DDAStatus.CLOSED, DDAStatus.PENDING_CLOSE)) {
            val ddaResult = ddaService.register(accountId = accountId, document = account.document)
            markers.andAppend("ddaStatus", ddaResult.name)
        }

        subscriptionRepository.save(subscription.copy(paymentStatus = SubscriptionPaymentStatus.PAID))
        accountRepository.save(account.copy(paymentStatus = AccountPaymentStatus.UpToDate, status = AccountStatus.ACTIVE))

        LOG.info(markers, logName)
        return Unit.right()
    }

    fun subscribe(
        accountId: AccountId,
        amount: Long,
        dayOfMonth: Int,
        sendNotification: Boolean = true,
    ): SubscribeResult {
        val markers = append("accountId", accountId.value)

        val account = accountRepository.findByIdOrNull(accountId)

        if (account == null) {
            LOG.info(markers, "SubscriptionServiceSubscribe")
            return SubscribeResult.AccountNotFound
        }

        if (account.status !in listOf(AccountStatus.ACTIVE, AccountStatus.BLOCKED)) {
            LOG.info(markers.andAppend("accountStatus", account.status), "SubscriptionServiceSubscribe")
            return SubscribeResult.AccountNotActive
        }

        if (accountConfigurationService.find(accountId).freeOfFridaySubscription) {
            return SubscribeResult.AccountIsFreeOfFridaySubscription
        }

        markers.andAppend("defaultWalletId", account.configuration.defaultWalletId)

        val subscription = subscriptionRepository.find(accountId)

        if (subscription != null && subscription.status == SubscriptionStatus.ACTIVE) {
            if (subscription.amount != amount || subscription.dayOfMonth != dayOfMonth) {
                LOG.warn(markers, "SubscriptionServiceSubscribe")
                return SubscribeResult.Conflict(subscription)
            }
        } else {
            val result = recurrenceService.create(
                recurrence = subscriptionConfiguration.toRecurrence(account, amount, dayOfMonth),
                dryRun = false,
            ).getOrElse {
                return it.handle(markers)
            }
            markers.andAppend("recurrenceId", result.recurrence.id.value)

            val firstActiveBill = result.recurrence.findFirstActiveBill() // TODO -
            markers.andAppend("firstActiveBillId", firstActiveBill?.billId?.value.orEmpty())

            val nextEffectiveDueDate =
                firstActiveBill?.effectiveDueDate ?: result.recurrence.findLastBill().effectiveDueDate

            val newSubscription = Subscription(
                accountId = accountId,
                document = Document(account.document),
                status = SubscriptionStatus.ACTIVE,
                amount = result.recurrence.amount,
                dayOfMonth = result.recurrence.rule.startDate.dayOfMonth,
                recurrenceId = result.recurrence.id,
                paymentStatus = SubscriptionPaymentStatus.PAID,
                walletId = account.defaultWalletId(),
                nextEffectiveDueDate = nextEffectiveDueDate,
            )
            subscriptionRepository.save(newSubscription)

            val logName = "SubscriptionService#Subscribe"
            val subscritionDiscountInterface = subscritionDiscountInterfaceProvider?.get()
            markers.andAppend("hasSubscritionDiscountInterfaceBean", subscritionDiscountInterface != null)

            val receivedDiscount = subscritionDiscountInterface?.checkAndApplyFirstInstallmentDiscount(account, newSubscription) ?: false
            logger.info(markers.andAppend("receivedDiscount", receivedDiscount), logName)

            if (sendNotification && !receivedDiscount) {
                notificationAdapter.notifySubscriptionCreated(
                    accountId = account.accountId,
                    dueDate = result.recurrence.rule.startDate,
                    amount = result.recurrence.amount,
                )
            }

            accountRepository.save(
                account.updateAccountStatus(
                    daysOverdue = 0,
                    inactivationFlowDates = null,
                    markers = markers,
                ),
            )
        }

        LOG.info(markers, "SubscriptionServiceSubscribe")
        return SubscribeResult.Success
    }

    fun unsubscribe(accountId: AccountId): Either<UnsubscribeError, Unit> {
        val markers = Markers.append("accountId", accountId.value)

        val account = accountRepository.findByIdOrNull(accountId)

        if (account == null) {
            LOG.error(markers, "SubscriptionServiceUnsubscribe")
            return UnsubscribeError.AccountNotFound.left()
        }

        val subscription = subscriptionRepository.find(accountId)

        if (subscription != null && subscription.status == SubscriptionStatus.ACTIVE) {
            markers.andAppend("walletId", subscription.walletId.value)

            val cancelSubscriptionResult = recurrenceService.cancelSubscription(
                subscription.recurrenceId,
                subscription.walletId,
            )
            markers.andAppend("cancelSubscriptionResult", cancelSubscriptionResult)

            when (cancelSubscriptionResult) {
                CancelSubscriptionResult.BillProcessing -> {
                    LOG.warn(markers, "SubscriptionServiceUnsubscribe")
                    return UnsubscribeError.BillProcessing.left()
                }

                CancelSubscriptionResult.RecurrenceInvalid,
                CancelSubscriptionResult.RecurrenceNotFound,
                -> {
                    LOG.error(markers, "SubscriptionServiceUnsubscribe")
                    return UnsubscribeError.ServerError(cancelSubscriptionResult.toString()).left()
                }

                is CancelSubscriptionResult.ServerError -> {
                    LOG.error(markers, "SubscriptionServiceUnsubscribe")
                    return UnsubscribeError.ServerError(cancelSubscriptionResult.message).left()
                }

                CancelSubscriptionResult.Success -> {
                    subscriptionRepository.save(subscription.copy(status = SubscriptionStatus.INACTIVE))
                }
            }
        }

        LOG.info(markers, "SubscriptionServiceUnsubscribe")
        return Unit.right()
    }

    fun updateAllSubscriptionsToNewAccount(
        oldBankAccount: String,
        limit: Int?,
    ): Pair<Int, Int> {
        val logName = "SubscriptionService#updateAllSubscrionsToNewAccount"

        fun updateSubscriptionBill(billId: BillId, bankAccount: BankAccount): Boolean {
            val bill = billEventRepository.getBillById(billId).getOrElse { throw IllegalStateException("error $billId") }

            if (bill.status != BillStatus.ACTIVE) {
                return false
            }
            val markers = append("billId", billId.value)
                .andAppend("walletId", bill.walletId.value)
                .andAppend("oldBankAccount", bill.recipient!!.bankAccount!!.accountNo)
                .andAppend("oldBankAccountDv", bill.recipient!!.bankAccount!!.accountDv)
                .andAppend("newBankAccount", bankAccount.accountNo)
                .andAppend("newBankAccountDv", bankAccount.accountDv)

            val billRecipientUpdated = BillRecipientUpdated(
                billId = billId,
                created = getZonedDateTime().toInstant().toEpochMilli(),
                walletId = bill.walletId,
                actionSource = ActionSource.System,
                recipient = bill.recipient!!.copy(bankAccount = bankAccount.copy()),
            )

            billEventRepository.save(billRecipientUpdated)
            val updatedBill = bill.apply(billRecipientUpdated)
            billRepository.save(updatedBill)
            logger.info(markers, logName)
            return true
        }

        fun updateSubscriptionRecurrence(recurrence: BillRecurrence): List<BillId> {
            val newBankAccount = subscriptionConfiguration.bankAccount.accountNo.toBigInteger()
            val newBankAccountDv = subscriptionConfiguration.bankAccount.accountDv
            val markers = append("recurrenceId", recurrence.id.value)
                .andAppend("walletId", recurrence.walletId.value)
                .andAppend("oldBankAccount", recurrence.recipientBankAccount!!.accountNo)
                .andAppend("oldBankAccountDv", recurrence.recipientBankAccount.accountDv)
                .andAppend("newBankAccount", newBankAccount)
                .andAppend("newBankAccountDv", newBankAccountDv)

            val receiptBankAccount = recurrence.recipientBankAccount.copy(accountNo = newBankAccount, accountDv = newBankAccountDv)

            recurrenceRepository.save(recurrence.copy(recipientBankAccount = receiptBankAccount))

            logger.info(markers, logName)

            return recurrence.bills.mapNotNull { billId ->
                val updatedBill = updateSubscriptionBill(billId, receiptBankAccount)
                if (updatedBill) {
                    billId
                } else {
                    null
                }
            }
        }

        val subscriptionRecurrences = recurrenceRepository.findAll(status = RecurrenceStatus.ACTIVE).filter { it.actionSource is ActionSource.Subscription && oldBankAccount == ("${it.recipientBankAccount?.accountNo}${it.recipientBankAccount?.accountDv}") }.take(limit ?: Int.MAX_VALUE)
        logger.info(append("recurrencesCount", subscriptionRecurrences.size).andAppend("oldBankAccount", oldBankAccount), logName)

        return subscriptionRecurrences.fold(0 to 0) { (totalSub, totalBills), recurrence ->
            try {
                val bills = updateSubscriptionRecurrence(recurrence)
                totalSub + 1 to totalBills + bills.size
            } catch (e: Exception) {
                logger.error(append("recurrenceId", recurrence.id.value), logName, e)
                totalSub to totalBills
            }
        }.also {
            logger.info(append("totalRecurrenceUpdted", it.first).andAppend("totalBillUpdted", it.second), logName)
        }
    }

    fun ignoreSubscriptionFeeComingMonths(accountId: AccountId, months: Int, reason: String): List<IgnoreSubscriptionFeeResult> {
        val logName = "SubscriptionService#ignoreSubscriptionFeeMonthsAhead"
        val markers = Markers.append("accountId", accountId.value).andAppend("months", months).andAppend("reason", reason)
        val subscription = subscriptionRepository.find(accountId) ?: return listOf(IgnoreSubscriptionFeeResult.SubscriptionNotFound)

        val results = mutableListOf<IgnoreSubscriptionFeeResult>()
        val dates = mutableListOf<LocalDate>()
        val currentDate = getLocalDate()

        for (month in 0 until months) {
            val dueDate = subscription.calcNextSubscriptionDueDate(currentDate.plusMonths(month.toLong()))
            dates.add(dueDate)
            results.add(ignoreSubscriptionFee(accountId, dueDate))
        }

        markers.andAppend("dates", dates)

        if (results.all { it == IgnoreSubscriptionFeeResult.Success }) {
            markers.andAppend("allSuccess", true)
            LOG.info(markers, logName)
        } else {
            markers.andAppend("results", results)
            LOG.warn(markers, logName)
        }
        intercomAdapter.publishEvent(accountId, "isencao_de_assinatura", mapOf("reason" to reason, "months" to months))

        return results
    }

    fun ignoreSubscriptionFee(accountId: AccountId, dueDate: LocalDate): IgnoreSubscriptionFeeResult {
        val account = accountRepository.findByIdOrNull(accountId) ?: return IgnoreSubscriptionFeeResult.AccountNotFound

        val subscription =
            subscriptionRepository.find(accountId) ?: return IgnoreSubscriptionFeeResult.SubscriptionNotFound

        return when (
            recurrenceService.ignoreSubscriptionFee(
                subscription.recurrenceId,
                subscription.walletId,
                dueDate,
            )
        ) {
            BillRecurrenceService.IgnoreSubscriptionRecurrenceResult.BillAlreadyPaid -> IgnoreSubscriptionFeeResult.SubscriptionFeeAlreadyPaid
            BillRecurrenceService.IgnoreSubscriptionRecurrenceResult.BillNotFound -> IgnoreSubscriptionFeeResult.SubscriptionFeeNotFound
            BillRecurrenceService.IgnoreSubscriptionRecurrenceResult.BillProcessing -> IgnoreSubscriptionFeeResult.SubscriptionFeeProcessing
            BillRecurrenceService.IgnoreSubscriptionRecurrenceResult.Success -> {
                synchronizePixSubscriptionPaymentStatus(account.accountId)
                IgnoreSubscriptionFeeResult.Success
            }
        }
    }

    fun synchronizePixSubscriptionPaymentStatus(
        accountId: AccountId,
    ): SubscriptionPaymentStatus {
        val markers = Markers.append("accountId", accountId)

        val subscription = subscriptionRepository.find(accountId)!!
        markers.andAppend("recurrenceId", subscription.recurrenceId)

        if (subscription.status == SubscriptionStatus.INACTIVE) {
            return subscription.paymentStatus
        }

        val recurrence = recurrenceService.find(subscription.recurrenceId, subscription.walletId)

        val firstActiveBill = recurrence.findFirstActiveBill()
        markers.andAppend("firstActiveBillId", firstActiveBill?.billId?.value.orEmpty())

        val subscriptionPaymentStatus =
            if (firstActiveBill == null || !firstActiveBill.effectiveDueDate.isBefore(getLocalDate())) {
                SubscriptionPaymentStatus.PAID
            } else {
                SubscriptionPaymentStatus.OVERDUE
            }
        markers.andAppend("subscriptionPaymentStatus", subscriptionPaymentStatus.name)

        val nextEffectiveDueDate = firstActiveBill?.effectiveDueDate ?: subscription.nextEffectiveDueDate.plusMonths(1)

        if (subscription.hasChanged(subscriptionPaymentStatus, nextEffectiveDueDate)) {
            subscriptionRepository.save(
                subscription.copy(
                    paymentStatus = subscriptionPaymentStatus,
                    nextEffectiveDueDate = nextEffectiveDueDate,
                ),
            )
        }

        val account = accountRepository.findById(subscription.accountId)

        if (account.subscriptionType != SubscriptionType.PIX) {
            return subscriptionPaymentStatus
        }

        val daysOverdue = firstActiveBill?.let { Duration.between(it.effectiveDueDate.atStartOfDay(), getLocalDate().atStartOfDay()).toDays() } ?: 0
        markers.andAppend("daysOverdue", daysOverdue)

        val updatedAccount = account.handleInactivationFlow(daysOverdue, nextEffectiveDueDate, subscription.amount, markers)

        if (account != updatedAccount) {
            accountRepository.save(updatedAccount)
        }

        LOG.info(markers, "synchronizeSubscriptionPaymentStatus")
        return subscriptionPaymentStatus
    }

    fun synchronizeAllPixSubscriptionsPaymentStatus(): Int {
        val activeSubscriptions = subscriptionRepository.find(status = SubscriptionStatus.ACTIVE)
        activeSubscriptions.forEach {
            try {
                synchronizePixSubscriptionPaymentStatus(it.accountId)
            } catch (e: Exception) {
                LOG.error(Markers.append("accountId", it.accountId.value), "synchronizeSubscriptionPaymentStatus")
            }
        }
        return activeSubscriptions.size
    }

    private fun synchronizeInAppSubscriptionStatusActive(
        accountId: AccountId,
    ): InAppSubscriptionStatus {
        val inAppSubscription = inAppSubscriptionService.getSubscription(accountId)!!

        if (inAppSubscription.status == InAppSubscriptionStatus.ACTIVE && inAppSubscription.endsAt.toLocalDate() < getLocalDate()) {
            val markers = append("accountId", accountId).andAppend("status", inAppSubscription.status).andAppend("reason", inAppSubscription.reason)

            when (inAppSubscription.reason) {
                InAppSubscriptionReason.BACKOFFICE, InAppSubscriptionReason.NO_STORE_COUPON -> {
                    inAppSubscriptionService.expireSubscription(inAppSubscription)
                    LOG.info(markers, "synchronizeInAppSubscriptionStatus")
                }

                InAppSubscriptionReason.SUBSCRIPTION, InAppSubscriptionReason.TRIAL -> {
                    LOG.error(markers, "synchronizeInAppSubscriptionStatus")
                }
            }
        }

        return inAppSubscription.status
    }

    fun synchronizeAllInAppSubscriptionStatusActive(): Int {
        val activeInAppSubscriptions = inAppSubscriptionService.getByStatus(InAppSubscriptionStatus.ACTIVE)

        activeInAppSubscriptions.forEach() {
            try {
                synchronizeInAppSubscriptionStatusActive(it.accountId)
            } catch (e: Exception) {
                LOG.error(Markers.append("accountId", it.accountId.value), "synchronizeAllInAppSubscriptionStatus")
            }
        }
        return activeInAppSubscriptions.size
    }

    fun synchronizeAllInAppSubscriptionStatusExpired(): Int {
        val expiredInAppSubscriptions = inAppSubscriptionService.getSubscriptionsEndingBefore(getLocalDate())

        expiredInAppSubscriptions.forEach {
            try {
                synchronizeInAppSubscriptionStatusExpired(it.accountId)
            } catch (e: Exception) {
                LOG.error(Markers.append("accountId", it.accountId.value), "synchronizeAllInAppSubscriptionStatusExpired", e)
            }
        }

        return expiredInAppSubscriptions.size
    }

    private fun synchronizeInAppSubscriptionStatusExpired(
        accountId: AccountId,
    ): InAppSubscriptionStatus {
        val markers = Markers.append("accountId", accountId)

        val subscription = inAppSubscriptionService.getSubscription(accountId)!!

        if (subscription.endsAt > getZonedDateTime()) {
            return subscription.status
        }

        val status = inAppSubscriptionService.expireSubscription(subscription).fold(
            ifLeft = {
                LOG.error(markers.andAppend("accountId", accountId).andAppend("result", it), "synchronizeInAppSubscriptionStatusExpired")
                subscription.status
            },
            ifRight = {
                markers.andAppend("status", it.status)
                LOG.info(markers, "synchronizeInAppSubscriptionStatusExpired")
                it.status
            },
        )
        return status
    }

    fun unscheduleCurrentSubscription() {
        val logName = "SubscriptionService#unscheduleCurrentSubscription"
        val activeSubscriptions = subscriptionRepository.find(status = SubscriptionStatus.ACTIVE)
        activeSubscriptions.forEach {
            val markers = Markers.append("accountId", it.accountId.value).and("recurrenceId" to it.recurrenceId, "walletId" to it.walletId, "paymentStatus" to it.paymentStatus)
            try {
                if (it.nextEffectiveDueDate.month.value < getLocalDate().month.value && it.paymentStatus == SubscriptionPaymentStatus.OVERDUE) {
                    val result = recurrenceService.ignoreSubscriptionFee(recurrenceId = it.recurrenceId, walletId = it.walletId, getLocalDate().withDayOfMonth(10))
                    LOG.info(markers.and("result" to result, "reason" to "subscription ignored"), logName)
                } else {
                    LOG.info(markers.andAppend("reason", "subscription up to date"), logName)
                }
            } catch (e: Exception) {
                LOG.error(markers, logName, e)
            }
        }
    }

    private fun Account.handleInactivationFlow(daysOverdue: Long, nextEffectiveDueDate: LocalDate, amount: Long, markers: LogstashMarker): Account {
        return if (daysOverdue > 0) {
            val inactivationFlowDates = InactivationFlowDates.from(firstMonthOverdue = nextEffectiveDueDate)
            markers.andAppend("inactivationFlowDates", inactivationFlowDates)

            updateAccountStatus(
                daysOverdue = daysOverdue,
                inactivationFlowDates = inactivationFlowDates,
                markers = markers,
            ).handleOverdue(
                days = daysOverdue,
                effectiveDueDate = nextEffectiveDueDate,
                amount = amount,
                inactivationFlowDates = inactivationFlowDates,
                markers = markers,
            )
        } else {
            updateAccountStatus(
                daysOverdue = daysOverdue,
                inactivationFlowDates = null,
                markers = markers,
            )
        }
    }

    private fun Account.updateAccountStatus(daysOverdue: Long, inactivationFlowDates: InactivationFlowDates?, markers: LogstashMarker): Account {
        val paymentStatus = when {
            inactivationFlowDates == null -> AccountPaymentStatus.UpToDate
            inactivationFlowDates.daysToDisableDetailedNotifications > daysOverdue -> AccountPaymentStatus.PastDue
            else -> AccountPaymentStatus.Overdue
        }
        markers.andAppend("paymentStatus", paymentStatus)

        val status = when {
            status in listOf(AccountStatus.CLOSED, AccountStatus.PENDING_CLOSE) -> this.status
            inactivationFlowDates == null -> AccountStatus.ACTIVE
            inactivationFlowDates.daysToBlockAccount > daysOverdue -> AccountStatus.ACTIVE
            else -> AccountStatus.BLOCKED
        }
        markers.andAppend("status", status)

        if (disableOverdueAccountDDA && status == AccountStatus.BLOCKED && this.status != AccountStatus.BLOCKED) {
            ddaService.remove(accountId, Document(document))
        }

        chatbotMessagePublisher.publishStateUpdate(this, paymentStatus, status)

        return copy(status = status, paymentStatus = paymentStatus)
    }

    private fun Account.handleOverdue(days: Long, effectiveDueDate: LocalDate, amount: Long, inactivationFlowDates: InactivationFlowDates, markers: LogstashMarker): Account {
        when (days) {
            in inactivationFlowDates.daysToNotifySubscriptionOverdue -> {
                markers.andAppend("notify", true)
                notificationAdapter.notifySubscriptionOverdue(accountId, effectiveDueDate, amount, days != 1L)
            }

            inactivationFlowDates.dayToNotifySubscriptionOverdueWarningDetailedNotificationsShutdown -> {
                markers.andAppend("notify", true)
                notificationAdapter.notifySubscriptionOverdueWarningDetailedNotificationsShutdown(accountId, effectiveDueDate)
            }

            inactivationFlowDates.daysToNotifyNotificationChannelDowngradeWarning -> {
                markers.andAppend("notify", true)
                notificationAdapter.notifySubscriptionOverdueNotificationChannelDowngradeWarning(accountId)
            }

            inactivationFlowDates.dayToNotifySubscriptionOverdueWarningBillNotificationsAndDDAShutdown -> {
                markers.andAppend("notify", true)
                notificationAdapter.notifySubscriptionOverdueWarningBillNotificationsAndDDAShutdown(accountId, inactivationFlowDates.month1Day2.minusDays(1))
            }

            in inactivationFlowDates.daysToNotifySubscriptionOverdueWarningAccountClosure -> {
                markers.andAppend("notify", true)
                notificationAdapter.notifySubscriptionOverdueWarningAccountClosure(
                    accountId = accountId,
                    daysUntilClosure = Duration.between(getLocalDate().atStartOfDay(), inactivationFlowDates.month1Day6.atStartOfDay()).toDays(),
                    closeDate = inactivationFlowDates.month1Day6.minusDays(1),
                )
            }

            else -> {
                markers.andAppend("notify", false)
            }
        }

        return when {
            days >= inactivationFlowDates.daysToCloseAccount -> close(days, markers)
            else -> this
        }
    }

    private fun Account.close(days: Long, markers: LogstashMarker): Account {
        markers.andAppend("action", "closeAccount")

        if (closeOverdueAccount && status !in listOf(AccountStatus.CLOSED, AccountStatus.PENDING_CLOSE)) {
            notificationAdapter.notifySubscriptionOverdueCloseAccount(accountId)
            closeAccountServiceProvider.get().closeAccount(
                accountId = accountId,
                closureDetails = AccountClosureDetails.WithReason(
                    reason = AccountClosureReason.SUBSCRIPTION_OVERDUE,
                    at = ZonedDateTime.now(),
                    description = "Overdue $days days",
                ),
                delaySeconds = 300,
            ).map {
                markers.andAppend("closeAccountResult", it)
            }.getOrElse {
                markers.andAppend("closeAccountError", it)
            }
        }
        return this
    }

    private fun Subscription.hasChanged(
        paymentStatus: SubscriptionPaymentStatus,
        nextEffectiveDueDate: LocalDate,
    ): Boolean {
        return paymentStatus != this.paymentStatus || nextEffectiveDueDate != this.nextEffectiveDueDate
    }

    fun findAllOverdue(): List<Subscription> {
        return subscriptionRepository.find(
            subscriptionStatus = SubscriptionStatus.ACTIVE,
            paymentStatus = SubscriptionPaymentStatus.OVERDUE,
        )
    }

    internal fun calcNextSubscriptionDueDate(now: LocalDate, dayOfMonth: Int): LocalDate {
        return if (now.dayOfMonth > dayOfMonth) {
            now.plusMonths(1).withDayOfMonth(dayOfMonth)
        } else {
            now.withDayOfMonth(dayOfMonth)
        }
    }

    fun Subscription.calcNextSubscriptionDueDate(now: LocalDate): LocalDate {
        return calcNextSubscriptionDueDate(now, dayOfMonth)
    }

    private fun BillRecurrence.findFirstActiveBill(): BillView? {
        bills.forEach {
            try {
                val bill = billRepository.findBill(it, walletId)
                if (bill.status == BillStatus.ACTIVE) {
                    return bill
                }
            } catch (e: IllegalStateException) {
                // do nothing, ignored subscription fees are removed from bill view
            }
        }

        return null
    }

    private fun BillRecurrence.findLastBill(): BillView {
        return billRepository.findBill(bills.last(), walletId)
    }

    private fun SubscriptionConfiguration.toRecurrence(account: Account, amount: Long, dayOfMonth: Int) = BillRecurrence(
        walletId = account.defaultWalletId(),
        description = description.format(account.name),
        amount = amount,
        rule = RecurrenceRule(
            frequency = RecurrenceFrequency.MONTHLY,
            startDate = calcNextSubscriptionDueDate(getLocalDate(), dayOfMonth = dayOfMonth),
        ),
        contactId = null,
        contactAccountId = account.accountId,
        recipientName = recipientName,
        recipientDocument = recipientDocument,
        recipientBankAccount = BankAccount(
            accountType = bankAccount.accountType,
            bankNo = bankAccount.bankNo,
            routingNo = bankAccount.routingNo,
            accountNo = bankAccount.accountNo.toBigInteger(),
            accountDv = bankAccount.accountDv,
            document = recipientDocument,
            ispb = bankAccount.ispb,
        ),
        recipientPixKey = null,
        actionSource = ActionSource.Subscription(accountId = account.accountId),
        created = getZonedDateTime(),
        status = RecurrenceStatus.ACTIVE,
        billType = BillType.PIX,
    )

    private fun RecurrenceCreationError.handle(markers: LogstashMarker): SubscribeResult.Error {
        markers.andAppend("recurrenceCreationError", this)
        if (this is RecurrenceCreationError.ServerError) {
            LOG.error(markers, "SubscriptionServiceSubscribe", exception)
        } else {
            LOG.error(markers, "SubscriptionServiceSubscribe")
        }
        return SubscribeResult.Error(message)
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(SubscriptionService::class.java)
    }
}

sealed class SubscribeResult : PrintableSealedClassV2() {
    data object AccountNotFound : SubscribeResult()
    data object AccountNotActive : SubscribeResult()
    data object AccountIsFreeOfFridaySubscription : SubscribeResult()
    data object Success : SubscribeResult()
    class Conflict(val subscription: Subscription) : SubscribeResult()
    class Error(val message: String) : SubscribeResult()
}

sealed class UnsubscribeError : PrintableSealedClassV2() {
    data object AccountNotFound : UnsubscribeError()
    data object BillProcessing : UnsubscribeError()
    class ServerError(val message: String) : UnsubscribeError()
}

sealed class IgnoreSubscriptionFeeResult : PrintableSealedClassV2() {
    data object AccountNotFound : IgnoreSubscriptionFeeResult()
    data object SubscriptionNotFound : IgnoreSubscriptionFeeResult()
    data object SubscriptionFeeNotFound : IgnoreSubscriptionFeeResult()
    data object SubscriptionFeeProcessing : IgnoreSubscriptionFeeResult()
    data object SubscriptionFeeAlreadyPaid : IgnoreSubscriptionFeeResult()
    class IgnoreBillError(val exception: Exception) : IgnoreSubscriptionFeeResult()
    data object Success : IgnoreSubscriptionFeeResult()
}

sealed class SubscriptionPaymentError : PrintableSealedClassV2() {
    data object SubscriptionNotFound : SubscriptionPaymentError()
    data object AccountNotFound : SubscriptionPaymentError()
}

enum class SubscriptionType {
    IN_APP, PIX;

    companion object {
        fun random(percentage: Float): SubscriptionType {
            return if (Random.nextFloat() < percentage) IN_APP else PIX
        }
    }
}

data class Subscription(
    val accountId: AccountId,
    val document: Document,
    val status: SubscriptionStatus,
    val amount: Long,
    val dayOfMonth: Int,
    val recurrenceId: RecurrenceId,
    val paymentStatus: SubscriptionPaymentStatus,
    val walletId: WalletId,
    val nextEffectiveDueDate: LocalDate,
)

enum class SubscriptionStatus {
    ACTIVE, INACTIVE
}

enum class SubscriptionPaymentStatus {
    PAID, OVERDUE
}

@ConfigurationProperties("subscription")
class SubscriptionConfiguration @ConfigurationInject constructor(
    val amount: Long,
    val dayOfMonth: Int,
    val description: String,
    val recipientName: String,
    val recipientDocument: String,
    val bankAccount: BankAccount,
) {
    @ConfigurationProperties("bankAccount")
    data class BankAccount
    @ConfigurationInject
    constructor(
        val accountType: AccountType,
        val bankNo: Long,
        val routingNo: Long,
        val accountNo: Long,
        val accountDv: String,
        val ispb: String,
    )
}

data class InactivationFlowDates(
    val month0Day0: LocalDate,
    val month0Day1: LocalDate,
    val month0Day2: LocalDate,
    val month0Day4: LocalDate,
    val month0Day6: LocalDate,
    val month0Day7: LocalDate,
    val month1Day0: LocalDate,
    val month1Day1: LocalDate,
    val month1Day2: LocalDate,
    val month1Day6: LocalDate,
) {
    val daysToDisableDetailedNotifications = month0Day7.dueDays()
    val daysToBlockAccount = month1Day2.dueDays()
    val daysToCloseAccount = month1Day6.dueDays()

    val daysToNotifySubscriptionOverdue = listOf(
        month0Day1.dueDays(),
        month0Day2.dueDays(),
    )

    val dayToNotifySubscriptionOverdueWarningDetailedNotificationsShutdown = month0Day4.dueDays()
    val daysToNotifyNotificationChannelDowngradeWarning = month0Day6.dueDays()
    val dayToNotifySubscriptionOverdueWarningBillNotificationsAndDDAShutdown = month1Day1.dueDays()

    val daysToNotifySubscriptionOverdueWarningAccountClosure = listOf(
        month1Day2.dueDays(),
    )

    private fun LocalDate.dueDays() = Duration.between(month0Day0.atStartOfDay(), this.atStartOfDay()).toDays()

    companion object {
        /**
         * É mantida uma documentação mais completa em https://3.basecamp.com/5754278/buckets/********/documents/**********
         *
         * Dia / Mês - Subscription - Account         - Notification
         * 10 M0 D00 - PAID         - ACTIVE/UpToDate - sua assinatura vence hoje
         * 11 M0 D01 - OVERDUE      - ACTIVE/PastDue  - notification_01 - não conseguimos debitar sua assinatura já existente
         * 12 M0 D02 - OVERDUE      - ACTIVE/PastDue  - notification_02 - não conseguimos debitar sua assinatura já existente v2
         * 14 M0 D04 - OVERDUE      - ACTIVE/PastDue  - notification_03 - não conseguimos efetuar o pagamento da sua assinatura, pague para continuar recebendo notificações
         * 17 M0 D07 - OVERDUE      - ACTIVE/Overdue  - parar notificações detalhadas (usuário passa a receber avisos genéricos)
         *
         * 10 M1 D00 - OVERDUE      - ACTIVE/Overdue  - cobrança de assinatura
         * 11 M1 D01 - OVERDUE      - ACTIVE/Overdue  - notification_04 - não identificamos o pagamento da assinatura, pague até D35 para não perder o buscador de boletos
         * 12 M1 D02 - OVERDUE      - BLOCKED/Overdue - notification_05 e banner in app - não identificamos o pagamento da assinatura, pague para continuar usando
         * 15 M1 D06 -              -                 - notifica e encerra a conta
         */
        internal fun from(firstMonthOverdue: LocalDate): InactivationFlowDates {
            val month0Day0 = firstMonthOverdue.withDayOfMonth(10).nextWorkingDay()
            val month0Day1 = month0Day0.plusDays(1).nextWorkingDay()
            val month0Day2 = month0Day0.plusDays(2).greaterThan(month0Day1).nextWorkingDay()
            val month0Day4 = month0Day0.plusDays(4).greaterThan(month0Day2).nextWorkingDay()
            val month0Day6 = month0Day0.plusDays(6).greaterThan(month0Day4).nextWorkingDay()
            val month0Day7 = month0Day0.plusDays(7).greaterThan(month0Day6).nextWorkingDay()

            val month1Day0 = firstMonthOverdue.plusMonths(1).withDayOfMonth(10).nextWorkingDay()
            val month1Day1 = month1Day0.plusDays(1).nextWorkingDay()
            val month1Day2 = month1Day0.plusDays(2).greaterThan(month1Day1).nextWorkingDay()
            val month1Day6 = month1Day0.plusDays(6).greaterThan(month1Day2).nextWorkingDay()

            return InactivationFlowDates(
                month0Day0 = month0Day0,
                month0Day1 = month0Day1,
                month0Day2 = month0Day2,
                month0Day4 = month0Day4,
                month0Day6 = month0Day6,
                month0Day7 = month0Day7,
                month1Day0 = month1Day0,
                month1Day1 = month1Day1,
                month1Day2 = month1Day2,
                month1Day6 = month1Day6,
            )
        }

        private fun LocalDate.greaterThan(otherDate: LocalDate): LocalDate {
            return if (this > otherDate) {
                this
            } else {
                otherDate.plusDays(1)
            }
        }

        private fun LocalDate.nextWorkingDay(): LocalDate {
            var currentLocalDate = this

            while (!FinancialInstitutionGlobalData.isBusinessDay(currentLocalDate)) {
                currentLocalDate = currentLocalDate.plusDays(1)
            }

            return currentLocalDate
        }
    }
}