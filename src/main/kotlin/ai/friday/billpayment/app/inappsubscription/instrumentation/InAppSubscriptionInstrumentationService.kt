package ai.friday.billpayment.app.inappsubscription.instrumentation

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.instrumentation.BaseInstrumentationService
import ai.friday.billpayment.app.instrumentation.InstrumentationRepository
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
open class InAppSubscriptionInstrumentationService(
    instrumentationRepository: InstrumentationRepository<InAppSubscriptionInstrumentationEvent>,
) : BaseInstrumentationService<InAppSubscriptionInstrumentationEvent>(instrumentationRepository) {
    open fun trialStarted(accountId: AccountId) {
        try {
            publishEventAsync(InAppSubscriptionInstrumentationEvent.TrialStarted(accountId))
        } catch (exception: Exception) {
            LOG.error(
                Markers.append("accountId", accountId),
                "trialStarted",
                exception,
            )
        }
    }

    open fun trialConverted(accountId: AccountId) {
        try {
            publishEventAsync(InAppSubscriptionInstrumentationEvent.TrialConverted(accountId))
        } catch (exception: Exception) {
            LOG.error(
                Markers.append("accountId", accountId),
                "trialConverted",
                exception,
            )
        }
    }

    open fun trialExpired(accountId: AccountId) {
        try {
            publishEventAsync(InAppSubscriptionInstrumentationEvent.TrialExpired(accountId))
        } catch (exception: Exception) {
            LOG.error(
                Markers.append("accountId", accountId),
                "trialExpired",
                exception,
            )
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(InAppSubscriptionInstrumentationService::class.java)
    }
}