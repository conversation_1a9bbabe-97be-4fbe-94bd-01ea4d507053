package ai.friday.billpayment.app.limit

import ai.friday.billpayment.Lock
import ai.friday.billpayment.adapters.lock.limitLockProvider
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.LimitRepository
import ai.friday.billpayment.withLock
import jakarta.inject.Named
import jakarta.inject.Singleton
import java.time.Duration

data class Limit(
    val count: Int,
    val maxCount: Int,
)

sealed class LimitIncrementResult {
    data object Ok : LimitIncrementResult()
    data object NotFound : LimitIncrementResult()
    data object Exceeded : LimitIncrementResult()
    data object Locked : LimitIncrementResult()
}

@Singleton
open class LimitService(
    private val limitDbRepository: LimitRepository,
    @Named(limitLockProvider) private val lockProvider: InternalLock,
) {
    fun createLimit(accountId: AccountId, key: String, maxCount: Int = 3, expiration: Duration = Duration.ofDays(1)) {
        limitDbRepository.create(accountId, key, maxCount, expiration)
    }

    open fun increment(accountId: AccountId, key: String): LimitIncrementResult {
        val lock = Lock.EphemeralLock("$limitLockProvider${accountId.value}#$key")

        return lockProvider.withLock(lock) {
            val limit = limitDbRepository.get(accountId, key) ?: return@withLock LimitIncrementResult.NotFound

            if (limit.count >= limit.maxCount) {
                LimitIncrementResult.Exceeded
            } else {
                limitDbRepository.increment(accountId, key)
                LimitIncrementResult.Ok
            }
        }.getOrElse { LimitIncrementResult.Locked }
    }
}