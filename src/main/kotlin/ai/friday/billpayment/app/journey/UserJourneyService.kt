package ai.friday.billpayment.app.journey

import ai.friday.billpayment.app.AsyncUtils.callAsync
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.UserJourneyRegisterException
import ai.friday.billpayment.app.account.UserJourneyTrackEventException
import ai.friday.billpayment.app.integrations.FeatureConfiguration
import ai.friday.billpayment.app.integrations.UserJourneyAdapter
import ai.friday.morning.log.andAppend
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
open class UserJourneyService(
    private val userJourneyAdapter: UserJourneyAdapter,
    private val featureConfiguration: FeatureConfiguration,
) {

    open fun registerAsync(account: Account) {
        if (!featureConfiguration.userPilotEnabled) {
            return
        }

        callAsync {
            val markers = Markers.append("account", account)
            try {
                userJourneyAdapter.register(account.accountId, account.name, account.emailAddress, account.type, account.created)
            } catch (ex: UserJourneyRegisterException) {
                throw ex
            } catch (ex: Exception) {
                logger.error(markers, "userJourneyRegister", ex)
                throw UserJourneyRegisterException(ex)
            }
        }
    }

    open fun trackEventAsync(accountId: AccountId, event: UserJourneyEvent) {
        if (!featureConfiguration.userPilotEnabled) {
            return
        }

        callAsync {
            val markers = Markers.append("accountId", accountId)
                .andAppend("eventName", event.eventName)

            try {
                userJourneyAdapter.trackEvent(accountId, event.eventName)
            } catch (ex: UserJourneyTrackEventException) {
                throw ex
            } catch (ex: Exception) {
                logger.error(markers, "userJourneyTrackEvent", ex)
                throw UserJourneyTrackEventException(ex)
            }
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(UserJourneyService::class.java)
    }
}

enum class UserJourneyEvent(val eventName: String) {
    SendBillByEmail("send_bill_by_email"),
    CashInCompleted("cash_in_completed"),
    UpgradeRequested("upgrade_requested"),
    OnboardingTestPixCreated("onboarding_test_pix_created"),
    UtilityAccountConnected("utility_account_connected"),
    BillPaid("bill_paid"),
    InvoiceBillPaid("invoice_bill_paid"),
    PixBillPaid("pix_bill_paid"),
    AutomaticPixBillPaid("automatic_pix_bill_paid"),
    InvestmentRequested("investment_requested"),
    CollectBillPaid("collect_bill_paid"),
    RegularBillPaid("regular_bill_paid"),
    GoalCreated("goal_created"),
}