package ai.friday.billpayment.app.register.kyc

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountRegisterData
import ai.friday.billpayment.app.account.StoredObject
import ai.friday.billpayment.app.account.UserFilesConfiguration
import ai.friday.billpayment.app.account.getCPF
import ai.friday.billpayment.app.documentscan.DocumentScanService
import ai.friday.billpayment.app.documentscan.GetDocumentScanResultError
import ai.friday.billpayment.app.documentscan.GetImageError
import ai.friday.billpayment.app.integrations.BigDataService
import ai.friday.billpayment.app.integrations.FaceMatcher
import ai.friday.billpayment.app.integrations.ObjectRepository
import ai.friday.billpayment.app.integrations.PDFConverter
import ai.friday.billpayment.app.integrations.TemplateCompiler
import ai.friday.billpayment.app.integrations.TemplateForm
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.micronaut.http.MediaType
import jakarta.inject.Singleton
import java.io.ByteArrayInputStream
import net.logstash.logback.marker.Markers.append
import org.slf4j.Logger
import org.slf4j.LoggerFactory

class KycGenerationException(e: Exception) : Exception(e)

class FaceMatcherException(message: String, e: Exception?) : Exception(message, e) {
    constructor(message: String) : this(message, null)
}

@Singleton
open class KycService(
    private val handlebarTemplateCompiler: TemplateCompiler,
    private val bigDataService: BigDataService,
    private val objectRepository: ObjectRepository,
    private val userFilesConfiguration: UserFilesConfiguration,
    private val faceMatcher: FaceMatcher,
    private val pdfConverter: PDFConverter,
    private val documentScanService: DocumentScanService,
) {
    open fun generate(accountRegisterData: AccountRegisterData): Either<Exception, Pair<StoredObject, KycDossier>> {
        return try {
            val kycDossier = bigDataService.getKycDossier(accountRegisterData.getCPF()!!)
                .getOrElse { throw it }

            val form = kycDossier.toForm(
                accountRegisterData = accountRegisterData,
                faceMatch = getFaceMatchResult(accountRegisterData),
                documentScan = documentScanService.getResult(accountRegisterData.accountId)
                    .getOrElse {
                        when (it) {
                            is GetDocumentScanResultError.Incomplete -> null
                            is GetDocumentScanResultError.AccountNotFound,
                            is GetDocumentScanResultError.Unexpected,
                            -> throw it
                        }
                    },
            )

            Pair(publishDocument(form, accountRegisterData.accountId), kycDossier).right()
        } catch (e: Exception) {
            logger.error(append("accountId", accountRegisterData.accountId), "KycService#generate", e)
            KycGenerationException(e).left()
        }
    }

    private fun getFaceMatchResult(accountRegisterData: AccountRegisterData): FaceMatchResult? {
        val selfie = accountRegisterData.uploadedSelfie!!

        return documentScanService.getImage(accountRegisterData.accountId)
            .getOrElse {
                when (it) {
                    is GetImageError.Incomplete -> null
                    is GetImageError.AccountNotFound,
                    is GetImageError.Unexpected,
                    -> throw Exception("Unexpected error getting face image documents ($it)")
                }
            }
            .let { image ->
                if (image != null) {
                    ByteArrayInputStream(image.face.content)
                } else {
                    (accountRegisterData.uploadedCNH ?: accountRegisterData.uploadedDocument?.front)
                        ?.let { storedObject -> objectRepository.loadObject(storedObject.bucket, storedObject.key) }
                }
            }
            ?.use { documentImageStream ->
                objectRepository.loadObject(selfie.bucket, selfie.key)
                    .use { selfieImageStream ->
                        faceMatcher.match(
                            documentImage = documentImageStream,
                            selfieImage = selfieImageStream,
                        ).getOrElse { null }
                    }
            }
    }

    private fun publishDocument(form: TemplateForm, accountId: AccountId): StoredObject {
        val html = handlebarTemplateCompiler.buildHtml(form)

        val pdfContent = pdfConverter.convert(html.value, false)

        val storedObject = with(userFilesConfiguration) {
            StoredObject(
                region = region,
                bucket = bucket,
                key = "$path/${accountId.value}/${kycPrefix}${getZonedDateTime().toEpochSecond()}.pdf",
            )
        }

        objectRepository.putObject(storedObject, pdfContent, MediaType.APPLICATION_PDF_TYPE)

        return storedObject
    }

    companion object {
        val logger: Logger = LoggerFactory.getLogger(KycService::class.java)
    }
}