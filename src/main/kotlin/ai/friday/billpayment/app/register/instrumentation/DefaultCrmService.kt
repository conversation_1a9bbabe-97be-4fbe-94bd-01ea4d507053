package ai.friday.billpayment.app.register.instrumentation

import ai.friday.billpayment.app.AsyncUtils.callAsync
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountRegisterData
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.PartialAccount
import ai.friday.billpayment.app.account.PaymentMethodNotFound
import ai.friday.billpayment.app.account.PaymentMethodType
import ai.friday.billpayment.app.account.RegistrationType
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.account.isClosed
import ai.friday.billpayment.app.account.isLegalPerson
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionReason
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionRepository
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.CrmContact
import ai.friday.billpayment.app.integrations.CrmRepository
import ai.friday.billpayment.app.integrations.CrmService
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.SubscriptionRepository
import ai.friday.billpayment.app.integrations.TemporaryCrmContact
import ai.friday.billpayment.app.integrations.TemporaryCrmContactMinimal
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.isValidCnpj
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.app.wallet.MemberStatus
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.isLegalPerson
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.log.andAppend
import jakarta.inject.Singleton
import java.time.LocalTime
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
open class DefaultCrmService(
    private val crmRepository: CrmRepository,
    private val accountRepository: AccountRepository,
    private val accountRegisterRepository: AccountRegisterRepository,
    private val subscriptionRepository: SubscriptionRepository,
    private val walletRepository: WalletRepository,
    private val inAppSubscriptionRepository: InAppSubscriptionRepository,
) : CrmService {

    override fun createContact(contact: TemporaryCrmContact) {
        crmRepository.createContact(contact)
    }

    override fun createContact(contact: TemporaryCrmContactMinimal) {
        crmRepository.createContact(contact)
    }

    override fun findContact(accountId: AccountId): CrmContact? {
        return crmRepository.findContact(accountId)
    }

    override fun findContact(document: Document): CrmContact? {
        return crmRepository.findContact(document)
    }

    internal open fun upsertContactAsync(contact: CrmContact) {
        callAsync {
            try {
                crmRepository.upsertContact(contact)
            } catch (e: ContactAlreadyExistsException) {
                LOG.warn(
                    Markers.append("accountId", contact.accountId.value).andAppend("email", contact.emailAddress),
                    "upsertContact",
                    e,
                )
            } catch (e: Exception) {
                LOG.error(
                    Markers.append("accountId", contact.accountId.value).andAppend("email", contact.emailAddress),
                    "upsertContact",
                    e,
                )
            }
        }
    }

    override fun upsertContact(account: Account): CrmContact {
        val extraData = contactExtraData(account)

        return CrmContact(
            accountId = account.accountId,
            emailAddress = account.emailAddress,
            role = Role.OWNER,
            name = account.name,
            mobilePhone = MobilePhone(account.mobilePhone),
            document = account.document,
            removed = account.isClosed(),
            groups = account.configuration.groups,
            bankAccount = extraData.bankAccounts.map { it.buildFullAccountNumber() },
            accountType = account.type,
            isCNPJAccount = extraData.isCNPJAccount,
            cnpjWallets = extraData.cnpjWallets.map { it.id },
            cpfWallets = extraData.cpfWallets.map { it.id },
            otherMembersOnWallets = extraData.otherMembersOnWallets,
            subscriptionType = account.subscriptionType,
            subscriptionEndsAt = extraData.subscriptionEndsAt?.format(DateTimeFormatter.ISO_DATE_TIME),
            subscriptionAccessConcessionReason = extraData.accessConcessionReason,
            subscriptionAutoRenew = extraData.subscriptionAutoRenew,
        ).also { upsertContactAsync(it) }
    }

    override fun upsertContact(account: PartialAccount): CrmContact {
        return upsertGuest(
            register = accountRegisterRepository.findByAccountId(accountId = account.id, checkOpenAccount = false),
            partialAccount = account,
        )
    }

    override fun upsertContact(account: AccountRegisterData): CrmContact {
        return upsertGuest(
            register = account,
            partialAccount = accountRepository.findPartialAccountById(accountId = account.accountId),
        )
    }

    // TODO: em nenhum momento é verificado se o usuário é guest ainda
    private fun upsertGuest(register: AccountRegisterData, partialAccount: PartialAccount): CrmContact {
        return CrmContact(
            accountId = register.accountId,
            emailAddress = register.emailAddress,
            role = Role.GUEST,
            mobilePhone = register.mobilePhone,
            document = register.documentInfo?.cpf ?: register.document?.value,
            groups = partialAccount.groups,
            accountType = if (register.registrationType == RegistrationType.BASIC) UserAccountType.BASIC_ACCOUNT else UserAccountType.FULL_ACCOUNT,
            name = register.documentInfo?.name ?: register.nickname,
            isCNPJAccount = register.document?.isValidCnpj(),
            removed = partialAccount.status in listOf(AccountStatus.CLOSED, AccountStatus.PENDING_CLOSE, AccountStatus.DENIED),
            cnpjWallets = null,
            cpfWallets = null,
            otherMembersOnWallets = null,
            subscriptionType = partialAccount.subscriptionType,
        ).also { upsertContactAsync(it) }
    }

    override fun removeContactAsync(accountId: AccountId) {
        callAsync {
            try {
                crmRepository.findContact(accountId)?.let { contact ->
                    upsertContactAsync(contact.copy(removed = true))
                }
            } catch (e: Exception) {
                LOG.error(
                    Markers.append("accountId", accountId),
                    "removeContact",
                    e,
                )
            }
        }
    }

    override fun contactExists(accountId: AccountId): Boolean {
        try {
            return crmRepository.findContact(accountId) != null
        } catch (e: Exception) {
            LOG.error(Markers.append("accountId", accountId.value), "contactExists", e)
            throw e
        }
    }

    private fun contactExtraData(account: Account): ContactExtraData {
        val markers = Markers.append("accountId", account.accountId.value).andAppend("email", account.emailAddress)

        val isCNPJAccount: Boolean = account.isLegalPerson()
        var bankAccounts: List<InternalBankAccount> = emptyList()
        var cnpjWallets: List<Wallet> = emptyList()
        var cpfWallets: List<Wallet> = emptyList()
        var otherMembersOnWallets: List<AccountId> = emptyList()
        var subscriptionEndsAt: ZonedDateTime? = null
        var subscriptionAutoRenew: Boolean? = null
        var accessConcessionReason: InAppSubscriptionReason? = null

        try {
            bankAccounts = try {
                accountRepository.findPhysicalBankAccountByAccountId(accountId = account.accountId)
            } catch (e: PaymentMethodNotFound) {
                emptyList()
            }.filter { it.method.type == PaymentMethodType.BALANCE }.map { it.method as InternalBankAccount }

            val accountWallets = try {
                walletRepository.findWallets(account.accountId, MemberStatus.ACTIVE)
            } catch (e: ItemNotFoundException) {
                emptyList()
            }

            val (otherCNPJWallets, otherCPFWallets) = accountWallets.partition { wallet ->
                wallet.founder.isLegalPerson()
            }
            cnpjWallets = otherCNPJWallets
            cpfWallets = otherCPFWallets

            otherMembersOnWallets = accountWallets.filter { it.founder.accountId == account.accountId }.flatMap { wallet ->
                wallet.activeMembers.filter { member -> member.accountId != account.accountId }.map { member -> member.accountId }
            }

            when (account.subscriptionType) {
                SubscriptionType.IN_APP -> {
                    inAppSubscriptionRepository.findOrNull(account.accountId)?.let {
                        subscriptionEndsAt = it.endsAt
                        accessConcessionReason = it.reason
                        subscriptionAutoRenew = it.autoRenew
                    }
                }

                SubscriptionType.PIX -> {
                    subscriptionEndsAt = subscriptionRepository.find(account.accountId)?.let { ZonedDateTime.of(it.nextEffectiveDueDate.plusDays(1), LocalTime.MIDNIGHT, brazilTimeZone) }
                    accessConcessionReason = null
                    subscriptionAutoRenew = null
                }
            }
        } catch (e: Exception) {
            LOG.error(
                Markers.append("accountId", account.accountId.value).andAppend("email", account.emailAddress),
                "upsertContact",
                e,
            )
        }

        return ContactExtraData(
            bankAccounts = bankAccounts,
            isCNPJAccount = isCNPJAccount,
            cnpjWallets = cnpjWallets,
            cpfWallets = cpfWallets,
            otherMembersOnWallets = otherMembersOnWallets,
            subscriptionEndsAt = subscriptionEndsAt,
            accessConcessionReason = accessConcessionReason,
            subscriptionAutoRenew = subscriptionAutoRenew,
        )
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(DefaultCrmService::class.java)
    }
}

class ContactAlreadyExistsException : RuntimeException()
class CrmContactNotFoundException(val accountId: AccountId) : Exception()

private data class ContactExtraData(
    val bankAccounts: List<InternalBankAccount>,
    val isCNPJAccount: Boolean,
    val cnpjWallets: List<Wallet>,
    val cpfWallets: List<Wallet>,
    val otherMembersOnWallets: List<AccountId>,
    val accessConcessionReason: InAppSubscriptionReason? = null,
    val subscriptionEndsAt: ZonedDateTime? = null,
    val subscriptionAutoRenew: Boolean? = null,
)