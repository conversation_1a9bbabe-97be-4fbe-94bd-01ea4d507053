package ai.friday.billpayment.app.reports

import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.NewAccountReportConfiguration
import ai.friday.billpayment.app.backoffice.BackofficeRegisterService
import ai.friday.billpayment.app.backoffice.UserAccountRegister
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.micronaut.context.annotation.Property
import io.micronaut.http.MediaType
import io.via1.communicationcentre.app.integrations.EmailSenderService
import jakarta.inject.Singleton
import java.time.temporal.ChronoUnit

@Singleton
class NewAccountReportService(
    private val backofficeRegisterService: BackofficeRegisterService,
    private val emailSender: EmailSenderService,
    private val reportConfiguration: NewAccountReportConfiguration,
) {

    @field:Property(name = "email.notification.email")
    lateinit var from: String

    fun build(): List<UserAccountRegister> {
        val newAccounts = backofficeRegisterService.getPending()

        emailSender.sendRawEmail(
            from,
            reportConfiguration.subject,
            buildReportContent(newAccounts),
            reportConfiguration.recipients,
            listOf(),
            MediaType.TEXT_HTML,
        )

        return newAccounts
    }

    internal fun buildReportContent(report: List<UserAccountRegister>): String {
        val tableStyle = reportConfiguration.tableStyle
        val thStyle = reportConfiguration.thStyle
        val tdStyle = reportConfiguration.tdStyle
        val now = getZonedDateTime()
        return buildString {
            appendLine("<table style=\"$tableStyle\" class=\"tg\">")
            appendLine("<tr><td style=\"$thStyle\">ID</td><td style=\"$thStyle\">Nome</td><td style=\"$thStyle\">CPF</td><td style=\"$thStyle\">email</td><td style=\"$thStyle\">telefone</td><td style=\"$thStyle\">estado atual</td><td style=\"$thStyle\">atualizado há</td></tr>")
            for (entry in report) {
                appendLine(
                    "<tr><td style=\"$tdStyle\">${entry.accountId.value}</td><td style=\"$tdStyle\">${entry.name}</td><td style=\"$tdStyle\">${
                    formatDocument(
                        entry.document,
                    )
                    }</td><td style=\"$tdStyle\">${entry.email.value}</td><td style=\"$tdStyle\">${entry.phone}</td><td style=\"$tdStyle\">${entry.status.format()}</td><td style=\"$tdStyle\">${
                    entry.updated?.let {
                        "${ChronoUnit.HOURS.between(it, now)}h"
                    } ?: "n/d"
                    }</td></tr>",
                )
            }
            append("</table>")
        }
    }

    private fun formatDocument(document: String): String {
        return document.replace("([0-9]{3})([0-9]{3})([0-9]{3})([0-9]{2})".toRegex(), "$1\\.$2\\.$3-$4")
    }

    private fun AccountStatus.format() =
        when (this) {
            AccountStatus.ACTIVE -> "Ativo"
            AccountStatus.CLOSED -> "Fechado"
            AccountStatus.REGISTER_INCOMPLETE -> "Reaberto para revisão"
            AccountStatus.DENIED -> "Negado"
            AccountStatus.UNDER_REVIEW -> "Em revisão interna"
            AccountStatus.UNDER_EXTERNAL_REVIEW -> "Em revisão externa"
            AccountStatus.APPROVED -> "Aguardando Ativação"
            AccountStatus.BLOCKED -> "Inadimplente"
            AccountStatus.PENDING_CLOSE -> "Fechamento pendente"
        }
}