package ai.friday.billpayment.app.reports

import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.banking.BankStatementItem
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.InternalBankRepository
import io.micronaut.context.annotation.Property
import io.via1.communicationcentre.app.integrations.EmailSenderService
import jakarta.inject.Singleton

@Singleton
class OmnibusAccountReportService(
    private val internalBankRepository: InternalBankRepository,
    private val accountRepository: AccountRepository,
    private val emailSender: EmailSenderService,
) {

    @field:Property(name = "email.notification.email")
    lateinit var from: String

    @field:Property(name = "email.finance-report-recipients")
    lateinit var recipients: String

    @field:Property(name = "internalBankService.omnibusBankAccount.accountPaymentMethodId")
    lateinit var omnibusAccountPaymentMethodId: String

    private val subject = "Relatório da conta omnibus VS contas virtuais"

    fun report(): OmnibusAccountReport {
        val virtualBankAccounts = accountRepository.findAllVirtualBankAccount()

        val virtualAccountsBalanceSum = virtualBankAccounts.map {
            val bankStatement = internalBankRepository.findAllBankStatementItem(it.id)
            bankStatement.balance.amount
        }.fold(0L) { acc, curr ->
            acc + curr
        }

        val omnibusBankAccountStatement =
            internalBankRepository.findAllOmnibusBankStatementItem(AccountPaymentMethodId(omnibusAccountPaymentMethodId))

        return OmnibusAccountReport(
            virtualAccountsBalanceSum = virtualAccountsBalanceSum,
            omnibusAccountsBalance = omnibusBankAccountStatement.balance.amount,
            unresolvedDeposits = omnibusBankAccountStatement.unresolvedDeposits,
        ).also {
            emailSender.sendRawEmail(from, subject, buildReportEmailBody(it), recipients)
        }
    }

    private fun buildReportEmailBody(reportData: OmnibusAccountReport): String {
        return buildString {
            appendLine("Somatório dos saldos das contas virtuais: R$ ${reportData.virtualAccountsBalanceSum / 100.0}")
            appendLine("Saldo da conta omnibus: R$ ${reportData.omnibusAccountsBalance / 100.0}")
            appendLine("Lista de depósitos não mapeados:")
            val separator = '\t'
            if (reportData.unresolvedDeposits.isNotEmpty()) {
                appendLine("Número da operação${separator}documento da contraparte${separator}valor${separator}data${separator}nome da contraparte")
                reportData.unresolvedDeposits.forEach {
                    appendLine("${it.operationNumber}${separator}${it.counterpartDocument}${separator}R$ ${it.amount / 100.0}${separator}${it.date}${separator}${it.counterpartName}")
                }
            }
        }
    }
}

data class OmnibusAccountReport(
    val virtualAccountsBalanceSum: Long,
    val omnibusAccountsBalance: Long,
    val unresolvedDeposits: List<BankStatementItem>,
)