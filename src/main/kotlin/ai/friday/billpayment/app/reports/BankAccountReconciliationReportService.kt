package ai.friday.billpayment.app.reports

import ai.friday.billpayment.adapters.arbi.ArbiAccountMissingPermissionsException
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.BankStatementItem
import ai.friday.billpayment.app.banking.BankStatementItemFlow
import ai.friday.billpayment.app.banking.BankStatementItemType
import ai.friday.billpayment.app.banking.DefaultBankStatementItem
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.bill.RefundedBill
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.AccountStatementAdapter
import ai.friday.billpayment.app.integrations.BankAccountService
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.OnboardingTestPixRepository
import ai.friday.billpayment.app.integrations.TransactionRepository
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.message.QueueMessage
import ai.friday.billpayment.app.message.QueueMessageBatch
import ai.friday.billpayment.app.onboarding.OnboardingTestPixConfiguration
import ai.friday.billpayment.app.payment.MultiplePaymentData
import ai.friday.billpayment.app.payment.MultiplePaymentMethodsDetail
import ai.friday.billpayment.app.payment.NoPaymentData
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithBalance
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithCreditCard
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithExternalPayment
import ai.friday.billpayment.app.payment.SinglePaymentData
import ai.friday.billpayment.app.payment.SinglePaymentMethodsDetail
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.app.wallet.MemberStatus
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.toAmountFormat
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.dateTimeFormat
import ai.friday.morning.log.andAppend
import io.micronaut.context.annotation.Property
import io.via1.communicationcentre.app.integrations.EmailSenderService
import jakarta.inject.Singleton
import java.time.LocalDate
import java.time.ZonedDateTime
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

const val arbiBankNo = 213L

@Singleton
class BankAccountReconciliationReportService(
    private val accountRepository: AccountRepository,
    private val arbiAdapter: BankAccountService,
    private val accountStatementAdapter: AccountStatementAdapter,
    private val billRepository: BillRepository,
    private val walletRepository: WalletRepository,
    private val transactionRepository: TransactionRepository,
    private val emailSender: EmailSenderService,
    private val onboardingTestPixRepository: OnboardingTestPixRepository,
    private val onboardingTestPixConfiguration: OnboardingTestPixConfiguration,
    private val messagePublisher: MessagePublisher,
    @Property(name = "friday.morning.messaging.consumer.account-reconciliation-report.queueName")private val accountReconciliationReportQueueName: String,
) {
    @field:Property(name = "integrations.arbi.inscricao")
    lateinit var arbiSettlementAccountDocument: String

    @field:Property(name = "integrations.arbi.contaLiquidacao")
    lateinit var arbiSettlementAccountNo: String

    @field:Property(name = "integrations.arbi.contaCashin")
    lateinit var arbiCashInAccountNo: String

    @field:Property(name = "email.notification.email")
    lateinit var from: String

    @field:Property(name = "internalBankService.checkStatus.endDatePlusDays")
    var endDatePlusDays: Long = 0

    private val subject = "Relatório de Conciliação de Contas Arbi e Conta Liquidação"

    private fun listAllEligibleAccounts(): List<AccountPaymentMethod> =
        accountRepository
            .findAllPhysicalBalanceAccount()
            .filter { it.isEligible() }

    private fun AccountPaymentMethod.isEligible(): Boolean =
        this.status == AccountPaymentMethodStatus.ACTIVE &&
            (this.method as? InternalBankAccount)?.buildFullAccountNumber()?.toLong() != arbiSettlementAccountNo.toLong()

    fun checkIsEligible(paymentMethod: AccountPaymentMethod): Boolean = paymentMethod.isEligible()

    fun generateArbiAccountsReportAsync(
        date: LocalDate,
        cutOffTime: Int,
    ): Int {
        val messages =
            listAllEligibleAccounts().map {
                AccountReconciliationReportQueueMessageTO(
                    date = date.format(dateFormat),
                    cutOffTime = cutOffTime,
                    accountId = it.accountId.value,
                    accountPaymentMethodId = it.id.value,
                    retry = 0,
                )
            }

        messagePublisher.sendMessageBatch(
            messageBatch =
            QueueMessageBatch(
                queueName = accountReconciliationReportQueueName,
                messages = messages.map { getObjectMapper().writeValueAsString(it) },
                delaySeconds = null,
            ),
        )

        return messages.size
    }

    private fun canCheckAgain(previouslyCheckedTimes: Int): Boolean = previouslyCheckedTimes < 4

    fun retryGenerateArbiAccountsReportAsync(
        date: LocalDate,
        cutOffTime: Int,
        accountPaymentMethod: AccountPaymentMethod,
        previouslyCheckedTimes: Int,
    ): Boolean {
        if (!canCheckAgain(previouslyCheckedTimes)) {
            return false
        }

        val message =
            AccountReconciliationReportQueueMessageTO(
                date = date.format(dateFormat),
                cutOffTime = cutOffTime,
                accountId = accountPaymentMethod.accountId.value,
                accountPaymentMethodId = accountPaymentMethod.id.value,
                retry = previouslyCheckedTimes + 1,
            )

        messagePublisher.sendMessage(
            QueueMessage(
                queueName = accountReconciliationReportQueueName,
                jsonObject = getObjectMapper().writeValueAsString(message),
                delaySeconds = 10 * 60, // 10 minutos
            ),
        )
        return true
    }

    fun generateArbiAccountsReport(
        date: LocalDate,
        cutOffTime: Int,
        accountPaymentMethod: AccountPaymentMethod,
        previouslyCheckedTimes: Int,
    ) = accountPaymentMethod.checkDivergences(date, cutOffTime, previouslyCheckedTimes)

    private fun getCurrentWindowBillsRefundedTransactions(
        walletId: WalletId,
        initialDate: ZonedDateTime,
        cutOffTime: Int,
    ) = billRepository.getRefundedBills(
        walletId = walletId,
        startDate = initialDate,
        endDate = initialDate.plusDays(1),
    ).filter {
        it.refundDate.hour < cutOffTime
    }.toTransactions()

    private fun List<RefundedBill>.toTransactions() = map { it.toTransaction() }

    private fun RefundedBill.toTransaction() = transactionRepository.findTransactions(billId, TransactionStatus.UNDONE).also {
        if (it.size > 1) {
            // FIXME poderia usar o transactionId da refundedBill para identificar a transação ?
            logger.warn(
                Markers
                    .append("ACTION", "VERIFY")
                    .andAppend("billId", billId.value)
                    .andAppend("context", "Bill com mais de uma transação UNDONE"),
                "BankAccountReconciliationReportService#calculateWalletTotalBillsRefunded",
            )
        }
    }.sortedBy { it.created }.last()

    private fun AccountPaymentMethod.checkDivergences(
        date: LocalDate,
        cutOffTime: Int,
        previouslyCheckedTimes: Int,
    ): ArbiAccountReportSummary {
        fun InternalBankAccount.buildArbiAccountReportSummary(
            debitsConciliation: ArbiAccountConciliation = ArbiAccountConciliation.ZERO,
            refundsConciliation: ArbiAccountConciliation = ArbiAccountConciliation.ZERO,
        ) = ArbiAccountReportSummary(
            accountId = accountId,
            accountNumber = accountNumber,
            date = date,
            cutOffTime = cutOffTime,
            debitsConciliation = debitsConciliation,
            refundsConciliation = refundsConciliation,
        )

        val wholeDayWindow = (cutOffTime == 24)

        val logName = if (wholeDayWindow) {
            "BankAccountReconciliationReportService#finalReport"
        } else {
            "BankAccountReconciliationReportService#temporaryReport"
        }
        val markers = Markers.append("params.accountId", accountId.value)
            .andAppend("params.cutoffTime", cutOffTime)
            .andAppend("params.date", date.format(dateFormat))
            .andAppend("params.previouslyCheckedTimes", previouslyCheckedTimes)

        val lastRetry = !canCheckAgain(previouslyCheckedTimes)
        markers.andAppend("lastRetry", lastRetry)
        val initialDate = date.atStartOfDay(brazilTimeZone)

        val bankAccount = method as InternalBankAccount
        markers.andAppend("params.fullAccountNumber", bankAccount.buildFullAccountNumber())
            .andAppend("params.document", bankAccount.document)

        if (bankAccount.bankNo != arbiBankNo) {
            return bankAccount.buildArbiAccountReportSummary()
        }

        val wallet = walletRepository.findWallets(
            accountId = accountId,
            memberStatus = MemberStatus.ACTIVE,
        ).singleOrNull { it.paymentMethodId == id }

        if (wallet == null) {
            logger.warn(
                markers.andAppend("status", "Conta já encerrada"),
                logName,
            )
            return bankAccount.buildArbiAccountReportSummary()
        }
        markers.andAppend("walletId", wallet.id.value)

        val currentDayStatement = bankAccount.getStatement(
            initialDate = initialDate.toLocalDate(),
            endDate = date.plusDays(endDatePlusDays),
            markers = markers,
        ).filter { it.lastUpdate != null && it.lastUpdate.toLocalDate() == date }
        markers.andAppend("currentDayStatement", currentDayStatement.map { it.toLogAttribute() })

        // BILLS REFUNDED
        val refundCurrentDayBills = billRepository.getRefundedBills(
            walletId = wallet.id,
            startDate = initialDate,
            endDate = initialDate.plusDays(1),
        )
        markers.andAppend("refund.currentDayBills", refundCurrentDayBills.map { it.toLogAttribute() })

        val debitsConciliation = checkDebits(
            wallet = wallet,
            date = date,
            cutOffTime = cutOffTime,
            currentDayStatement = currentDayStatement,
            refundCurrentDayBills = refundCurrentDayBills,
            markers = markers,
        )

        val refundsConciliation = checkRefunds(
            date = date,
            cutOffTime = cutOffTime,
            currentDayStatement = currentDayStatement,
            refundCurrentDayBills = refundCurrentDayBills,
            markers = markers,
        )

        val summary = bankAccount.buildArbiAccountReportSummary(
            debitsConciliation = debitsConciliation,
            refundsConciliation = refundsConciliation,
        )
        markers.andAppend("summary", summary.toLogAttribute())

        if (summary.divergent) {
            markers.andAppend("status", "Conta divergente")
            if (lastRetry) {
                logger.error(markers, logName)
            } else {
                logger.warn(markers, logName)
            }
        } else {
            markers.andAppend("status", "Conta Ok")
            logger.info(markers, logName)
        }
        return summary
    }

    private fun AccountPaymentMethod.checkRefunds(
        date: LocalDate,
        cutOffTime: Int,
        currentDayStatement: List<DefaultBankStatementItem>,
        refundCurrentDayBills: List<RefundedBill>,
        markers: LogstashMarker,
    ): ArbiAccountConciliation {
        // REFUNDS
        val currentWindowStatementItems = currentDayStatement.filter { (it.isDevolucaoTEF() || it.isDevolucaoTED()) && it.lastUpdate!!.hour < cutOffTime }
        markers.andAppend("refund.statement.items", currentWindowStatementItems.map { it.toLogAttribute() })

        val currentWindowStatementItemsAmount = currentWindowStatementItems.sumOf { it.amount }
        markers.andAppend("refund.statement.amount", currentWindowStatementItemsAmount)

        // BILLS REFUNDED
        val currentWindowTransactions = refundCurrentDayBills.filter { it.refundDate.hour < cutOffTime }.toTransactions().toMutableList()
        markers.andAppend("refund.transaction.items", currentWindowTransactions.map { it.toLogAttribute() })

        val currentWindowTransactionsPaidWithBalanceAmount = currentWindowTransactions.sumOf { it.amountPaidWithBalance() }
        markers.andAppend("refund.transaction.amountPaidWithBalance", currentWindowTransactionsPaidWithBalanceAmount)

        // TRI PIX aparece no extrato igual estorno quando a transferência é feita a partir da conta liquidação
        val triPixFromSettlementAccount = if (onboardingTestPixConfiguration.originAccountNo.toLong() == arbiSettlementAccountNo.toLong()) {
            onboardingTestPixRepository.findByAccountId(accountId)?.let {
                if (it.createdAt.toLocalDate() == date && it.createdAt.hour < cutOffTime) {
                    it.billIds.size
                } else {
                    0
                }
            } ?: 0
        } else {
            0
        }
        markers.andAppend("refund.triPixFromSettlementAccount", triPixFromSettlementAccount)

        val conciliation = ArbiAccountConciliation(
            currentAmount = currentWindowStatementItemsAmount - triPixFromSettlementAccount,
            expectedAmount = currentWindowTransactionsPaidWithBalanceAmount,
        )

        if (conciliation.divergent) {
            var toleranceAmount = 0L

            val toleranceWindowTransactionsPaidAndRefunded = refundCurrentDayBills.filter { it.refundDate.hour == cutOffTime && it.refundDate.minute < 5 }.toTransactions().toMutableList()
            markers.andAppend("refund.transaction.toleranceItems", toleranceWindowTransactionsPaidAndRefunded.map { it.toLogAttribute() })

            val divergentStatementItemsWithoutTransaction = currentWindowStatementItems.filter { statementItem ->
                val matchingRefundAmount = currentWindowTransactions.removeMatchingTransaction(statementItem)

                matchingRefundAmount == null
            }.filter { statementItem ->
                val matchingRefundAmount = toleranceWindowTransactionsPaidAndRefunded.removeMatchingTransaction(statementItem)

                matchingRefundAmount?.let {
                    toleranceAmount += it
                }

                matchingRefundAmount == null
            }
            markers.andAppend("refund.divergence.transactionsWithoutStatementItem", currentWindowTransactions.map { it.toLogAttribute() })
                .andAppend("refund.divergence.statementItemsWithoutTransaction", divergentStatementItemsWithoutTransaction.map { it.toLogAttribute() })

            return ArbiAccountConciliation(
                currentAmount = currentWindowStatementItemsAmount - triPixFromSettlementAccount,
                expectedAmount = currentWindowTransactionsPaidWithBalanceAmount + toleranceAmount,
            )
        }

        return conciliation
    }

    private fun checkDebits(
        wallet: Wallet,
        date: LocalDate,
        cutOffTime: Int,
        currentDayStatement: List<DefaultBankStatementItem>,
        refundCurrentDayBills: List<RefundedBill>,
        markers: LogstashMarker,
    ): ArbiAccountConciliation {
        // DEBITS
        val currentDayStatementItems = currentDayStatement.filter { it.flow == BankStatementItemFlow.DEBIT }

        val statementItems = currentDayStatementItems.filter { it.lastUpdate!!.hour < cutOffTime }
        markers.andAppend("debit.statement.items", statementItems.map { it.toLogAttribute() })

        val statementItemsAmount = statementItems.sumOf { it.amount }
        markers.andAppend("debit.statement.amount", statementItemsAmount)

        // BILLS PAID
        val currentDayBillsPaid = billRepository.findByWalletAndStatus(
            walletId = wallet.id,
            status = BillStatus.PAID,
        ).filter { it.source !is ActionSource.OpenFinance && it.paidDate!!.toLocalDate() == date }

        val billsPaid = currentDayBillsPaid.filter { it.paidDate!!.hour < cutOffTime }.toMutableList()
        markers.andAppend("debit.billPaid.items", billsPaid.map { it.toLogAttribute() })

        val billsPaidWithBalanceAmount = billsPaid.sumOf { it.amountPaidWithBalance() }
        markers.andAppend("debit.billPaid.amountPaidWithBalance", billsPaidWithBalanceAmount)

        // BILLS PAID TODAY AND REFUNDED
        val transactionsPaidTodayAndRefunded = refundCurrentDayBills.filter {
            it.refundDate.hour < cutOffTime && it.originalPaidDate.toLocalDate() == date
        }.toTransactions().toMutableList()
        markers.andAppend("debit.transactionPaidTodayAndRefunded.items", transactionsPaidTodayAndRefunded.map { it.toLogAttribute() })

        val transactionsPaidTodayWithBalanceAndRefundedAmount = transactionsPaidTodayAndRefunded.sumOf { it.amountPaidWithBalance() }
        markers.andAppend("debit.transactionPaidTodayAndRefunded.amountPaidWithBalance", transactionsPaidTodayWithBalanceAndRefundedAmount)

        val conciliation = ArbiAccountConciliation(
            currentAmount = statementItemsAmount,
            expectedAmount = billsPaidWithBalanceAmount + transactionsPaidTodayWithBalanceAndRefundedAmount,
        )

        if (conciliation.divergent) {
            var toleranceAmount = 0L

            val toleranceWindowBillsPaid = currentDayBillsPaid.filter { it.paidDate!!.hour == cutOffTime && it.paidDate.minute < 5 }.toMutableList()
            markers.andAppend("debit.billPaid.toleranceItems", toleranceWindowBillsPaid.map { it.toLogAttribute() })

            val toleranceWindowTransactionsPaidTodayAndRefunded = refundCurrentDayBills.filter {
                it.refundDate.hour == cutOffTime && it.refundDate.minute < 5 && it.originalPaidDate.toLocalDate() == date
            }.toTransactions().toMutableList()
            markers.andAppend("debit.transactionPaidTodayAndRefunded.toleranceItems", toleranceWindowTransactionsPaidTodayAndRefunded.map { it.toLogAttribute() })

            val divergentStatementItemsWithoutBillPaid = statementItems.filter { statementItem ->
                val matchingPaymentAmount = billsPaid.removeMatchingBill(statementItem)
                    ?: transactionsPaidTodayAndRefunded.removeMatchingTransaction(statementItem)

                matchingPaymentAmount == null
            }.filter { statementItem ->
                val matchingPaymentAmount = toleranceWindowBillsPaid.removeMatchingBill(statementItem)
                    ?: toleranceWindowTransactionsPaidTodayAndRefunded.removeMatchingTransaction(statementItem)

                matchingPaymentAmount?.let {
                    toleranceAmount += it
                }

                matchingPaymentAmount == null
            }
            markers.andAppend("debit.divergence.billsPaidWithoutStatementItem", billsPaid.map { it.toLogAttribute() })
                .andAppend("debit.divergence.transactionsPaidTodayAndRefundedWithoutStatementItem", transactionsPaidTodayAndRefunded.map { it.toLogAttribute() })
                .andAppend("debit.divergence.statementItemsWithoutBillPaid", divergentStatementItemsWithoutBillPaid.map { it.toLogAttribute() })

            return ArbiAccountConciliation(
                currentAmount = statementItemsAmount,
                expectedAmount = billsPaidWithBalanceAmount + transactionsPaidTodayWithBalanceAndRefundedAmount + toleranceAmount,
            )
        }

        return conciliation
    }

    private fun MutableList<BillView>.removeMatchingBill(statementItem: DefaultBankStatementItem) = firstOrNull {
        it.amountPaidWithBalance() == statementItem.amount
    }.also {
        remove(it)
    }?.amountPaidWithBalance()

    private fun MutableList<Transaction>.removeMatchingTransaction(statementItem: DefaultBankStatementItem) = firstOrNull {
        it.amountPaidWithBalance() == statementItem.amount
    }.also {
        remove(it)
    }?.amountPaidWithBalance()

    private fun InternalBankAccount.getStatement(initialDate: LocalDate, endDate: LocalDate, markers: LogstashMarker) = try {
        accountStatementAdapter
            .getStatement(
                accountNo = buildFullAccountNumber(),
                document = Document(value = document),
                initialDate = initialDate,
                endDate = endDate,
            ).items
    } catch (e: ArbiAccountMissingPermissionsException) {
        markers.andAppend("statementError", "Conta sem permissão. Provavelmente desativada no Arbi por falta de uso.")
        emptyList()
    } catch (e: Exception) {
        markers.andAppend("statementError", "Não foi possível consultar o extrato: ${e.message.orEmpty()}")
        emptyList()
    }

    // FIXME counterpartAccountNo long
    private fun DefaultBankStatementItem.isDevolucaoTEF() =
        flow == BankStatementItemFlow.CREDIT &&
            type == BankStatementItemType.TRANSFERENCIA_CC &&
            (
                counterpartAccountNo?.toLong() == arbiSettlementAccountNo.toLong() ||
                    arbiSettlementAccountNo.contains(documentNumber) // FIXME counterpartAccountNo para crédito de TEF não está vindo corretamente. O documentNumber está sendo comparado por isso
                )

    private fun DefaultBankStatementItem.isDevolucaoTED() = flow == BankStatementItemFlow.CREDIT && type == BankStatementItemType.DEVOLUCAO_TED

    private fun ArbiAccountReportSummary.toLogAttribute() = mapOf(
        "accountId" to accountId.value,
        "fullAccountNumber" to accountNumber.fullAccountNumber,
        "date" to date.format(dateFormat),
        "cutOffTime" to cutOffTime,
        "debitsConciliation" to debitsConciliation,
        "refundsConciliation" to refundsConciliation,
        "divergent" to divergent,
    )

    private fun RefundedBill.toLogAttribute() = mapOf(
        "amount" to amount,
        "billId" to billId.value,
        "type" to type,
        "transactionId" to transactionId,
        "refundDate" to refundDate.format(dateTimeFormat),
        "originalPaidDate" to originalPaidDate.format(dateTimeFormat),
    )

    private fun Transaction.toLogAttribute() = mapOf(
        "id" to id.value,
        "billId" to settlementData.settlementTarget.settlementTargetId(),
        "amountPaidWithBalance" to amountPaidWithBalance(),
        "amountPaidWithCreditCard" to amountPaidWithCreditCard(),
    )

    private fun BankStatementItem.toLogAttribute() = mapOf(
        "amount" to amount,
        "date" to date.format(dateFormat),
        "description" to description,
        "operationNumber" to operationNumber,
        "ref" to ref,
        "lastUpdate" to lastUpdate?.format(dateTimeFormat),
    )

    private fun BillView.toLogAttribute() = mapOf(
        "amount" to amountPaid,
        "amountPaidWithBalance" to amountPaidWithBalance(),
        "amountPaidWithCreditCard" to amountPaidWithCreditCard(),
        "billId" to billId.value,
        "paidDate" to paidDate?.format(dateTimeFormat),
        "type" to billType,
    )

    fun sendEmailReport(
        date: LocalDate,
        cutOffTime: Int,
        settlementAccountReport: SettlementAccountReport,
        cashInAccountReport: SettlementAccountReport,
        allBoletosCreditCardRefunded: Long,
        recipient: String,
    ) {
        val title =
            if (cutOffTime != 24) {
                "$subject - ${date.format(dateFormat)} até as ${cutOffTime}hs"
            } else {
                "$subject - ${date.format(dateFormat)} (dia completo)"
            }

        val content =
            buildString {
                appendLine("---   ---   ---   PAGAMENTOS UTILIZANDO SALDO   ---   ---   ---")
                appendLine("Total Créditos Conta Liquidação Arbi: ${settlementAccountReport.totalAmountArbi.toAmountFormat()}")
                appendLine("Total boletos liquidados com saldo do usuário: ${settlementAccountReport.totalAmountFriday.toAmountFormat()}")
                appendLine("Diferença: ${(settlementAccountReport.totalAmountArbi - settlementAccountReport.totalAmountFriday).toAmountFormat()}")
                appendLine("")
                appendLine("---   ---   ---   PAGAMENTOS UTILIZANDO CARTÃO   ---   ---   ---")
                appendLine("Total transferido da conta Cash-in para Celcoin: ${cashInAccountReport.totalAmountArbi.toAmountFormat()}")
                appendLine("Total boletos liquidados com cartão de crédito: ${cashInAccountReport.totalAmountFriday.toAmountFormat()}")
                appendLine("Diferença: ${(cashInAccountReport.totalAmountArbi - cashInAccountReport.totalAmountFriday).toAmountFormat()}")
                appendLine("")
                appendLine("---   ---   ---   ESTORNOS DE BOLETOS PAGOS COM CARTÃO   ---   ---   ---")
                appendLine("Total boletos estornados com cartão de crédito: ${allBoletosCreditCardRefunded.toAmountFormat()}")
                appendLine("Total transferido da conta liquidação Arbi para a conta Cashin: ${cashInAccountReport.totalRefundedToCashinAccount.toAmountFormat()}")
                appendLine("Diferença: ${(allBoletosCreditCardRefunded - cashInAccountReport.totalRefundedToCashinAccount).toAmountFormat()}")
            }

        emailSender.sendRawEmail(from, title, content, recipient)
    }

    fun generateSettlementAccountReport(
        date: LocalDate,
        cutOffTime: Int,
    ): Triple<SettlementAccountReport, SettlementAccountReport, Long> {
        val accounts = accountRepository.findAllAccountsActivatedSince(LocalDate.EPOCH)

        val allWallets =
            accounts
                .map { account ->
                    walletRepository
                        .findWallets(account.accountId, MemberStatus.ACTIVE)
                        .filter { it.founder.accountId == account.accountId }
                }.flatten()

        val allBoletosPaid =
            allWallets
                .map { wallet ->
                    billRepository
                        .findByWalletAndStatus(walletId = wallet.id, status = BillStatus.PAID)
                        .filter { it.billType.isBoleto() && it.paidDate!!.toLocalDate() == date && it.paidDate.hour < cutOffTime }
                }.flatten()

        val allBoletosPaidAmountBalanceTotal =
            allBoletosPaid.sumOf {
                it.amountPaidWithBalance()
            }

        val allBoletosPaidAmountCreditCardTotal =
            allBoletosPaid.sumOf {
                it.amountPaidWithCreditCard()
            }

        val allBoletosCreditCardRefunded =
            allWallets
                .flatMap { wallet ->
                    getCurrentWindowBillsRefundedTransactions(
                        walletId = wallet.id,
                        initialDate = date.atStartOfDay(brazilTimeZone),
                        cutOffTime = cutOffTime,
                    )
                }.sumOf { it.amountPaidWithCreditCard() }

        val balanceStatementItems =
            arbiAdapter
                .getStatement(
                    accountNumber = AccountNumber(arbiSettlementAccountNo),
                    document = arbiSettlementAccountDocument,
                    initialDate = date.atStartOfDay(brazilTimeZone),
                    endDate = date.plusDays(endDatePlusDays).atStartOfDay(brazilTimeZone),
                ).items
                .filter { it.lastUpdate != null && it.lastUpdate.toLocalDate() == date && it.lastUpdate.hour < cutOffTime }

        val credits =
            balanceStatementItems
                .filter { it.flow == BankStatementItemFlow.CREDIT }
                .sumOf { it.amount }

        val returnedToUser =
            balanceStatementItems
                .filter { it.flow == BankStatementItemFlow.DEBIT && it.type == BankStatementItemType.TRANSFERENCIA_CC && it.counterpartDocument != arbiSettlementAccountDocument }
                .sumOf { it.amount }

        val returnedToFriday =
            balanceStatementItems
                .filter { it.flow == BankStatementItemFlow.DEBIT && it.type == BankStatementItemType.PIX && it.counterpartDocument == arbiSettlementAccountDocument }
                .sumOf { it.amount }

        val settlementAccountReport =
            SettlementAccountReport(
                totalAmountArbi = credits - returnedToUser,
                totalAmountFriday = allBoletosPaidAmountBalanceTotal,
                totalRefundedToCashinAccount = returnedToFriday,
            )

        val creditCardCashInAccountStatementItems =
            if (arbiCashInAccountNo != "0") {
                arbiAdapter
                    .getStatement(
                        accountNumber = AccountNumber(arbiCashInAccountNo),
                        document = arbiSettlementAccountDocument,
                        initialDate = date.atStartOfDay(brazilTimeZone),
                        endDate = date.plusDays(endDatePlusDays).atStartOfDay(brazilTimeZone),
                    ).items
                    .filter { it.lastUpdate != null && it.lastUpdate.toLocalDate() == date && it.lastUpdate.hour < cutOffTime }
            } else {
                emptyList()
            }

        val pixToFridayCNPJ =
            creditCardCashInAccountStatementItems
                .filter { it.flow == BankStatementItemFlow.DEBIT && it.type == BankStatementItemType.PIX && it.counterpartDocument == arbiSettlementAccountDocument }
                .sumOf { it.amount }

        val depositsFromSettlement =
            creditCardCashInAccountStatementItems
                .filter { it.flow == BankStatementItemFlow.CREDIT && it.type == BankStatementItemType.PIX && it.counterpartDocument == arbiSettlementAccountDocument }
                .sumOf { it.amount }

        creditCardCashInAccountStatementItems
            .filter {
                it.flow == BankStatementItemFlow.DEBIT && it.type == BankStatementItemType.PIX && it.counterpartDocument != arbiSettlementAccountDocument
            }.also { pixToOthersCNPJs ->
                if (pixToOthersCNPJs.isNotEmpty()) {
                    logger.error(
                        Markers
                            .append("ACTION", "VERIFY")
                            .andAppend("context", "Pix saindo da conta Cashin para uma conta que não é Friday detectado")
                            .andAppend(
                                "bankStatementItems",
                                pixToOthersCNPJs,
                            ),
                        "GenerateSettlementAccountReport",
                    )
                }
            }

        val cashInAccountReport =
            SettlementAccountReport(
                totalAmountArbi = pixToFridayCNPJ,
                totalAmountFriday = allBoletosPaidAmountCreditCardTotal,
                totalRefundedToCashinAccount = depositsFromSettlement,
            )

        return Triple(settlementAccountReport, cashInAccountReport, allBoletosCreditCardRefunded)
    }

    private fun BillView.amountPaidWithBalance() =
        when (paymentDetails) {
            is MultiplePaymentMethodsDetail -> paymentDetails.methods.sumOf { it.amountPaidWithBalance() }
            is SinglePaymentMethodsDetail -> paymentDetails.amountPaidWithBalance()
            null -> this.amountTotal
        }

    private fun BillView.amountPaidWithCreditCard() =
        when (paymentDetails) {
            is MultiplePaymentMethodsDetail -> paymentDetails.methods.sumOf { it.amountPaidWithCreditCard() }
            is SinglePaymentMethodsDetail -> paymentDetails.amountPaidWithCreditCard()
            null -> 0L
        }

    private fun Transaction.amountPaidWithBalance() =
        when (paymentData) {
            is MultiplePaymentData -> paymentData.payments.sumOf { it.details.amountPaidWithBalance() }
            is SinglePaymentData -> paymentData.details.amountPaidWithBalance()
            NoPaymentData -> throw IllegalStateException("NoPaymentData não pode ser usado aqui")
        }

    private fun Transaction.amountPaidWithCreditCard() =
        when (paymentData) {
            is MultiplePaymentData -> paymentData.payments.sumOf { it.details.amountPaidWithCreditCard() }
            is SinglePaymentData -> paymentData.details.amountPaidWithCreditCard()
            NoPaymentData -> throw IllegalStateException("NoPaymentData não pode ser usado aqui")
        }

    private fun SinglePaymentMethodsDetail.amountPaidWithBalance() =
        when (this) {
            is PaymentMethodsDetailWithBalance -> amount
            is PaymentMethodsDetailWithCreditCard -> 0L
            is PaymentMethodsDetailWithExternalPayment -> 0L
        }

    private fun SinglePaymentMethodsDetail.amountPaidWithCreditCard() =
        when (this) {
            is PaymentMethodsDetailWithBalance -> 0L
            is PaymentMethodsDetailWithCreditCard -> netAmount
            is PaymentMethodsDetailWithExternalPayment -> 0L
        }

    companion object {
        private val logger = LoggerFactory.getLogger(BankAccountReconciliationReportService::class.java)
    }
}

data class ArbiAccountReportSummary(
    val accountId: AccountId,
    val accountNumber: AccountNumber,
    val date: LocalDate,
    val cutOffTime: Int,
    val debitsConciliation: ArbiAccountConciliation,
    val refundsConciliation: ArbiAccountConciliation,
) {
    val divergent: Boolean = debitsConciliation.divergent || refundsConciliation.divergent
}

data class ArbiAccountConciliation(
    val currentAmount: Long,
    val expectedAmount: Long,
) {
    val divergent: Boolean = currentAmount != expectedAmount
    val missingAmount: Long = currentAmount - expectedAmount

    companion object {
        val ZERO = ArbiAccountConciliation(0, 0)
    }
}

data class UserBankAccountDivergence(
    val accountId: AccountId,
    val totalAmountArbi: Long,
    val totalAmountFriday: Long,
)

data class SettlementAccountReport(
    val totalAmountArbi: Long,
    val totalAmountFriday: Long,
    val totalRefundedToCashinAccount: Long,
) {
    fun hasDivergence() = totalAmountArbi != totalAmountFriday
}

data class AccountReconciliationReportQueueMessageTO(
    val date: String,
    val cutOffTime: Int,
    val accountId: String,
    val accountPaymentMethodId: String,
    val retry: Int,
)