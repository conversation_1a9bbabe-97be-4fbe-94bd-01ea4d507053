package ai.friday.billpayment.app.banking

import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.FinancialInstitutionGlobalData
import ai.friday.billpayment.app.integrations.ExternalBankAccountRespository
import jakarta.inject.Singleton

interface ExternalBankAccountService {
    fun saveLastUsed(externalBankAccount: ExternalBankAccount)
    fun findLastUsed(document: Document): ExternalBankAccount?
    fun findAll(document: Document): List<ExternalBankAccount>
}

@Singleton
class DefaultExternalBankAccountService(private val externalBankAccountRespository: ExternalBankAccountRespository) :
    ExternalBankAccountService {

    override fun saveLastUsed(externalBankAccount: ExternalBankAccount) {
        if (externalBankAccount.bankNo == null && externalBankAccount.bankISPB.isNullOrEmpty()) {
            throw IllegalArgumentException("External bank account must have either bankNo or bankISPB")
        }

        externalBankAccountRespository.saveLastUsed(externalBankAccount.fulfillMissingData())
    }

    private fun ExternalBankAccount.fulfillMissingData(): ExternalBankAccount {
        val pixParticipant = if (bankISPB.isNullOrEmpty()) {
            FinancialInstitutionGlobalData.pixParticipants.firstOrNull {
                it.compe == bankNo
            }
        } else if (bankNo == null) {
            FinancialInstitutionGlobalData.pixParticipants.firstOrNull {
                it.ispb == bankISPB
            }
        } else {
            null
        }

        return if (pixParticipant != null) {
            copy(bankISPB = pixParticipant.ispb, bankNo = pixParticipant.compe)
        } else {
            this
        }
    }

    override fun findLastUsed(document: Document): ExternalBankAccount? {
        return externalBankAccountRespository.findLastUsed(document)
    }

    override fun findAll(document: Document): List<ExternalBankAccount> {
        return externalBankAccountRespository.findAll(document)
    }
}

data class ExternalBankAccount(
    val document: Document,
    val bankNo: Long?,
    val bankISPB: String?,
    val routingNo: Long,
    val accountNo: Long,
    val accountDv: String,
    val accountType: String?,
    val lastUsed: Long? = null,
)

fun ExternalBankAccount.accountAndDv(separator: String = "") = "$accountNo$separator$accountDv"