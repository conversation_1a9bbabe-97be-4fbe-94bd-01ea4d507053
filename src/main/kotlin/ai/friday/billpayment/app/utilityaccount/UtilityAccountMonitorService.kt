package ai.friday.billpayment.app.utilityaccount

import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.bill.segmentAndCovenantCode
import ai.friday.billpayment.app.bill.utility
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.log.andAppend
import jakarta.inject.Singleton
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
class UtilityAccountMonitorService(
    private val walletService: WalletService,
    private val accountService: AccountService,
    private val billRepository: BillRepository,
) {
    fun findMatchingBill(utilityAccount: UtilityAccount, monthsAgo: Long = 1): BillView? {
        val today = getLocalDate()
        val nextMonthFirstDay = today.plusMonths(1).withDayOfMonth(1)
        val cutOffDate = utilityAccount.lastDueDateFound ?: today.minusMonths(monthsAgo)

        val markers = Markers.append("walletId", utilityAccount.walletId.value)
            .andAppend("utilityAccountId", utilityAccount.id.value)
            .andAppend("utility", utilityAccount.utility.name)
            .andAppend("lastBillIdFound", utilityAccount.lastBillIdFound?.value)
            .andAppend("lastDueDateFound", utilityAccount.lastDueDateFound?.format(DateTimeFormatter.ISO_DATE))

        fun BillView.isCreatedCurrentMonth(): Boolean {
            return createdOn.toLocalDate().isAfter(cutOffDate) && createdOn.toLocalDate().isBefore(nextMonthFirstDay)
        }

        val walletId = walletService.findWalletOrNull(utilityAccount.walletId)?.let { wallet ->
            accountService.findAccountByIdOrNull(wallet.founder.accountId)?.configuration?.defaultWalletId
        } ?: utilityAccount.walletId

        val mailboxUtilityBillsCreatedCurrentMonth =
            billRepository.findByWallet(walletId).filter { bill ->
                (bill.isMailbox() || bill.source is ActionSource.ConnectUtility) && bill.isConcessionaria() && bill.isCreatedCurrentMonth()
            }

        val matchingBill = mailboxUtilityBillsCreatedCurrentMonth.sortedBy {
            it.dueDate
        }.lastOrNull {
            utilityAccount.utility.matches(it.barCode!!)
        }

        if (matchingBill != null) {
            markers.andAppend("currentBillIdFound", matchingBill.billId)
                .andAppend("currentBillCreatedOn", matchingBill.createdOn.format(DateTimeFormatter.ISO_DATE))
                .andAppend("currentDueDateFound", matchingBill.dueDate.format(DateTimeFormatter.ISO_DATE))
            if (matchingBill.source is ActionSource.WalletMailBox) {
                markers.andAppend("from", (matchingBill.source).from)
            } else {
                markers.andAppend("from", "ConnectUtility")
            }

            LOG.info(markers, "UtilityAccountMonitorService")
        } else {
            val possibleSegments = mailboxUtilityBillsCreatedCurrentMonth.map {
                it.barCode!!.segmentAndCovenantCode() to it.barCode.utility()
            }

            markers.andAppend("possibleSegments", possibleSegments)
            LOG.warn(markers, "UtilityAccountMonitorService")
        }
        return matchingBill
    }

    private fun BillView.isConcessionaria() = billType == BillType.CONCESSIONARIA

    private fun BillView.isMailbox() = source is ActionSource.WalletMailBox

    companion object {
        private val LOG = LoggerFactory.getLogger(UtilityAccountMonitorService::class.java)
    }
}

internal fun Utility.matches(barCode: BarCode): Boolean {
    return codes.matches(barCode.number)
}

private fun List<Regex>.matches(value: String) = any { value.matches(it) }