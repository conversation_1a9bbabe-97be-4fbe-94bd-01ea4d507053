package ai.friday.billpayment.app.utilityaccount

import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.and
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.CreateBillResult
import ai.friday.billpayment.app.bill.CreateBillService
import ai.friday.billpayment.app.bill.CreateConcessionariaRequest
import ai.friday.billpayment.app.bill.CreateFichaDeCompensacaoRequest
import ai.friday.billpayment.app.bill.ExternalBillId
import ai.friday.billpayment.app.bill.FichaCompensacaoService
import ai.friday.billpayment.app.bill.calculateEffectiveDate
import ai.friday.billpayment.app.bill.checkIsConcessionaria
import ai.friday.billpayment.app.bill.isFicha
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.CrmRepository
import ai.friday.billpayment.app.integrations.KmsService
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.ObjectRepository
import ai.friday.billpayment.app.integrations.UserEvent
import ai.friday.billpayment.app.integrations.UserEventService
import ai.friday.billpayment.app.integrations.UtilityAccountRepository
import ai.friday.billpayment.app.journey.UserJourneyEvent
import ai.friday.billpayment.app.journey.UserJourneyService
import ai.friday.billpayment.app.manualentry.ManualEntryService
import ai.friday.billpayment.app.manualentry.ManualEntryStatus
import ai.friday.billpayment.app.manualentry.ManualEntryType
import ai.friday.billpayment.app.message.QueueMessage
import ai.friday.billpayment.app.metrics.UtilityAccountFinalConnectionStatus
import ai.friday.billpayment.app.metrics.UtilityAccountUnexpectedError
import ai.friday.billpayment.app.metrics.metricRegister
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import io.micronaut.context.annotation.Property
import io.micronaut.http.MediaType
import io.micronaut.tracing.annotation.NewSpan
import io.via1.communicationcentre.app.email.Attachment
import io.via1.communicationcentre.app.integrations.EmailSenderService
import jakarta.inject.Named
import jakarta.inject.Singleton
import java.time.LocalDate
import kotlinx.coroutines.runBlocking
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Singleton
open class UtilityAccountService(
    private val utilityAccountRepository: UtilityAccountRepository,
    private val accountRepository: AccountRepository,
    private val sqsMessagePublisher: MessagePublisher,
    private val notificationAdapter: NotificationAdapter,
    private val intercomAdapter: CrmRepository,
    private val kmsAdapter: KmsService,
    private val emailSenderService: EmailSenderService,
    private val utilityAccountMonitorService: UtilityAccountMonitorService,
    private val createBillService: CreateBillService,
    private val fichaCompensacaoService: FichaCompensacaoService,
    private val userJourneyService: UserJourneyService,
    private val walletService: WalletService,
    private val manualEntryService: ManualEntryService,
    private val userEventService: UserEventService,
    @Named("sa") private val externalFilesRepositorySA: ObjectRepository,

    @Property(name = "sqs.utilityRequestFlowConnectQueueName") private val utilityAccountActivationQueueName: String,
    @Property(name = "sqs.utilityRequestFlowQueueName") private val utilityFlowRequestInvoicesQueueName: String,
    @Property(name = "email.notification.email") private val sender: String,
    @Property(name = "emailDomain") private val emailDomain: String,
    @Property(name = "utility-account.manual-workflow-email") private val manualWorkFlowEmail: String,
) {
    private val logger = LoggerFactory.getLogger(UtilityAccountService::class.java)

    fun create(
        connectionDetails: UtilityAccountConnectionDetails,
        walletFounderAccountId: AccountId,
        currentAccountId: AccountId,
        walletId: WalletId,
        utility: Utility,
        connectionMethod: UtilityConnectionMethod,
    ): Either<UtilityAccountError, UtilityAccount> {
        val userAccount = accountRepository.findById(walletFounderAccountId)

        val document = userAccount.document

        val markers = append("connectionDetails", connectionDetails)
            .and(
                "accountId" to walletFounderAccountId,
                "document" to document,
                "utility" to utility,
            )

        val foundAccount = utilityAccountRepository.findByIndex(walletId).find {
            connectionDetails == it.connectionDetails && it.utility == utility && it.status != UtilityAccountConnectionStatus.DISCONNECTED
        }

        if (foundAccount != null) {
            logger.error(markers, "UtilityAccountService#create")
            return Either.Left(UtilityAccountError.AccountAlreadyCreated)
        }

        val parsedConnectionDetails = if (connectionDetails.requiresEncryption()) {
            val (_, rawPassword) = connectionDetails.loginInfo()
            connectionDetails.clone(password = encryptPassword(rawPassword).getOrElse { return it.left() })
        } else {
            connectionDetails
        }

        val utilityAccount = UtilityAccount(
            status = UtilityAccountConnectionStatus.PENDING,
            walletId = walletId,
            accountEmail = "$document@$emailDomain",
            attempts = 0,
            utility = utility,
            createdAt = getZonedDateTime(),
            connectionMethod = connectionMethod,
            connectionDetails = parsedConnectionDetails,
            addedBy = currentAccountId,
        )

        utilityAccountRepository.save(utilityAccount)

        userEventService.save(UserEvent(utilityAccount.id, "ua_created"))

        if (connectionMethod == UtilityConnectionMethod.MANUAL) {
            notifyInternal(
                utilityAccount = utilityAccount,
                subject = "Conta de consumo manual - conexão",
            )
        } else {
            postMessage(utilityAccount)
        }

        logger.info(markers, "UtilityAccountService#create")

        return utilityAccount.right()
    }

    fun reconnect(
        utilityAccountId: UtilityAccountId,
        currentAccountId: AccountId,
        wallet: Wallet,
        connectionDetailsMap: Map<String, String>,
    ): Either<UtilityAccountError, UtilityAccount> {
        val oldUA = utilityAccountRepository.findById(utilityAccountId, wallet.id)
            ?: return UtilityAccountError.AccountNotFound.left()

        val connectionDetails = try {
            UtilityAccountConnectionDetails.create(oldUA.utility, details = connectionDetailsMap)
        } catch (e: IllegalArgumentException) {
            return UtilityAccountError.CannotConnectAccount.left()
        }

        val newUA = create(
            connectionDetails = connectionDetails,
            connectionMethod = oldUA.utility.method,
            currentAccountId = currentAccountId,
            walletId = oldUA.walletId,
            walletFounderAccountId = wallet.founder.accountId,
            utility = oldUA.utility,
        ).fold(
            ifLeft = {
                return it.left()
            },

            ifRight = {
                it
            },
        )

        disconnectAccount(utilityAccountId, wallet).fold(
            ifLeft = {
                return it.left()
            },

            ifRight = {},
        )

        return newUA.right()
    }

    fun listAllEnabledUtilities(): List<UtilityAccountWithFormData> {
        return Utility.entries.mapNotNull {
            // TODO - deveria vir de algum outro lugar
            it.buildEnabledUtilityAccountWithFormData()
        }
    }

    fun listAllUtilities(): List<UtilityAccountWithFormData> {
        return Utility.entries.mapNotNull {
            it.buildUtilityAccountWithFormData()
        }
    }

    private fun encryptPassword(password: String): Either<UtilityAccountError, String> {
        val encryptedPassword = runBlocking { kmsAdapter.encryptData(password) }
            ?: return Either.Left(UtilityAccountError.CannotEncryptPassword)
        return encryptedPassword.right()
    }

    fun listAllAccounts(walletId: WalletId): List<UtilityAccount> {
        val accounts = utilityAccountRepository.findByIndex(walletId).filter {
            it.status != UtilityAccountConnectionStatus.DISCONNECTED
        }
        return accounts
    }

    fun findAllConnectedByFlow(): List<UtilityAccount> {
        val utilityAccounts = utilityAccountRepository.findByIndex(UtilityAccountConnectionStatus.CONNECTED)

        return utilityAccounts.filter(::shouldSearchForNewInvoices)
    }

    fun findAllByStatus(status: UtilityAccountConnectionStatus): List<UtilityAccount> {
        return utilityAccountRepository.findByIndex(status)
    }

    private fun UtilityAccount.isLastDueDateGreaterThanAMonth(today: LocalDate) =
        lastDueDateFound != null && today.plusMonths(1).isBefore(lastDueDateFound)

    private fun UtilityAccount.isLastBillFoundPast20Days(today: LocalDate) =
        lastBillFound != null && today.minusDays(19).isAfter(lastBillFound)

    private fun shouldSearchForNewInvoices(utilityAccount: UtilityAccount): Boolean {
        val today = LocalDate.now()
        val cutOffDate = utilityAccount.lastDueDateFound?.plusDays(15)

        if (shouldDisableRequestInvoices(utilityAccount.utility)) {
            return false
        }

        return utilityAccount.connectionMethod == UtilityConnectionMethod.FLOW &&
            (
                utilityAccount.lastDueDateFound == null ||
                    today.isAfter(cutOffDate) || // Regra base
                    utilityAccount.isLastBillFoundPast20Days(today) || // Regra para achar o dia certo da conta
                    (utilityAccount.isLastDueDateGreaterThanAMonth(today) && utilityAccount.isLastBillFoundPast20Days(today)) // Regra para conexões com contas de meses depois
                )
    }

    fun shouldDisableRequestInvoices(utility: Utility): Boolean {
        return utility.disableRequestInvoices
    }

    fun setStatus(
        utilityAccountId: UtilityAccountId,
        walletId: WalletId,
        currentStatus: UtilityAccountConnectionStatus,
        newStatus: UtilityAccountConnectionStatus,
        statusMessage: String?,
    ): Either<UtilityAccountError, Unit> {
        val utility = utilityAccountRepository.findById(
            utilityAccountId = utilityAccountId,
            walletId = walletId,
        ) ?: return UtilityAccountError.AccountNotFound.left()

        if (utility.status != currentStatus) {
            return UtilityAccountError.CannotConnectAccount.left()
        }

        this.update(UtilityAccountUpdateCommand(utilityAccount = utility.copy(status = newStatus, statusMessage = statusMessage)))
        return Unit.right()
    }

    fun requestReconnection(utilityAccountId: UtilityAccountId, walletId: WalletId): Either<UtilityAccountError, Unit> {
        val utilityAccount = utilityAccountRepository.findById(
            utilityAccountId = utilityAccountId,
            walletId = walletId,
        ) ?: return UtilityAccountError.AccountNotFound.left()

        val allowedStatuses = listOf(
            UtilityAccountConnectionStatus.CONNECTED,
            UtilityAccountConnectionStatus.PENDING,
        )

        if (utilityAccount.status !in allowedStatuses) {
            return UtilityAccountError.CannotConnectAccount.left()
        }

        utilityAccountRepository.save(utilityAccount.copy(status = UtilityAccountConnectionStatus.INVALID_CREDENTIALS))

        if (walletService.findWalletOrNull(utilityAccount.walletId)?.hasActiveMember(utilityAccount.addedBy) == true) {
            notificationAdapter.notifyUtilityAccountRequestReconnection(utilityAccount)
        }

        return Unit.right()
    }

    fun disconnectAccount(
        utilityAccountId: UtilityAccountId,
        wallet: Wallet,
    ): Either<UtilityAccountError, Unit> {
        val markers = append("utilityAccountId", utilityAccountId.value).and("walletId" to wallet.id.value)
        val utilityAccountToDisconnect = utilityAccountRepository.findById(utilityAccountId, wallet.id)
        if (utilityAccountToDisconnect == null) {
            logger.error(markers, "UtilityAccountService#disconnectAccount")
            return Either.Left(UtilityAccountError.AccountNotFound)
        }
        val utility = utilityAccountToDisconnect.utility

        logger.info(markers, "UtilityAccountService#disconnectAccount")

        when (utilityAccountToDisconnect.status) {
            UtilityAccountConnectionStatus.CONNECTED -> {
                val shouldDisconnectDirectly = utility == Utility.COMGAS && utilityAccountToDisconnect.connectionMethod == UtilityConnectionMethod.SCRAPING

                val finalStatus = if (shouldDisconnectDirectly) {
                    UtilityAccountConnectionStatus.DISCONNECTED
                } else {
                    UtilityAccountConnectionStatus.PENDING_DISCONNECTION
                }

                val utilityAccount = utilityAccountToDisconnect.copy(
                    status = finalStatus,
                    accountEmail = wallet.founder.emailAddress.value,
                    errorFiles = null,
                )
                utilityAccountRepository.save(utilityAccount)

                if (utility.method == UtilityConnectionMethod.MANUAL) {
                    notifyInternal(
                        utilityAccount = utilityAccount,
                        subject = "Conta de consumo manual - desconexão",
                    )
                } else if (shouldDisconnectDirectly) {
                    update(UtilityAccountUpdateCommand(utilityAccount))
                } else {
                    postMessage(utilityAccount)
                }
            }

            UtilityAccountConnectionStatus.CONNECTION_ERROR,
            UtilityAccountConnectionStatus.DISCONNECTION_ERROR,
            UtilityAccountConnectionStatus.INVALID_CREDENTIALS,
            UtilityAccountConnectionStatus.REQUIRES_RECONNECTION,
            UtilityAccountConnectionStatus.FINAL_ERROR,
            -> {
                val utilityAccount =
                    utilityAccountToDisconnect.copy(status = UtilityAccountConnectionStatus.DISCONNECTED)
                addMetric(utilityAccount)
                utilityAccountRepository.save(utilityAccount)
            }

            UtilityAccountConnectionStatus.PENDING,
            UtilityAccountConnectionStatus.PENDING_DISCONNECTION,
            UtilityAccountConnectionStatus.DISCONNECTED,
            -> {
                return Either.Left(UtilityAccountError.CannotDisconnectUtilityAccount)
            }

            UtilityAccountConnectionStatus.NOT_SUPPORTED -> {
                logger.warn(markers.andAppend("whenUnknownBranch", utilityAccountToDisconnect.status), "WHENUNKNOWNBRANCH#UtilityAccountService#disconnectAccount")
            }
        }

        return Either.Right(Unit)
    }

    fun disconnectLegacy(
        utilityAccount: UtilityAccount,
    ): Either<UtilityAccountError, Unit> {
        if (utilityAccount.status != UtilityAccountConnectionStatus.CONNECTED) {
            return UtilityAccountError.CannotDisconnectUtilityAccount.left()
        }

        val updatedUtilityAccount = utilityAccount.copy(status = UtilityAccountConnectionStatus.DISCONNECTED, errorFiles = null)
        addMetric(updatedUtilityAccount)
        utilityAccountRepository.save(updatedUtilityAccount)

        if (walletService.findWalletOrNull(utilityAccount.walletId)?.hasActiveMember(utilityAccount.addedBy) == true) {
            notificationAdapter.notifyDisconnectLegacyUtilityAccount(updatedUtilityAccount)
        }

        return Either.Right(Unit)
    }

    fun getPendingConnectionsForMoreThanOneDay(): List<UtilityAccount> {
        val pendingConnections = utilityAccountRepository.findByIndex(UtilityAccountConnectionStatus.PENDING)
        val pendingDisconnections =
            utilityAccountRepository.findByIndex((UtilityAccountConnectionStatus.PENDING_DISCONNECTION))

        val yesterday = getZonedDateTime().minusDays(1)

        return pendingConnections.plus(pendingDisconnections).filter { it.updatedAt.isBefore(yesterday) }
    }

    fun retryUtilityAccount(
        walletId: WalletId,
        utilityAccountId: UtilityAccountId,
    ): Either<UtilityAccountError, UtilityAccount> {
        val utilityAccount = utilityAccountRepository.findById(utilityAccountId, walletId)
            ?: return UtilityAccountError.AccountNotFound.left()

        val allowedRetryStates =
            listOf(UtilityAccountConnectionStatus.CONNECTION_ERROR, UtilityAccountConnectionStatus.DISCONNECTION_ERROR)

        if (utilityAccount.status !in allowedRetryStates) {
            return UtilityAccountError.CannotRetryAccount.left()
        }

        val updatedAccount = utilityAccount.copy(
            status = when (utilityAccount.status) {
                UtilityAccountConnectionStatus.CONNECTION_ERROR -> UtilityAccountConnectionStatus.PENDING
                UtilityAccountConnectionStatus.DISCONNECTION_ERROR -> UtilityAccountConnectionStatus.PENDING_DISCONNECTION
                else -> return UtilityAccountError.CannotRetryAccount.left()
            },
            errorFiles = null,
        )
        utilityAccountRepository.save(updatedAccount)
        postMessage(updatedAccount)
        return updatedAccount.right()
    }

    fun convertToFlow(walletId: WalletId, utilityAccountId: UtilityAccountId, connectionDetailsData: Map<String, String>): Either<UtilityAccountError.AccountNotFound, UtilityAccount> {
        val utilityAccount = utilityAccountRepository.findById(utilityAccountId, walletId)
            ?: return UtilityAccountError.AccountNotFound.left()

        val connectionDetails = UtilityAccountConnectionDetails.create(utilityAccount.utility, connectionDetailsData)

        val updatedAccount = utilityAccount.copy(
            connectionMethod = UtilityConnectionMethod.FLOW,
            connectionDetails = connectionDetails,
            errorFiles = null,
        )
        utilityAccountRepository.save(updatedAccount)
        postMessage(updatedAccount)
        return updatedAccount.right()
    }

    fun find(utilityAccountId: UtilityAccountId): UtilityAccount? =
        utilityAccountRepository.find(connectUtilityId = utilityAccountId)

    fun find(walletId: WalletId, utilityAccountId: UtilityAccountId): UtilityAccount? =
        utilityAccountRepository.findById(walletId = walletId, utilityAccountId = utilityAccountId)

    fun update(updateCommand: UtilityAccountUpdateCommand) {
        val utilityAccount = updateCommand.utilityAccount

        var utilityAccountToSave = notifyStatusUpdate(utilityAccount, updateCommand)

        val shouldNotifyInternal = listOf(
            UtilityAccountConnectionStatus.CONNECTION_ERROR,
            UtilityAccountConnectionStatus.DISCONNECTION_ERROR,
        )
        if (shouldNotifyInternal.contains(utilityAccount.status)) {
            val errorFiles = fileKeysFromPath(updateCommand)
            notifyInternal(
                utilityAccount = utilityAccount,
                subject = "Erro ao conectar conta de consumo",
                utilityAccountErrorFiles = errorFiles,
            )
            utilityAccountToSave = utilityAccountToSave.copy(errorFiles = errorFiles)
        }

        addMetric(utilityAccountToSave)
        publishEvent(utilityAccountToSave)
        utilityAccountRepository.save(utilityAccountToSave)
    }

    private fun notifyStatusUpdate(utilityAccount: UtilityAccount, updateCommand: UtilityAccountUpdateCommand): UtilityAccount {
        val member = walletService.findWalletOrNull(updateCommand.utilityAccount.walletId)
            ?.getActiveMemberOrNull(utilityAccount.addedBy)

        if (member == null || updateCommand.silent) {
            return utilityAccount
        }

        when (utilityAccount.status) {
            UtilityAccountConnectionStatus.CONNECTED -> {
                notificationAdapter.notifyUtilityAccountConnected(utilityAccount, updateCommand.additionalInfo)
            }

            UtilityAccountConnectionStatus.INVALID_CREDENTIALS,
            UtilityAccountConnectionStatus.FINAL_ERROR,
            UtilityAccountConnectionStatus.REQUIRES_RECONNECTION,
            UtilityAccountConnectionStatus.DISCONNECTED,
            -> {
                notificationAdapter.notifyUtilityAccountUpdatedStatus(utilityAccount)
            }

            // Outros status não notificam
            else -> {
                return utilityAccount
            }
        }

        return utilityAccount.copy(notificatedAt = getZonedDateTime())
    }

    fun handleResponseInvoices(utilityInvoices: UtilityFlowInvoices): Either<UtilityAccountError, UtilityAccountResponseProcessingResult> {
        val (utilityAccountId, lastDueDateFound, invoices) = utilityInvoices

        val utilityAccount = find(utilityAccountId) ?: return UtilityAccountError.InvalidInvoicesResponse().left()

        if (utilityAccount.status !in listOf(UtilityAccountConnectionStatus.CONNECTED, UtilityAccountConnectionStatus.PENDING)) {
            return UtilityAccountError.InvalidUtilityAccountStatus(utilityAccount.status).left()
        }

        val updatedUtilityAccount = handlePendingAccount(utilityAccount)

        return handleResponseInvoices(updatedUtilityAccount, utilityInvoices)
    }

    fun handleResponseInvoices(
        utilityAccount: UtilityAccount,
        utilityInvoices: UtilityFlowInvoices,
    ): Either<UtilityAccountError, UtilityAccountResponseProcessingResult> {
        try {
            val (_, lastDueDateFound, invoices) = utilityInvoices

            if (utilityAccount.status != UtilityAccountConnectionStatus.CONNECTED) {
                return UtilityAccountError.InvalidUtilityAccountStatus(utilityAccount.status).left()
            }

            val markers = append("accountId", utilityAccount.walletId.value)
                .and(
                    "utilityAccountId" to utilityAccount.id.value,
                    "utility" to utilityAccount.utility.name.lowercase(),
                    "invoices" to invoices,
                    "lastDueDateFound" to lastDueDateFound,
                )

            if (utilityAccount.invalidCredentialsCount > 0) {
                logger.warn(markers.andAppend("invalidCredentialsCount", utilityAccount.invalidCredentialsCount), "ScanSuccessInvoicesWhenInvalidCredentials")
            }

            if (invoices.isEmpty()) {
                utilityAccountRepository.save(
                    utilityAccount.copy(
                        lastSuccessfulScan = getLocalDate(),
                        scanFailureCount = 0,
                        invalidCredentialsCount = 0,
                        lastDueDateFound = getMostRecentDueDate(lastDueDateFound, utilityAccount.lastDueDateFound),
                    ),
                )

                return UtilityAccountResponseProcessingResult(billsCreated = 0, paidEntriesCreated = 0).right()
            }

            val bills = createBillsFromUtilityInvoices(invoices, utilityAccount, markers)
            val paidInvoicesResult = createEntriesFromPaidInvoices(invoices, utilityAccount, markers)

            val successfullyCreatedBills = bills.filterIsInstance<CreateBillResult.SUCCESS>().map { it.bill }
            val alreadyExistingBills = bills.filterIsInstance<CreateBillResult.FAILURE.BillAlreadyExists>().map { it.bill }
            val allBills = successfullyCreatedBills + alreadyExistingBills

            val paidInvoicesResultMap = paidInvoicesResult.groupingBy { it }.eachCount()
            val paidEntriesCreated = paidInvoicesResultMap[CreateUtilityInvoiceEntryResult.CREATED] ?: 0

            logger.info(
                markers.andAppend("newBills", successfullyCreatedBills.size)
                    .andAppend("existingBills", alreadyExistingBills.size)
                    .andAppend("paidInvoicesResult", paidInvoicesResultMap)
                    .andAppend("billsNotCreated", bills.size - allBills.size),
                "UtilityAccountService/billsAdded",
            )

            if (allBills.isEmpty()) {
                return UtilityAccountResponseProcessingResult(
                    billsCreated = successfullyCreatedBills.size,
                    paidEntriesCreated = paidEntriesCreated,
                ).right()
            }

            val mostRecentBill = allBills.maxByOrNull { it.dueDate }!!

            utilityAccountRepository.save(
                utilityAccount.copy(
                    lastBillIdFound = mostRecentBill.billId,
                    lastBillFound = getLocalDate(),
                    lastDueDateFound = getMostRecentDueDate(lastDueDateFound, utilityAccount.lastDueDateFound),
                    lastSuccessfulScan = getLocalDate(),
                    scanFailureCount = 0,
                    invalidCredentialsCount = 0,
                ),
            )

            return UtilityAccountResponseProcessingResult(
                billsCreated = successfullyCreatedBills.size,
                paidEntriesCreated = paidEntriesCreated,
            ).right()
        } catch (ex: Exception) {
            return UtilityAccountError.InvalidInvoicesResponse(ex).left()
        }
    }

    private fun createEntriesFromPaidInvoices(
        invoices: List<Invoice>,
        utilityAccount: UtilityAccount,
        markers: LogstashMarker,
    ) = invoices.filterIsInstance<Invoice.Paid>().map { paidInvoice ->

        if (!shouldAddPaidInvoice(utilityAccount)) {
            return@map CreateUtilityInvoiceEntryResult.IGNORED_CUTOFF_DATE
        }

        // TODO: Estamos assumindo que é concessionária e que o segmento do barcode é TELECOM, ELECTRICITY_AND_GAS ou SANITATION
        val effectiveDueDate = calculateEffectiveDate(paidInvoice.dueDate, BillType.CONCESSIONARIA)

        val dueDateBills =
            createBillService.findBillsByWalletAndEffectiveDueDate(utilityAccount.walletId, effectiveDueDate)

        if (dueDateBills.any { it.externalId == paidInvoice.externalId }) {
            return@map CreateUtilityInvoiceEntryResult.IGNORED_DUPLICATE_BILL_EXTERNAL_ID
        }

        if (dueDateBills.any { it.amount == paidInvoice.amount && it.assignor in utilityAccount.utility.assignors }) {
            return@map CreateUtilityInvoiceEntryResult.IGNORED_DUPLICATE_BILL_AMOUNT_DUEDATE_ASSIGNOR
        }

        val duplicateEntry =
            manualEntryService.findAllByWalletAndDueDate(utilityAccount.walletId, paidInvoice.dueDate).firstOrNull {
                it.externalId == paidInvoice.externalId
            }

        if (duplicateEntry != null) {
            return@map CreateUtilityInvoiceEntryResult.IGNORED_DUPLICATE_ENTRY
        }

        manualEntryService.create(
            walletId = utilityAccount.walletId,
            dueDate = paidInvoice.dueDate,
            amount = paidInvoice.amount,
            type = ManualEntryType.UTILITY_INVOICE,
            status = ManualEntryStatus.PAID,
            title = "Fatura ${utilityAccount.utility.viewName}",
            categoryId = null,
            source = ActionSource.ConnectUtility(utilityAccount.addedBy),
            externalId = paidInvoice.externalId,
        ).getOrElse {
            logger.error(
                markers.andAppend("invoice", paidInvoice).andAppend("error", it),
                "UtilityAccountService/errorCreatingUtilityEntry",
            )
            return@map CreateUtilityInvoiceEntryResult.ERROR
        }

        CreateUtilityInvoiceEntryResult.CREATED
    }

    private fun createBillsFromUtilityInvoices(
        invoices: List<Invoice>,
        utilityAccount: UtilityAccount,
        markers: LogstashMarker,
    ) = invoices.filterIsInstance<Invoice.Active>().map {
        if (checkIsConcessionaria(it.barcode)) {
            createBillService.createConcessionaria(
                request = CreateConcessionariaRequest(
                    description = "Fatura ${utilityAccount.utility.viewName}",
                    dueDate = it.dueDate,
                    barcode = it.barcode,
                    walletId = utilityAccount.walletId,
                    source = ActionSource.ConnectUtility(accountId = utilityAccount.addedBy),
                    member = null,
                    securityValidationErrors = listOf(),
                    externalBillId = it.externalId,
                ),
            )
        } else if (it.barcode.isFicha()) {
            fichaCompensacaoService.createFichaDeCompensacao(
                request = CreateFichaDeCompensacaoRequest(
                    description = "Fatura ${utilityAccount.utility.viewName}",
                    barcode = it.barcode,
                    walletId = utilityAccount.walletId,
                    source = ActionSource.ConnectUtility(accountId = utilityAccount.addedBy),
                    member = null,
                    securityValidationErrors = listOf(),
                    externalBillId = it.externalId,
                ),
            )
        } else {
            logger.warn(
                markers.andAppend("barcode", it.barcode.digitable),
                "UtilityAccountService#handleResponseInvoices",
            )
            CreateBillResult.FAILURE.BillUnableToValidate("invalid barcode")
        }
    }

    private fun handlePendingAccount(utilityAccount: UtilityAccount): UtilityAccount {
        if (utilityAccount.status != UtilityAccountConnectionStatus.PENDING) {
            return utilityAccount
        }
        val updatedUtilityAccount = utilityAccount.copy(status = UtilityAccountConnectionStatus.CONNECTED)
        val command = UtilityAccountUpdateCommand(utilityAccount = updatedUtilityAccount)
        update(command)
        return updatedUtilityAccount
    }

    fun handleScanFailure(utilityAccount: UtilityAccount) {
        userEventService.save(UserEvent(utilityAccount.id, "ua_scan_fail"))
        utilityAccountRepository.save(utilityAccount.copy(scanFailureCount = utilityAccount.scanFailureCount + 1))
    }

    fun handleInvalidCredentials(utilityAccount: UtilityAccount) {
        utilityAccountRepository.save(utilityAccount.copy(invalidCredentialsCount = utilityAccount.invalidCredentialsCount + 1))
    }

    private fun fileKeysFromPath(updateCommand: UtilityAccountUpdateCommand): UtilityAccountErrorFiles? {
        val bucket = updateCommand.errorFilesBucket ?: return null
        val path = updateCommand.errorFilesPath ?: return null

        return try {
            UtilityAccountErrorFiles(
                bucket = bucket,
                filesKey = externalFilesRepositorySA.listObjectKeys(bucket, path),
                region = externalFilesRepositorySA.region(),
            )
        } catch (e: Exception) {
            logger.error(append("updateCommand", updateCommand), "UtilityAccountService#fileKeysFromPath", e)
            null
        }
    }

    fun errorFileMediaType(fileKey: String): String {
        return when {
            fileKey.endsWith("png") -> MediaType.IMAGE_PNG
            fileKey.endsWith("html") -> MediaType.TEXT_HTML
            else -> MediaType.IMAGE_PNG
        }
    }

    private fun downloadAttachmentsFromPath(filesToDownload: UtilityAccountErrorFiles): List<Attachment>? {
        return try {
            filesToDownload.filesKey.map { fileKey ->
                val inputStream = externalFilesRepositorySA.loadObject(filesToDownload.bucket, fileKey)
                Attachment.of(
                    inputStream,
                    errorFileMediaType(fileKey),
                    fileKey.replace("/", "_"),
                )
            }
        } catch (e: Exception) {
            logger.error(append("bucket", filesToDownload.bucket).andAppend("filesKey", filesToDownload.filesKey), "UtilityAccountService#downloadAttachmentsFromPath", e)
            null
        }
    }

    fun refreshAllConnectedUtilities() {
        val today = getLocalDate()

        fun shouldRefresh(utility: UtilityAccount) =
            utility.connectionMethod != UtilityConnectionMethod.FLOW &&
                utility.createdAt.isBefore(today.minusMonths(1).atStartOfDay(brazilTimeZone)) &&
                (utility.lastDueDateFound?.isBefore(today.minusDays(25)) ?: true) &&
                (utility.lastDueDateFound?.isAfter(today.minusMonths(3)) ?: true)

        val utilityAccountsToRefresh = utilityAccountRepository.findByIndex(UtilityAccountConnectionStatus.CONNECTED)
            .filter(::shouldRefresh)

        utilityAccountsToRefresh.forEach { utilityAccount ->
            val now = getZonedDateTime()
            val matchingBill = utilityAccountMonitorService.findMatchingBill(utilityAccount)
            val updatedUtilityAccount =
                if (matchingBill != null && (
                    utilityAccount.lastDueDateFound == null || matchingBill.dueDate.isAfter(
                            utilityAccount.lastDueDateFound,
                        )
                    )
                ) {
                    utilityAccount.copy(
                        lastBillIdFound = matchingBill.billId,
                        lastBillFound = getLocalDate(),
                        lastDueDateFound = matchingBill.dueDate,
                        lastScannedAt = now,
                    )
                } else {
                    logger.info(
                        append("matchingBillId", matchingBill?.billId).andAppend(
                            "utilityAccount",
                            utilityAccount,
                        ),
                        "refreshAllConnectedUtilities",
                    )
                    utilityAccount.copy(lastScannedAt = now)
                }
            utilityAccountRepository.save(updatedUtilityAccount)
        }
    }

    fun listAllManual(exposePasswords: Boolean = false): List<UtilityAccount> {
        val manual = (getPendingConnectionsForMoreThanOneDay() + utilityAccountRepository.findAllManual()).distinctBy { it.id }
        return manual.map { utilityAccount ->
            if (exposePasswords && utilityAccount.connectionDetails.requiresEncryption()) {
                val (_, rawPassword) = utilityAccount.connectionDetails.loginInfo()
                if (rawPassword.isEmpty()) {
                    utilityAccount
                } else {
                    val password = runBlocking { kmsAdapter.decryptData(rawPassword) }
                    utilityAccount.copy(connectionDetails = utilityAccount.connectionDetails.clone(password = password))
                }
            } else {
                utilityAccount
            }
        }
    }

    fun findUtilityAccountWithPlainCredentials(walletId: WalletId, utilityAccountId: UtilityAccountId): UtilityAccount? {
        val utilityAccount = utilityAccountRepository.findById(utilityAccountId, walletId) ?: return null

        return if (utilityAccount.connectionDetails.requiresEncryption()) {
            val (_, rawPassword) = utilityAccount.connectionDetails.loginInfo()
            val password = runBlocking { kmsAdapter.decryptData(rawPassword) }
            utilityAccount.copy(connectionDetails = utilityAccount.connectionDetails.clone(password = password))
        } else {
            utilityAccount
        }
    }

    fun postRequestInvoicesMessage(utilityAccount: UtilityAccount) {
        sqsMessagePublisher.sendMessage(
            QueueMessage(
                utilityFlowRequestInvoicesQueueName,
                getObjectMapper().writeValueAsString(utilityAccount.toConnectUtilityRequest()),
            ),
        )
    }

    fun notifyInternal(
        utilityAccountList: List<UtilityAccount>,
        subject: String,
    ) {
        val message = utilityAccountList.joinToString("\n\n") { buildUtilityInternalNotificationMessage(it) }
        sendInternalNotificationEmail(subject, message, null)
    }

    private fun notifyInternal(
        utilityAccount: UtilityAccount,
        subject: String,
        utilityAccountErrorFiles: UtilityAccountErrorFiles? = null,
    ) {
        val message = buildUtilityInternalNotificationMessage(utilityAccount)
        sendInternalNotificationEmail(
            subject = subject,
            message = message,
            attachments = utilityAccountErrorFiles?.let { downloadAttachmentsFromPath(utilityAccountErrorFiles) },
        )
    }

    private fun buildUtilityInternalNotificationMessage(
        utilityAccount: UtilityAccount,
    ): String {
        val (login, rawPassword) = utilityAccount.connectionDetails.loginInfo()

        val password = if (utilityAccount.connectionDetails.requiresEncryption()) {
            runBlocking { kmsAdapter.decryptData(rawPassword) }
        } else {
            rawPassword
        }

        return """
            walletId: ${utilityAccount.walletId.value}
            utilityAccountId: ${utilityAccount.id.value}
            utility: ${utilityAccount.utility}
            connectionMethod: ${utilityAccount.connectionMethod.name}"
            status: ${utilityAccount.status.name}
            lastBillFound: ${utilityAccount.lastBillIdFound?.value}
            lastDueDateFound: ${utilityAccount.lastDueDateFound?.toString()}
            daysSinceLastBill: ${utilityAccount.daysSinceLastDueDateFound()}
            login: $login
            password: $password
        """.trimIndent()
    }

    private fun sendInternalNotificationEmail(
        subject: String,
        message: String,
        attachments: List<Attachment>?,
    ) {
        emailSenderService.sendRawEmail(sender, subject, message, manualWorkFlowEmail, attachments)
    }

    private fun publishEvent(utilityAccount: UtilityAccount) {
        if (utilityAccount.status == UtilityAccountConnectionStatus.CONNECTED) {
            userJourneyService.trackEventAsync(utilityAccount.addedBy, UserJourneyEvent.UtilityAccountConnected)
            intercomAdapter.publishEvent(
                utilityAccount.addedBy,
                "conta_de_consumo_conectada",
                mapOf("concessionaria" to utilityAccount.utility.viewName, "walletId" to utilityAccount.walletId.value),
            )
        }
    }

    private fun addMetric(utilityAccount: UtilityAccount) {
        val metric = when (utilityAccount.status) {
            UtilityAccountConnectionStatus.CONNECTED,
            UtilityAccountConnectionStatus.DISCONNECTED,
            UtilityAccountConnectionStatus.INVALID_CREDENTIALS,
            -> UtilityAccountFinalConnectionStatus()

            UtilityAccountConnectionStatus.DISCONNECTION_ERROR,
            UtilityAccountConnectionStatus.CONNECTION_ERROR,
            -> UtilityAccountUnexpectedError()

            else -> null
        }

        if (metric != null) {
            metricRegister(
                metric,
                "status" to utilityAccount.status.name,
                "accountId" to utilityAccount.addedBy.value,
                "walletId" to utilityAccount.walletId.value,
                "utilityAccountId" to utilityAccount.id.value,
                "attempts" to utilityAccount.attempts,
                "utility" to utilityAccount.utility.name,
            )
        }
    }

    private fun postMessage(utilityAccount: UtilityAccount) {
        sqsMessagePublisher.sendMessage(
            QueueMessage(
                utilityAccountActivationQueueName,
                getObjectMapper().writeValueAsString(utilityAccount.toConnectUtilityRequest()),
            ),
        )
    }

    @NewSpan
    open fun updateToInvalidCredentials(utilityAccount: UtilityAccount): UtilityAccount {
        if (utilityAccount.invalidCredentialsCount > 5) {
            val updatedUtilityAccount = utilityAccount.copy(status = UtilityAccountConnectionStatus.INVALID_CREDENTIALS)
            val command = UtilityAccountUpdateCommand(
                utilityAccount = updatedUtilityAccount,
            )
            update(command)
            val markers = append("utilityAccount", utilityAccount)
            logger.info(markers, "UtilityAccountService#updateToInvalidCredentials")
            return updatedUtilityAccount
        }
        return utilityAccount
    }

    fun notifyInvoicesNotFound(utilityAccount: UtilityAccount) {
        if (shouldNotifyScanError(utilityAccount.scanFailureCount)) {
            notificationAdapter.notifyInvoicesScanErrorUtilityAccount(utilityAccount)
            return
        }

        if (shouldNotifyNotFound(utilityAccount)) {
            notificationAdapter.notifyInvoicesNotFoundUtilityAccount(utilityAccount)
            return
        }
    }

    fun updateStatus(utilityAccountId: UtilityAccountId, status: UtilityAccountConnectionStatus, statusMessage: String?, attempts: Int, updateInfo: UtilityAccountUpdateInfo?): Either<Unit, UtilityAccount> {
        val dbUtilityAccount = find(utilityAccountId)

        when (dbUtilityAccount?.status) {
            UtilityAccountConnectionStatus.CONNECTION_ERROR,
            UtilityAccountConnectionStatus.FINAL_ERROR,
            UtilityAccountConnectionStatus.REQUIRES_RECONNECTION,
            UtilityAccountConnectionStatus.DISCONNECTED,
            UtilityAccountConnectionStatus.DISCONNECTION_ERROR,
            UtilityAccountConnectionStatus.INVALID_CREDENTIALS,
            UtilityAccountConnectionStatus.NOT_SUPPORTED,
            null,
            -> {
                return Unit.left()
            }

            UtilityAccountConnectionStatus.CONNECTED -> {
                when (status) {
                    UtilityAccountConnectionStatus.CONNECTION_ERROR -> {
                        handleScanFailure(dbUtilityAccount)
                        return Unit.left()
                    }
                    UtilityAccountConnectionStatus.INVALID_CREDENTIALS -> {
                        handleInvalidCredentials(dbUtilityAccount)
                        return Unit.left()
                    }

                    UtilityAccountConnectionStatus.CONNECTED -> {
                        userEventService.save(UserEvent(utilityAccountId, "ua_scan_success"))
                        // Segue para o update de status
                    }

                    UtilityAccountConnectionStatus.FINAL_ERROR,
                    UtilityAccountConnectionStatus.REQUIRES_RECONNECTION,
                    -> {} // Final Error e Requires Reconnection seguem para o update do status
                    else -> {
                        return Unit.left()
                    }
                }
            }

            UtilityAccountConnectionStatus.PENDING -> {
                when (status) {
                    UtilityAccountConnectionStatus.CONNECTED -> userEventService.save(UserEvent(utilityAccountId, "ua_connection_success"))

                    UtilityAccountConnectionStatus.INVALID_CREDENTIALS -> userEventService.save(UserEvent(utilityAccountId, "ua_connection_invalid_credentials"))

                    UtilityAccountConnectionStatus.CONNECTION_ERROR,
                    UtilityAccountConnectionStatus.FINAL_ERROR,
                    -> userEventService.save(UserEvent(utilityAccountId, "ua_connection_fail"))

                    else -> {}
                }
            }

            UtilityAccountConnectionStatus.PENDING_DISCONNECTION,
            -> {
                if (status != UtilityAccountConnectionStatus.DISCONNECTED) {
                    return Unit.left()
                }
            }
        }

        if (dbUtilityAccount.status == status) {
            return dbUtilityAccount.right()
        }

        val updatedUtilityAccount = dbUtilityAccount.copy(status = status, attempts = attempts, statusMessage = statusMessage)

        update(UtilityAccountUpdateCommand(utilityAccount = updatedUtilityAccount, additionalInfo = updateInfo))

        return updatedUtilityAccount.right()
    }

    private fun shouldNotifyNotFound(utilityAccount: UtilityAccount): Boolean {
        if (utilityAccount.scanFailureCount >= 10) {
            return false
        }
        return if (utilityAccount.lastDueDateFound == null) {
            utilityAccount.createdAt.plusDays(10).dayOfMonth == getLocalDate().dayOfMonth ||
                utilityAccount.createdAt.plusDays(15).dayOfMonth == getLocalDate().dayOfMonth
        } else {
            utilityAccount.lastDueDateFound.plusDays(25).dayOfMonth == getLocalDate().dayOfMonth ||
                utilityAccount.lastDueDateFound.plusDays(30).dayOfMonth == getLocalDate().dayOfMonth
        }
    }

    private fun shouldNotifyScanError(scanFailureCount: Int): Boolean {
        return ((scanFailureCount - 10) % 30) == 0
    }

    private fun shouldAddPaidInvoice(utilityAccount: UtilityAccount): Boolean {
        val cutoffDate = LocalDate.of(2024, 7, 1)
        val wallet = walletService.findWalletOrNull(utilityAccount.walletId) ?: return false

        return wallet.founder.created.toLocalDate().isAfter(cutoffDate)
    }
}

fun UtilityAccount.toConnectUtilityRequest() =
    ConnectUtilityRequestTO(
        connectionId = id.value,
        utility = utility,
        status = status,
        connectionDetails = connectionDetails,
        attempts = attempts,
        flowName = null,
    )

@JsonIgnoreProperties(ignoreUnknown = true)
data class UtilityAccountTO(
    val id: String,
    val status: UtilityAccountConnectionStatus,
    val statusMessage: String?,
    val accountEmail: String,
    val attempts: Int = 0,
    val utility: Utility,
    val errorFilesBucket: String?,
    val errorFilesPath: String?,
    val connectionMethod: UtilityConnectionMethod,
    val connectionDetails: UtilityAccountConnectionDetails,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class ConnectUtilityRequestTO(
    val connectionId: String,
    val utility: Utility,
    val flowName: String?,
    val status: UtilityAccountConnectionStatus?,
    val connectionDetails: UtilityAccountConnectionDetails,
    val attempts: Int?,
)

data class UtilityAccountAdditionalInfoTO(
    val lastDueDateFound: String?,
)

sealed class UtilityAccountError : PrintableSealedClassV2() {
    data object AccountAlreadyCreated : UtilityAccountError()
    data object AccountNotFound : UtilityAccountError()
    data object CannotConnectAccount : UtilityAccountError()
    data object CannotRetryAccount : UtilityAccountError()
    data object CannotEncryptPassword : UtilityAccountError()
    data object CannotDisconnectUtilityAccount : UtilityAccountError()
    class InvalidInvoicesResponse(val exception: Exception? = null) : UtilityAccountError()
    class InvalidUtilityAccountStatus(val status: UtilityAccountConnectionStatus) : UtilityAccountError()
}

data class UtilityFlowInvoices(
    val utilityAccountId: UtilityAccountId,
    val lastDueDateFound: LocalDate?,
    val invoices: List<Invoice>,
)

sealed interface Invoice {
    val dueDate: LocalDate
    val status: UtilityAccountInvoiceStatus
    val externalId: ExternalBillId?

    data class Paid(
        val amount: Long,
        override val dueDate: LocalDate,
        override val externalId: ExternalBillId? = null,
    ) : Invoice {
        override val status = UtilityAccountInvoiceStatus.PAID
    }

    data class Active(
        val barcode: BarCode,
        override val dueDate: LocalDate,
        override val externalId: ExternalBillId? = null,
    ) : Invoice {
        override val status = UtilityAccountInvoiceStatus.ACTIVE
    }
}

data class UtilityAccountResponseProcessingResult(
    val billsCreated: Int,
    val paidEntriesCreated: Int,
)

enum class UtilityAccountInvoiceStatus {
    ACTIVE, PAID
}

enum class CreateUtilityInvoiceEntryResult {
    CREATED,
    IGNORED_CUTOFF_DATE,
    IGNORED_DUPLICATE_BILL_EXTERNAL_ID,
    IGNORED_DUPLICATE_BILL_AMOUNT_DUEDATE_ASSIGNOR,
    IGNORED_DUPLICATE_ENTRY,
    ERROR,
}

private fun getMostRecentDueDate(responseDueDate: LocalDate?, uaDueDate: LocalDate?): LocalDate? {
    return when {
        responseDueDate == null -> uaDueDate

        uaDueDate == null -> responseDueDate

        responseDueDate.isAfter(uaDueDate) -> responseDueDate

        else -> uaDueDate
    }
}

private fun Utility.buildEnabledUtilityAccountWithFormData(): UtilityAccountWithFormData? {
    if (!this.enabled) {
        return null
    }
    return buildUtilityAccountWithFormData()
}

private fun Utility.buildUtilityAccountWithFormData(): UtilityAccountWithFormData? {
    return when (this) {
        Utility.UNKNOWN -> null
        Utility.VIVO_MOBILE -> UtilityAccountWithFormData(
            utility = this,
            category = "mobile",
            form = listOf(
                UtilityAccountFormData(
                    type = "text",
                    name = "login",
                    placeholder = "CPF ou e-mail",
                    label = "CPF ou e-mail",
                    parsers = listOf("email", "cpf"),
                    errorText = "Informe um CPF ou e-mail válido",
                ),
                passwordField.copy(
                    parserRegex = "^\\d{6}$",
                    errorText = "Digite a senha numérica com 6 dígitos",
                ),

            ),
        )

        Utility.VIVO_COMBO -> UtilityAccountWithFormData(
            utility = this,
            category = "internet-and-cable-tv",
            form = listOf(
                UtilityAccountFormData(
                    type = "text",
                    name = "login",
                    placeholder = "CPF ou e-mail",
                    label = "CPF ou e-mail",
                    parsers = listOf("email", "cpf"),
                    errorText = "Informe um CPF ou e-mail válido",
                ),
                passwordField.copy(
                    parserRegex = "^\\d{6}$",
                    errorText = "Digite a senha numérica com 6 dígitos",
                ),

            ),
        )

        Utility.CLARO_HOME -> UtilityAccountWithFormData(
            utility = this,
            category = "internet-and-cable-tv",
            form = listOf(
                UtilityAccountFormData(
                    type = "text",
                    name = "username",
                    placeholder = "CPF, e-mail ou nome de usuário",
                    label = "CPF, e-mail ou nome de usuário",
                    errorText = "Informe um CPF/email/nome de usuário válido",
                ),
                passwordField,
            ),
        )

        Utility.CLARO_MOBILE -> UtilityAccountWithFormData(
            utility = this,
            category = "mobile",
            form = listOf(
                UtilityAccountFormData(
                    type = "text",
                    name = "emailOrCpf",
                    placeholder = "CPF ou e-mail",
                    label = "CPF ou e-mail",
                    parsers = listOf("email", "cpf"),
                    errorText = "Informe um CPF ou e-mail válido",
                ),
                passwordField,
            ),
        )

        Utility.TIM_MOBILE -> UtilityAccountWithFormData(
            utility = this,
            category = "mobile",
            form = listOf(phoneNumberField, passwordField),
        )

        Utility.OI_HOME -> UtilityAccountWithFormData(
            utility = this,
            category = "internet-and-cable-tv",
            form = listOf(cpfField, passwordField),
        )

        Utility.LIGHT_RJ -> UtilityAccountWithFormData(
            utility = this,
            category = "electricity",
            form = listOf(
                cpfField,
                passwordField,
                UtilityAccountFormData(
                    type = "numeric",
                    name = "installationNumber",
                    placeholder = "000000",
                    label = "Código de instalação",
                    description = "Confira se o código é da instalação e __não do cliente__",
                    errorText = "Digite um código de instalação válido",
                ),
            ),
        )

        Utility.NATURGY -> UtilityAccountWithFormData(
            utility = this,
            category = "gas",
            form = listOf(
                cpfField,
                passwordField,
                UtilityAccountFormData(
                    type = "numeric",
                    name = "clientNumber",
                    placeholder = "********",
                    label = "Número do cliente",
                    errorText = "Digite um número do cliente válido",
                ),
            ),
        )

        Utility.ENEL_SP -> UtilityAccountWithFormData(
            utility = this,
            category = "electricity",
            form = listOf(
                emailField,
                passwordField,
                installationNumber,
            ),
        )

        Utility.SABESP -> UtilityAccountWithFormData(
            utility = this,
            category = "water-and-sewage",
            form = listOf(cpfField.copy(name = "login"), passwordField),
        )

        Utility.COMGAS -> UtilityAccountWithFormData(
            utility = this,
            category = "gas",
            form = listOf(
                emailField,
                passwordField,
            ),
        )

        Utility.CEMIG -> UtilityAccountWithFormData(
            utility = this,
            category = "electricity",
            form = listOf(
                cpfField,
                passwordField,
                installationNumber,
            ),
        )

        Utility.COPEL -> UtilityAccountWithFormData(
            utility = this,
            category = "electricity",
            form = listOf(
                cpfField,
                UtilityAccountFormData(
                    type = "text",
                    name = "motherFirstName",
                    placeholder = "Maria",
                    label = "Primeiro nome da mãe",
                    errorText = "Nome inválido",
                ),
                installationNumber.copy(
                    label = "Número predial",
                    errorText = "Digite um número predial válido",
                ),
            ),
        )

        Utility.CPFL -> UtilityAccountWithFormData(
            utility = this,
            category = "electricity",
            form = listOf(
                emailField,
                passwordField,
                installationNumber,
            ),
        )

        Utility.EDP_ES, Utility.EDP_SP -> UtilityAccountWithFormData(
            utility = this,
            category = "electricity",
            form = listOf(
                emailField,
                passwordField,
                installationNumber,
            ),
        )
    }
}