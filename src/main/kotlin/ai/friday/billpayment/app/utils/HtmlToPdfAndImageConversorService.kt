package ai.friday.billpayment.app.utils

import ai.friday.billpayment.app.CompiledHtml
import ai.friday.billpayment.app.integrations.PDFConverter
import jakarta.inject.Singleton
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import javax.imageio.ImageIO
import org.apache.pdfbox.pdmodel.PDDocument
import org.apache.pdfbox.rendering.ImageType
import org.apache.pdfbox.rendering.PDFRenderer

@Singleton
class HtmlToPdfAndImageConversorService(
    private val pdfConverter: PDFConverter,
) {

    fun convertHtml2Pdf(receiptHtml: CompiledHtml, cropToFit: Boolean): ByteArray {
        return pdfConverter.convert(receiptHtml.value, cropToFit)
    }

    fun convertPdf2Image(pdfByteArray: ByteArray, imageResolution: Int, imageFormat: String): ByteArray {
        val image = PDDocument.load(ByteArrayInputStream(pdfByteArray)).use { pdfDocument ->
            val pdfRenderer = PDFRenderer(pdfDocument)
            pdfRenderer.renderImageWithDPI(0, imageResolution.toFloat(), ImageType.RGB)
        }
        val imageOutputStream = ByteArrayOutputStream()
        ImageIO.write(image, imageFormat, imageOutputStream)
        return imageOutputStream.toByteArray()
    }
}