package ai.friday.billpayment.app.contact

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.Recipient
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.ContactRepository
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyDetails
import ai.friday.billpayment.app.pix.PixTransactionError
import ai.friday.billpayment.app.recurrence.BillRecurrenceService
import ai.friday.billpayment.app.wallet.WalletId
import arrow.core.getOrElse
import jakarta.inject.Provider
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Singleton
open class ContactService(
    private val contactRepository: ContactRepository,
    private val billEventRepository: BillEventRepository,
    private val updateBillService: UpdateBillService,
    private val recurrenceServiceProvider: Provider<BillRecurrenceService>,
) {

    private val recurrenceService: BillRecurrenceService by lazy {
        recurrenceServiceProvider.get()
    }

    fun delete(accountId: AccountId, contactId: ContactId) {
        contactRepository.delete(accountId, contactId)
    }

    fun updateContact(accountId: AccountId, contactId: ContactId, alias: String): Contact {
        val contact = contactRepository.findByIdAndAccountId(contactId, accountId)
        if (contact.alias.equals(alias)) {
            return contact
        }
        return contact.copy(
            alias = alias,
        ).also {
            updateBillService.updateContactAlias(
                contactId = contactId,
                alias = alias,
                actionSource = ActionSource.System,
            )
            recurrenceService.updateContactAlias(
                contactId = it.id,
                alias = alias,
            )
            contactRepository.save(it)
        }
    }

    fun deleteBankAccount(accountId: AccountId, contactId: ContactId, bankAccountId: BankAccountId) {
        contactRepository.deleteBankAccount(accountId, contactId, bankAccountId)
    }

    fun addBankAccount(accountId: AccountId, contactId: ContactId, bankAccount: BankAccount) {
        val savedRecipient = contactRepository.findByIdAndAccountId(accountId = accountId, contactId = contactId)
        appendToSavedRecipient(
            accountId = accountId,
            contact = savedRecipient,
            savedBankAccount = bankAccount.toSavedBankAccount(),
        )
    }

    fun updateBankAccount(
        accountId: AccountId,
        contactId: ContactId,
        bankAccountId: BankAccountId,
        bankAccount: BankAccount,
    ) {
        val contact = contactRepository.findByIdAndAccountId(contactId, accountId)
        val savedBankAccount = bankAccount.toSavedBankAccount().copy(id = bankAccountId)

        val duplicatedAccount = contact.bankAccounts.firstOrNull { it == savedBankAccount }

        when {
            duplicatedAccount == null -> {
                contactRepository.deleteBankAccount(accountId, contactId, bankAccountId)
                appendToSavedRecipient(
                    accountId = accountId,
                    contact = contact,
                    savedBankAccount = savedBankAccount,
                )

                val currentStoredBankAccount =
                    contact.bankAccounts.first { it.id == bankAccountId }.toBankAccount(contact.document)
                updateBillService.updateContact(
                    contactId = contactId,
                    oldBankAccount = currentStoredBankAccount,
                    newBankAccount = bankAccount,
                    actionSource = ActionSource.System,
                )
                recurrenceService.updateContact(
                    contactId = contact.id,
                    oldBankAccount = currentStoredBankAccount,
                    newBankAccount = savedBankAccount.toBankAccount(document = contact.document),
                )
            }

            duplicatedAccount.id != bankAccountId -> throw IllegalBankAccountUpdate()
        }
    }

    fun addPixKey(accountId: AccountId, contactId: ContactId, pixKeyDetails: PixKeyDetails) {
        val contact = contactRepository.findByIdAndAccountId(contactId, accountId)
        if (contact.document != pixKeyDetails.owner.document) {
            throw InvalidPixKeyOwner(pixKeyDetails.owner.document, contact.document)
        }
        appendToSavedRecipient(
            accountId = accountId,
            contact = contact,
            savedPixKey = SavedPixKey(
                value = pixKeyDetails.key.value,
                type = pixKeyDetails.key.type,
            ),
        )
    }

    fun deletePixKey(accountId: AccountId, contactId: ContactId, value: String) {
        contactRepository.deletePixKey(accountId, contactId, value)
    }

    open fun getAll(accountId: AccountId): List<Contact> {
        return contactRepository.findByAccountId(accountId)
    }

    fun save(recipient: Recipient, accountId: AccountId, contactId: ContactId?): Result<ContactId> {
        try {
            val savedBankAccount = recipient.bankAccount?.toSavedBankAccount()
            val savedPixKey = recipient.pixKeyDetails?.let {
                SavedPixKey(
                    value = it.key.value,
                    type = it.key.type,
                )
            }
            if (contactId != null) {
                saveByContactId(
                    accountId = accountId,
                    contactId = contactId,
                    savedBankAccount = savedBankAccount,
                    savedPixKey = savedPixKey,
                )
                return Result.success(contactId)
            } else {
                return Result.success(
                    saveByDocument(
                        recipient = recipient,
                        accountId = accountId,
                        savedBankAccount = savedBankAccount,
                        savedPixKey = savedPixKey,
                    ),
                )
            }
        } catch (e: Exception) {
            LOG.error(
                append("accountId", accountId.value).and(append("document", recipient.document)),
                "SaveRecipientService",
                e,
            )
            return Result.failure(e)
        }
    }

    private fun saveByContactId(
        accountId: AccountId,
        contactId: ContactId,
        savedBankAccount: SavedBankAccount?,
        savedPixKey: SavedPixKey?,
    ) {
        val savedRecipient = contactRepository.findByIdAndAccountId(contactId = contactId, accountId = accountId)
        appendToSavedRecipient(
            accountId = accountId,
            contact = savedRecipient,
            savedBankAccount = savedBankAccount,
            savedPixKey = savedPixKey,
        )
        contactRepository.updateLastUsed(
            accountId,
            contactId,
            createLastUsedOf(contact = savedRecipient, savedBankAccount = savedBankAccount, savedPixKey = savedPixKey),
        )
    }

    private fun saveByDocument(
        recipient: Recipient,
        accountId: AccountId,
        savedBankAccount: SavedBankAccount?,
        savedPixKey: SavedPixKey?,
    ): ContactId {
        val document = recipient.document ?: throw IllegalArgumentException("Document must exists!")
        val contact = contactRepository.findRecipientByAccountIDAndDocument(accountId, document)
            .map {
                appendToSavedRecipient(accountId, it, savedBankAccount, savedPixKey)
                it
            }.getOrElse {
                createContact(accountId, recipient, savedBankAccount, savedPixKey)
            }
        contactRepository.updateLastUsed(
            accountId,
            contact.id,
            lastUsed = createLastUsedOf(
                contact = contact,
                savedBankAccount = savedBankAccount,
                savedPixKey = savedPixKey,
            ),
        )
        return contact.id
    }

    private fun appendToSavedRecipient(
        accountId: AccountId,
        contact: Contact,
        savedBankAccount: SavedBankAccount? = null,
        savedPixKey: SavedPixKey? = null,
    ) {
        savedBankAccount?.let {
            if (!contact.bankAccounts.contains(savedBankAccount)) {
                contactRepository.append(accountId, contact.id, savedBankAccount)
            }
        }
        savedPixKey?.let {
            if (!contact.pixKeys.contains(savedPixKey)) {
                contactRepository.append(accountId, contact.id, savedPixKey)
            } else {
                revalidatePixKey(
                    recipient = contact,
                    pixKey = PixKey(value = savedPixKey.value, type = savedPixKey.type),
                )
            }
        }
    }

    private fun createContact(
        accountId: AccountId,
        recipient: Recipient,
        savedBankAccount: SavedBankAccount?,
        savedPixKey: SavedPixKey?,
    ): Contact {
        val contact = Contact(
            accountId = accountId,
            alias = recipient.alias,
            name = recipient.name,
            document = recipient.document!!,
            bankAccounts = if (savedBankAccount != null) listOf(savedBankAccount) else emptyList(),
            pixKeys = if (savedPixKey != null) listOf(savedPixKey) else emptyList(),
            lastUsed = null,
        )
        contactRepository.save(contact)
        return contact
    }

    fun invalidateSettlementInfo(walletId: WalletId, billId: BillId, message: String): Contact {
        val bill =
            billEventRepository.getBill(walletId, billId).getOrElse { throw IllegalStateException() }
        val contactId = checkNotNull(bill.contactId) { "bill contactId must not be null" }
        val recipient = checkNotNull(bill.recipient) { "bill recipient must not be null" }
        val contact = contactRepository.findById(contactId)
        if (recipient.bankAccount != null) {
            invalidateBankAccount(contact, recipient.bankAccount, message)
        } else {
            invalidatePixKey(contact, recipient.pixKeyDetails!!.key)
        }
        return contact
    }

    fun invalidateSettlementInfo(accountId: AccountId, pixKey: PixKey, contactId: ContactId) {
        retrieveContact(accountId, contactId)?.let { recipient ->
            invalidatePixKey(recipient, pixKey)
        }
    }

    private fun revalidatePixKey(
        recipient: Contact,
        pixKey: PixKey,
    ) {
        recipient.pixKeys
            .filter { it.value == pixKey.value }
            .ifEmpty { return }
            .forEach {
                it.apply {
                    invalidated = false
                    invalidationCode = null
                }
            }
        contactRepository.save(recipient)
    }

    private fun createLastUsedOf(
        contact: Contact,
        savedBankAccount: SavedBankAccount?,
        savedPixKey: SavedPixKey?,
    ): LastUsed? {
        if (savedBankAccount != null) {
            val lastUsed = contact.bankAccounts?.firstOrNull {
                it == savedBankAccount
            } ?: savedBankAccount
            return LastUsed(bankAccountId = lastUsed.id)
        }
        if (savedPixKey != null) {
            return LastUsed(pixKey = savedPixKey.value)
        }
        return null
    }

    private fun invalidatePixKey(
        recipient: Contact,
        pixKey: PixKey,
    ) {
        recipient.pixKeys
            .filter { it.value == pixKey.value }
            .ifEmpty { return }
            .forEach {
                it.apply {
                    invalidated = true
                    invalidationCode = InvalidationCode.INVALID_DATA
                }
            }
        contactRepository.save(recipient)
    }

    private fun invalidateBankAccount(
        recipient: Contact,
        bankAccount: BankAccount,
        message: String,
    ) {
        recipient.bankAccounts
            .filter { it isSameBankAccount bankAccount }
            .ifEmpty { return }
            .forEach {
                it.apply {
                    invalidated = true
                    if (message == PixTransactionError.SettlementDestinationAccountTypeInvalid.code) {
                        invalidationMessage = ""
                        invalidationCode = InvalidationCode.INVALID_TYPE
                    } else {
                        invalidationMessage = message
                        invalidationCode = InvalidationCode.INVALID_DATA
                    }
                }
            }
        contactRepository.save(recipient)
    }

    private fun retrieveContact(accountId: AccountId, contactId: ContactId): Contact? {
        return try {
            contactRepository.findByIdAndAccountId(
                accountId = accountId,
                contactId = contactId,
            )
        } catch (e: ItemNotFoundException) {
            return null
        }
    }

    private infix fun SavedBankAccount.isSameBankAccount(bankAccount: BankAccount?): Boolean {
        bankAccount?.let {
            return accountDv == it.accountDv &&
                routingNo == bankAccount.routingNo &&
                bankNo == bankAccount.bankNo &&
                accountNo == bankAccount.accountNo &&
                accountType == bankAccount.accountType &&
                ispb == bankAccount.ispb
        } ?: return false
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(ContactService::class.java)
    }
}

fun BankAccount.toSavedBankAccount() =
    SavedBankAccount(
        accountType = accountType,
        bankNo = bankNo,
        routingNo = routingNo,
        accountNo = accountNo,
        accountDv = accountDv,
        ispb = ispb,
    )