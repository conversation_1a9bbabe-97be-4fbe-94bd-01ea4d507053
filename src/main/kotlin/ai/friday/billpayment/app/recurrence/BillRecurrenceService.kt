package ai.friday.billpayment.app.recurrence

import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.app.AsyncUtils.callAsync
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillIgnored
import ai.friday.billpayment.app.bill.BillPaymentScheduleCanceled
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.CreateBillResult
import ai.friday.billpayment.app.bill.CreateBillService
import ai.friday.billpayment.app.bill.CreateInvoiceRequest
import ai.friday.billpayment.app.bill.CreatePixRequest
import ai.friday.billpayment.app.bill.RecipientRequest
import ai.friday.billpayment.app.bill.RecurrenceUpdated
import ai.friday.billpayment.app.bill.ScheduleCanceledReason
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.bill.WarningCode
import ai.friday.billpayment.app.bill.calculateEffectiveDate
import ai.friday.billpayment.app.bill.duplication.PossibleDuplicate
import ai.friday.billpayment.app.bill.duplication.PossibleDuplicateBillService
import ai.friday.billpayment.app.bill.schedule.ScheduleBillService
import ai.friday.billpayment.app.contact.ContactId
import ai.friday.billpayment.app.dropFirst
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.BillInstrumentationService
import ai.friday.billpayment.app.integrations.BillRecurrenceRepository
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithBalance
import ai.friday.billpayment.app.payment.ScheduleStrategy
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.canView
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import jakarta.inject.Singleton
import java.time.LocalDate
import java.util.concurrent.CompletableFuture
import java.util.concurrent.CompletionStage
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.runBlocking
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Singleton
open class BillRecurrenceService(
    private val billRecurrenceRepository: BillRecurrenceRepository,
    private val createBillService: CreateBillService,
    private val updateBillService: UpdateBillService,
    private val billInstrumentationService: BillInstrumentationService,
    private val possibleDuplicateBillService: PossibleDuplicateBillService,
    accountRepository: AccountRepository,
    walletRepository: WalletRepository,
    private val scheduleBillService: ScheduleBillService,
    recurrenceConfiguration: RecurrenceConfiguration,
) : BaseRecurrenceService<BillRecurrence>(
    recurrenceRepository = billRecurrenceRepository,
    recurrenceConfiguration = recurrenceConfiguration,
    accountRepository = accountRepository,
    walletRepository = walletRepository,
) {

    override val LOG = LoggerFactory.getLogger(BillRecurrenceService::class.java)
    override val canStartInThePast: Boolean = false

    override fun getPossibleDuplicates(recurrence: BillRecurrence): List<PossibleDuplicate> = possibleDuplicateBillService.check(recurrence)

    override fun doCreate(recurrence: BillRecurrence, sequence: List<LocalDate>): CompletionStage<BillRecurrence> {
        return try {
            CompletableFuture.completedStage(
                sequence
                    .fold(recurrence) { aggregate, current -> generateRecurringInstance(aggregate, current, false) },
            ).also {
                billInstrumentationService.createdRecurring(recurrence = recurrence)
            }
        } catch (e: Exception) {
            LOG.error(append("recurrence", recurrence), "doCreate", e)
            CompletableFuture.failedStage(e)
        }
    }
    override fun calculateWarningCode(recurrence: BillRecurrence, firstDueDate: LocalDate): WarningCode? {
        return createBillService.calculateWarningCode(
            walletId = recurrence.walletId,
            amount = recurrence.amount,
            date = calculateEffectiveDate(firstDueDate, recurrence.billType),
        )
    }

    fun ignore(
        walletId: WalletId,
        member: Member,
        billId: BillId,
        actionSource: ActionSource,
    ): Either<IgnoreRecurrenceErrors, Unit> {
        val markers = append("wallet", walletId.value)
            .andAppend("member", member.accountId.value)
            .andAppend("bill", billId.value)
            .andAppend("actionSource", actionSource)
        LOG.info(markers, "RecurrenceServiceIgnore")
        return updateBillService.getBill(walletId = walletId, billId = billId).map { bill ->
            if (!member.canView(bill)) {
                markers.andAppend("memberCanViewBill", false)
                LOG.info(markers, "RecurrenceServiceIgnore")
                return IgnoreRecurrenceErrors.MemberNotAllowed.left()
            }
            markers.andAppend("billIsRecurrent", bill.isRecurrent())
                .andAppend("billIsIgnorable", bill.isIgnorable())
            LOG.info(markers, "RecurrenceServiceIgnore")
            when {
                !bill.isRecurrent() -> Either.Left(IgnoreRecurrenceErrors.BillIsNotRecurring)
                !bill.isIgnorable() -> Either.Left(IgnoreRecurrenceErrors.BillIsNotIgnorable)
                else -> {
                    callAsync {
                        doIgnore(bill, actionSource)
                    }
                    Either.Right(Unit)
                }
            }
        }.getOrElse {
            LOG.error(markers, "RecurrenceServiceIgnore", it)
            when (it) {
                is ItemNotFoundException -> Either.Left(IgnoreRecurrenceErrors.BillNotFound)
                else -> Either.Left(IgnoreRecurrenceErrors.ServerException(it))
            }
        }
    }

    internal open fun doIgnore(bill: Bill, actionSource: ActionSource): CompletionStage<BillRecurrence> {
        val markers = append("bill", bill.billId.value)
            .andAppend("billSource", bill.source)
        try {
            val originalRecurrence = recurrenceRepository.find(bill.getRecurrenceId(), bill.walletId)
            markers.andAppend("recurrence", originalRecurrence.id.value)
                .andAppend("billsBeforeIgnore", originalRecurrence.bills)
            LOG.info(markers, "RecurrenceServiceDoIgnore")

            val billsToIgnore = originalRecurrence.bills.dropWhile { it != bill.billId }
            markers.andAppend("billsToIgnore", billsToIgnore)
            LOG.info(markers, "RecurrenceServiceDoIgnore")

            val recurrence = billsToIgnore.fold(originalRecurrence) { acc, currentBillId ->
                doIgnoreRecurrenceBill(acc, currentBillId, actionSource)
            }
            markers.andAppend("billsAfterIgnore", originalRecurrence.bills)
            LOG.info(markers, "RecurrenceServiceDoIgnore")

            val newRecurrence = if (recurrence.status == RecurrenceStatus.ACTIVE) {
                updateBillService.getBill(walletId = bill.walletId, billId = recurrence.bills.last())
                    .fold(
                        ifLeft = { recurrence },
                        ifRight = { updateRecurrenceEndDate(recurrence, it.dueDate, actionSource) },
                    )
            } else {
                recurrence
            }
            recurrenceRepository.save(newRecurrence)
            markers.andAppend("recurrenceEndDate", newRecurrence.rule.endDate)
            LOG.info(markers, "RecurrenceServiceDoIgnore")

            return CompletableFuture.completedStage(newRecurrence)
        } catch (e: Exception) {
            LOG.error(markers, "RecurrenceServiceDoIgnore", e)
            throw e
        }
    }

    fun updateContactAlias(contactId: ContactId, alias: String) {
        runBlocking {
            billRecurrenceRepository.findByContactId(contactId).forEach {
                async(Dispatchers.Default) { updateContactAlias(it.walletId, it.id, alias) }
            }
        }
    }

    private fun updateContactAlias(walletId: WalletId, recurrenceId: RecurrenceId, alias: String) {
        val originalRecurrence = recurrenceRepository.find(recurrenceId, walletId)

        val newRecurrence = originalRecurrence.copy(
            recipientAlias = alias,
        )

        runBlocking {
            newRecurrence.bills.forEach { billId ->
                async(Dispatchers.Default) {
                    updateBillService.updateContactAlias(
                        walletId = walletId,
                        billId = billId,
                        alias = alias,
                        actionSource = ActionSource.System,
                    )
                }
            }
        }

        recurrenceRepository.save(newRecurrence)
    }

    fun updateContact(contactId: ContactId, oldBankAccount: BankAccount, newBankAccount: BankAccount) {
        runBlocking {
            billRecurrenceRepository.findByContactId(contactId).forEach {
                async(Dispatchers.Default) {
                    updateContact(it, oldBankAccount, newBankAccount)
                }
            }
        }
    }

    private fun updateContact(recurrence: BillRecurrence, oldBankAccount: BankAccount, newBankAccount: BankAccount) {
        when {
            recurrence.status == RecurrenceStatus.IGNORED -> return
            oldBankAccount != recurrence.recipientBankAccount -> return
        }

        val newRecurrence = recurrence.copy(
            recipientBankAccount = newBankAccount,
        )

        runBlocking {
            newRecurrence.bills.forEach { billId ->
                async(Dispatchers.Default) {
                    updateBillService.updateContact(
                        walletId = recurrence.walletId,
                        billId = billId,
                        oldBankAccount = oldBankAccount,
                        newBankAccount = newBankAccount,
                        actionSource = ActionSource.System,
                    )
                }
            }
        }
        recurrenceRepository.save(newRecurrence)
    }

    private fun doIgnoreRecurrenceBill(
        recurrence: BillRecurrence,
        billId: BillId,
        actionSource: ActionSource,
    ): BillRecurrence {
        val markers = append("recurrence", recurrence.id.value)
            .andAppend("bill", billId.value)
            .andAppend("actionSource", actionSource)
        updateBillService.getBill(walletId = recurrence.walletId, billId = billId).map { recurrentBill ->
            val removeFromListing = recurrentBill.isIgnorable()
            val removeFromRecurrence = !recurrentBill.isIgnorable()

            markers.andAppend("removeFromListing", removeFromListing)
                .andAppend("removeFromRecurrence", removeFromRecurrence)
            LOG.info(markers, "RecurrenceServiceDoIgnoreRecurrenceBill")

            updateBillService.publishEvent(
                recurrentBill,
                BillIgnored(
                    billId = recurrentBill.billId,
                    walletId = recurrentBill.walletId,
                    actionSource = actionSource,
                    removeFromListing = removeFromListing,
                    removeFromRecurrence = removeFromRecurrence,
                ),
            )
        }.getOrElse {
            LOG.error(markers, "RecurrenceServiceDoIgnoreRecurrenceBill", it)
        }
        return recurrence.ignoreBillId(billId)
    }

    private fun updateRecurrenceEndDate(
        recurrence: BillRecurrence,
        endDate: LocalDate,
        actionSource: ActionSource,
    ): BillRecurrence {
        return updateRecurrenceBills(recurrence.setEndDate(endDate), actionSource)
    }

    private fun updateRecurrenceBills(recurrence: BillRecurrence, actionSource: ActionSource): BillRecurrence {
        recurrence.bills.forEach { billId ->
            updateBillService.getBill(walletId = recurrence.walletId, billId = billId)
                .fold(
                    ifLeft = {
                        LOG.error(
                            append("billId", billId)
                                .and<LogstashMarker>(append("accountId", recurrence.walletId))
                                .and(append("recurrenceId", recurrence.id)),
                            "doIgnore",
                            it,
                        )
                    },
                    ifRight = { billToUpdate ->
                        updateBillService.publishEvent(
                            billToUpdate,
                            RecurrenceUpdated(
                                billId = billToUpdate.billId,
                                walletId = billToUpdate.walletId,
                                actionSource = actionSource,
                                recurrenceRule = recurrence.rule,
                            ),
                        )
                    },
                )
        }
        return recurrence
    }

    override fun generateRecurringInstance(
        recurrence: BillRecurrence,
        dueDate: LocalDate,
        isFirstRecurrenceInstance: Boolean,
    ): BillRecurrence {
        val billActionSource = if (recurrence.actionSource is ActionSource.Subscription) {
            ActionSource.SubscriptionRecurrence(
                recurrenceId = recurrence.id,
                accountId = recurrence.actionSource.accountId!!,
            )
        } else {
            ActionSource.WalletRecurrence(
                recurrenceId = recurrence.id,
                accountId = recurrence.actionSource.accountId!!,
            )
        }

        val bill = when (recurrence.billType) {
            BillType.PIX -> createPix(recurrence, dueDate, billActionSource, isFirstRecurrenceInstance)
            BillType.INVOICE -> createInvoice(recurrence, dueDate, billActionSource)
            BillType.CONCESSIONARIA, BillType.FICHA_COMPENSACAO, BillType.INVESTMENT, BillType.OTHERS, BillType.AUTOMATIC_PIX -> throw UnsupportedOperationException()
        }

        if (recurrence.actionSource is ActionSource.Subscription) {
            val wallet = walletRepository.findWallet(bill.walletId)

            scheduleBillService.schedulePayment(
                paymentWallet = wallet,
                bill = bill,
                member = wallet.founder,
                actionSource = recurrence.actionSource,
                scheduleStrategy = ScheduleStrategy.ofDueDate(),
                paymentMethodsDetail = PaymentMethodsDetailWithBalance(
                    amount = bill.amountTotal,
                    paymentMethodId = wallet.paymentMethodId,
                    calculationId = null,
                ),
            ).getOrElse {
                throw it
            }
        }

        return recurrence.plusBillId(bill.billId).setLastBillDueDate(dueDate).copy(contactId = bill.contactId)
            .also { recurrenceRepository.save(it) }
    }

    private fun createInvoice(
        recurrence: BillRecurrence,
        dueDate: LocalDate,
        source: ActionSource.WalletActionSource,
    ): Bill {
        val addInvoiceRequest = CreateInvoiceRequest(
            description = recurrence.description,
            walletId = recurrence.walletId,
            dueDate = dueDate,
            amount = recurrence.amount,
            recipient = RecipientRequest(
                id = recurrence.contactId,
                accountId = recurrence.contactAccountId,
                name = recurrence.recipientName,
                document = recurrence.recipientDocument,
                alias = recurrence.recipientAlias,
                bankAccount = recurrence.recipientBankAccount,
                qrCode = null,
            ),
            source = source,
            categoryId = recurrence.billCategoryId,
            recurrenceRule = recurrence.rule,
            contactId = recurrence.contactId,
        )
        return when (val result = createBillService.createInvoice(addInvoiceRequest)) {
            is CreateBillResult.SUCCESS -> result.bill
            else -> throw IllegalStateException("Could not create invoice. Result status $result")
        }
    }

    private fun createPix(
        recurrence: BillRecurrence,
        dueDate: LocalDate,
        source: ActionSource.WalletActionSource,
        isFirstRecurrenceBill: Boolean,
    ): Bill {
        val addPixRequest = CreatePixRequest(
            description = recurrence.description,
            dueDate = dueDate,
            amount = recurrence.amount,
            recipient = RecipientRequest(
                id = recurrence.contactId,
                accountId = recurrence.contactAccountId,
                name = recurrence.recipientName,
                document = recurrence.recipientDocument,
                alias = recurrence.recipientAlias,
                pixKey = recurrence.recipientPixKey,
                bankAccount = recurrence.recipientBankAccount,
                qrCode = null,
            ),
            recurrenceRule = recurrence.rule,
            categoryId = recurrence.billCategoryId,
            source = source,
            walletId = recurrence.walletId,
            automaticPixAuthorizationMaximumAmount = null,
            automaticPixData = null,
        )

        return when (
            val result = if (isFirstRecurrenceBill || recurrence.recipientPixKey == null) {
                createBillService.createPix(
                    addPixRequest,
                    false,
                )
            } else {
                createBillService.createPixKeyFromRecurrence(addPixRequest, recurrence)
            }
        ) {
            is CreateBillResult.SUCCESS -> result.bill
            is CreateBillResult.FAILURE.BillAlreadyExists -> throw IllegalStateException("Could not create pix. Result status $result - ${result.bill.billId.value}")
            is CreateBillResult.FAILURE.BillNotPayable -> throw IllegalStateException("Could not create pix. Result status $result - ${result.description}")
            is CreateBillResult.FAILURE.BillUnableToValidate -> throw IllegalStateException("Could not create pix. Result status $result - ${result.description}")
            is CreateBillResult.FAILURE.AlreadyPaid.WithData -> throw IllegalStateException("Could not create pix. Result status $result - ${result.description}")
            is CreateBillResult.FAILURE.AlreadyPaid.WithoutData -> throw IllegalStateException("Could not create pix. Result status $result - ${result.description}")
            is CreateBillResult.FAILURE.ServerError -> {
                LOG.error("CreatePix", result.throwable)
                throw IllegalStateException(
                    "Could not create pix. Result status $result",
                    result.throwable,
                )
            }
        }
    }

    fun cancelSubscription(recurrenceId: RecurrenceId, walletId: WalletId): CancelSubscriptionResult {
        val markers = Markers.append("recurrenceId", recurrenceId.value)
            .andAppend("walletId", walletId.value)

        val recurrence = recurrenceRepository.findOrNull(recurrenceId, walletId)

        if (recurrence == null) {
            LOG.warn(markers, "cancelSubscription")
            return CancelSubscriptionResult.RecurrenceNotFound
        }

        markers.andAppend("recurrenceStatus", recurrence.status.name)
        if (recurrence.status != RecurrenceStatus.IGNORED) {
            if (recurrence.actionSource !is ActionSource.Subscription) {
                LOG.error(markers, "cancelSubscription")
                return CancelSubscriptionResult.RecurrenceInvalid
            }

            val activeBills = recurrence.bills.mapActiveBills(walletId).getOrElse {
                LOG.error(markers, "cancelSubscription")
                return it
            }

            val billsToRemove = activeBills.dropFirst {
                it.isOverdue()
            }

            billsToRemove.forEach {
                if (it.schedule != null) {
                    updateBillService.publishEvent(
                        it,
                        BillPaymentScheduleCanceled(
                            billId = it.billId,
                            walletId = it.walletId,
                            reason = ScheduleCanceledReason.SUBSCRIPTION_CANCELED,
                            actionSource = ActionSource.System,
                            batchSchedulingId = it.schedule!!.batchSchedulingId,
                        ),
                    )
                }
            }

            activeBills.firstOrNull()
                ?.let { doIgnore(it, actionSource = ActionSource.System).toCompletableFuture().get() }
        }

        LOG.info(markers, "cancelSubscription")
        return CancelSubscriptionResult.Success
    }

    private fun List<BillId>.mapActiveBills(walletId: WalletId): Either<CancelSubscriptionResult, List<Bill>> {
        return this.map { billId ->

            val bill = updateBillService.getBill(billId = billId, walletId = walletId).getOrElse {
                return CancelSubscriptionResult.ServerError(it.message.orEmpty()).left()
            }

            if (bill.status == BillStatus.PROCESSING) {
                return CancelSubscriptionResult.BillProcessing.left()
            }

            bill
        }.filter { it.status == BillStatus.ACTIVE }.right()
    }

    fun updateAmount(
        billId: BillId,
        amount: Long,
        actionSource: ActionSource.Api,
    ): Either<Exception, Bill> {
        val markers = append("billId", billId.value)
            .andAppend("actionSource", actionSource).andAppend("amount", amount)
        LOG.info(markers, "RecurrenceService#UpdateAmount")

        return updateBillService.updateAmount(
            billId = billId,
            amount = amount,
            actionSource = actionSource,
            unscheduleValidation = UpdateBillService.UnscheduleValidation.FAIL,
        ).map { bill ->
            LOG.info(markers.andAppend("firstBillUpdated", true), "RecurrenceService#UpdateAmount")
            if (bill.isRecurrent()) {
                callAsync {
                    doUpdateAmount(
                        bill,
                        amount,
                        actionSource,
                    )
                }
            }
            bill.right()
        }.getOrElse {
            LOG.warn(markers.andAppend("firstBillUpdated", false), "RecurrenceService#UpdateAmount", it)
            it.left()
        }
    }

    internal open fun doUpdateAmount(
        bill: Bill,
        amount: Long,
        actionSource: ActionSource.Api,
    ): CompletionStage<BillRecurrence> {
        val markers = append("firstBillId", bill.billId.value).andAppend("billSource", bill.source)
            .andAppend("actionSource", actionSource).andAppend("amount", amount)
        try {
            val originalRecurrence = recurrenceRepository.find(bill.getRecurrenceId(), bill.walletId)
            val billsToUpdateAmount = originalRecurrence.bills.dropWhile { it != bill.billId }.drop(1)
            markers.andAppend("recurrence", originalRecurrence).andAppend("totalBills", billsToUpdateAmount.size)
            LOG.info(markers, "RecurrenceService#UpdateAmount")

            billsToUpdateAmount.forEach { billId ->
                LOG.info(markers.andAppend("billId", billId.value), "RecurrenceService#UpdateAmount")
                updateBillService.updateAmount(
                    billId = billId,
                    amount = amount,
                    actionSource = actionSource,
                    unscheduleValidation = UpdateBillService.UnscheduleValidation.ALLOW,
                ).mapLeft {
                    LOG.error(
                        markers.andAppend(
                            "error",
                            "billAmountNotUpdated - algumas bills não foram atualizadas na recorrencia",
                        ).andAppend("ACTION", "VERIFY"),
                        "RecurrenceService#UpdateAmount",
                        it,
                    )
                }
            }
            val updatedRecurrence = originalRecurrence.copy(amount = amount)
            recurrenceRepository.save(updatedRecurrence)
            return CompletableFuture.completedStage(updatedRecurrence)
        } catch (e: Exception) {
            LOG.warn(markers, "RecurrenceService#UpdateAmount", e)
            throw e
        }
    }

    fun ignoreSubscriptionFee(
        recurrenceId: RecurrenceId,
        walletId: WalletId,
        dueDate: LocalDate,
    ): IgnoreSubscriptionRecurrenceResult {
        val recurrence = recurrenceRepository.find(recurrenceId, walletId)

        val bill = recurrence.getIgnorableBillByDueDate(dueDate).getOrElse {
            return it
        }

        updateBillService.publishEvent(
            bill,
            BillPaymentScheduleCanceled(
                billId = bill.billId,
                walletId = bill.walletId,
                reason = ScheduleCanceledReason.SUBSCRIPTION_CANCELED,
                actionSource = ActionSource.System,
                batchSchedulingId = bill.schedule?.batchSchedulingId,
            ),
        )

        doIgnoreRecurrenceBill(recurrence, bill.billId, ActionSource.System)

        return IgnoreSubscriptionRecurrenceResult.Success
    }

    private fun BillRecurrence.getIgnorableBillByDueDate(dueDate: LocalDate): Either<IgnoreSubscriptionRecurrenceResult, Bill> {
        bills.forEach { billId ->
            val bill = updateBillService.getBill(walletId, billId).getOrElse {
                return IgnoreSubscriptionRecurrenceResult.BillNotFound.left()
            }

            if (bill.dueDate == dueDate) {
                return when (bill.status) {
                    BillStatus.PROCESSING -> IgnoreSubscriptionRecurrenceResult.BillProcessing.left()
                    BillStatus.PAID -> IgnoreSubscriptionRecurrenceResult.BillAlreadyPaid.left()
                    else -> bill.right()
                }
            }
        }
        return IgnoreSubscriptionRecurrenceResult.BillNotFound.left()
    }

    sealed class IgnoreSubscriptionRecurrenceResult : PrintableSealedClassV2() {
        data object BillProcessing : IgnoreSubscriptionRecurrenceResult()
        data object BillAlreadyPaid : IgnoreSubscriptionRecurrenceResult()
        data object BillNotFound : IgnoreSubscriptionRecurrenceResult()
        data object Success : IgnoreSubscriptionRecurrenceResult()
    }
}

@Singleton
open class BackOfficeRecurrenceService(private val billRecurrenceRepository: BillRecurrenceRepository) {
    fun ignoreRecurrences(walletId: WalletId) {
        val recurrences = billRecurrenceRepository.findByWalletId(walletId)

        recurrences.forEach { recurrence ->
            if (recurrence.status == RecurrenceStatus.ACTIVE) {
                billRecurrenceRepository.save(recurrence.copy(status = RecurrenceStatus.IGNORED))
            }
        }
    }
}

fun Bill.getRecurrenceId(): RecurrenceId {
    return when (val source = source) {
        is ActionSource.Recurrence -> source.recurrenceId
        is ActionSource.WalletRecurrence -> source.recurrenceId
        is ActionSource.SubscriptionRecurrence -> source.recurrenceId
        else -> throw IllegalStateException("Bill is not recurring")
    }
}

sealed class CancelSubscriptionResult() : PrintableSealedClassV2() {
    data object RecurrenceNotFound : CancelSubscriptionResult()
    data object RecurrenceInvalid : CancelSubscriptionResult()

    data object Success : CancelSubscriptionResult()

    data object BillProcessing : CancelSubscriptionResult()
    class ServerError(val message: String) : CancelSubscriptionResult()
}