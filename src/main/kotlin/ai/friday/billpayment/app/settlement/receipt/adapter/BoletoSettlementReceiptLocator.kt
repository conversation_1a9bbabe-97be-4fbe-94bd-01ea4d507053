package ai.friday.billpayment.app.settlement.receipt.adapter

import ai.friday.billpayment.app.payment.Transaction
import jakarta.inject.Named
import jakarta.inject.Singleton

interface BoletoSettlementReceiptLocator {
    fun get(transaction: Transaction): GetBoletoSettlementReceiptAdapter
}

@Singleton
class DefaultBoletoSettlementReceiptLocator(
    @Named("celcoinReceiptAdapter") private val celcoinReceiptAdapter: GetBoletoSettlementReceiptAdapter?,
) : BoletoSettlementReceiptLocator {

    override fun get(transaction: Transaction): GetBoletoSettlementReceiptAdapter {
        return celcoinReceiptAdapter ?: throw IllegalStateException("No BoletoSettlementReceiptAdapter found")
    }
}