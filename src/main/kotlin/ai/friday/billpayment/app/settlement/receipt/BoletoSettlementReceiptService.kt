package ai.friday.billpayment.app.settlement.receipt

import ai.friday.billpayment.app.payment.BillPaymentReceipt
import ai.friday.billpayment.app.payment.BoletoSettlementException
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.settlement.receipt.adapter.BoletoSettlementReceiptLocator
import ai.friday.billpayment.app.settlement.receipt.adapter.GetBoletoSettlementReceiptResult
import jakarta.inject.Singleton

interface BoletoSettlementReceiptService {
    fun getReceipt(transaction: Transaction): BillPaymentReceipt
}

@Singleton
class DefaultBoletoSettlementReceiptService(
    private val boletoSettlementReceiptLocator: BoletoSettlementReceiptLocator,
) : BoletoSettlementReceiptService {

    override fun getReceipt(transaction: Transaction): BillPaymentReceipt {
        val receiptAdapter = boletoSettlementReceiptLocator.get(transaction)

        when (val result = receiptAdapter.getReceipt(transaction)) {
            is GetBoletoSettlementReceiptResult.Success -> {
                return BillPaymentReceipt(
                    createdOn = result.createdOn,
                    transactionId = result.transactionId,
                    authentication = result.authentication,
                    paymentPartnerName = result.paymentPartnerName,
                )
            }

            is GetBoletoSettlementReceiptResult.Error -> {
                throw BoletoSettlementException(
                    gateway = result.gateway,
                    message = result.reason,
                    e = result.exception,
                )
            }
        }
    }
}