package ai.friday.billpayment.app.itp

import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.ExternalBankAccountService
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.chatbot.GetAccountOrganizations
import ai.friday.billpayment.app.chatbot.Organization
import ai.friday.billpayment.app.chatbot.OtherInstitutionsResponseTO
import ai.friday.billpayment.app.chatbot.PaymentOrganizationResult
import ai.friday.billpayment.app.forecast.ForecastPeriod
import ai.friday.billpayment.app.forecast.ForecastService
import ai.friday.billpayment.app.forecast.forPeriod
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.onepixpay.OnePixPayErrors
import ai.friday.billpayment.app.onepixpay.OnePixPayService
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import javax.security.auth.login.AccountNotFoundException

data class CreatePaymentIntentCommand(
    val accountId: AccountId,
    val walletId: WalletId,
    val authorizationServerId: String,
)

@Singleton
open class DefaultITPService(
    private val itpAdapter: ITPAdapter,
    private val accountService: AccountService,
    private val walletService: WalletService,
    private val forecastService: ForecastService,
    private val externalBankAccountService: ExternalBankAccountService,
    private val paymentIntentRepository: PaymentIntentRepository,
    private val notificationAdapter: NotificationAdapter,
    private val onePixPayService: OnePixPayService,
    @Property(
        name = "integrations.btg.itp.static.institutions.general-available",
    ) private val itpStaticInstitutions: Map<String, ITPStaticInstitution>,
    @Property(
        name = "integrations.btg.itp.static.institutions.alpha",
    ) private val itpStaticInstitutionsAlpha: Map<String, ITPStaticInstitution>,
) :
    ITPService {

    override fun listPaymentOrganizations(accountId: AccountId): Either<ITPServiceError, GetAccountOrganizations> {
        try {
            val account = accountService.findAccountById(accountId)

            val document = Document(account.document)

            val organizations =
                listFinancialInstitution(account.hasGroup(AccountGroup.ALPHA)).getOrElse { return it.left() }
            val otherFinancialInstitutions = organizations.map {
                OtherInstitutionsResponseTO(id = it.authorizationServerId, title = it.friendlyName)
            }

            val result = GetAccountOrganizations(
                size = 0,
                document = document,
                result = mapOf(),
                otherFinancialInstitutions = otherFinancialInstitutions,
            )

            val bankAccounts = externalBankAccountService.findAll(document).sortedByDescending { it.lastUsed }

            if (bankAccounts.isEmpty()) return result.right()

            val bankAccount = bankAccounts.firstOrNull { organizations.map { org -> org.ispb }.contains(it.bankISPB) }
            val organization = organizations.firstOrNull { it.ispb.equals(bankAccount?.bankISPB) }

            if (bankAccount == null || organization == null) return result.right()

            return result.apply {
                this.size = 1
                this.result = mapOf(
                    0 to PaymentOrganizationResult(
                        authorizationServerId = organization.authorizationServerId,
                        bankISPB = organization.ispb!!, // FIX ME
                        routingNo = bankAccount.routingNo,
                        accountNo = bankAccount.accountNo,
                        accountDv = bankAccount.accountDv,
                        accountType = bankAccount.accountType,
                        bankNo = bankAccount.bankNo,
                        institutionName = organization.friendlyName,
                    ),
                )
            }.right()
        } catch (ex: AccountNotFoundException) {
            return ITPServiceError.AccountNotFound().left()
        } catch (ex: Exception) {
            return ITPServiceError.ITPErrorWithException.ITPInternalError(exception = ex).left()
        }
    }

    private fun toAccountType(accountType: String?) = when (accountType) { // TODO - isso não deveria estar aqui!
        "CACC" -> AccountType.CHECKING
        "SVGS" -> AccountType.SAVINGS
        "SLRY" -> AccountType.SALARY
        "TRAN" -> AccountType.PAYMENT
        else -> null
    }

    override fun createPaymentIntentWithOnePixPay(
        command: CreatePaymentIntentCommand,
        bills: List<BillView>,
        useCurrentBalance: Boolean,
    ): Either<ITPServiceError, PaymentIntentId> {
        val onePixPay = onePixPayService.create(bills, useCurrentBalance).getOrElse {
            return when (it) {
                OnePixPayErrors.AtLeastOneBillRequired,
                OnePixPayErrors.OnlyActiveOrScheduledBillsAreAllowed,
                is OnePixPayErrors.SingleWalletRequired,
                -> ITPServiceError.NestedOnePixPayError(nested = it)

                OnePixPayErrors.InvalidAmount -> ITPServiceError.SufficientBalance()
            }.left()
        }

        val account = accountService.findAccountById(command.accountId)
        val authorizationServer =
            getIspbFromAuthorizationServerId(command.authorizationServerId).getOrElse { return it.left() }
        val userLastUsedBankAccount = externalBankAccountService.findLastUsed(Document(account.document))

        val paymentIntent = paymentIntentRepository.save(
            PaymentIntent(
                accountId = command.accountId,
                walletId = command.walletId,
                document = account.document,
                authorizationServerId = authorizationServer.authorizationServerId,
                authorizationServerName = authorizationServer.friendlyName,
                routingNo = userLastUsedBankAccount?.routingNo ?: 0,
                accountNo = userLastUsedBankAccount?.accountNo ?: 0,
                accountDv = userLastUsedBankAccount?.accountDv ?: "",
                bankISPB = authorizationServer.ispb,
                accountType = toAccountType(userLastUsedBankAccount?.accountType),
                bankNo = userLastUsedBankAccount?.bankNo,
                forecastPeriod = null,
                includeScheduledBillsOnly = null,
                details = PaymentIntentDetails.WithQRCode(qrCode = onePixPay.qrCode),
                status = PaymentIntentStatus.CREATED,
            ),
        )

        onePixPayService.save(onePixPay.asRequested())

        return paymentIntent.paymentIntentId.right()
    }

    override fun createPaymentIntent(
        command: CreatePaymentIntentCommand,
        forecastPeriod: ForecastPeriod,
    ): Either<ITPServiceError, PaymentIntentId> {
        try {
            val wallet = walletService.findWallet(command.walletId)

            val walletForecast = forecastService.calculateWalletBalanceForecast(wallet)
            val forecastAmountForPeriod = walletForecast.forPeriod(period = forecastPeriod)

            val hasFunds = walletForecast.amount >= forecastAmountForPeriod

            if (hasFunds) return ITPServiceError.SufficientBalance().left()
            val missingAmount = forecastAmountForPeriod - walletForecast.amount

            val account = accountService.findAccountById(command.accountId)
            val authorizationServer =
                getIspbFromAuthorizationServerId(command.authorizationServerId).getOrElse { return it.left() }
            val userLastUsedBankAccount = externalBankAccountService.findLastUsed(Document(account.document))

            val paymentIntent = paymentIntentRepository.save(
                PaymentIntent(
                    accountId = command.accountId,
                    walletId = command.walletId,
                    document = account.document,
                    authorizationServerId = authorizationServer.authorizationServerId,
                    authorizationServerName = authorizationServer.friendlyName,
                    routingNo = userLastUsedBankAccount?.routingNo ?: 0,
                    accountNo = userLastUsedBankAccount?.accountNo ?: 0,
                    accountDv = userLastUsedBankAccount?.accountDv ?: "",
                    bankISPB = authorizationServer.ispb,
                    accountType = toAccountType(userLastUsedBankAccount?.accountType),
                    bankNo = userLastUsedBankAccount?.bankNo,
                    forecastPeriod = forecastPeriod,
                    includeScheduledBillsOnly = forecastPeriod == ForecastPeriod.TODAY,
                    details = PaymentIntentDetails.WithPixKey(pixKey = walletService.walletPixKey(wallet), amount = missingAmount),
                    status = PaymentIntentStatus.CREATED,
                ),
            )
            return paymentIntent.paymentIntentId.right()
        } catch (ex: Exception) {
            return ITPServiceError.ITPErrorWithException.ITPProviderError(exception = ex).left()
        }
    }

    override fun processPaymentIntentStatusChanged(
        paymentIntentId: PaymentIntentId,
        paymentIntentStatus: PaymentIntentStatus,
    ): Either<Exception, PaymentIntent> {
        return try {
            val paymentIntent = paymentIntentRepository.find(paymentIntentId)
            paymentIntentRepository.save(paymentIntent.copy(status = paymentIntentStatus))

            if (paymentIntentStatus == PaymentIntentStatus.FAILED) {
                notificationAdapter.notifyPaymentIntentFailed(
                    accountId = paymentIntent.accountId,
                    paymentIntentId = paymentIntentId,
                )
            }
            paymentIntent.right()
        } catch (e: Exception) {
            e.left()
        }
    }

    override fun processPaymentIntentStatusChanged(
        consentId: ConsentId,
        paymentIntentStatus: PaymentIntentStatus,
    ): Either<Exception, PaymentIntent> {
        return try {
            val paymentIntent = paymentIntentRepository.findByConsentId(consentId)
            paymentIntentRepository.save(paymentIntent.copy(status = paymentIntentStatus))

            if (paymentIntentStatus == PaymentIntentStatus.FAILED) {
                notificationAdapter.notifyPaymentIntentFailed(
                    accountId = paymentIntent.accountId,
                    paymentIntentId = paymentIntent.paymentIntentId,
                )
            }
            paymentIntent.right()
        } catch (e: Exception) {
            e.left()
        }
    }

    override fun listAllStaticInstitutions(): Map<String, ITPStaticInstitution> {
        return itpStaticInstitutions + itpStaticInstitutionsAlpha.mapValues {
            it.value.copy(title = "ALPHA:${it.value.title}")
        }
    }

    override fun retrievePaymentRedirectUrl(paymentIntentId: PaymentIntentId): Either<ITPServiceError, PaymentIntentResponse> {
        return try {
            val paymentIntent = paymentIntentRepository.find(paymentIntentId)
            if (paymentIntent.status == PaymentIntentStatus.SUCCESS) return ITPServiceError.PaymentIntentNotFound().left()

            accountService.findAccountByDocumentOrNull(paymentIntent.document)
                ?: return ITPServiceError.AccountNotFound().left()

            val response = itpAdapter.createPaymentRedirectUrl(paymentIntent)
            if (response.isRight()) {
                paymentIntentRepository.save(paymentIntent.copy(consentId = response.getOrNull()!!.consentId))
            }
            return response
        } catch (ex: Exception) {
            when (ex) {
                is AccountNotFoundException -> ITPServiceError.AccountNotFound()
                is ItemNotFoundException -> ITPServiceError.PaymentIntentNotFound()
                else -> ITPServiceError.ITPErrorWithException.ITPProviderError(exception = ex)
            }.left()
        }
    }

    override fun callbackForPaymentInitiationConsent(
        state: String,
        code: String?,
        idToken: String?,
        error: String?,
        errorDescription: String?,
    ): Either<ITPServiceError, Unit> {
        return itpAdapter.callbackForPaymentInitiationConsent(
            state = state,
            code = code,
            idToken = idToken,
            error = error,
            errorDescription = errorDescription,
        )
    }

    private fun getIspbFromAuthorizationServerId(authorizationServerId: String): Either<ITPServiceError, Organization> {
        val financialInstitutions = listFinancialInstitution().getOrElse {
            return it.left()
        }

        val institution = financialInstitutions.find { it.authorizationServerId == authorizationServerId }

        if (institution?.ispb != null) {
            return institution.right()
        }

        return ITPServiceError.ISPBNotFound().left()
    }

    private fun listFinancialInstitution(includeAlpha: Boolean = true): Either<ITPServiceError, List<Organization>> {
        val institutions = itpAdapter.listFinancialInstitutions().getOrElse { return it.left() }

        val filteredInstitutions = if (includeAlpha) {
            listAllStaticInstitutions()
        } else {
            itpStaticInstitutions
        }

        if (filteredInstitutions.isEmpty()) return institutions.right()

        return institutions.filter { filteredInstitutions.containsKey(it.authorizationServerId) }.map {
            Organization(
                authorizationServerId = it.authorizationServerId,
                clearingCode = it.clearingCode,
                ispb = it.ispb,
                friendlyName = filteredInstitutions[it.authorizationServerId]!!.title,
            )
        }.right()
    }
}

data class PaymentIntentResponse(
    val paymentIntentId: PaymentIntentId,
    val url: String,
    val consentId: ConsentId,
)