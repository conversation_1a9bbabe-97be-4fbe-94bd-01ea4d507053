package ai.friday.billpayment.app.statement

import ai.friday.billpayment.adapters.arbi.ArbiAccountMissingPermissionsException
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.PaymentMethodType
import ai.friday.billpayment.app.account.isBetaGroup
import ai.friday.billpayment.app.balance.Balance
import ai.friday.billpayment.app.banking.BankStatement
import ai.friday.billpayment.app.banking.BankStatementItemFlow
import ai.friday.billpayment.app.banking.BankStatementItemType
import ai.friday.billpayment.app.banking.DefaultBankStatementItem
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.integrations.AccountStatementAdapter
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.InternalBankRepository
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.Statement
import ai.friday.billpayment.app.integrations.StatementItemConverter
import ai.friday.billpayment.app.notification.ByteArrayWithNameAndType
import ai.friday.billpayment.app.payment.MultiplePaymentMethodsDetail
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithBalance
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithCreditCard
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithExternalPayment
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.wallet.BillPermission
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.dateFormatBR
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import datadog.trace.api.Trace
import io.micronaut.context.annotation.Property
import io.micronaut.http.MediaType
import io.via1.communicationcentre.app.message.ForwardMessageException
import jakarta.inject.Singleton
import java.security.MessageDigest
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.format.DateTimeFormatter
import kotlin.math.abs
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Singleton
open class StatementService(
    private val internalBankRepository: InternalBankRepository,
    private val billRepository: BillRepository,
    private val walletService: WalletService,
    private val accountService: AccountService,
    private val notificationAdapter: NotificationAdapter,
    private val statementItemConverter: StatementItemConverter,
    private val accountStatementAdapter: AccountStatementAdapter,
    private val sqsMessagePublisher: MessagePublisher,
    private val walletBillCategoryService: PFMWalletCategoryService,
    @Property(name = "sqs.statementQueueName") private val queueName: String,
    @Property(name = "integrations.arbi.inscricao") private val selfCNPJArbiAccount: String,
    @Property(name = "integrations.arbi.contaLiquidacao") private val contaLiquidacao: String,
    @Property(name = "tenant.displayName") private val displayName: String,
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    fun findDepositsByDate(
        walletId: WalletId,
        startDate: LocalDate,
        endDate: LocalDate,
    ): Either<StatementError, List<StatementItem>> {
        return findAllStatementsByDate(walletId, startDate, endDate).getOrElse {
            return it.left()
        }.statementItems.mapNotNull { statementItem ->
            if (statementItem.flow == BankStatementItemFlow.CREDIT) {
                statementItem
            } else {
                null
            }
        }.right()
    }

    @Trace
    open fun findAllCredits(walletId: WalletId, walletMember: Member): Either<StatementError, List<DefaultBankStatementItem>> {
        try {
            if (!walletMember.permissions.viewBalance) {
                return emptyList<DefaultBankStatementItem>().right()
            }
            val wallet = walletService.findWalletOrNull(walletId) ?: return StatementError.WalletNotFound.left()
            val (_, rawCredits) = internalBankRepository.findAllBankStatementCredits(
                wallet.paymentMethodId,
                LocalDate.EPOCH,
                getLocalDate(),
            ).partitionByAbatementItems(wallet.founder.accountId)
            val (validCredits, _) = rawCredits.partition { it.counterpartDocument != "***********" && it.counterpartName.isNotBlank() || it.type == BankStatementItemType.INVESTMENT_REDEMPTION }

            logger.info(
                append("wallet", walletId.value)
                    .andAppend("accountId", walletMember.accountId.value)
                    .andAppend("paymentMethodId", wallet.paymentMethodId.value)
                    .andAppend("validCredits", validCredits.size),
                "StatementService#findAllCredits",
            )

            return validCredits.map {
                if (it.type == BankStatementItemType.INVESTMENT_REDEMPTION) {
                    it.copy(counterpartDocument = wallet.founder.document) // TODO - remover isso depois que o front conhecer o tipo e não precisar mais do documento valido
                } else {
                    it
                }
            }.right()
        } catch (ex: Exception) {
            return StatementError.ServerError.left()
        }
    }

    fun findAllStatementsByDate(
        walletId: WalletId,
        startDate: LocalDate,
        endDate: LocalDate,
    ): Either<StatementError, UserStatement> {
        val wallet = walletService.findWalletOrNull(walletId) ?: return StatementError.WalletNotFound.left()
        val balance = accountService.findAccountPaymentMethodByIdAndAccountId(wallet.paymentMethodId, wallet.founder.accountId).method as InternalBankAccount
        return findAllStatementsByDate(wallet, balance, startDate, endDate)
    }

    private fun findAllStatementsByDate(
        wallet: Wallet,
        balance: InternalBankAccount,
        startDate: LocalDate,
        endDate: LocalDate,
    ): Either<StatementError, UserStatement> {
        try {
            val categories = walletBillCategoryService.findWalletCategories(wallet.id)
                .associate { it.categoryId to it.name }

            val bankStatement = accountStatementAdapter.getStatement(
                accountNo = balance.buildFullAccountNumber(),
                document = Document(wallet.founder.document),
                initialDate = startDate,
                endDate = endDate,
            ).ignoreFridayCreditMovements(wallet.founder.accountId)

            val creditItems = bankStatement.items
                .filter { it.flow == BankStatementItemFlow.CREDIT }
                .map { it.toItem() }

            val paidBillItems = billRepository.getPaidBills(wallet.id, startDate.atStartOfDay(brazilTimeZone), endDate.atTime(LocalTime.MAX).atZone(brazilTimeZone))
                .removeCreditCardPayments()
                .removeOpenFinancePayments()
                .map { it.toItem(categories) }

            var currentBalance = bankStatement.initialBalance
            val items = (paidBillItems + creditItems)
                .sortedBy { it.postedAt }
                .map {
                    val absoluteAmount = abs(it.amount)

                    currentBalance += when (it.flow) {
                        BankStatementItemFlow.CREDIT -> Balance(absoluteAmount)
                        BankStatementItemFlow.DEBIT -> Balance(-absoluteAmount)
                    }

                    it.copy(balance = currentBalance)
                }

            return UserStatement(
                walletId = wallet.id,
                startDate = startDate,
                initialBalance = bankStatement.initialBalance,
                statementItems = items,
                endDate = endDate,
                finalBalance = bankStatement.finalBalance,
            ).right()
        } catch (ex: ItemNotFoundException) {
            logger.error(append("exceptionType", "ResourceNotFoundException"), "StatementService#findAllStatementsByDate", ex)
            return StatementError.WalletNotFound.left()
        } catch (ex: ArbiAccountMissingPermissionsException) {
            logger.error(append("exceptionType", "ArbiAccountMissingPermissionsException"), "StatementService#findAllStatementsByDate", ex)
            return StatementError.AccountMissingPermissionError.left()
        } catch (ex: Exception) {
            logger.error(append("exceptionType", "Exception"), "StatementService#findAllStatementsByDate", ex)
            return StatementError.ServerError.left()
        }
    }

    private fun DefaultBankStatementItem.toItem(balance: Balance? = null): StatementItem {
        return StatementItem(
            id = operationNumber.toSha256(),
            amount = amount,
            counterPartName = counterpartName,
            postedAt = lastUpdate?.toLocalDateTime() ?: date.atStartOfDay(),
            description = description,
            flow = flow,
            balance = balance ?: Balance(0),
            category = null,
        )
    }

    private fun BillView.toItem(categories: Map<PFMCategoryId, String>, balance: Balance? = null): StatementItem {
        return StatementItem(
            id = billId.value.toSha256(),
            amount = amountTotal,
            counterPartName = payee,
            postedAt = paidDate!!,
            description = billDescription,
            flow = BankStatementItemFlow.DEBIT,
            balance = balance ?: Balance(0),
            category = categoryId?.let { categories.getOrDefault(it, null) },
        )
    }

    private fun String.toSha256(): String {
        val digest = MessageDigest.getInstance("SHA-256")
        val hashBytes = digest.digest(this.toByteArray())
        return hashBytes.joinToString("") { "%02x".format(it) }
    }

    private fun BankStatement.ignoreFridayCreditMovements(accountId: AccountId): BankStatement {
        val (_, regularItems) = this.items.partitionByAbatementItems(accountId)

        return BankStatement(
            items = regularItems,
            initialBalance = this.initialBalance,
            finalBalance = Balance(this.finalBalance.amount),
        )
    }

    private fun List<DefaultBankStatementItem>.partitionByAbatementItems(accountId: AccountId): Pair<List<DefaultBankStatementItem>, List<DefaultBankStatementItem>> {
        val account = accountService.findAccountById(accountId)
        val isSelfAccount = account.document == selfCNPJArbiAccount

        // Note: pra quando é um reembolso. Esse crédito vem pela conta liquidação, mas não é um crédito de fato a ser contabilizado.
        // Mas se for um cashin feito por cartão, deve ser contabilizado
        val (abatementItems, regularItems) = this.partition {
            it.flow == BankStatementItemFlow.CREDIT && !isSelfAccount && it.counterpartDocument == selfCNPJArbiAccount && it.isSettlementAccount()
        }

        return Pair(abatementItems, regularItems)
    }

    private fun DefaultBankStatementItem.isSettlementAccount(): Boolean {
        return contaLiquidacao.contains(this.documentNumber) || (this.counterpartAccountNo?.let { contaLiquidacao.contains(it) } ?: false)
    }

    fun sendStatementByEmail(
        accountId: AccountId,
        walletId: WalletId,
        startDate: LocalDate,
        endDate: LocalDate,
    ): Either<StatementError, UserStatement> {
        try {
            val account = accountService.findAccountById(accountId)

            fun isFullMonth(startDate: LocalDate, endDate: LocalDate) =
                startDate.month == endDate.month && startDate.year == endDate.year && startDate.dayOfMonth == 1 && endDate.plusDays(
                    1,
                ).dayOfMonth == 1

            val fileName = if (isFullMonth(startDate, endDate)) {
                "${startDate.year}_${startDate.month.value}"
            } else {
                "de_${startDate.year}_${startDate.month.value}_${startDate.dayOfMonth}_ate_${endDate.year}_${endDate.month.value}_${endDate.dayOfMonth}"
            }

            val periodMessage = "${startDate.format(dateFormatBR)} a ${endDate.format(dateFormatBR)}"

            val wallet = walletService.findWalletOrNull(walletId) ?: return StatementError.WalletNotFound.left()
            val balance = accountService.findAccountPaymentMethodByIdAndAccountId(wallet.paymentMethodId, wallet.founder.accountId).method as InternalBankAccount
            val founderAccount = accountService.findAccountById(wallet.founder.accountId)

            val userStatement = findAllStatementsByDate(wallet, balance, startDate, endDate).getOrElse {
                return it.left()
            }

            val statement = Statement(
                name = founderAccount.name,
                walletName = walletService.findWallet(walletId).name,
                bankAccount = balance,
                startDate = startDate,
                endDate = endDate,
                created = getZonedDateTime().toLocalDateTime(),
                items = userStatement.statementItems,
                initialBalance = userStatement.initialBalance,
                finalBalance = userStatement.finalBalance,
                initialBalanceDate = startDate.minusDays(1),
                finalBalanceDate = endDate,
            )

            notificationAdapter.notifyAccountStatement(
                emailAddress = account.emailAddress,
                name = account.name,
                files = listOfNotNull(
                    ByteArrayWithNameAndType(
                        fileName = "Extrato_${displayName}_$fileName.csv",
                        mediaType = MediaType.TEXT_CSV,
                        data = statementItemConverter.convertToCsv(statement.items).toByteArray(),
                    ),
                    ByteArrayWithNameAndType(
                        fileName = "Extrato_${displayName}_$fileName.pdf",
                        mediaType = MediaType.APPLICATION_PDF,
                        data = statementItemConverter.convertToPdf(statement),
                    ),
                    if (account.isBetaGroup()) {
                        ByteArrayWithNameAndType(
                            fileName = "Extrato_${displayName}_$fileName.ofx",
                            mediaType = "application/ofx",
                            data = statementItemConverter.convertToOfx(statement),
                        )
                    } else {
                        null
                    },
                ),
                periodMessage = periodMessage,
            )
            return userStatement.right()
        } catch (e: ForwardMessageException) {
            return StatementError.FailedToSendEmail.left()
        } catch (e: Exception) {
            LOG.error("StatementService#sendStatementByEmail", e)
            return StatementError.ServerError.left()
        }
    }

    fun requestStatement(
        accountId: AccountId,
        walletId: WalletId,
        startDate: LocalDate,
        endDate: LocalDate,
    ): Either<StatementError, Unit> {
        try {
            val wallet = walletService.findWallet(walletId)
            val walletMember = wallet.getActiveMember(accountId)

            if (walletMember.permissions.viewBills != BillPermission.ALL_BILLS) {
                return StatementError.NotAllowed.left()
            }

            sqsMessagePublisher.sendMessage(
                queueName = queueName,
                body = RequestStatementMessageTO(
                    accountId = accountId.value,
                    walletId = wallet.id.value,
                    startDate = startDate.format(DateTimeFormatter.ISO_DATE),
                    endDate = endDate.format(DateTimeFormatter.ISO_DATE),
                ),
            )

            return Unit.right()
        } catch (e: NoSuchElementException) {
            return StatementError.NotAllowed.left()
        } catch (e: Exception) {
            LOG.error("StatementService#requestStatement", e)
            return StatementError.ServerError.left()
        }
    }

    fun getWalletPaymentMethodCreation(walletId: WalletId): Either<StatementError, LocalDate> {
        return try {
            val wallet = walletService.findWallet(walletId)
            val accountPaymentMethod = accountService.findAccountPaymentMethodByIdAndAccountId(
                accountId = wallet.founder.accountId,
                accountPaymentMethodId = wallet.paymentMethodId,
            )
            accountPaymentMethod.created!!.toLocalDate().right()
        } catch (e: Exception) {
            StatementError.ServerError.left()
        }
    }

    private fun List<BillView>.removeOpenFinancePayments(): List<BillView> {
        return this.filter { it.source !is ActionSource.OpenFinance }
    }

    private fun List<BillView>.removeCreditCardPayments(): List<BillView> {
        return this.filter {
            when (it.paymentDetails) {
                is MultiplePaymentMethodsDetail -> {
                    it.paymentDetails.methods.find { paymentMethod ->
                        paymentMethod.methodType() == PaymentMethodType.CREDIT_CARD
                    } == null
                }

                is PaymentMethodsDetailWithBalance -> true
                is PaymentMethodsDetailWithCreditCard -> false
                is PaymentMethodsDetailWithExternalPayment -> false
                null -> false
            }
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(StatementService::class.java)
    }
}

sealed class StatementError : Exception() {
    data object WalletNotFound : StatementError()
    data object AccountMissingPermissionError : StatementError()
    data object ServerError : StatementError()
    data object NotAllowed : StatementError()
    data object FailedToSendEmail : StatementError()
}

data class UserStatement(
    val walletId: WalletId,
    val startDate: LocalDate,
    val initialBalance: Balance,
    val statementItems: List<StatementItem>,
    val endDate: LocalDate,
    val finalBalance: Balance,
)

data class StatementItem(
    val id: String,
    val amount: Long,
    val counterPartName: String,
    val postedAt: LocalDateTime,
    val description: String,
    val flow: BankStatementItemFlow,
    val balance: Balance,
    val category: String?,
)

data class RequestStatementMessageTO(
    val accountId: String,
    val walletId: String,
    val startDate: String,
    val endDate: String,
)