package ai.friday.billpayment.app.notification

import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.integrations.NotificationService
import ai.friday.billpayment.app.integrations.NotifyService
import ai.friday.billpayment.app.wallet.BillPermission
import io.via1.communicationcentre.app.notification.NotificationFormatter
import jakarta.inject.Singleton

@Singleton
open class EmailNotProcessedNotificationService(
    private val notificationService: NotificationService,
    private val templatesConfiguration: TemplatesConfiguration,
) : NotifyService<EmailNotProcessedNotificationRequest> {
    override fun notify(request: EmailNotProcessedNotificationRequest) = with(request) {
        val viewAllBillsMembers = members.filter { it.permissions.viewBills == BillPermission.ALL_BILLS }

        notificationService.notifyMembers(viewAllBillsMembers, NotificationType.EMAIL_NOT_PROCESSED) { it ->
            WhatsappNotification(
                receiver = MobilePhone(it.mobilePhone),
                accountId = it.accountId,
                template = NotificationTemplate(value = templatesConfiguration.whatsappTemplates.postalBoxAddManualReview),
                configurationKey = "postal-box-add-manual-review",
                parameters = buildList {
                    add(sender.value)
                    add(walletName)
                    add(NotificationFormatter.buildFormattedDateTime(dateTime))
                    add(NotificationFormatter.sanitize(subject.take(40)))
                },
            )
        }
    }
}