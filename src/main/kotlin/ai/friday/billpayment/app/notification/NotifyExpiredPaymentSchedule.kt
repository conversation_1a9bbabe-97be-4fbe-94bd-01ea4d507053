package ai.friday.billpayment.app.notification

import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.bill.BillPaymentScheduleCanceled
import ai.friday.billpayment.app.bill.ScheduleCanceledReason
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.NotificationService
import ai.friday.billpayment.app.wallet.WalletService
import arrow.core.getOrElse
import io.via1.communicationcentre.app.notification.NotificationFormatter
import jakarta.inject.Singleton

interface NotifyExpiredPaymentSchedule {
    fun notify(scheduleCanceled: BillPaymentScheduleCanceled)
}

@Singleton
open class ExpiredBillScheduleNotificationService(
    private val walletService: WalletService,
    private val billEventRepository: BillEventRepository,
    private val notificationService: NotificationService,
    private val templatesConfiguration: TemplatesConfiguration,
) : NotifyExpiredPaymentSchedule {

    override fun notify(scheduleCanceled: BillPaymentScheduleCanceled) {
        if (scheduleCanceled.reason != ScheduleCanceledReason.EXPIRATION) return

        val bill = billEventRepository.getBillById(scheduleCanceled.billId).getOrElse { throw IllegalStateException() }

        with(bill) {
            val wallet = walletService.findWallet(walletId)
            val members = wallet.getMembersCanView(this)

            notificationService.notifyMembers(
                members,
                NotificationType.BILL_SCHEDULE_EXPIRED,
            ) { account ->
                WhatsappNotification(
                    receiver = MobilePhone(account.mobilePhone),
                    accountId = account.accountId,
                    template = NotificationTemplate(templatesConfiguration.whatsappTemplates.walletApprovedPaymentCancelled),
                    configurationKey = "wallet-approved-payment-cancelled",
                    parameters = buildList {
                        add(getPayee())
                        add(NotificationFormatter.buildFormattedDueDate(effectiveDueDate))
                        add(NotificationFormatter.buildFormattedAmount(amountTotal))
                        add(wallet.name)
                    },
                    buttonWhatsAppParameter = buildWalletBillButtonPath(wallet.id, billId),
                )
            }
        }
    }
}