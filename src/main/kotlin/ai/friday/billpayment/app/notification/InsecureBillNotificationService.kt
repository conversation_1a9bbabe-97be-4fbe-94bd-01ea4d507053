package ai.friday.billpayment.app.notification

import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.integrations.NotificationService
import ai.friday.billpayment.app.integrations.NotifyService
import io.via1.communicationcentre.app.notification.NotificationFormatter
import jakarta.inject.Singleton

@Singleton
open class InsecureBillNotificationService(
    private val notificationService: NotificationService,
    private val templatesConfiguration: TemplatesConfiguration,
) : NotifyService<InsecureBillNotificationRequest> {

    override fun notify(request: InsecureBillNotificationRequest) = with(request) {
        notificationService.notifyMembers(members, NotificationType.INSECURE_BILL) {
            WhatsappNotification(
                receiver = MobilePhone(msisdn = it.mobilePhone),
                accountId = it.accountId,
                template = NotificationTemplate(value = templatesConfiguration.whatsappTemplates.mailboxBillInsecure),
                configurationKey = "mailbox-bill-insecure",
                parameters = buildList {
                    add(NotificationFormatter.sanitize(subject.take(40)))
                },
            )
        }
    }
}