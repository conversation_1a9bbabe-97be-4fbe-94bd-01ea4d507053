package ai.friday.billpayment.app.notification

import ai.friday.billpayment.adapters.lock.onePixPayNotification
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.ChatbotType
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.account.isBetaGroup
import ai.friday.billpayment.app.account.isLegalPerson
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.bill.schedule.ScheduleBillService
import ai.friday.billpayment.app.chatbot.ChatbotNotificationService
import ai.friday.billpayment.app.integrations.BillService
import ai.friday.billpayment.app.integrations.FindBillService
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.OnePixPayInstrumentation
import ai.friday.billpayment.app.message.QueueMessageBatch
import ai.friday.billpayment.app.onepixpay.OnePixPayService
import ai.friday.billpayment.app.usage.MAX_MONTHLY_PAYMENT_LIMIT_BASIC_ACCOUNT
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.app.wallet.findAuthorOrNull
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import io.micronaut.context.annotation.Property
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Named
import jakarta.inject.Singleton
import java.time.Duration
import java.time.ZonedDateTime
import java.time.temporal.ChronoUnit
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
open class BillComingDueService(
    private val findBillService: FindBillService,
    private val billService: BillService,
    private val walletService: WalletService,
    private val notificationAdapter: NotificationAdapter,
    private val notificationHintService: NotificationHintService,
    private val accountService: AccountService,
    private val onePixPayService: OnePixPayService,
    private val onePixPayInstrumentation: OnePixPayInstrumentation,
    private val scheduleBillService: ScheduleBillService,
    @Named(onePixPayNotification) private val onePixPayLock: InternalLock,
    private val messagePublisher: MessagePublisher,
    private val chatBotNotificationService: ChatbotNotificationService,

    @Property(name = "sqs.queues.notifyBillComingDue") private val billComingDueExecutionQueueName: String,
) {

    @field:Property(name = "features.filterAccountsThatReceiveNotification")
    var filterAccountsThatReceiveNotification: Boolean = false

    open fun addWalletsOnExecutionQueue(notifyBillComingDueType: NotifyBillComingDueType) {
        val messages = findBillService.findAllWalletsThatReceiveNotification().map { wallet ->
            onePixPayInstrumentation.requestedToVerifyBillsComingDueForWallet(wallet)

            val message = NotifyBillComingDueMessage(
                walletId = wallet.id,
                type = notifyBillComingDueType,
            )
            getObjectMapper().writeValueAsString(message)
        }

        messagePublisher.sendMessageBatch(
            messageBatch = QueueMessageBatch(
                queueName = billComingDueExecutionQueueName,
                messages = messages,
                delaySeconds = null,
            ),
        )
    }

    open fun findBillsComingDueToNotify(walletId: WalletId): BillsComingDueLists {
        val markers = Markers.append("walletId", walletId.value)

        val (billsComingDueToday, todayOverdueBills) = findBillService.findBillsComingDue(walletId).filter {
            !it.isTriPix()
        }.partition { !it.isOverdueToday() }
        markers.andAppend("billsComingDueSize", billsComingDueToday.size)

        val (todaySubscriptionFee, othersBillsComingDueToday) = billsComingDueToday.partition { it.subscriptionFee }

        val (userScheduledBillsDueToday, notScheduledBillsDueToday) = othersBillsComingDueToday.partition { it.isPaymentScheduled() }
        markers.andAppend("scheduledBillsSize", userScheduledBillsDueToday.size)
            .andAppend("notScheduledBillsSize", notScheduledBillsDueToday.size)
        logger.debug(markers, "NotifyBillComingDueJob")

        val otherBillsIds = (userScheduledBillsDueToday + notScheduledBillsDueToday + todaySubscriptionFee).map { it.billId }.toSet()

        val (pendingSubscriptions, userScheduledBillsDueNotToday) = scheduleBillService.getOrderedScheduledBills(
            walletId = walletId,
            scheduledDate = getLocalDate(),
        ).filter {
            it.billId !in otherBillsIds
        }.map {
            findBillService.find(walletId, it.billId)
        }.partition {
            it.subscriptionFee
        }

        val overdueBills = findBillService.findOverdueBills(walletId) + todayOverdueBills
        return BillsComingDueLists(
            userScheduledBills = userScheduledBillsDueToday + userScheduledBillsDueNotToday,
            notScheduledBillsDueToday = notScheduledBillsDueToday,
            subscriptionsBills = todaySubscriptionFee + pendingSubscriptions,
            overdueNotScheduledBills = overdueBills.filter { !it.isPaymentScheduled() },
        )
    }

    open fun executeChatBotAiRegular(walletId: WalletId) {
        val wallet = walletService.findWallet(walletId)
        val founderAccount = accountService.findAccountById(wallet.founder.accountId)
        val chatbotType = accountService.getChatbotType(founderAccount)

        val markers = Markers.append("walletId", walletId.value)
            .andAppend("founderAccountId", founderAccount.accountId.value)
            .andAppend("founderGroups", founderAccount.configuration.groups)
            .andAppend("isBetaUser", founderAccount.isBetaGroup())

        val billsToNotify = findBillsComingDueToNotify(walletId) // TODO - tem que achar as contas WAITING_APPROVAL
        val shouldNotifyBillsComingDue = billsToNotify.hasBillsToResolveToday() && (billsToNotify.notScheduledBillsDueToday.isNotEmpty() || billsToNotify.hasScheduledBillsWaitingFunds())

        if (shouldNotifyBillsComingDue) {
            val totalBillToNotify = billsToNotify.notScheduledBillsDueToday + billsToNotify.userScheduledBills + billsToNotify.subscriptionsBills

            markers.andAppend("billsToNotifySize", totalBillToNotify.size)
            markers.andAppend("subscriptionBillsSize", billsToNotify.subscriptionsBills.size)

            logger.info(markers, "executeChatBotAiRegular")

            publishBillComingDueChatbotNotification(
                wallet = wallet,
                account = founderAccount,
                bills = totalBillToNotify,
                lastWarn = false,
                hint = null,
                chatbotType = chatbotType,
            )
        } else {
            onePixPayInstrumentation.decidedNotToNotifyBillsComingDue(wallet, founderAccount, billsToNotify)
        }
    }

    open fun executeRegular(walletId: WalletId) {
        val billsToNotify = findBillsComingDueToNotify(walletId)
        val wallet = walletService.findWallet(walletId)
        val founderAccount = accountService.findAccountById(wallet.founder.accountId)
        val markers = Markers.append("walletId", walletId.value)
        if (accountService.getChatbotType(founderAccount).checkHasAIChatbotEnabled()) {
            return
        }

        billsToNotify.notScheduledBillsDueToday
            .filter { bill -> !bill.isBillComingDueMessageSent }
            .forEach { bill ->
                try {
                    publishBillComingDueEvent(bill)
                } catch (e: Exception) {
                    logger.error(
                        Markers.append("billId", bill.billId).andAppend("walletId", walletId.value),
                        "NotifyBillComingDueJob",
                        e,
                    )
                }
            }

        markers.andAppend("notScheduledBills", billsToNotify.notScheduledBillsDueToday)
            .andAppend("todaySubscriptionFee", billsToNotify.subscriptionsBills)

        if (billsToNotify.userScheduledBills.isEmpty() && billsToNotify.notScheduledBillsDueToday.isNotEmpty()) {
            try {
                val scheduledBills = scheduleBillService.getOrderedScheduledBills(
                    walletId = walletId,
                    scheduledDate = getLocalDate(),
                )
                markers.andAppend("scheduledBillsSize", scheduledBills.size)

                val userScheduledOverdueBills = scheduledBills.map {
                    findBillService.find(walletId, it.billId)
                }.filter {
                    it.isOverdue && !it.subscriptionFee
                }
                val isLegalPerson = founderAccount.isLegalPerson()
                markers.andAppend("userScheduledOverdueBills", userScheduledOverdueBills.size).andAppend("isLegalPerson", isLegalPerson)

                logger.info(markers, "NotifyOnePixPayJobWrapper")
                if (userScheduledOverdueBills.isEmpty() && !isLegalPerson) {
                    publishOnePixPay(
                        wallet = wallet,
                        bills = billsToNotify.notScheduledBillsDueToday + billsToNotify.subscriptionsBills,
                    )
                }
            } catch (e: Exception) {
                logger.error(
                    markers,
                    "NotifyOnePixPayJobWrapper",
                    e,
                )
            }
        }
    }

    open fun executeLastWarn(walletId: WalletId) {
        val offset = 2L
        val nextHourLocalTime = getZonedDateTime().truncatedTo(ChronoUnit.HOURS).plusHours(offset).toLocalTime()

        val comingDueBills = findBillService.findBillsComingDue(walletId)
        val expiringBills = comingDueBills
            .filter { it.paymentLimitTime.hour == nextHourLocalTime.hour && !it.isTriPix() }
            .sortedBy { it.paymentLimitTime }

        val billViews = expiringBills.takeWhile { it.paymentLimitTime == expiringBills.firstOrNull()?.paymentLimitTime }

        val markers = Markers.append("WalletId", walletId).andAppend("hour", nextHourLocalTime.hour)

        notifyLastWarn(walletId, billViews, comingDueBills, markers)
    }

    open fun executeLastWarnRestOfDay(walletId: WalletId) {
        val offset = 2L
        val nextHourLocalTime = getZonedDateTime().truncatedTo(ChronoUnit.HOURS).plusHours(offset).toLocalTime()

        val comingDueBills = findBillService.findBillsComingDue(walletId)
        val expiringBills = comingDueBills
            .filter { it.paymentLimitTime.hour >= nextHourLocalTime.hour && !it.isTriPix() }
            .sortedBy { it.paymentLimitTime }

        val markers = Markers.append("WalletId", walletId).andAppend("hour", nextHourLocalTime.hour)

        notifyLastWarn(walletId, expiringBills, comingDueBills, markers)
    }

    private fun notifyLastWarn(
        walletId: WalletId,
        billViews: List<BillView>,
        comingDueBills: List<BillView>,
        markers: LogstashMarker,
    ) {
        try {
            val wallet = walletService.findWallet(walletId)
            val founderAccount = accountService.findAccountById(wallet.founder.accountId)

            if (billViews.isEmpty()) {
                onePixPayInstrumentation.decidedNotToNotifyBillsComingDue(wallet, founderAccount, comingDueBills)
                return
            }

            val hint = if (billViews.any { it.billType.isBoleto() }) {
                notificationHintService.getBillComingDueLastWarn(walletId = wallet.id, founder = wallet.founder)
            } else {
                null
            }

            val chatbotType = accountService.getChatbotType(founderAccount)

            markers.andAppend("chatbotType", chatbotType)

            publishBillComingDueChatbotNotification(
                wallet = wallet,
                account = founderAccount,
                bills = billViews,
                lastWarn = true,
                hint = hint,
                chatbotType = chatbotType,
            )
            logger.info(
                markers.andAppend("notifiedBy", "chatbotAI"),
                "NotifyBillComingDueLastWarn",
            )
            return
        } catch (e: Exception) {
            logger.error(Markers.append("BillId", billViews.first().billId), "NotifyBillComingDueLastWarn", e)
        }
    }

    open fun executeOverdueYesterday(walletId: WalletId) {
        val yesterday = getZonedDateTime().minusDays(1)

        findBillService.findOverdueBills(walletId = walletId)
            .filter { it.effectiveDueDate.year == yesterday.year && it.effectiveDueDate.dayOfYear == yesterday.dayOfYear && !it.isPaymentScheduled() }
            .distinctBy { it.walletId }
            .forEach { bill ->
                try {
                    val wallet = walletService.findWallet(bill.walletId)
                    logger.debug(Markers.append("WalletId", bill.walletId), "NotifyBillOverdueYesterday")
                    val (founder, otherMembers) = wallet.getMembersCanSchedule(bill).partition {
                        it.accountId == wallet.founder.accountId
                    }
                    val hint = if (bill.billType.isBoleto()) {
                        notificationHintService.getBillOverdueYesterday(walletId = wallet.id, founder = wallet.founder)
                    } else {
                        null
                    }
                    notificationAdapter.notifyBillOverdueYesterday(
                        members = otherMembers,
                        walletName = wallet.name,
                        walletId = wallet.id,
                        hint = null,
                    )
                    notificationAdapter.notifyBillOverdueYesterday(
                        members = founder,
                        walletName = wallet.name,
                        walletId = wallet.id,
                        hint = hint,
                    )
                } catch (e: Exception) {
                    logger.error(Markers.append("BillId", bill.billId), "NotifyBillOverdueYesterday", e)
                }
            }
    }

    private fun BillView.isOverdueToday() = when (billType) {
        BillType.CONCESSIONARIA, BillType.FICHA_COMPENSACAO, BillType.INVESTMENT -> isOverdue
        BillType.INVOICE, BillType.PIX, BillType.OTHERS, BillType.AUTOMATIC_PIX -> false
    }

    @NewSpan
    open fun publishOnePixPay(wallet: Wallet, bills: List<BillView>) {
        val markers = Markers.append("walletId", wallet.id.value).andAppend("bills", bills)
        val lock = onePixPayLock.acquireLock(wallet.id.value) ?: return

        try {
            val founderAccount = accountService.findAccountById(wallet.founder.accountId)

            markers.andAppend("accountId", founderAccount.accountId.value)
                .andAppend("groups", founderAccount.configuration.groups)

            val onePixPay = onePixPayService.create(bills, true)
                .getOrElse {
                    logger.error(markers.andAppend("error", it), "NotifyOnePixPayJob")
                    return
                }

            val hasBasicAccountLimitExceeded = if (founderAccount.type == UserAccountType.BASIC_ACCOUNT) {
                hasBasicAccountLimitExceeded(wallet.id, founderAccount.activated, bills)
            } else {
                false
            }

            markers.andAppend("onePixPay", onePixPay)
                .andAppend("hasBasicAccountLimitExceeded", hasBasicAccountLimitExceeded)

            notificationAdapter.notifyOnePixPay(
                members = listOf(wallet.founder),
                walletName = wallet.name,
                onePixPayId = onePixPay.id,
                delay = Duration.ofSeconds(60),
                bills = bills,
                hasBasicAccountLimitExceeded = hasBasicAccountLimitExceeded,
            )

            logger.info(markers, "NotifyOnePixPayJob")
        } catch (e: Exception) {
            logger.error(markers, "NotifyOnePixPayJob", e)
        } finally {
            lock.unlock()
        }
    }

    private fun hasBasicAccountLimitExceeded(walletId: WalletId, activatedAt: ZonedDateTime?, bills: List<BillView>): Boolean {
        val paidBills = if (activatedAt != null) {
            billService.getPaidBills(
                walletId = walletId,
                startDate = activatedAt,
                endDate = getZonedDateTime(),
            )
        } else {
            listOf()
        }

        val limitExceeded = (paidBills + bills).sumOf {
            it.amountTotal
        } > MAX_MONTHLY_PAYMENT_LIMIT_BASIC_ACCOUNT

        return limitExceeded
    }

    private fun publishBillComingDueEvent(bill: BillView) {
        val wallet = walletService.findWallet(bill.walletId)

        val (founder, otherMembers) = wallet.getMembersCanView(bill).partition {
            it.accountId == wallet.founder.accountId
        }

        notificationAdapter.notifyBillComingDue(
            members = otherMembers,
            author = bill.findAuthorOrNull(wallet),
            walletName = wallet.name,
            walletId = wallet.id,
            billView = bill,
        )

        notificationAdapter.notifyBillComingDue(
            members = founder,
            author = bill.findAuthorOrNull(wallet),
            walletName = wallet.name,
            walletId = wallet.id,
            billView = bill,
            hint = notificationHintService.getBillComingDue(walletId = wallet.id, founder = wallet.founder),
        )

        billService.markBillComingDueMessageSent(bill.billId, bill.walletId)
    }

    private fun publishBillComingDueChatbotNotification(wallet: Wallet, account: Account, bills: List<BillView>, lastWarn: Boolean, hint: String?, chatbotType: ChatbotType) {
        if (wallet.checkPrimary(account.accountId) && chatbotType == ChatbotType.CHABOT_AI && !account.isLegalPerson()) {
            onePixPayInstrumentation.decidedToNotifyBillsComingDue(wallet, account, bills)
            chatBotNotificationService.notifyBillComingDue(account, wallet, bills, lastWarn, hint)
        } else {
            val membersToNotify = wallet.getMembersCanSchedule(bills.first())

            logger.info(Markers.append("walletId", wallet.id.value).andAppend("accountId", account.accountId.value).andAppend("lastWarn", lastWarn).andAppend("members", membersToNotify), "notifySecondaryWallet")

            if (lastWarn) {
                notificationAdapter.notifyBillComingDueLastWarn(
                    members = membersToNotify,
                    wallet = wallet,
                    dueDate = bills.first().effectiveDueDate,
                    paymentLimitTime = bills.first().paymentLimitTime,
                    hint = hint,
                    bills = bills,
                )
            } else {
                notificationAdapter.notifyBillComingDueSecondaryWallet(
                    members = membersToNotify,
                    wallet = wallet,
                    bills = bills,
                )
            }
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(BillComingDueService::class.java)
    }
}

data class BillsComingDueLists(
    val userScheduledBills: List<BillView>,
    val notScheduledBillsDueToday: List<BillView>,
    val subscriptionsBills: List<BillView>,
    val overdueNotScheduledBills: List<BillView>,
) {
    val todayBills = userScheduledBills + notScheduledBillsDueToday + subscriptionsBills
    fun hasBillsToResolveToday(): Boolean {
        return todayBills.isNotEmpty()
    }

    fun hasScheduledBillsWaitingFunds(): Boolean {
        return userScheduledBills.any { it.schedule != null && it.schedule.waitingFunds }
    }
}

data class NotifyBillComingDueMessage(
    val walletId: WalletId,
    val type: NotifyBillComingDueType,
)

enum class NotifyBillComingDueType {
    YESTERDAY, LAST_WARN, LAST_WARN_REST_OF_DAY, REGULAR, REGULAR_CHATBOT
}