package ai.friday.billpayment.app.notification

import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentStatus
import ai.friday.billpayment.app.account.isAlphaGroup
import ai.friday.billpayment.app.chatbot.ChatbotMessagePublisher
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.NotificationService
import ai.friday.billpayment.app.message.QueueMessage
import ai.friday.billpayment.app.message.QueueNotificationTO
import ai.friday.billpayment.app.metrics.metricRegister
import ai.friday.billpayment.app.wallet.Member
import ai.friday.morning.log.andAppend
import io.micronaut.context.annotation.Property
import io.via1.communicationcentre.app.notification.Notification
import io.via1.communicationcentre.app.notification.NotificationChannel
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
class DefaultNotificationService(
    private val accountRepository: AccountRepository,
    private val shouldNotifyUser: ShouldNotifyUser,
    private val messagePublisher: MessagePublisher,
    @Property(name = "friday.morning.messaging.consumer.bill-notification.queueName") private val notificationQueueName: String,
    private val pushNotificationService: PushNotificationService,
    private val chatbotMessagePublisher: ChatbotMessagePublisher,
) : NotificationService {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun notifyMembers(
        members: List<Member>,
        type: NotificationType,
        buildNotification: (account: Account) -> BillPaymentNotification?,
    ) {
        for (member in members) {
            this.notifyAccount(member.accountId, type, buildNotification)
        }
    }

    override fun notifyAccount(
        accountId: AccountId,
        type: NotificationType,
        buildNotification: (account: Account) -> BillPaymentNotification?,
    ) {
        val account = accountRepository.findById(accountId)
        this.notifyAccount(account, type, buildNotification)
    }

    override fun notifyAccount(
        account: Account,
        type: NotificationType,
        buildNotification: (account: Account) -> BillPaymentNotification?,
    ) {
        if (!shouldNotifyUser.shouldNotify(account, type)) return

        val notification = buildNotification(account)
        if (notification != null) {
            doSend(account, type, notification)
        }
    }

    override fun sendNotification(
        account: Account?,
        type: NotificationType,
        billPaymentNotification: BillPaymentNotification,
    ) {
        doSend(account, type, billPaymentNotification)
    }

    private fun doSend(account: Account?, type: NotificationType, billPaymentNotification: BillPaymentNotification) {
        val markers = Markers.append("billPaymentNotification", billPaymentNotification).andAppend("accountId", account?.accountId?.value)
        val logName = "DefaultNotificationPublisher#doSend"
        try {
            val channel = chooseChannel(account, billPaymentNotification)
            val shouldChangeToPushWhenPossible = shouldChangeToPushWhenPossible(account)
            markers.andAppend("choosedChannel", channel).andAppend("originalChannel", billPaymentNotification.preferredChannel).andAppend("shouldChangeToPushWhenPossible", shouldChangeToPushWhenPossible)

            when (channel) {
                BillPaymentNotificationChannel.EMAIL -> {
                    when (billPaymentNotification) {
                        is EmailNotification -> messagePublisher.sendMessage(QueueMessage(notificationQueueName, billPaymentNotification.toComCentreNotification()))
                        is MultiChannelNotification -> throw IllegalStateException("MultiChannelNotification to email converter not implemented yet")
                        else -> throw IllegalStateException("Only EmailNotification should be sent via Email")
                    }
                }

                BillPaymentNotificationChannel.CHATBOT -> {
                    when (billPaymentNotification) {
                        is WhatsappNotification -> chatbotMessagePublisher.publishGenericWhatsappNotification(account, billPaymentNotification)
                        is ChatbotNotification -> chatbotMessagePublisher.publishChatbotNotification(billPaymentNotification)
                        is EmailNotification -> throw IllegalStateException("EmailNotification should not be sent via Chatbot")
                        is MultiChannelNotification -> chatbotMessagePublisher.publishNotification(billPaymentNotification)
                    }
                }

                BillPaymentNotificationChannel.PUSH -> {
                    pushNotificationService.sendNotificationAsync(account?.accountId!!, billPaymentNotification)
                        .onFailure {
                            logger.error(markers, logName, it)
                        }.onSuccess { result ->
                            if (result != SendPushAsyncResult.Requested) {
                                logger.error(markers.andAppend("result", result.javaClass.simpleName).andAppend("billPaymentNotification", billPaymentNotification).andAppend("context", "nada foi adicionado a fila do push."), logName)
                            }
                        }
                }
            }

            metricRegister(
                ai.friday.billpayment.app.metrics.Notification(),
                "type" to type,
                "gateway" to account?.configuration?.notificationGateway,
                "channel" to channel,
                "template" to billPaymentNotification.template?.value,
            )

            if (channel != BillPaymentNotificationChannel.PUSH && account != null && account.isAlphaGroup()) {
                pushNotificationService.sendNotificationAsync(account.accountId, billPaymentNotification)
                    .onFailure { logger.error(markers, logName, it) }
            }

            logger.info(markers, logName)
        } catch (e: Exception) {
            logger.error(markers, logName, e)
        }
    }

    private fun shouldChangeToPushWhenPossible(account: Account?): Boolean {
        return account != null && (account.paymentStatus == AccountPaymentStatus.Overdue || account.hasGroup(AccountGroup.PUSH_OVER_WHATSAPP))
    }

    private fun chooseChannel(account: Account?, billPaymentNotification: BillPaymentNotification): BillPaymentNotificationChannel {
        val preferredChannel = billPaymentNotification.preferredChannel
        val shouldChangeToPushWhenPossible = shouldChangeToPushWhenPossible(account)

        if (shouldChangeToPushWhenPossible || preferredChannel == BillPaymentNotificationChannel.PUSH) {
            if (pushNotificationService.canSendViaPush(billPaymentNotification)) {
                return BillPaymentNotificationChannel.PUSH
            }
            if (preferredChannel == BillPaymentNotificationChannel.PUSH) {
                return BillPaymentNotificationChannel.CHATBOT // usa CHATBOT como fallback do push
            }
        }

        return preferredChannel
    }
}

private fun EmailNotification.toComCentreNotification(): QueueNotificationTO {
    val current = this
    return QueueNotificationTO(
        notification = Notification().apply {
            template = current.template.value
            notificationChannel = NotificationChannel.EMAIL
            externalUserId = current.accountId?.value
            emailParameters = current.parameters
            email = current.receiver.value
        },
    )
}

enum class BillPaymentNotificationChannel {
    EMAIL, CHATBOT, PUSH
}