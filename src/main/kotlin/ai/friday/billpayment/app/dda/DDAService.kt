package ai.friday.billpayment.app.dda

import ai.friday.billpayment.PrintableSealedClass
import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.adapters.arbi.ArbiDDAPayerNotFoundException
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.FinancialInstitutionGlobalData
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountNotFoundException
import ai.friday.billpayment.app.account.hasEarlyAccess
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillSynchronizationStatus
import ai.friday.billpayment.app.bill.CreateBillResult
import ai.friday.billpayment.app.bill.CreateFichaDeCompensacaoRequest
import ai.friday.billpayment.app.bill.FichaCompensacaoService
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.chatbot.OpenFinanceIncentiveService
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.BillValidationService
import ai.friday.billpayment.app.integrations.DDABatchResult
import ai.friday.billpayment.app.integrations.DDAProviderService
import ai.friday.billpayment.app.integrations.DDARepository
import ai.friday.billpayment.app.integrations.DocumentType
import ai.friday.billpayment.app.integrations.FeatureConfiguration
import ai.friday.billpayment.app.integrations.FindBillService
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.message.QueueMessage
import ai.friday.billpayment.app.payment.BillValidationResponse
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.app.wallet.WalletStatus
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import com.fasterxml.jackson.annotation.JsonIgnore
import datadog.trace.api.Trace
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import java.time.LocalDate
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import net.logstash.logback.marker.Markers
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import parallelMap

@Singleton
open class DDAService(
    private val ddaProviderService: DDAProviderService,
    private val fichaCompensacaoService: FichaCompensacaoService,
    private val accountRepository: AccountRepository,
    private val billValidationService: BillValidationService,
    private val findBillService: FindBillService,
    private val updateBillService: UpdateBillService,
    private val ddaRepository: DDARepository,
    private val fullDDAPostProcessor: FullDDAPostProcessor,
    private val messagePublisher: MessagePublisher,
    private val ddaConfig: DDAConfig,
    private val featureConfiguration: FeatureConfiguration,
    private val createDDARegister: CreateDDARegister,
    private val walletService: WalletService,
    private val openFinanceIncentiveService: OpenFinanceIncentiveService,
    @Property(name = "sqs.ddaFullImportQueueName") private val ddaFullImportQueueName: String,
) : DDARegisterService {
    @field:Property(name = "febraban.max-amount")
    var febrabanMaxAmount: Double = 250_000.0

    open fun batchRegister() {
        val ddaRegisterList = findPending().shuffled().take(ddaConfig.maxAddUser)
            .filter { it.provider == DDAProvider.ARBI }
            .ifEmpty {
                LOG.info(append("emptyList", true), "DDAService#batchRegister")
                batchDeregister()
                return
            }

        val marker = append("accountIds", ddaRegisterList.joinToString { it.accountId.value })
        LOG.info(marker, "DDAService#batchRegister")

        try {
            val personalDocuments = ddaRegisterList.filter { Document(it.document).type == DocumentType.CPF }
            val businessDocuments = ddaRegisterList.filter { it.document.isCnpj() }

            val personalSuccessfulDocuments = if (personalDocuments.isEmpty()) emptyList<Document>() else ddaProviderService.add(personalDocuments.map { Document(it.document) }, DocumentType.CPF)
            val businessSuccessfulDocuments = if (businessDocuments.isEmpty()) emptyList<Document>() else ddaProviderService.add(businessDocuments.map { Document(it.document) }, DocumentType.CNPJ)

            val successfulDocuments = personalSuccessfulDocuments + businessSuccessfulDocuments

            val successfulDDARegister = ddaRegisterList.filter { successfulDocuments.contains(Document(it.document)) }
            successfulDDARegister.forEach {
                ddaRepository.save(
                    ddaRegister = it.copy(
                        status = DDAStatus.REQUESTED,
                    ),
                )
            }
            if (successfulDDARegister.isNotEmpty()) {
                messagePublisher.sendBatchDDARequestedMessage(successfulDDARegister.map { it.toDDARegisterMessage() })
            }
        } catch (e: Exception) {
            LOG.error(marker, "DDAService#batchRegister", e)
            throw e
        }
    }

    private fun String.isCnpj() = try {
        when (val documentType = Document(this).type) {
            is DocumentType.CNPJ -> true
            is DocumentType.OTHER -> documentType.value == "CNPJ"
            else -> false
        }
    } catch (ex: Exception) {
        false
    }

    open fun batchDeregister(): Boolean {
        if (!FinancialInstitutionGlobalData.isBusinessDay(getLocalDate())) {
            return false
        }

        val ddaRegister = ddaRepository.findByStatus(DDAStatus.PENDING_CLOSE)
            .firstOrNull { it.provider == DDAProvider.ARBI }

        if (ddaRegister == null) {
            LOG.info(append("emptyList", true), "DDAService#batchDeregister")
            return false
        }
        val marker = append("accountId", ddaRegister.accountId.value)
        LOG.info(marker, "DDAService#batchDeregister")

        ddaProviderService.remove(Document(ddaRegister.document))
        ddaRepository.save(
            ddaRegister = ddaRegister.copy(
                status = DDAStatus.CLOSED,
            ),
        )

        return true
    }

    open fun batchMigration() {
        val removeLimit = 1000

        val ddaRegisterList = ddaRepository
            .findByStatus(DDAStatus.PENDING_MIGRATION_OPTOUT)
            .take(removeLimit * 10)
            .shuffled()
            .take(removeLimit)

        if (ddaRegisterList.isEmpty()) {
            LOG.info(append("emptyList", true), "DDAService#batchMigration")
            return
        }

        ddaRegisterList.chunked(100).forEach { chunk ->
            LOG.info(append("accountIds", chunk.joinToString { it.accountId.value }), "DDAService#batchMigration")
        }

        try {
            val pendingListDocuments = ddaRegisterList.map { Document(it.document) }

            when (val batchResult = ddaProviderService.batchRemove(pendingListDocuments)) {
                is DDABatchResult.UnknownResponse -> {
                    throw IllegalStateException("DDABatchResult.UnknownResponse")
                }

                is DDABatchResult.Accepted -> {
                    LOG.info(
                        append("idRequisicaoLote", batchResult.requestId),
                        "DDAService#batchMigration",
                    )

                    runBlocking(Dispatchers.IO) {
                        ddaRegisterList.parallelMap {
                            ddaRepository.save(it.copy(status = DDAStatus.MIGRATING_OPTOUT))
                        }
                    }

                    messagePublisher.sendBatchMigrationOrderMessage(
                        BatchMigrationOrder(
                            requestId = batchResult.requestId,
                            documents = ddaRegisterList.map { it.document },
                        ),
                    )
                }
            }
        } catch (e: Exception) {
            LOG.error("DDAService#batchMigration", e)
            throw e
        }
    }

    fun batchRegisterAsync() {
        val pendingList = findPending().take(ddaConfig.maxAddUserAsync)
            .filter { it.provider == DDAProvider.ARBI }
            .ifEmpty {
                LOG.info(append("emptyList", true).andAppend("ddaConfig", ddaConfig), "DDAService#batchRegisterAsync")
                batchDeregister()
                return
            }

        pendingList.chunked(100).forEach { chunk ->
            LOG.info(append("accountIds", chunk.joinToString { it.accountId.value }), "DDAService#batchRegisterAsync")
        }

        try {
            val pendingListDocuments = pendingList.map { Document(it.document) }

            when (val batchResult = ddaProviderService.batchAdd(pendingListDocuments)) {
                is DDABatchResult.UnknownResponse -> {
                    throw IllegalStateException("DDABatchAddResult.UnknownResponse")
                }

                is DDABatchResult.Accepted -> {
                    LOG.info(
                        append("idRequisicaoLote", batchResult.requestId),
                        "DDAService#batchRegisterAsync",
                    )

                    runBlocking(Dispatchers.IO) {
                        pendingList.parallelMap {
                            ddaRepository.save(it.copy(status = DDAStatus.REQUESTING))
                        }
                    }

                    messagePublisher.sendBatchAddOrderMessage(
                        BatchAddOrder(
                            requestId = batchResult.requestId,
                            documents = pendingList.map { it.document },
                        ),
                    )
                }
            }
        } catch (e: Exception) {
            LOG.error("DDAService#batchRegisterAsync", e)
            throw e
        }
    }

    open fun register(accountId: AccountId, document: String, ddaProvider: DDAProvider? = null) =
        createDDARegister.register(
            accountId = accountId,
            document = document,
            ddaProvider = ddaProvider,
        )

    open fun remove(accountId: AccountId, document: Document): Either<Exception, Unit> {
        return try {
            val register = ddaRepository.find(accountId)

            register?.let {
                if (it.status == DDAStatus.ACTIVE) {
                    ddaRepository.save(it.copy(status = DDAStatus.PENDING_CLOSE))
                }
            }

            Unit.right()
        } catch (e: Exception) {
            e.left()
        }
    }

    open fun activate(accountId: AccountId) {
        ddaRepository.find(accountId)?.let {
            ddaRepository.save(it.copy(status = DDAStatus.ACTIVE))
        } ?: throw DDARegisterNotFoundException(accountId)
    }

    open fun findPending(): List<DDARegister> {
        return ddaRepository.findByStatus(DDAStatus.PENDING)
    }

    open fun getBills(ddaRegister: DDARegister): List<DDAItem> {
        return ddaProviderService.getBills(getLocalDate().minusMonths(1), ddaRegister.document)
    }

    open fun findActive(): List<DDARegister> {
        return ddaRepository.findByStatus(DDAStatus.ACTIVE)
    }

    open fun updateLastExecutionSuccessful(accountId: AccountId) {
        ddaRepository.find(accountId)?.let {
            ddaRepository.save(it.copy(lastSuccessfullExecution = getZonedDateTime()))
        } ?: throw DDARegisterNotFoundException(accountId)
    }

    override fun findByAccount(accountId: AccountId): DDARegister? = ddaRepository.find(accountId = accountId)

    open fun findDDARegister(document: Document): DDARegister? = try {
        val account = accountRepository.findAccountByDocument(document = document.value)

        ddaRepository.find(accountId = account.accountId)
    } catch (e: AccountNotFoundException) {
        LOG.error("DDAService", e)
        throw e
    }

    @Trace
    open fun executeBill(
        ddaItem: DDAItem,
        billPreemptValidationResponse: BillValidationResponse? = null,
    ): DDAExecutionResult {
        val markers = Markers.append("ddaItem", ddaItem)
        billPreemptValidationResponse?.let {
            markers.andAppend("preemptValidationStatus", it.getStatus())
        }
        return try {
            val billPayer = accountRepository.findAccountByDocument(ddaItem.document)
            markers.andAppend("accountId", billPayer.accountId.value)

            val ddaRegister = ddaRepository.find(accountId = billPayer.accountId)
            ddaRegister?.let {
                markers.andAppend("ddaRegisterStatus", ddaRegister.status.name)
                    .andAppend("ddaRegisterProvider", ddaRegister.provider.name)

                val acceptedStatuses = listOf(
                    DDAStatus.ACTIVE,
                    DDAStatus.REQUESTED,
                    DDAStatus.PENDING_MIGRATION_OPTOUT,
                    DDAStatus.MIGRATING_OPTOUT,
                )
                if (!acceptedStatuses.contains(it.status)) {
                    return DDAExecutionResult.NotAdded("DDAStatus is not active, requested or migrating - ${it.status}")
                        .also { result ->
                            LOG.info(markers.andAppend("result", result), "DDAService#executeBill")
                        }
                }

                if (ddaItem.ddaProvider != it.provider) {
                    return DDAExecutionResult.NotAdded("Account DDAProvider is different from bill").also { result ->
                        LOG.info(markers.andAppend("result", result), "DDAService#executeBill")
                    }
                }
            }

            executeBill(ddaItem, billPayer, billPreemptValidationResponse)
        } catch (e: AccountNotFoundException) {
            DDAExecutionResult.AccountNotFound
        }.also { result ->
            LOG.info(markers.andAppend("result", result), "DDAService#executeBill")
        }
    }

    private fun executeBill(
        ddaRegister: DDARegister,
        ddaItem: DDAItem,
        billPreemptValidationResponse: BillValidationResponse? = null,
    ): DDAExecutionResult {
        val account = accountRepository.findById(accountId = ddaRegister.accountId)
        return executeBill(
            ddaItem,
            account,
            billPreemptValidationResponse,
        )
    }

    private fun executeBill(
        ddaItem: DDAItem,
        account: Account,
        billPreemptValidationResponse: BillValidationResponse? = null,
    ): DDAExecutionResult {
        val markers = Markers.append("ddaItem", ddaItem)
            .andAppend("accountId", account.accountId.value)
        billPreemptValidationResponse?.let {
            markers.andAppend("preemptValidationStatus", it.getStatus())
        }

        val ddaBill = ddaRepository.find(ddaItem.barcode, account.document, ddaItem.dueDate)

        if (!account.accountId.hasEarlyAccess() && ddaItem.amount > febrabanMaxAmount) {
            return DDAExecutionResult.NotAdded("febraban max ammount violated")
        }

        if (ddaBill != null) {
            val wallet = walletService.findWallet(ddaBill.walletId)
            markers.andAppend("ddaBill", ddaBill).andAppend("wallet", wallet)

            if (wallet.status == WalletStatus.ACTIVE) {
                return if (canUpdateBill(ddaBill)) {
                    val result = tryUpdateBill(ddaBill.billId, ddaItem.barcode, billPreemptValidationResponse)
                    if (result is DDAExecutionResult.Updated) {
                        when (result.status) {
                            is BillSynchronizationStatus.BillStatusUpdated -> ddaRepository.save(ddaBill.copy(lastStatus = result.status.billStatus))
                            is BillSynchronizationStatus.BillIsNotActive -> ddaRepository.save(ddaBill.copy(lastStatus = result.status.billStatus))
                            is BillSynchronizationStatus.BillAmountTotalUpdated,
                            is BillSynchronizationStatus.BillAmountTotalUpdatedToLess,
                            is BillSynchronizationStatus.BillAmountCalculationValidUntilUpdated,
                            BillSynchronizationStatus.BillNotModified,
                            is BillSynchronizationStatus.UnableToValidate,
                            -> {
                                LOG.warn(markers.andAppend("whenUnknownBranch", result.status), "WHENUNKNOWNBRANCH#DDAService#executeBill")
                            }
                        }
                    }
                    result
                } else {
                    DDAExecutionResult.NotUpdated("bill is not active")
                }.also { result ->
                    LOG.info(markers.andAppend("result", result), "DDAService#executeBill")
                }
            }
        }

        val addBillResult = fichaCompensacaoService.createFichaDeCompensacao(
            request = CreateFichaDeCompensacaoRequest(
                description = "",
                walletId = account.configuration.defaultWalletId!!,
                barcode = ddaItem.barcode,
                source = ActionSource.DDA(accountId = account.accountId),
                payerAlias = null,
                member = null,
            ),
            preemptValidation = billPreemptValidationResponse,
        )
        markers.andAppend("addBillResult", addBillResult::class.java.simpleName)

        return when (addBillResult) {
            is CreateBillResult.SUCCESS -> {
                val ddaBill = DDABill(
                    barcode = ddaItem.barcode,
                    document = ddaItem.document,
                    dueDate = ddaItem.dueDate,
                    walletId = account.configuration.defaultWalletId!!,
                    billId = addBillResult.bill.billId,
                    lastStatus = addBillResult.bill.status,
                )
                ddaRepository.save(ddaBill)

                DDAExecutionResult.Added
            }

            is CreateBillResult.FAILURE.BillAlreadyExists -> {
                val ddaBill = DDABill(
                    barcode = ddaItem.barcode,
                    document = ddaItem.document,
                    dueDate = ddaItem.dueDate,
                    walletId = account.configuration.defaultWalletId!!,
                    billId = addBillResult.bill.billId,
                    lastStatus = addBillResult.bill.status,
                )
                ddaRepository.save(ddaBill)

                DDAExecutionResult.Added
            }

            is CreateBillResult.FAILURE.BillNotPayable, is CreateBillResult.FAILURE.AlreadyPaid.WithData, is CreateBillResult.FAILURE.AlreadyPaid.WithoutData -> DDAExecutionResult.NotAdded(
                addBillResult::class.java.simpleName,
            )

            is CreateBillResult.FAILURE.ServerError -> {
                DDAExecutionResult.Error(throwable = addBillResult.throwable)
            }

            is CreateBillResult.FAILURE.BillUnableToValidate -> {
                DDAExecutionResult.Error(
                    throwable = null,
                    description = addBillResult.description,
                    isRetryable = addBillResult.isRetryable,
                )
            }
        }.also { result ->
            LOG.info(markers.andAppend("result", result), "DDAService#executeBill")
        }
    }

    private fun canUpdateBill(bill: Bill) = bill.status == BillStatus.ACTIVE
    private fun canUpdateBill(bill: DDABill) = bill.lastStatus == BillStatus.ACTIVE

    private fun tryUpdateBill(
        billId: BillId,
        barCode: BarCode,
        billPreemptValidationResponse: BillValidationResponse? = null,
    ): DDAExecutionResult {
        val billValidationResponse = billPreemptValidationResponse ?: billValidationService.validate(barCode)
        val response = updateBillService.synchronizeBill(billId, billValidationResponse)

        if (response.status is BillSynchronizationStatus.BillStatusUpdated && response.status.billStatus == BillStatus.ALREADY_PAID) {
            openFinanceIncentiveService.processDDA(billId)
        }

        return when {
            response.alreadyInSync() -> DDAExecutionResult.NotUpdated(response.toString())
            response.unableToValidate() -> DDAExecutionResult.Error(isRetryable = (response.status as BillSynchronizationStatus.UnableToValidate).isRetryable)
            else -> DDAExecutionResult.Updated(response.status)
        }
    }

    @Trace
    fun executeFullImport(ddaRegister: DDARegister): DDAFullImportResult {
        val marker = append("accountId", ddaRegister.accountId.value)
        return try {
            if (featureConfiguration.skipFullImportWhenAccountHasBills) {
                val bills = findBillService.findByWalletId(WalletId(ddaRegister.accountId.value))
                if (bills.isNotEmpty()) {
                    return ddaRegister.tryActivate(executionResult = DDAFullImportResult.Skipped("account has ${bills.size} bills"))
                }
                val message = DDARegisterMessage(ddaRegister.accountId, ddaRegister.document)
                messagePublisher.sendMessage(
                    QueueMessage(
                        ddaFullImportQueueName,
                        getObjectMapper().writeValueAsString(message),
                        delaySeconds = 900,
                    ),
                )

                return DDAFullImportResult.Delayed
            }

            val ddaItems = ddaProviderService.getBills(getLocalDate().minusMonths(1), ddaRegister.document)

            val added = ddaItems.map { ddaItem ->
                try {
                    executeBill(ddaRegister, ddaItem)
                } catch (e: Exception) {
                    LOG.warn(marker.and(append("digitableLine", ddaItem.barcode.digitable)), "executeFullImport", e)
                    DDAExecutionResult.Error(e)
                }
            }.any {
                it == DDAExecutionResult.Added
            }

            return when {
                added -> ddaRegister.tryActivate(executionResult = DDAFullImportResult.Imported)
                !ddaRegister.canRetry() -> ddaRegister.tryActivate(executionResult = DDAFullImportResult.Expired)
                else -> DDAFullImportResult.NotImported
            }
        } catch (e: ArbiDDAPayerNotFoundException) {
            LOG.warn(marker, "executeFullImport", e)
            register(ddaRegister.accountId, ddaRegister.document)
            DDAFullImportResult.NotImported
        } catch (e: Exception) {
            LOG.error(marker, "executeFullImport", e)
            DDAFullImportResult.Error(throwable = e, description = "Exception")
        }
    }

    private fun DDARegister.tryActivate(executionResult: DDAFullImportResult) = fullDDAPostProcessor.process(this).map {
        activate(this.accountId)
        executionResult
    }.getOrElse {
        DDAFullImportResult.Error(throwable = it.exception, description = it.javaClass.simpleName)
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(DDAService::class.java)
    }

    private fun DDARegister.canRetry() =
        this.lastUpdated.plusMinutes(ddaConfig.activateWaitMinutes).isAfter(getZonedDateTime())
}

interface DDARegisterService {
    fun findByAccount(accountId: AccountId): DDARegister?
}

data class BatchAddOrder(val requestId: String, val documents: List<String>)

data class BatchMigrationOrder(val requestId: String, val documents: List<String>)

sealed class DDAExecutionResult : PrintableSealedClass() {
    data object Added : DDAExecutionResult()
    class Updated(val status: BillSynchronizationStatus) : DDAExecutionResult()
    class NotUpdated(val reason: String? = null) : DDAExecutionResult()
    class NotAdded(val reason: String? = null) : DDAExecutionResult()
    data object AccountNotFound : DDAExecutionResult()
    class Error(val throwable: Throwable? = null, val description: String? = null, val isRetryable: Boolean = true) :
        DDAExecutionResult()
}

sealed class DDAFullImportResult(val endState: Boolean) : PrintableSealedClassV2() {
    data object NotImported : DDAFullImportResult(endState = false)
    data object Imported : DDAFullImportResult(endState = true)
    data object Expired : DDAFullImportResult(endState = true)
    data object Delayed : DDAFullImportResult(endState = true)
    class Skipped(val reason: String) : DDAFullImportResult(endState = true)
    class Error(
        @JsonIgnore
        val throwable: Throwable,
        val description: String,
    ) : DDAFullImportResult(endState = false)
}

data class DDABill(
    val barcode: BarCode,
    val document: String,
    val dueDate: LocalDate,
    val walletId: WalletId,
    val billId: BillId,
    val lastStatus: BillStatus,
)

data class DDAItem(
    val amount: Double,
    val barcode: BarCode,
    val document: String,
    val dueDate: LocalDate,
    val ddaProvider: DDAProvider,
)

@ConfigurationProperties("dda")
class DDAConfig @ConfigurationInject constructor(
    val maxAddUser: Int,
    val maxAddUserAsync: Int,
    val provider: DDAProvider,
    val activateWaitMinutes: Long,
)