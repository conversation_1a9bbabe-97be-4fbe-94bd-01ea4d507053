package ai.friday.billpayment.app.cashIn

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.InvalidInstallmentCalculation
import ai.friday.billpayment.app.bill.InvalidSchedulePaymentFee
import ai.friday.billpayment.app.fee.FeePaymentCreditCardInstallment
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.CreditCardFeeCalculatorService
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import kotlin.math.pow
import kotlin.math.roundToLong
import org.slf4j.LoggerFactory

@ConfigurationProperties("creditCard.installments")
interface CreditCardsInstallmentsFeeConfiguration {
    val fees: Map<Int, Double>
}

@Singleton
open class DefaultCreditCardFeeCalculatorService(
    private val accountRepository: AccountRepository,
    private val creditCardsInstallmentsFeeConfiguration: CreditCardsInstallmentsFeeConfiguration,
    @Property(name = "features.useInternalCreditCardCalculator") private val useInternalCreditCardCalculator: Boolean,
) : CreditCardFeeCalculatorService {

    private val logger = LoggerFactory.getLogger(DefaultCreditCardFeeCalculatorService::class.java)

    override fun calculateFeeAmount(
        accountId: AccountId,
        netAmount: Long,
        installments: Int,
        calculationId: String?,
    ): Either<InvalidSchedulePaymentFee, Long> {
        if (useInternalCreditCardCalculator) {
            return calculationId?.let {
                InvalidSchedulePaymentFee("Calculation id must be null").left()
            } ?: internalFeeCalculator(accountId, netAmount, installments)
        }

        return calculationId?.let {
            (0L).right()
        } ?: InvalidSchedulePaymentFee("CalculationId is mandatory").left()
    }

    override fun calculateInstallment(
        accountId: AccountId,
        netAmount: Long,
        installments: Int,
        calculationId: String?,
    ): Either<InvalidInstallmentCalculation, FeePaymentCreditCardInstallment> {
        if (!useInternalCreditCardCalculator) {
            return InvalidInstallmentCalculation("no external calculator available").left()
        }

        val fee = getFee(installments) ?: return InvalidInstallmentCalculation("invalid installments").left()

        val amount = getPriceTableInstallmentAmount(netAmount, fee, installments)

        return FeePaymentCreditCardInstallment(
            quantity = installments,
            amount = amount.roundToLong(),
            total = (installments * amount).roundToLong(),
            fee = fee,
            feeAmount = null,
        ).right()
    }

    override fun getFee(installments: Int): Double? {
        return creditCardsInstallmentsFeeConfiguration.fees[installments]
    }

    private fun internalFeeCalculator(
        accountId: AccountId,
        netAmount: Long,
        installments: Int,
    ): Either<InvalidSchedulePaymentFee, Long> {
        val account = accountRepository.findById(accountId)

        return if (account.hasCreditCardEnabled()) {
            val fee = getFee(installments) ?: return InvalidSchedulePaymentFee("invalid installments").left()

            val installmentAmount = getPriceTableInstallmentAmount(netAmount, fee, installments)

            ((installments * installmentAmount) - netAmount).roundToLong().right()
        } else {
            InvalidSchedulePaymentFee("account has no credit card enabled").left()
        }
    }

    private fun getPriceTableInstallmentAmount(amount: Long, fee: Double, installments: Int): Double {
        val monthlyFee = fee / 100.00
        val multiplier = (monthlyFee + 1).pow(installments)

        return amount * ((monthlyFee * multiplier) / (multiplier - 1))
    }
}