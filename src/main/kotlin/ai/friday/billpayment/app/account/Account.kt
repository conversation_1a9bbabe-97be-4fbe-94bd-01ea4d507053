package ai.friday.billpayment.app.account

import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.bill.BillEventDependency
import ai.friday.billpayment.app.integrations.ChatbotUserId
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.isValidCnpj
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.app.wallet.WalletId
import java.time.ZonedDateTime
import java.util.UUID

data class Account(
    val accountId: AccountId,
    val name: String,
    var emailAddress: EmailAddress,
    var document: String,
    val documentType: String,
    var mobilePhone: String,
    val created: ZonedDateTime,
    val activated: ZonedDateTime? = null,
    val updated: ZonedDateTime,
    val status: AccountStatus,
    val paymentStatus: AccountPaymentStatus = AccountPaymentStatus.UpToDate,
    val upgradeStatus: UpgradeStatus? = null,
    val configuration: LegacyAccountConfiguration,
    val firstLoginAsOwner: ZonedDateTime? = null,
    val type: UserAccountType = UserAccountType.FULL_ACCOUNT,
    val channel: String? = null,
    val imageUrlSmall: String?,
    val imageUrlLarge: String?,
    val subscriptionType: SubscriptionType,
) {
    val closed = this.status == AccountStatus.CLOSED
    fun chatbotUserId() = ChatbotUserId(mobilePhone.replace("+", ""))

    val creditCardConfiguration = configuration.creditCardConfiguration

    /**
     *  Configuração que determina para qual carteira as contas de DDA e email vão
     *  <AUTHOR> Developer
     */
    fun defaultWalletId(): WalletId {
        return configuration.defaultWalletId ?: throw ItemNotFoundException("Account has no default wallet yet")
    }

    fun externalId(): ExternalId? = configuration.externalId

    fun hasGroup(group: AccountGroup) = configuration.groups.contains(group)

    fun hasCreditCardEnabled(): Boolean {
        return creditCardConfiguration.quota > 0
    }

    fun hasAnyGroup(vararg groups: AccountGroup) = configuration.groups.intersect(groups.asList().toSet()).isNotEmpty()
}

fun Account.isLegalPerson(): Boolean {
    return Document(this.document).isValidCnpj()
}

fun Account.isAlphaGroup() = this.hasGroup(AccountGroup.ALPHA)

fun Account.isBetaGroup() = this.hasGroup(AccountGroup.BETA)

fun Account.isClosed() = this.status == AccountStatus.CLOSED || this.status == AccountStatus.PENDING_CLOSE

fun Account.isOpen() = this.status == AccountStatus.ACTIVE || this.status == AccountStatus.BLOCKED

fun Account.attemptToUpdateStatus(request: UpdateAccountStatusRequest): Account? {
    return request.apply(this)
}

enum class NotificationGateways {
    WHATSAPP,
}

@Deprecated("Agora temos uma entidade no banco para representar o AccountConfiguration")
data class LegacyAccountConfiguration(
    val accountId: AccountId? = null,
    val creditCardConfiguration: CreditCardConfiguration,
    val defaultWalletId: WalletId?,
    val receiveDDANotification: Boolean,
    val receiveNotification: Boolean = true,
    val accessToken: String? = null,
    val refreshToken: String? = null,
    val externalId: ExternalId? = null,
    val groups: List<AccountGroup> = emptyList(),
    val notificationGateway: NotificationGateways = NotificationGateways.WHATSAPP,
)

data class AccountConfiguration(
    val accountId: AccountId,
    val pushNotificationTokens: Set<String>,
    val receiveMonthlyStatement: Boolean,
    val freeOfFridaySubscription: Boolean,
)

data class CreditCardConfiguration(
    val quota: Long = 0,
    val allowedRisk: RiskLevel = RiskLevel.LOW,
)

@BillEventDependency
enum class AccountProviderName { MOTOROLA, FRIDAY, ME_POUPE }

data class ExternalId(val value: String, val providerName: AccountProviderName)

data class PartialAccount(
    val id: AccountId,
    val name: String,
    val emailAddress: EmailAddress,
    val status: AccountStatus,
    val statusUpdated: ZonedDateTime?,
    val role: Role,
    val groups: List<AccountGroup>,
    val registrationType: RegistrationType,
    val subscriptionType: SubscriptionType,
)

enum class AccountStatus {
    // Usado somente pelo Account
    ACTIVE,
    CLOSED,

    /**
     * Apesar do nome, significa que o usuário está inadimplente e com alguma possível limitação de features.
     * O `AccountPaymentStatus` possui a situação de pagamento atual.
     */
    BLOCKED,
    PENDING_CLOSE,

    // Usado somente pelo PartialAccount
    REGISTER_INCOMPLETE,
    DENIED,
    UNDER_REVIEW,
    UNDER_EXTERNAL_REVIEW,
    APPROVED,
}

enum class AccountPaymentStatus {
    /* todas as assinaturas em dia */
    UpToDate,

    /* assinatura vencida ha menos de 7 dias*/
    PastDue,

    /* assinatura vencida ha 7 dias ou mais*/
    Overdue,
}

enum class UpgradeStatus {
    INCOMPLETE, UNDER_REVIEW, DENIED, COMPLETED, UNDER_EXTERNAL_REVIEW, EXTERNAL_DENIED
}

enum class AccountGroup(val value: String) {
    DEVELOPER("developer"),
    PUSH_OVER_WHATSAPP("push-over-whatsapp"),
    ALPHA("alpha"),
    BETA("beta"),
    APP_REVIEWER("app-reviewer"),
    RELEASE_CANDIDATE("release-candidate"),
    CREDIT_CARD_STANDARD_PLAN("credit-card-standard-plan"),
    SUBSCRIPTION_STANDARD_PLAN("subscription-standard-plan"),
    EXPERIMENT_PAY_BUTTON("experiment-pay-button"),
    TIMELINE_FACELIFT("timeline-facelift"),
    MULTI_SELECT_BILLS("multi-select-bills"),
    CHATBOT_AI_ENABLED("chatbot-ai-enabled"),
    CHATBOT_AI_NEW_USERS("chatbot-ai-new-users"),
    CHATBOT_HUMAN_FIRST("chatbot-human-first"),
    CHATBOT_LEGACY("chatbot-legacy"),
    ONBOARDING_TEST_PIX("onboarding-test-pix"),
    AB_TEST_SIGN_UP("ab-test-sign-up"),
    NOTIFICATION_SERVICE("notification-service"),
    IN_APP_SUBSCRIPTION_PRICE_TEST("in-app-subscription-price-test"),
    IN_APP_SUBSCRIPTION_PAYWALL_TEST("paywall-layout-option-test"),
    INVESTMENT_CAMPAIGN("investment-campaign"),
    MAY_2025_CAMPAIGN("may-2025-campaign"),
    ;

    companion object {
        fun find(group: String): AccountGroup? {
            return entries.firstOrNull {
                it.value == group
            }
        }
    }
}

data class AccountGroupAddOrRemove(val accountId: String, val groups: List<AccountGroup>)

data class AccountGroupResult(val successes: List<String>, val errors: List<String>)

data class Principal(val id: AccountId, val role: Role)

private const val AcccountIdPrefix = "ACCOUNT-"

@BillEventDependency
data class AccountId(val value: String) {
    constructor() : this("$AcccountIdPrefix${UUID.randomUUID()}")

    companion object {
        fun create(value: String): AccountId {
            if (!value.startsWith(AcccountIdPrefix)) {
                throw IllegalArgumentException("Account ids are expected to start with $AcccountIdPrefix")
            }

            return AccountId(value)
        }
    }
}

enum class Role {
    OWNER,

    @Deprecated("System does not have assistants anymore")
    ASSISTANT,
    GUEST,

    @Deprecated("Foi substituido por BACKOFFICE")
    INTERNAL,
    BACKOFFICE,
    BACKOFFICE_ADMIN,
    CELCOIN_CALLBACK,
    ARBI_CALLBACK,
    FRIDAY_CALLBACK,
    DEPENDENT,
    ADMIN,
    GUEST_OTP,
    REVENUE_CAT_CALLBACK,
    VEHICLE_DEBTS_CALLBACK,
    INTERCOM_CALLBACK,
    ;

    data object Code {
        const val OWNER = "OWNER"
        const val BACKOFFICE = "BACKOFFICE"
        const val BACKOFFICE_ADMIN = "BACKOFFICE_ADMIN"
        const val GUEST = "GUEST"
        const val GUEST_OTP = "GUEST_OTP"
        const val CELCOIN_CALLBACK = "CELCOIN_CALLBACK"
        const val ARBI_CALLBACK = "ARBI_CALLBACK"
        const val FRIDAY_CALLBACK = "FRIDAY_CALLBACK"
        const val ADMIN = "ADMIN"
        const val REVENUE_CAT_CALLBACK = "REVENUE_CAT_CALLBACK"
        const val VEHICLE_DEBTS_CALLBACK = "VEHICLE_DEBTS_CALLBACK"
        const val INTERCOM_CALLBACK = "INTERCOM_CALLBACK"
    }
}

enum class UserAccountType {
    BASIC_ACCOUNT, FULL_ACCOUNT
}

fun Account.getDisplayName(): String {
    return this.name.substringBefore(" ").lowercase().replaceFirstChar { c -> c.uppercase() }
}