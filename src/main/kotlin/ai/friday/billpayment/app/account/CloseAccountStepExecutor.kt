package ai.friday.billpayment.app.account

import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.dda.DDAService
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.CrmService
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.RegisterInstrumentationService
import ai.friday.billpayment.app.integrations.UserPoolAdapter
import ai.friday.billpayment.app.liveness.LivenessErrors
import ai.friday.billpayment.app.liveness.LivenessService
import ai.friday.billpayment.app.login.LoginService
import ai.friday.billpayment.app.pix.DeletePixKeyError
import ai.friday.billpayment.app.pix.PixKeyService
import ai.friday.billpayment.app.subscription.SubscriptionService
import ai.friday.billpayment.app.subscription.UnsubscribeError
import ai.friday.billpayment.app.wallet.BackOfficeWalletService
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonTypeInfo
import jakarta.inject.Singleton
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

abstract class CloseAccountStepExecutor<in T : CloseAccountStep>(
    val stepType: CloseAccountStepType,
) {
    private val logger = LoggerFactory.getLogger(CloseAccountStepExecutor::class.java)

    fun execute(account: Account, step: T): Either<CloseAccountStepError, CloseAccountStepStatus> {
        val markers = append("stepType", this.stepType)
            .andAppend("accountId", account.accountId.value)

        return try {
            doExecute(account, step, markers)
        } catch (e: Exception) {
            CloseAccountStepError.GenericException(e).left()
        }.also {
            it.map {
                markers.andAppend("stepResult", it)
                logger.info(markers, "CloseAccountStepExecutor")
            }.getOrElse {
                markers.andAppend("stepResult", it)
                logger.warn(markers, "CloseAccountStepExecutor")
            }
        }
    }

    protected abstract fun doExecute(account: Account, step: T, markers: LogstashMarker): Either<CloseAccountStepError, CloseAccountStepStatus>
}

abstract class SimpleCloseAccountStepExecutor(
    stepType: CloseAccountStepType,
) : CloseAccountStepExecutor<SimpleCloseAccountStep>(stepType)

@JsonTypeInfo(use = JsonTypeInfo.Id.MINIMAL_CLASS)
sealed interface CloseAccountStep {
    val type: CloseAccountStepType
    var status: CloseAccountStepStatus
    var errorMessage: String?
}

sealed class CloseAccountStepError : PrintableSealedClassV2() {
    data class GenericError(val error: String) : CloseAccountStepError()
    data class GenericException(
        @JsonIgnore // para nao quebrar o log
        val exception: Exception,
    ) : CloseAccountStepError() {
        val reason = exception.javaClass.simpleName
        val message = exception.message
    }
}

enum class CloseAccountStepStatus {
    Pending,
    Success,
    Warning,
    Error,
}

data class SimpleCloseAccountStep(
    override val type: CloseAccountStepType,
    override var status: CloseAccountStepStatus = CloseAccountStepStatus.Pending,
    override var errorMessage: String? = null,
) : CloseAccountStep

data class DeactivateAccountRegisterStep(
    val closureDetails: AccountClosureDetails,
    override var status: CloseAccountStepStatus = CloseAccountStepStatus.Pending,
    override var errorMessage: String? = null,
) : CloseAccountStep {
    override val type: CloseAccountStepType = CloseAccountStepTypeDeactivateAccountRegister
}

data class CloseFounderWalletCloseAccountStep(
    val walletId: WalletId,
    val steps: List<CloseWalletStep>,
    override var status: CloseAccountStepStatus = CloseAccountStepStatus.Pending,
    override var errorMessage: String? = null,
) : CloseAccountStep {
    override val type: CloseAccountStepType = CloseAccountStepTypeCloseFounderWallet
}

data class CloseAccountStepType(val value: String)

val CloseAccountStepTypeUnsubscribe = CloseAccountStepType("Unsubscribe")
val CloseAccountStepTypeInAppUnsubscribe = CloseAccountStepType("InAppUnsubscribe")
val CloseAccountStepTypeRemoveFromDDA = CloseAccountStepType("RemoveFromDDA")
val CloseAccountStepTypeCloseFounderWallet = CloseAccountStepType("CloseFounderWallet")
val CloseAccountStepTypeSignOutUser = CloseAccountStepType("SignOutUser")
val CloseAccountStepTypeDisableNotification = CloseAccountStepType("DisableNotification")
val CloseAccountStepTypeRemoveFromUserPool = CloseAccountStepType("RemoveFromUserPool")
val CloseAccountStepTypeRemoveLogin = CloseAccountStepType("RemoveLogin")
val CloseAccountStepTypeUpdateStatusClosed = CloseAccountStepType("UpdateStatusClosed")
val CloseAccountStepTypeRemoveFromWallets = CloseAccountStepType("RemoveFromWallets")
val CloseAccountStepTypeDeleteCustomPixKeys = CloseAccountStepType("DeleteCustomPixKeys")
val CloseAccountStepTypeCloseCreditCards = CloseAccountStepType("CloseCreditCards")
val CloseAccountStepTypeCloseCrmContact = CloseAccountStepType("CloseCrmContact")
val CloseAccountStepTypeDeactivateAccountRegister = CloseAccountStepType("DeactivateAccountRegister")
val CloseAccountStepTypeMarkAsFraud = CloseAccountStepType("MarkAsFraud")

fun CloseAccountStepType.toSimpleStep(status: CloseAccountStepStatus = CloseAccountStepStatus.Pending) = SimpleCloseAccountStep(this, status)

@Singleton
class UnsubscribeStepExecutor(
    private val subscriptionService: SubscriptionService,
) : SimpleCloseAccountStepExecutor(CloseAccountStepTypeUnsubscribe) {
    override fun doExecute(account: Account, step: SimpleCloseAccountStep, markers: LogstashMarker): Either<CloseAccountStepError, CloseAccountStepStatus> {
        return subscriptionService.unsubscribe(account.accountId).map {
            CloseAccountStepStatus.Success.right()
        }.getOrElse { error ->
            markers.andAppend("stepError", error)
            when (error) {
                UnsubscribeError.AccountNotFound -> CloseAccountStepStatus.Warning.right()
                UnsubscribeError.BillProcessing -> CloseAccountStepError.GenericError(error.toString()).left()
                is UnsubscribeError.ServerError -> CloseAccountStepError.GenericError(error.message).left()
            }
        }
    }
}

@Singleton
class RemoveFromDDAStepExecutor(
    private val ddaService: DDAService,
) : SimpleCloseAccountStepExecutor(CloseAccountStepTypeRemoveFromDDA) {
    override fun doExecute(account: Account, step: SimpleCloseAccountStep, markers: LogstashMarker): Either<CloseAccountStepError, CloseAccountStepStatus> {
        return ddaService.remove(account.accountId, Document(account.document)).map {
            CloseAccountStepStatus.Success.right()
        }.getOrElse {
            markers.andAppend("stepError", it.message)
            CloseAccountStepError.GenericException(it).left()
        }
    }
}

@Singleton
class SignOutUserStepExecutor(
    private val userPoolAdapter: UserPoolAdapter,
) : SimpleCloseAccountStepExecutor(CloseAccountStepTypeSignOutUser) {
    override fun doExecute(account: Account, step: SimpleCloseAccountStep, markers: LogstashMarker): Either<CloseAccountStepError, CloseAccountStepStatus> {
        userPoolAdapter.signOutUser(account.document)
        return CloseAccountStepStatus.Success.right()
    }
}

@Singleton
class DisableNotificationStepExecutor(
    private val accountService: AccountService,
) : SimpleCloseAccountStepExecutor(CloseAccountStepTypeDisableNotification) {
    override fun doExecute(account: Account, step: SimpleCloseAccountStep, markers: LogstashMarker): Either<CloseAccountStepError, CloseAccountStepStatus> {
        return accountService.updateAccountConfig(
            accountId = account.accountId,
            name = AccountConfigurationName.RECEIVE_NOTIFICATION,
            value = false.toString(),
        ).map {
            CloseAccountStepStatus.Success.right()
        }.getOrElse { error ->
            markers.andAppend("stepError", error)
            when (error) {
                UpdateAccountConfigError.UserNotAllowed -> CloseAccountStepStatus.Warning.right()
                UpdateAccountConfigError.WalletCannotBeSetAsDefault -> CloseAccountStepError.GenericError(error.toString()).left()
                UpdateAccountConfigError.WalletNotFound -> CloseAccountStepStatus.Warning.right()
            }
        }
    }
}

@Singleton
class RemoveFromUserPoolStepExecutor(
    private val userPoolAdapter: UserPoolAdapter,
) : SimpleCloseAccountStepExecutor(CloseAccountStepTypeRemoveFromUserPool) {
    override fun doExecute(account: Account, step: SimpleCloseAccountStep, markers: LogstashMarker): Either<CloseAccountStepError, CloseAccountStepStatus> {
        return userPoolAdapter.disableUser(account.document).map {
            CloseAccountStepStatus.Success.right()
        }.getOrElse { error ->
            markers.andAppend("stepError", error)
            when (error) {
                is UserPoolRemoveUserError.Unknown -> CloseAccountStepError.GenericException(error.exception).left()
                UserPoolRemoveUserError.UserNotFound -> CloseAccountStepStatus.Warning.right()
            }
        }
    }
}

@Singleton
class RemoveLoginStepExecutor(
    private val loginService: LoginService,
) : SimpleCloseAccountStepExecutor(CloseAccountStepTypeRemoveLogin) {
    override fun doExecute(account: Account, step: SimpleCloseAccountStep, markers: LogstashMarker): Either<CloseAccountStepError, CloseAccountStepStatus> {
        loginService.remove(account.accountId)
        return CloseAccountStepStatus.Success.right()
    }
}

@Singleton
class UpdateStatusClosedStepExecutor(
    private val backOfficeAccountService: BackOfficeAccountService,
) : SimpleCloseAccountStepExecutor(CloseAccountStepTypeUpdateStatusClosed) {
    override fun doExecute(account: Account, step: SimpleCloseAccountStep, markers: LogstashMarker): Either<CloseAccountStepError, CloseAccountStepStatus> {
        backOfficeAccountService.updateStatusClosed(account.accountId)
        return CloseAccountStepStatus.Success.right()
    }
}

@Singleton
class RemoveFromWalletsStepExecutor(
    private val backOfficeWalletService: BackOfficeWalletService,
) : SimpleCloseAccountStepExecutor(CloseAccountStepTypeRemoveFromWallets) {
    override fun doExecute(account: Account, step: SimpleCloseAccountStep, markers: LogstashMarker): Either<CloseAccountStepError, CloseAccountStepStatus> {
        backOfficeWalletService.removeFromWallets(account.accountId)
        return CloseAccountStepStatus.Success.right()
    }
}

@Singleton
class DeleteCustomPixKeysStepExecutor(
    private val pixKeyService: PixKeyService,
    private val walletService: WalletService,
) : SimpleCloseAccountStepExecutor(CloseAccountStepTypeDeleteCustomPixKeys) {
    override fun doExecute(account: Account, step: SimpleCloseAccountStep, markers: LogstashMarker): Either<CloseAccountStepError, CloseAccountStepStatus> {
        walletService.findAllFounderWallets(account.accountId).forEach { wallet ->
            pixKeyService.listPixKeys(wallet.id).forEach {
                pixKeyService.deletePixKey(wallet.id, it).getOrElse { error ->
                    markers.andAppend("stepError", error).andAppend("walletId", wallet.id.value)
                    return when (error) {
                        DeletePixKeyError.AccountNotFound -> CloseAccountStepStatus.Warning.right()
                        DeletePixKeyError.PixKeyNotFound -> CloseAccountStepStatus.Warning.right()
                        is DeletePixKeyError.ServerError -> CloseAccountStepError.GenericError(error.message).left()
                    }
                }
            }
        }

        return CloseAccountStepStatus.Success.right()
    }
}

@Singleton
class CloseCreditCardsStepExecutor(
    private val backOfficeAccountService: BackOfficeAccountService,
) : SimpleCloseAccountStepExecutor(CloseAccountStepTypeCloseCreditCards) {
    override fun doExecute(account: Account, step: SimpleCloseAccountStep, markers: LogstashMarker): Either<CloseAccountStepError, CloseAccountStepStatus> {
        backOfficeAccountService.closeCreditCards(account.accountId)
        return CloseAccountStepStatus.Success.right()
    }
}

@Singleton
class CloseCrmContactStepExecutor(
    private val crmService: CrmService,
) : SimpleCloseAccountStepExecutor(CloseAccountStepTypeCloseCrmContact) {
    override fun doExecute(account: Account, step: SimpleCloseAccountStep, markers: LogstashMarker): Either<CloseAccountStepError, CloseAccountStepStatus> {
        crmService.removeContactAsync(account.accountId)
        return CloseAccountStepStatus.Success.right()
    }
}

@Singleton
class DeactivateAccountRegisterStepExecutor(
    private val accountRegisterRepository: AccountRegisterRepository,
    private val registerInstrumentationService: RegisterInstrumentationService,
) : CloseAccountStepExecutor<DeactivateAccountRegisterStep>(CloseAccountStepTypeDeactivateAccountRegister) {
    private val logger = LoggerFactory.getLogger(DeactivateAccountRegisterStepExecutor::class.java)

    override fun doExecute(account: Account, step: DeactivateAccountRegisterStep, markers: LogstashMarker): Either<CloseAccountStepError, CloseAccountStepStatus> {
        return try {
            registerInstrumentationService.closed(account.accountId, account.type, step.closureDetails)
            accountRegisterRepository.deactivate(account.accountId, step.closureDetails)
            CloseAccountStepStatus.Success.right()
        } catch (e: ItemNotFoundException) {
            logger.warn(
                append("accountId", account.accountId.value),
                "DeactivateAccountRegisterStepExecutor",
                e,
            )
            CloseAccountStepStatus.Warning.right()
        }
    }
}

@Singleton
class MarkAsFraudStepExecutor(
    private val livenessService: LivenessService,
) : SimpleCloseAccountStepExecutor(CloseAccountStepTypeMarkAsFraud) {
    override fun doExecute(account: Account, step: SimpleCloseAccountStep, markers: LogstashMarker): Either<CloseAccountStepError, CloseAccountStepStatus> {
        return livenessService.markAsFraud(account.accountId).map {
            CloseAccountStepStatus.Success.right()
        }.getOrElse { error ->
            markers.andAppend("stepError", error)
            when (error) {
                is LivenessErrors.Error -> CloseAccountStepError.GenericException(error.e).left()
                is LivenessErrors.AccountNotFound -> CloseAccountStepStatus.Warning.right()
                else -> CloseAccountStepError.GenericError(error.toString()).left()
            }
        }
    }
}

@Singleton
class CloseFounderWalletStepExecutor(
    private val walletService: WalletService,
    private val executors: List<CloseWalletStepExecutor>,
) : CloseAccountStepExecutor<CloseFounderWalletCloseAccountStep>(CloseAccountStepTypeCloseFounderWallet) {
    override fun doExecute(account: Account, step: CloseFounderWalletCloseAccountStep, markers: LogstashMarker): Either<CloseAccountStepError, CloseAccountStepStatus> {
        val wallet = walletService.findWallet(step.walletId)

        step.steps.forEach { walletStep ->
            executeWalletStep(wallet, walletStep).getOrElse {
                return it.left()
            }
        }

        return CloseAccountStepStatus.Success.right()
    }

    private fun executeWalletStep(wallet: Wallet, step: CloseWalletStep): Either<CloseAccountStepError, Unit> {
        if (step.status in listOf(CloseWalletStepStatus.Pending, CloseWalletStepStatus.Error)) {
            val executor = executors.singleOrNull { it.stepType == step.type } ?: throw IllegalStateException("executor not found for step ${step.type}")

            val result = executor.execute(wallet)

            result.map {
                step.status = it
                step.errorMessage = null
            }.getOrElse {
                step.status = CloseWalletStepStatus.Error
                step.errorMessage = when (it) {
                    is CloseWalletStepError.GenericError -> it.error
                    is CloseWalletStepError.GenericException -> it.message
                }

                return when (it) {
                    is CloseWalletStepError.GenericError -> CloseAccountStepError.GenericError(it.error).left()
                    is CloseWalletStepError.GenericException -> CloseAccountStepError.GenericException(it.exception).left()
                }
            }
        }

        return Unit.right()
    }
}