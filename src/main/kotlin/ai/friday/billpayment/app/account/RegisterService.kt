package ai.friday.billpayment.app.account

import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.adapters.lock.updateAccountStatusLockProvider
import ai.friday.billpayment.and
import ai.friday.billpayment.app.AsyncUtils.callAsync
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.chatbot.ChatbotNotificationService
import ai.friday.billpayment.app.dda.DDAProvider
import ai.friday.billpayment.app.dda.DDAService
import ai.friday.billpayment.app.dda.optedOut
import ai.friday.billpayment.app.documentscan.DocumentScanImageWithType
import ai.friday.billpayment.app.documentscan.DocumentScanService
import ai.friday.billpayment.app.documentscan.GetImageError
import ai.friday.billpayment.app.documentscan.ImageContent
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.AlreadyLockedException
import ai.friday.billpayment.app.integrations.BigDataService
import ai.friday.billpayment.app.integrations.CrmService
import ai.friday.billpayment.app.integrations.DocumentOCRParser
import ai.friday.billpayment.app.integrations.DocumentOCRParserException
import ai.friday.billpayment.app.integrations.DocumentType
import ai.friday.billpayment.app.integrations.ECMProvider
import ai.friday.billpayment.app.integrations.ECMProviderException
import ai.friday.billpayment.app.integrations.EventPublisher
import ai.friday.billpayment.app.integrations.ExternalAccountRegister
import ai.friday.billpayment.app.integrations.ExternalAccountRegisterException
import ai.friday.billpayment.app.integrations.FraudListConfiguration
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.InvalidDocumentImageException
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.LoginRepository
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.PersonBasicInfo
import ai.friday.billpayment.app.integrations.RegisterInstrumentationService
import ai.friday.billpayment.app.integrations.TokenType
import ai.friday.billpayment.app.integrations.UserPoolAdapter
import ai.friday.billpayment.app.integrations.UserPoolUsernameExistsException
import ai.friday.billpayment.app.integrations.WelcomeMessageType
import ai.friday.billpayment.app.isValidCpf
import ai.friday.billpayment.app.journey.UserJourneyEvent
import ai.friday.billpayment.app.journey.UserJourneyService
import ai.friday.billpayment.app.liveness.LivenessErrors
import ai.friday.billpayment.app.liveness.LivenessSelfieError
import ai.friday.billpayment.app.liveness.LivenessService
import ai.friday.billpayment.app.login.ProviderName
import ai.friday.billpayment.app.login.ProviderUser
import ai.friday.billpayment.app.msisdnauth.createWhatsappEmail
import ai.friday.billpayment.app.onboarding.OnboardingTestPixService
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.pix.RegisterPixKeyCommand
import ai.friday.billpayment.app.register.instrumentation.CrmContactNotFoundException
import ai.friday.billpayment.app.register.instrumentation.RegisterUtil
import ai.friday.billpayment.app.register.kyc.KycService
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.app.usage.WalletLimitsService
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.context.annotation.EachProperty
import io.micronaut.context.annotation.Parameter
import io.micronaut.context.annotation.Property
import io.micronaut.http.MediaType
import io.via1.communicationcentre.app.email.Attachment
import io.via1.communicationcentre.app.integrations.EmailSenderService
import io.via1.communicationcentre.app.integrations.PdfFileParser
import jakarta.inject.Named
import jakarta.inject.Singleton
import java.awt.image.BufferedImage
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.io.InputStream
import java.time.LocalDate
import java.util.*
import javax.imageio.ImageIO
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers.append
import org.imgscalr.Scalr
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.cognitoidentityprovider.model.UserNotFoundException

@ConfigurationProperties("accountRegister.user_files")
class UserFilesConfiguration {
    lateinit var bucket: String
    lateinit var path: String
    lateinit var documentPrefix: String
    lateinit var selfiePrefix: String
    lateinit var region: String
    lateinit var contractPrefix: String
    lateinit var declarationOfResidencyPrefix: String
    lateinit var kycPrefix: String
}

@ConfigurationProperties("accountRegister.address")
class UserAddressConfiguration {
    lateinit var streetType: String
    lateinit var streetName: String
    lateinit var number: String
    lateinit var complement: String
    lateinit var neighborhood: String
    lateinit var city: String
    lateinit var state: String
    lateinit var zipCode: String
}

fun UserAddressConfiguration.toAddress(): Address {
    return Address(
        streetType = streetType,
        streetName = streetName,
        number = number,
        complement = complement,
        neighborhood = neighborhood,
        city = city,
        state = state,
        zipCode = zipCode,
    )
}

data class CreateAccountCommand(
    val name: String,
    val document: String,
    val email: String,
    val mobilePhone: MobilePhone,
    val notificationGateway: NotificationGateways,
    val externalId: ExternalId? = null,
    val receiveNotification: Boolean = false,
    val ddaOptin: Boolean = true,
    val channel: String? = null,
    val providerName: ProviderName,
    val ddaProvider: DDAProvider?,
    val imageUrlSmall: String?,
    val imageUrlLarge: String?,
    val importPastWallets: Boolean = false,
    val subscriptionType: SubscriptionType,
)

// TODO: vai virar o UpgradeRegisterService aqui vai ficar todas chamadas feitas pelos fluxos de upgrade, o que for do fluxo básico deve ser migrado para o BasicRegisterService
// TODO: Upload de document parece ser o servico que mais faz sentido ser extraido, podemos criar um UploadDocumentService separado
@Singleton
open class RegisterService(
    private val tokenService: TokenService,
    private val accountRegisterRepository: AccountRegisterRepository,
    private val accountService: AccountService,
    private val accountRegisterService: AccountRegisterService,
    private val documentOCRParser: DocumentOCRParser,
    private val userFilesConfiguration: UserFilesConfiguration,
    private val agreementFilesService: AgreementFilesService,
    private val notificationSenderService: EmailSenderService,
    private val notificationAdapter: NotificationAdapter,
    private val userPoolAdapter: UserPoolAdapter,
    private val activityService: SystemActivityService,
    private val bigDataService: BigDataService,
    private val kycService: KycService,
    private val externalAccountRegister: ExternalAccountRegister,
    private val ecmProvider: ECMProvider,
    private val walletService: WalletService,
    private val walletLimitsService: WalletLimitsService,
    private val crmService: CrmService,
    private val registerInstrumentationService: RegisterInstrumentationService,
    private val pdfFileParser: PdfFileParser,
    private val ddaService: DDAService,
    private val userJourneyService: UserJourneyService,
    private val closeAccountService: CloseAccountService,
    private val systemActivityService: SystemActivityService,
    private val livenessService: LivenessService,
    private val eventPublisher: EventPublisher,
    @Named("pending-internal-approve")
    private val pendingInternalApproveConfiguration: NewAccountEmailConfiguration,
    private val pendingUpgradeInternalApproveConfiguration: NewUpgradeAccountEmailConfiguration,
    @Named("pending-internal-review")
    private val pendingInternalReviewConfiguration: NewAccountEmailConfiguration,
    @Named("pending-activation")
    private val pendingActivationConfiguration: NewAccountEmailConfiguration,
    @Named(updateAccountStatusLockProvider) private val accountStatusLockProvider: InternalLock,
    private val fraudList: FraudListConfiguration,
    private val messagePublisher: MessagePublisher,
    private val adService: AdService,
    private val chatBotNotificationService: ChatbotNotificationService,
    private val walletBillCategoryService: PFMWalletCategoryService,
    private val documentScanService: DocumentScanService,
    private val loginRepository: LoginRepository,
) {

    private val acceptedDocumentType = listOf(DocumentType.CNH, DocumentType.CNHV2)

    @field:Property(name = "email.notification.email")
    lateinit var from: String

    @field:Property(name = "accountRegister.pixKey.emailDomain")
    lateinit var pixKeyEmailDomain: String

    @field:Property(name = "integrations.bigdatacorp.minimum-document-size-to-optimize")
    lateinit var maxDocumentSize: Number

    @field:Property(name = "sqs.registerPixKeyQueueName")
    lateinit var registerPixKeyQueueName: String

    fun processMonthlyIncome(
        accountId: AccountId,
        monthlyIncome: MonthlyIncome,
    ): Either<Exception, AccountRegisterData> {
        if (checkUserBlockedForEdition(accountId)) {
            return Either.Left(AccountIsBlockedForEdition())
        }

        if (!isMonthlyIncomeValid(monthlyIncome)) {
            return Either.Left(InvalidMonthlyIncomeException())
        }

        val accountRegisterUpdated = accountRegisterRepository.findByAccountId(accountId).copy(
            monthlyIncome = monthlyIncome,
        )

        return Either.Right(accountRegisterRepository.save(accountRegisterUpdated))
    }

    fun createAccountRegister(
        accountId: AccountId,
        emailAddress: EmailAddress,
        username: String,
        mobilePhone: MobilePhone? = null,
        registrationType: RegistrationType,
    ) {
        accountRegisterRepository.create(
            accountId = accountId,
            emailAddress = emailAddress,
            username = username,
            mobilePhone = mobilePhone,
            mobilePhoneVerified = false,
            registrationType = registrationType,
        )
    }

    @Deprecated("O documento é enviado via document scan")
    open fun processRGDocumentFile(
        accountId: AccountId,
        document: InputStream,
        frontSide: Boolean,
        extension: String,
        ocr: Boolean = true,
    ): Either<Exception, AccountRegisterData> {
        try {
            if (checkUserBlockedForEdition(accountId)) {
                return Either.Left(AccountIsBlockedForEdition())
            }

            val account = accountRegisterRepository.findByAccountId(accountId)

            if (account.documentScan != null) {
                return Exception("Cannot process RG when account has document scan").left()
            }

            return if (frontSide) {
                processFrontRGDocumentImage(
                    accountRegisterData = account,
                    document = document,
                    extension = extension,
                    ocr = ocr,
                )
            } else {
                processBackRGDocumentImage(
                    accountRegisterData = account,
                    document = document,
                    extension = extension,
                    ocr = ocr,
                )
            }.right()
        } catch (e: Exception) {
            if (e is InvalidDocumentImageException) {
                return e.left()
            }

            if (e is DocumentOCRParserException) {
                return e.left()
            }
            return DocumentOCRParserException(e).left()
        }
    }

    private fun fetchDocumentQualityExcludingCNH(base64Image: String): DocumentQuality {
        val documentQuality = documentOCRParser.fetchDocumentQuality(base64Image).getOrElse {
            throw it
        }

        return if (documentQuality.typeOfDocument == DocumentType.CNH) {
            documentQuality.copy(typeOfDocument = DocumentType.RG)
        } else {
            documentQuality
        }
    }

    private fun processFrontRGDocumentImage(
        accountRegisterData: AccountRegisterData,
        document: InputStream,
        extension: String,
        ocr: Boolean = true,
    ): AccountRegisterData {
        val (bytesToOCRParser, storedObject) = storeDocumentFile(
            extension = extension,
            accountId = accountRegisterData.accountId,
            originalBytes = document.toByteArray(),
            keySuffix = "FRONT_",
        )

        if (!ocr) {
            val updatedAccountRegister = accountRegisterData.copy(
                uploadedCNH = null,
                uploadedDocument = UploadedDocumentImages(
                    front = storedObject,
                    documentType = DocumentType.RG,
                    back = null,
                ),
            )
            accountRegisterRepository.save(updatedAccountRegister)

            return updatedAccountRegister
        }

//        FIXME removendo temporariamente. O bigDataCorp comecou a responder que a imagem n˜åo esta no tamanho certo apesar de estar
//        val base64Encoder = Base64.getEncoder()
//        val base64Image = base64Encoder.encodeToString(bytesToOCRParser)
//        val documentQuality = fetchDocumentQualityExcludingCNH(base64Image)
//        if (!documentQuality.containsFace) {
//            throw FaceNotFoundException()
//        }

        val accountRegisterUpdated = accountRegisterData.copy(
            uploadedCNH = null,
            uploadedDocument = UploadedDocumentImages(
                front = storedObject,
                documentType = DocumentType.RG,
                back = null,
            ),
        )
        accountRegisterRepository.save(accountRegisterUpdated)

        return accountRegisterUpdated
    }

    private fun processBackRGDocumentImage(
        accountRegisterData: AccountRegisterData,
        document: InputStream,
        extension: String,
        ocr: Boolean = true,
    ): AccountRegisterData {
        if (accountRegisterData.uploadedDocument == null) {
            throw InvalidDocumentImageException("Face document must be sent first")
        }

        val base64Encoder = Base64.getEncoder()

        val (bytesToOCRParser, storedObject) = storeDocumentFile(
            extension = extension,
            accountId = accountRegisterData.accountId,
            originalBytes = document.toByteArray(),
            keySuffix = "BACK_",
        )

        if (!ocr) {
            val updatedAccountRegister = accountRegisterData.copy(
                uploadedDocument = accountRegisterData.uploadedDocument.copy(
                    back = storedObject,
                ),
            )

            accountRegisterRepository.save(updatedAccountRegister)

            return updatedAccountRegister
        }

        val backDocumentBase64 = base64Encoder.encodeToString(bytesToOCRParser)

        val frontBytes =
            accountRegisterRepository.getDocumentInputStream(accountRegisterData.uploadedDocument.front).toByteArray()
        val frontDocumentBase64 = base64Encoder.encodeToString(frontBytes)

        val ocrParserResult = documentOCRParser.parseDocument(
            frontDocumentBase64,
            backDocumentBase64,
            accountRegisterData.uploadedDocument.documentType,
        ).getOrElse { throw it }

        return validateDocumentOcrDataAndUpdateAccountRegister(
            ocrParserResult = ocrParserResult,
            accountId = accountRegisterData.accountId,
            uploadedCNH = null,
            uploadedDocument = accountRegisterData.uploadedDocument.copy(
                back = storedObject,
            ),
        )
    }

    open fun processCNHDocumentFile(
        accountId: AccountId,
        document: InputStream,
        extension: String,
        ocr: Boolean = true,
    ): Either<Exception, AccountRegisterData> {
        try {
            if (checkUserBlockedForEdition(accountId)) {
                return Either.Left(AccountIsBlockedForEdition())
            }

            val (bytesToOCRParser, storedObject) = storeDocumentFile(
                extension = extension,
                accountId = accountId,
                originalBytes = document.toByteArray(),
            )

            if (!ocr) {
                val updatedAccountRegister = accountRegisterRepository.findByAccountId(accountId).copy(
                    isDocumentEdited = false,
                    uploadedCNH = storedObject,
                )

                accountRegisterRepository.save(updatedAccountRegister)

                return updatedAccountRegister.right()
            }

            val base64Image = Base64.getEncoder().encodeToString(bytesToOCRParser)
            val ocrParserResult = documentOCRParser.parseDocument(base64Image).getOrElse {
                return Either.Left(it)
            }
            return if (!acceptedDocumentType.contains(ocrParserResult.docType)) {
                Either.Left(InvalidDocumentImageException("Tipo de documento inválido(${ocrParserResult.docType.value})."))
            } else {
                return validateDocumentOcrDataAndUpdateAccountRegister(
                    ocrParserResult = ocrParserResult,
                    accountId = accountId,
                    uploadedCNH = storedObject,
                    uploadedDocument = null,
                ).right()
            }
        } catch (e: Exception) {
            return Either.Left(e)
        }
    }

    private fun validateDocumentOcrDataAndUpdateAccountRegister(
        ocrParserResult: DocumentInfo,
        accountId: AccountId,
        uploadedCNH: StoredObject?,
        uploadedDocument: UploadedDocumentImages?,
    ): AccountRegisterData {
        val ocrParserResultWithValidDocument = if (ocrParserResult.cpf.isValidCpf()) {
            ocrParserResult
        } else {
            ocrParserResult.copy(cpf = "")
        }

        val personalData = if (ocrParserResultWithValidDocument.cpf.isNotBlank()) {
            bigDataService.getPersonBasicInfo(ocrParserResultWithValidDocument.cpf)
                .getOrElse {
                    LOG.warn(append("accountId", accountId.value), "getPersonBasicInfo", it)
                    null
                }
        } else {
            null
        }

        val validatedDocumentInfo =
            personalData?.let { validateOcrResult(ocrParserResultWithValidDocument, personalData) }
                ?: validateOcrResult(ocrParserResultWithValidDocument)

        val accountRegisterUpdated = accountRegisterRepository.findByAccountId(accountId).copy(
            documentInfo = validatedDocumentInfo,
            isDocumentEdited = false,
            uploadedCNH = uploadedCNH,
            uploadedDocument = uploadedDocument,
            calculatedGender = personalData.calculateGender(validatedDocumentInfo),
        )

        return accountRegisterRepository.save(accountRegisterUpdated)
            .also {
                updateUserOnCrm(it)
                accountRegisterRepository.saveOriginalOcrAndPersonData(
                    accountId = accountId,
                    originalDocumentInfo = ocrParserResult,
                    personBasicInfo = personalData,
                    validatedDocumentInfo = validatedDocumentInfo,
                )
            }
    }

    private fun PersonBasicInfo?.calculateGender(validatedDocumentInfo: DocumentInfo) = when (this?.gender) {
        "M", "F" -> Gender.valueOf(gender)
        else -> RegisterUtil.findGenderByName(validatedDocumentInfo.name)
    }

    internal fun validateOcrResult(
        documentInfo: DocumentInfo,
        personalData: PersonBasicInfo,
    ): DocumentInfo {
        return documentInfo.copy(
            name = validateOcrResult(documentInfo.name, personalData.name),
            motherName = validateOcrResult(documentInfo.motherName, personalData.motherName),
            fatherName = validateOcrResult(documentInfo.fatherName, personalData.fatherName),
            birthDate = documentInfo.birthDate?.let { this.validateOcrResult(it, personalData.birthDate) },
            expeditionDate = documentInfo.expeditionDate?.takeIf {
                getLocalDate().minusYears(70) < it && it <= getLocalDate()
            },
        )
    }

    private fun validateOcrResult(
        documentInfo: DocumentInfo,
    ): DocumentInfo {
        return documentInfo.copy(
            birthDate = documentInfo.birthDate?.takeIf {
                getLocalDate().minusYears(100) < it && it <= getLocalDate()
            },
            expeditionDate = documentInfo.expeditionDate?.takeIf {
                getLocalDate().minusYears(70) < it && it <= getLocalDate()
            },
        )
    }

    private fun validateOcrResult(toBeValidated: String, valid: String?): String {
        if (valid != null && toBeValidated.contains(other = valid, ignoreCase = true)) {
            return valid
        }

        return ""
    }

    private fun validateOcrResult(toBeValidated: LocalDate, valid: LocalDate?): LocalDate? {
        if (valid != null && toBeValidated == valid) {
            return valid
        }

        return null
    }

    private fun storeDocumentFile(
        extension: String,
        accountId: AccountId,
        originalBytes: ByteArray?,
        keySuffix: String = "",
    ): Pair<ByteArray, StoredObject> {
        return when (extension) {
            "jpeg", "png" -> {
                val key = buildObjectPath(accountId, userFilesConfiguration.documentPrefix + keySuffix, extension)
                val storedObject = StoredObject(userFilesConfiguration.region, userFilesConfiguration.bucket, key)

                Pair(
                    ImageOptimizer.optimize(ByteArrayInputStream(originalBytes), extension).toByteArray(),
                    storedObject,
                )
            }

            "pdf" -> {
                val OPTIMIZE_THRESHOLD = 1048576 // 1024 * 1024
                val extensionToConvert = "jpeg"
                val key =
                    buildObjectPath(accountId, userFilesConfiguration.documentPrefix + keySuffix, extensionToConvert)
                val storedObject = StoredObject(userFilesConfiguration.region, userFilesConfiguration.bucket, key)

                val bufferedImage = pdfFileParser.parseToImage(ByteArrayInputStream(originalBytes), 0)
                val baos = ByteArrayOutputStream()
                ImageIO.write(bufferedImage.get(), extensionToConvert, baos)
                val unOptimizedImageSize = baos.size()
                val finalImageByteArray: ByteArray = if (unOptimizedImageSize > OPTIMIZE_THRESHOLD) {
                    ImageOptimizer.optimize(ByteArrayInputStream(baos.toByteArray()), extensionToConvert).toByteArray()
                } else {
                    baos.toByteArray()
                }
                Pair(finalImageByteArray, storedObject)
            }

            else -> {
                throw IllegalStateException("Media type is not supported yet")
            }
        }.also { (bytesToOCRParser, storedObject) ->
            val originalMediaType = when (extension) {
                "jpeg", "jpg" -> MediaType.IMAGE_JPEG_TYPE
                "png" -> MediaType.IMAGE_PNG_TYPE
                "pdf" -> MediaType.APPLICATION_PDF_TYPE
                else -> MediaType.APPLICATION_OCTET_STREAM_TYPE
            }
            val processedMediaType = when (extension) {
                "pdf" -> MediaType.IMAGE_JPEG_TYPE // PDF is converted to JPEG
                "jpeg", "jpg" -> MediaType.IMAGE_JPEG_TYPE
                "png" -> MediaType.IMAGE_PNG_TYPE
                else -> MediaType.APPLICATION_OCTET_STREAM_TYPE
            }

            accountRegisterRepository.putDocument(
                storedObject.copy(
                    key = buildObjectPath(
                        accountId = accountId,
                        prefix = "${userFilesConfiguration.documentPrefix}${keySuffix}original_",
                        extension = extension,
                    ),
                ),
                ByteArrayInputStream(originalBytes),
                originalMediaType,
            )
            accountRegisterRepository.putDocument(storedObject, ByteArrayInputStream(bytesToOCRParser), processedMediaType)
        }
    }

    // TODO: deve ir para o basic
    open fun createLivenessId(accountId: AccountId): Either<LivenessErrors, AccountRegisterData> {
        val livenessId = livenessService.enroll(accountId).getOrElse {
            return it.left()
        }

        val accountRegisterUpdated = accountRegisterRepository.findByAccountId(accountId).copy(
            livenessId = livenessId,
        )

        return accountRegisterRepository.save(accountRegisterUpdated).right()
    }

    // TODO: deve ir para o basic
    open fun processLiveness(accountRegister: AccountRegisterData): Either<Exception, AccountRegisterData> {
        if (accountRegister.livenessId == null || accountRegister.uploadedSelfie != null) {
            return accountRegister.right()
        }

        val selfieBytes = livenessService.retrieveEnrollmentSelfie(accountRegister.livenessId).getOrElse {
            return when (it) {
                is LivenessSelfieError.Error -> it.e.left()
                LivenessSelfieError.Unavailable -> accountRegister.right()
            }
        }

        return processSelfie(
            accountId = accountRegister.accountId,
            selfie = ByteArrayInputStream(selfieBytes),
            extension = "jpeg",
            optimize = false,
        )
    }

    // TODO: deve ir para o basic
    open fun processSelfie(
        accountId: AccountId,
        selfie: InputStream,
        extension: String,
        optimize: Boolean = true,
    ): Either<Exception, AccountRegisterData> {
        try {
            if (checkUserBlockedForEdition(accountId)) {
                return Either.Left(AccountIsBlockedForEdition())
            }
            val key = buildObjectPath(accountId, userFilesConfiguration.selfiePrefix, extension)
            val storedObject = StoredObject(userFilesConfiguration.region, userFilesConfiguration.bucket, key)

            val originalBytes = selfie.toByteArray()

            val selfieMediaType = when (extension) {
                "jpeg", "jpg" -> MediaType.IMAGE_JPEG_TYPE
                "png" -> MediaType.IMAGE_PNG_TYPE
                else -> MediaType.APPLICATION_OCTET_STREAM_TYPE
            }

            val databaseReferencedSelfie = if (optimize) {
                accountRegisterRepository.putDocument(
                    storedObject.copy(
                        key = buildObjectPath(
                            accountId,
                            userFilesConfiguration.selfiePrefix + "original_",
                            extension,
                        ),
                    ),
                    ByteArrayInputStream(originalBytes),
                    selfieMediaType,
                )
                ImageOptimizer.optimize(ByteArrayInputStream(originalBytes), extension).toByteArray()
            } else {
                originalBytes
            }

            accountRegisterRepository.putDocument(storedObject, ByteArrayInputStream(databaseReferencedSelfie), selfieMediaType)

            val accountRegisterUpdated =
                accountRegisterRepository.findByAccountId(accountId = accountId, checkOpenAccount = false)

            return Either.Right(
                accountRegisterRepository.save(
                    accountRegisterUpdated.copy(
                        uploadedSelfie = storedObject,
                        uploadedSelfieDateTime = getZonedDateTime(),
                    ),
                ),
            )
        } catch (e: Exception) {
            return Either.Left(e)
        }
    }

    open fun processDocumentDetails(
        accountId: AccountId,
        documentDetails: DocumentDetails,
    ): Either<Exception, AccountRegisterData> {
        try {
            if (checkUserBlockedForEdition(accountId)) {
                return Either.Left(AccountIsBlockedForEdition())
            }

            val accountRegisterData = accountRegisterRepository.findByAccountId(accountId)
            val documentInfo = DocumentInfo(
                name = accountRegisterData.nickname,
                cpf = accountRegisterData.document!!.value,
                birthDate = accountRegisterData.birthDate,
                fatherName = documentDetails.fatherName ?: "",
                motherName = documentDetails.motherName,
                rg = if (documentDetails.documentNumber.type == DocumentDetailsType.IDENTITY) {
                    documentDetails.documentNumber.value
                } else {
                    ""
                },
                docType = when (documentDetails.documentNumber.type) {
                    DocumentDetailsType.IDENTITY -> DocumentType.RG
                    DocumentDetailsType.DRIVERS_LICENSE -> DocumentType.CNH
                },
                cnhNumber = if (documentDetails.documentNumber.type == DocumentDetailsType.DRIVERS_LICENSE) {
                    documentDetails.documentNumber.value
                } else {
                    null
                },
                orgEmission = documentDetails.orgEmission,
                expeditionDate = documentDetails.expeditionDate,
                birthCity = documentDetails.birthCity,
                birthState = documentDetails.birthState,
            )

            val updatedAccountRegisterData = accountRegisterData.copy(documentInfo = documentInfo)

            return accountRegisterRepository.save(updatedAccountRegisterData)
                .also { updateUserOnCrm(it) }
                .right()
        } catch (e: Exception) {
            return Either.Left(e)
        }
    }

    open fun processAddress(accountId: AccountId, address: Address): Either<Exception, AccountRegisterData> {
        try {
            if (checkUserBlockedForEdition(accountId)) {
                return Either.Left(AccountIsBlockedForEdition())
            }
            val accountRegisterData = accountRegisterRepository.findByAccountId(accountId)

            val temp = accountRegisterData.copy(
                address = Address(
                    streetType = address.streetType,
                    streetName = address.streetName,
                    number = address.number,
                    complement = address.complement,
                    neighborhood = address.neighborhood,
                    city = address.city,
                    state = address.state,
                    zipCode = address.zipCode,
                ),
            )
            return Either.Right(accountRegisterRepository.save(temp))
        } catch (e: Exception) {
            return Either.Left(e)
        }
    }

    open fun processAgreement(accountId: AccountId, clientIP: String): Either<Exception, AccountRegisterData> {
        try {
            if (checkUserBlockedForEdition(accountId)) {
                return Either.Left(AccountIsBlockedForEdition())
            }
            val accountRegisterData = accountRegisterRepository.findByAccountId(accountId)
            if (!accountRegisterData.readyForAgreement()) {
                return Either.Left(RegisterIncompleteException())
            }

            if (!accountRegisterService.checkIsTestAccount(accountId)) {
                val activeAccountId = findActiveAccountIdByMobilePhone(accountId, accountRegisterData.mobilePhone!!)
                if (activeAccountId != null) {
                    return Either.Left(MobilePhoneAlreadyInUseException(accountId = activeAccountId))
                }
            }

            try {
                registerInstrumentationService.completed(
                    accountRegisterData.accountId,
                    accountRegisterData.registrationType,
                )
            } catch (e: Exception) {
                LOG.error(append("ACTION", "VERIFY"), "AgreementCRMError", e)
            }

            val userContractSignature = agreementFilesService.getContractSignature(accountRegisterData.toContractForm())

            val agreementData = if (userContractSignature == accountRegisterData.agreementData?.userContractSignature) {
                accountRegisterData.agreementData.copy(acceptedAt = getZonedDateTime())
            } else {
                val userContractFile = createContractPdf(
                    accountRegisterData = accountRegisterData,
                    clientIP = clientIP,
                )
                val declarationOfResidencyFile = createDeclarationOfResidencyPdf(
                    accountRegisterData = accountRegisterData,
                    clientIP = clientIP,
                )
                AgreementData(
                    acceptedAt = getZonedDateTime(),
                    userContractFile = userContractFile,
                    userContractSignature = userContractSignature,
                    declarationOfResidencyFile = declarationOfResidencyFile,
                )
            }

            val openForUserReview = accountRegisterData.openForUserReview

            val accountRegisterDataUpdated = accountRegisterRepository.save(
                accountRegisterData.copy(
                    agreementData = agreementData,
                    openForUserReview = false,
                    fraudListMatch = accountRegisterData.getCPF() in fraudList.documents,
                ),
            )
            accountService.updatePartialAccountStatus(accountId, AccountStatus.UNDER_REVIEW)

            if (openForUserReview) {
                notificationAdapter.notifyRegisterUpdated(
                    accountId,
                    accountRegisterDataUpdated.mobilePhone!!,
                    accountRegisterDataUpdated.getName(),
                )
            } else {
                val partialAccount = accountService.findPartialAccountById(accountId)
                chatBotNotificationService.notifyRegisterCompleted(partialAccount)
            }

            if (!accountRegisterService.checkIsTestAccount(accountRegisterDataUpdated.mobilePhone!!) && accountRegisterDataUpdated.livenessId != null) {
                livenessService.verifyDuplication(accountId).map {
                    accountRegisterRepository.save(
                        accountRegisterDataUpdated.copy(
                            livenessEnrollmentVerification = it,
                        ),
                    )
                }.getOrElse {
                    LOG.error(
                        append("accountId", accountId.value).andAppend("ACTION", "VERIFY"),
                        "UnableToVerifyLivenessDuplication",
                        it,
                    )
                }
            }

            if (!accountRegisterService.checkIsTestAccount(accountRegisterDataUpdated.mobilePhone)) {
                callAsync { submitRegisterForInternalReview(accountId) }
            }

            return Either.Right(accountRegisterDataUpdated)
        } catch (e: Exception) {
            return Either.Left(e)
        }
    }

    open fun processUpgradeAgreement(accountId: AccountId, clientIP: String): Either<Exception, AccountRegisterData> {
        try {
            if (checkUserBlockedForEdition(accountId)) {
                return Either.Left(AccountIsBlockedForEdition())
            }
            val accountRegisterData = accountRegisterRepository.findByAccountId(accountId)
            if (!accountRegisterData.readyForAgreement()) {
                return Either.Left(RegisterIncompleteException())
            }

            try {
                registerInstrumentationService.accountUpgraded(
                    accountRegisterData.accountId,
                )
            } catch (e: Exception) {
                LOG.error(append("ACTION", "VERIFY"), "AgreementCRMError", e)
            }

            val userContractSignature = agreementFilesService.getContractSignature(accountRegisterData.toContractForm())

            val upgradeAgreementData =
                if (userContractSignature == accountRegisterData.upgradeAgreementData?.userContractSignature) {
                    accountRegisterData.upgradeAgreementData.copy(acceptedAt = getZonedDateTime())
                } else {
                    val userContractFile = createContractPdf(
                        accountRegisterData = accountRegisterData,
                        clientIP = clientIP,
                    )
                    val declarationOfResidencyFile = createDeclarationOfResidencyPdf(
                        accountRegisterData = accountRegisterData,
                        clientIP = clientIP,
                    )
                    AgreementData(
                        acceptedAt = getZonedDateTime(),
                        userContractFile = userContractFile,
                        userContractSignature = userContractSignature,
                        declarationOfResidencyFile = declarationOfResidencyFile,
                    )
                }

            val accountRegisterDataUpdated = accountRegisterRepository.save(
                accountRegisterData.copy(
                    upgradeAgreementData = upgradeAgreementData,
                    openForUserReview = false,
                    fraudListMatch = accountRegisterData.getCPF() in fraudList.documents,
                ),
            )

            accountService.updateUpgradeStatus(accountId, UpgradeStatus.UNDER_REVIEW)

            if (!accountRegisterService.checkIsTestAccount(accountRegisterDataUpdated.mobilePhone!!)) {
                callAsync { submitUpgradeForInternalReview(accountId) }
            }

            userJourneyService.trackEventAsync(accountId, UserJourneyEvent.UpgradeRequested)

            return Either.Right(accountRegisterDataUpdated)
        } catch (e: Exception) {
            return Either.Left(e)
        }
    }

    open fun startUpgrade(accountId: AccountId) {
        val account = accountService.findAccountById(accountId)

        if (account.upgradeStatus == null) {
            accountService.save(account.copy(upgradeStatus = UpgradeStatus.INCOMPLETE))

            val accountRegisterData = accountRegisterRepository.findByAccountId(accountId)

            accountRegisterRepository.save(accountRegisterData.copy(upgradeStarted = getZonedDateTime()))
        }
    }

    open fun cleanUpTestAccount(accountId: AccountId, mobilePhone: MobilePhone) {
        if (accountRegisterService.checkIsTestAccount(mobilePhone)) {
            closeAccountService.deletePartialTestAccount(accountId)
        }
    }

    open fun findByAccountId(accountId: AccountId): Either<Exception, AccountRegisterData> {
        return try {
            val account = accountRegisterRepository.findByAccountId(accountId = accountId, checkOpenAccount = false)

            val accountWithPhoneVerification = tokenService.findIssuedToken(accountId, TokenType.MOBILE_PHONE)
                .map { token ->
                    account.copy(
                        mobilePhone = MobilePhone(token.value),
                        mobilePhoneTokenExpiration = token.expiration,
                    )
                }.getOrElse {
                    account
                }

            return tokenService.findIssuedToken(accountId, TokenType.EMAIL).map { token ->
                accountWithPhoneVerification.copy(
                    emailAddress = EmailAddress(token.value),
                    emailTokenExpiration = token.expiration,
                )
            }.getOrElse {
                accountWithPhoneVerification
            }.right()
        } catch (e: Exception) {
            Either.Left(e)
        }
    }

    /*
    primeiro passo apos aprovação interna
    - manda docs pra paperoff
    - notifica o arbi com a transacao 2
     */
    open fun internalApproveAccount(accountId: AccountId): Either<Exception, Unit> {
        return try {
            val partialAccount = accountService.findPartialAccountById(accountId)
            if (partialAccount.status != AccountStatus.UNDER_REVIEW) {
                return Either.Left(AccountIsNotUnderReview(partialAccount.status))
            }

            val accountRegisterData = accountRegisterRepository.findByAccountId(accountId)

            val sendAccountRegisterDocumentsResponse = ecmProvider.sendAccountRegisterDocuments(buildInsertRequest(accountRegisterData))

            if (!sendAccountRegisterDocumentsResponse.success) {
                return Either.Left(ECMProviderException(sendAccountRegisterDocumentsResponse.status))
            }

            val notifyResponse = externalAccountRegister.notifyAccountRegisterDocumentsSent(
                name = accountRegisterData.getName(),
                document = accountRegisterData.getCPF()!!,
            )

            if (!notifyResponse) {
                return Either.Left(ExternalAccountRegisterException("Error notifying account documents sent."))
            }

            accountService.updatePartialAccountStatus(accountId, AccountStatus.UNDER_EXTERNAL_REVIEW)

            Either.Right(Unit)
        } catch (e: Exception) {
            Either.Left(e)
        }
    }

    open fun internalApproveUpgrade(accountId: AccountId): Either<Exception, Unit> {
        return try {
            val account = accountService.findAccountById(accountId)
            if (account.upgradeStatus != UpgradeStatus.UNDER_REVIEW) {
                return Either.Left(AccountIsNotUnderReview(account.status))
            }

            val accountRegisterData = accountRegisterRepository.findByAccountId(accountId)

            val sendUpgradeDocumentsResponse = ecmProvider.sendUpgradeDocuments(buildUpgradeRequest(accountRegisterData))

            if (!sendUpgradeDocumentsResponse.success) {
                return Either.Left(ECMProviderException(sendUpgradeDocumentsResponse.status))
            }

            val notifyResponse = externalAccountRegister.notifyAccountRegisterDocumentsSent(
                name = accountRegisterData.getName(),
                document = accountRegisterData.getCPF()!!,
            )

            if (!notifyResponse) {
                return Either.Left(ExternalAccountRegisterException("Error notifying account documents sent."))
            }

            accountService.updateUpgradeStatus(accountId, UpgradeStatus.UNDER_EXTERNAL_REVIEW)

            Either.Right(Unit)
        } catch (e: Exception) {
            Either.Left(e)
        }
    }

    /*
        callback do arbi com a criação de conta ou nao (acontece apos o internalApproveAccount)
        se a conta foi criada:
          - cria pix
          - dda
          - cria accountPaymentMethod
          - cria um Account OWNER com receiveDDANotification false (e nao apaga o account GUEST, esse é necessario para que o usuario continue logando como GUEST)
          - notifica atendimento
        se a conta nao foi criada:
          - notifica atendimento
     */
    open fun updateAccountStatus(
        document: String,
        status: ExternalRegisterStatus,
    ): Either<Exception, SetupAccountResult> {
        val markers = append("document", document)
            .andAppend("status", status)
        val lock = accountStatusLockProvider.acquireLock(document) ?: return AlreadyLockedException(document).left()
        return try {
            val accountRegisterData = accountRegisterRepository.findByDocument(document)
            markers.andAppend("accountId", accountRegisterData.accountId.value)

            val account = accountService.findAccountByIdOrNull(accountRegisterData.accountId)
            if (account?.upgradeStatus != null) {
                if (account.upgradeStatus == UpgradeStatus.UNDER_EXTERNAL_REVIEW) {
                    updateUpgradeAccountStatus(account, markers, status)
                } else {
                    markers.andAppend("context", "upgradeStatus não está UNDER_EXTERNAL_REVIEW")
                    when (account.upgradeStatus) {
                        UpgradeStatus.DENIED -> SetupAccountResult.AccountRejected.right()
                        UpgradeStatus.COMPLETED -> SetupAccountResult.AccountApproved.right()
                        UpgradeStatus.EXTERNAL_DENIED -> SetupAccountResult.AccountRejected.right()
                        UpgradeStatus.INCOMPLETE, UpgradeStatus.UNDER_EXTERNAL_REVIEW, UpgradeStatus.UNDER_REVIEW -> throw IllegalStateException("upgradeStatus não está UNDER_EXTERNAL_REVIEW")
                    }
                }
            } else {
                updatePartialAccountStatus(accountRegisterData, markers, status)
            }
        } catch (e: Exception) {
            LOG.error(markers, "updateAccountStatus", e)
            e.left()
        } finally {
            lock.unlock()
        }
    }

    private fun updatePartialAccountStatus(
        accountRegisterData: AccountRegisterData,
        markers: LogstashMarker,
        status: ExternalRegisterStatus,
    ): Either<Exception, SetupAccountResult> {
        val partialAccount = accountService.findPartialAccountById(accountRegisterData.accountId)
        markers.andAppend("accountStatus", partialAccount.status.name)

        if (partialAccount.status == AccountStatus.APPROVED && status == ExternalRegisterStatus.APPROVED) {
            LOG.info(markers, "updateAccountStatus")
            return SetupAccountResult.AccountApproved.right()
        }
        if (partialAccount.status == AccountStatus.UNDER_REVIEW && status == ExternalRegisterStatus.REJECTED) {
            LOG.info(markers, "updateAccountStatus")
            return SetupAccountResult.AccountRejected.right()
        }
        if (partialAccount.status != AccountStatus.UNDER_EXTERNAL_REVIEW) {
            LOG.warn(markers, "updateAccountStatus")
            return Either.Left(AccountIsNotUnderExternalReview(partialAccount.status))
        }

        return when (status) {
            ExternalRegisterStatus.APPROVED -> handleAccountExternallyApproved(
                accountRegisterData,
                partialAccount,
                markers,
            )

            ExternalRegisterStatus.REJECTED -> handleAccountExternallyRejected(accountRegisterData, markers).right()
        }
    }

    private fun updateUpgradeAccountStatus(
        account: Account,
        markers: LogstashMarker,
        status: ExternalRegisterStatus,
    ): Either<Exception, SetupAccountResult> {
        return try {
            when (status) {
                ExternalRegisterStatus.APPROVED -> upgradeAccount(account).map {
                    ecmProvider.notifyUpgradeStatus(NotifyUpgradeStatusRequest(account.document, status))
                    SetupAccountResult.AccountApproved
                }

                ExternalRegisterStatus.REJECTED -> {
                    ecmProvider.notifyUpgradeStatus(NotifyUpgradeStatusRequest(account.document, status))
                    accountService.save(
                        account.copy(
                            upgradeStatus = UpgradeStatus.EXTERNAL_DENIED,
                        ),
                    )
                    LOG.warn(markers.andAppend("reason", "Upgrade de conta rejeitado externamente.").andAppend("ACTION", "VERIFY"), "updateUpgradeAccountStatus")
                    SetupAccountResult.AccountRejected.right()
                }
            }
        } catch (e: Exception) {
            LOG.error(markers, "updateUpgradeAccountStatus", e)
            e.left()
        }
    }

    /*
    ultimo passo de aprovação do cliente (acontece apos updateAccountStatus, porém tem que esperar 1hora para dar tempo do dda rodar pro usuario)
      se aprovado:
        - apaga account GUEST (partial account)
        - muda estado do account para ACTIVE
        - apaga o login de GUEST e cria o de OWNER
        - ajusta receiveDDANotification para true
        - notifica o usuario
      se rejeitado:
        - notifica o atendimento
     */
    open fun activateAccount(accountId: AccountId): Either<Exception, Account> {
        return try {
            val partialAccount = accountService.findPartialAccountById(accountId)
            if (partialAccount.status != AccountStatus.APPROVED) {
                return Either.Left(AccountIsNotUnderReview(partialAccount.status))
            }

            val accountRegister = accountRegisterRepository.findByAccountId(accountId)

            val username = accountRegister.getCPF().toString()
            if (userPoolAdapter.doesUserExist(username) && !userPoolAdapter.isUserEnabled(username)) {
                reactivateUserInCognito(
                    username,
                    accountRegister.accountId,
                    accountRegister.emailAddress,
                    accountRegister.mobilePhone!!,
                )
            }

            val account = doActivateAccount(accountRegister).getOrElse {
                return it.left()
            }

            account.right()
        } catch (e: AccountNotFoundException) {
            try {
                val account = accountService.findAccountById(accountId)

                if (account.status == AccountStatus.ACTIVE) {
                    account.right()
                } else {
                    Either.Left(e)
                }
            } catch (e2: Exception) {
                Either.Left(e)
            }
        } catch (e: Exception) {
            Either.Left(e)
        }
    }

    open fun updateAgreementFiles(accountId: AccountId, clientIP: String): AgreementData? {
        val accountRegisterData = accountRegisterRepository.findByAccountId(
            accountId = accountId,
            checkOpenAccount = false,
        )

        if (!accountRegisterData.readyForAgreement()) {
            return accountRegisterData.agreementData
        }

        val isUpgradeAccount = accountRegisterData.upgradeStarted != null

        if (isUpgradeAccount && accountRegisterData.upgradeAgreementData?.acceptedAt != null) {
            return accountRegisterData.upgradeAgreementData
        }

        if (!isUpgradeAccount && accountRegisterData.agreementData?.acceptedAt != null) {
            return accountRegisterData.agreementData
        }

        val contractForm = accountRegisterData.toContractForm()
        val signatureKey = contractForm.generateSignatureKey()

        if (!isUpgradeAccount && signatureKey == accountRegisterData.agreementData?.userContractSignature) {
            return accountRegisterData.agreementData
        }
        if (isUpgradeAccount && signatureKey == accountRegisterData.upgradeAgreementData?.userContractSignature) {
            return accountRegisterData.upgradeAgreementData
        }

        val contractStoredFile = createContractPdf(
            accountRegisterData = accountRegisterData,
            clientIP = clientIP,
        )
        val declarationOfResidencyFile = createDeclarationOfResidencyPdf(
            accountRegisterData = accountRegisterData,
            clientIP = clientIP,
        )

        val agreementData = AgreementData(
            acceptedAt = if (isUpgradeAccount) accountRegisterData.upgradeAgreementData?.acceptedAt else accountRegisterData.agreementData?.acceptedAt,
            userContractFile = contractStoredFile,
            userContractSignature = signatureKey,
            declarationOfResidencyFile = declarationOfResidencyFile,
        )

        return if (isUpgradeAccount) {
            accountRegisterRepository.save(accountRegisterData.copy(upgradeAgreementData = agreementData)).upgradeAgreementData
        } else {
            accountRegisterRepository.save(accountRegisterData.copy(agreementData = agreementData)).agreementData
        }
    }

    private fun findActiveAccountIdByMobilePhone(accountId: AccountId, mobilePhone: MobilePhone): AccountId? {
        accountRegisterRepository.findByVerifiedMobilePhone(mobilePhone)
            .filter { it.accountId != accountId }.map {
                val account = accountService.findAccountByIdOrNull(it.accountId)

                if (account != null && account.isOpen()) {
                    return account.accountId
                }

                val partialAccount = accountService.findPartialAccountByIdOrNull(it.accountId)

                if (partialAccount != null && partialAccount.status in listOf(
                        AccountStatus.UNDER_REVIEW,
                        AccountStatus.UNDER_EXTERNAL_REVIEW,
                        AccountStatus.APPROVED,
                    )
                ) {
                    return partialAccount.id
                }
            }

        return null
    }

    private fun updateUserOnCrm(accountRegisterData: AccountRegisterData) {
        try {
            crmService.upsertContact(accountRegisterData)
        } catch (e: Exception) {
            LOG.error(append("accountId", accountRegisterData.accountId), "UpdateUserOnCrm", e)
        }
    }

    fun createSimpleOwnerUser(accountId: AccountId, password: String): Either<Exception, User> {
        return createOwnerUserWrapper(accountId, password, "RegisterService#createSimpleOwnerUser") { account ->
            userPoolAdapter.createUser(
                username = account.document,
                password = password,
                accountId = accountId,
                emailAddress = account.emailAddress,
                phoneNumber = buildMobilePhone(account.mobilePhone),
                role = Role.OWNER,
                name = account.name,
                setMFA = false,
            )
        }
    }

    private fun createOwnerUserWrapper(
        accountId: AccountId,
        password: String,
        logName: String,
        userCreation: (account: Account) -> Unit,
    ): Either<Exception, User> {
        with(accountService.findAccountById(accountId)) {
            val markers = append("accountId", accountId).andAppend("document", document)
            return if (!userPoolAdapter.doesUserExist(document)) {
                try {
                    userCreation(this)
                    LOG.info(markers.andAppend("action", "activation"), logName)
                    User(document).right()
                } catch (e: Exception) {
                    return e.left()
                }
            } else {
                val isUserEnabled = userPoolAdapter.isUserEnabled(document)

                if (isUserEnabled) {
                    return UserPoolUsernameExistsException("user already exists").left()
                }

                reactivateUserInCognito(document, this.accountId, this.emailAddress, MobilePhone(this.mobilePhone)).mapLeft {
                    return when (it) {
                        is UserPoolEnableUserError.UserNotFound -> UserPoolUsernameExistsException("user already exists").left()
                        is UserPoolEnableUserError.Unknown -> it.exception.left()
                    }
                }

                userPoolAdapter.setUserPassword(document, password)

                LOG.info(markers.andAppend("action", "reactivation"), logName)

                User(document).right()
            }.map {
                eventPublisher.publish(
                    UserCreated(
                        accountId = accountId,
                        username = it.username,
                        userPoolProvider = UserPoolProvider.COGNITO,
                        mfaEnabled = false,
                    ),
                )

                runCatching {
                    activityService.setCreatedPassword(
                        accountId = accountId,
                        created = getZonedDateTime(),
                    )
                }.onFailure { throwable ->
                    LOG.error(markers, logName, throwable)
                }

                it
            }
        }
    }

    private fun reactivateUserInCognito(document: String, accountId: AccountId, emailAddress: EmailAddress, mobilePhone: MobilePhone): Either<UserPoolEnableUserError, Unit> {
        try {
            userPoolAdapter.setAccountId(document, accountId)
            userPoolAdapter.updateEmailAddress(document, emailAddress)
            userPoolAdapter.updatePhoneNumber(document, mobilePhone)
        } catch (e: UserNotFoundException) {
            UserPoolEnableUserError.UserNotFound.left()
        } catch (e: Exception) {
            UserPoolEnableUserError.Unknown(e).left()
        }

        userPoolAdapter.enableUser(document).mapLeft {
            return it.left()
        }

        return Unit.right()
    }

    private fun isMonthlyIncomeValid(monthlyIncome: MonthlyIncome): Boolean {
        if (monthlyIncome.lowerBound == 0L && monthlyIncome.upperBound == 2_000_00L) {
            return true
        }

        if (monthlyIncome.lowerBound == 2_000_01L && monthlyIncome.upperBound == 4_000_00L) {
            return true
        }

        if (monthlyIncome.lowerBound == 4_000_01L && monthlyIncome.upperBound == 10_000_00L) {
            return true
        }

        if (monthlyIncome.lowerBound == 10_000_01L && monthlyIncome.upperBound == 20_000_00L) {
            return true
        }

        if (monthlyIncome.lowerBound == 20_000_01L && monthlyIncome.upperBound == null) {
            return true
        }

        return false
    }

    private fun createAttachment(documentKey: String, inputStream: InputStream): Attachment {
        val documentKeyFormatted = documentKey.lowercase()
        return Attachment.of(
            inputStream,
            when {
                documentKeyFormatted.endsWith("pdf") -> MediaType.APPLICATION_PDF
                documentKeyFormatted.endsWith("png") -> MediaType.IMAGE_PNG
                documentKeyFormatted.endsWith("html") -> MediaType.TEXT_HTML
                else -> MediaType.IMAGE_JPEG
            },
            documentKeyFormatted,
        )
    }

    private fun handleAccountExternallyRejected(
        accountRegisterData: AccountRegisterData,
        markers: LogstashMarker,
    ): SetupAccountResult.AccountRejected {
        accountService.updatePartialAccountStatus(accountRegisterData.accountId, AccountStatus.UNDER_REVIEW)
        notifyAccountPendingReview(accountRegisterData, markers)
        try {
            registerInstrumentationService.externallyRejected(
                accountRegisterData.accountId,
                accountRegisterData.registrationType,
            )
        } catch (e: Exception) {
            LOG.error(markers.and(append("ACTION", "VERIFY")), "AccountExternallyRejectedCRMError", e)
        }
        return SetupAccountResult.AccountRejected
    }

    private fun handleAccountExternallyApproved(
        accountRegisterData: AccountRegisterData,
        partialAccount: PartialAccount,
        markers: LogstashMarker,
    ): Either<Exception, SetupAccountResult> {
        return try {
            val shouldSetupAccount = try {
                accountService.findPhysicalBankAccountByAccountId(accountRegisterData.accountId)
                false
            } catch (e: PaymentMethodNotFound) {
                true
            }
            markers.andAppend("shouldSetupAccount", shouldSetupAccount)

            if (shouldSetupAccount) {
                val registerNaturalPersonResponse =
                    externalAccountRegister.registerNaturalPerson(
                        customer = ExternalAccount(
                            documentInfo = accountRegisterData.documentInfo!!,
                            address = accountRegisterData.address!!,
                            email = accountRegisterData.emailAddress.value,
                            mobilePhone = accountRegisterData.mobilePhone!!,
                            politicallyExposed = accountRegisterData.politicallyExposed!!,
                            monthlyIncome = accountRegisterData.monthlyIncome!!,
                            calculatedGender = accountRegisterData.calculatedGender
                                ?: Gender.M, // TODO Remover valor default apos o rollout (quando nao tiver mais cadastros com esse campo vazio)
                        ),
                    )

                if (!registerNaturalPersonResponse.success) {
                    return Either.Left(RegisterNaturalPersonException())
                }

                setupAccountInternally(
                    accountRegisterData,
                    partialAccount,
                    BankAccount(
                        accountType = AccountType.CHECKING,
                        bankNo = 213,
                        routingNo = 1,
                        accountNo = registerNaturalPersonResponse.accountNumber!!.toBigInteger(),
                        accountDv = registerNaturalPersonResponse.accountDv!!,
                        document = accountRegisterData.documentInfo.cpf,
                        ispb = null, // TODO
                    ),
                )
            }

            val setupAccountResult = registerPixKeyAndDDA(accountRegisterData)
            markers.andAppend("setupAccountResult", setupAccountResult)

            accountService.updatePartialAccountStatus(accountRegisterData.accountId, AccountStatus.APPROVED)

            notifyAccountPendingActivation(setupAccountResult, accountRegisterData, markers)

            LOG.info(markers, "updateAccountStatus")
            setupAccountResult.right()
        } catch (e: Exception) {
            LOG.error(markers, "updateAccountStatus", e)
            Either.Left(e)
        }
    }

    private fun notifyAccountPendingActivation(
        setupAccountResult: SetupAccountResult,
        accountRegisterData: AccountRegisterData,
        markers: LogstashMarker,
    ) {
        try {
            val pixCreatedMessage = if (setupAccountResult != SetupAccountResult.AddPixKeyFailed) {
                "Sim"
            } else {
                "Não"
            }
            val ddaRegisteredMessage = if (setupAccountResult == SetupAccountResult.AccountApproved) {
                "Sim"
            } else {
                "Não"
            }

            val subject = String.format(pendingActivationConfiguration.subject, accountRegisterData.accountId.value)
            val message = String.format(
                pendingActivationConfiguration.message,
                accountRegisterData.accountId.value,
                accountRegisterData.getName(),
                accountRegisterData.emailAddress,
                pixCreatedMessage,
                ddaRegisteredMessage,
            )

            notificationSenderService.sendRawEmail(from, subject, message, pendingActivationConfiguration.recipients)
            notificationSenderService.sendRawEmail(
                from,
                subject,
                message,
                pendingActivationConfiguration.sensitiveRecipients,
            )
        } catch (e: Exception) {
            LOG.error(markers, "notifyAccountPendingActivation", e)
        }
    }

    private fun notifyAccountPendingReview(accountRegisterData: AccountRegisterData, markers: LogstashMarker) {
        try {
            val subject = String.format(pendingInternalReviewConfiguration.subject, accountRegisterData.accountId.value)
            val message = String.format(
                pendingInternalReviewConfiguration.message,
                accountRegisterData.accountId.value,
                accountRegisterData.getName(),
                accountRegisterData.emailAddress,
            )

            notificationSenderService.sendRawEmail(
                from,
                subject,
                message,
                pendingInternalReviewConfiguration.recipients,
            )

            sendEmailWithUserDocuments(
                accountRegisterData,
                subject,
                message,
                pendingInternalReviewConfiguration.sensitiveRecipients,
            )
        } catch (e: Exception) {
            LOG.error(markers, "notifyAccountPendingReview", e)
        }
    }

    private fun sendEmailWithUserDocuments(
        accountRegisterData: AccountRegisterData,
        subject: String,
        message: String,
        recipient: String,
    ) {
        accountRegisterData.withSensitiveDocuments { sensitiveDocuments ->
            notificationSenderService.sendRawEmail(
                from,
                subject,
                message,
                recipient,
                sensitiveDocuments,
            )
        }
    }

    private fun AccountRegisterData.withSensitiveDocuments(block: (streams: List<Attachment>) -> Unit) {
        val isUpgradeAccount = upgradeStarted != null
        val agreement = if (isUpgradeAccount) upgradeAgreementData else agreementData

        val storedObjects = listOfNotNull(
            agreement?.userContractFile,
            uploadedSelfie,
            kycFile,
            agreement?.declarationOfResidencyFile,
        )

        val attachments = storedObjects.map { storedObject ->
            val inputStream = accountRegisterRepository.getDocumentInputStream(storedObject)
            val attachment = createAttachment(storedObject.key, inputStream)

            LOG.info(
                append("listSize", storedObjects.size)
                    .andAppend("fileName", attachment.fileName)
                    .andAppend("contentType", attachment.contentType),
                "attachAll",
            )

            attachment
        }

        val allSensitiveDocuments = attachments + getUserDocuments(this)
            .map {
                createAttachment(documentKey = "${it.name}.${it.extension}", inputStream = ByteArrayInputStream(it.content))
            }

        kotlin.runCatching { block(allSensitiveDocuments) }
            .onFailure {
                LOG.error("attachAll", it)
                throw it
            }
            .also { allSensitiveDocuments.forEach { kotlin.runCatching { it.content.close() } } }
    }

    private fun doActivateAccount(accountRegister: AccountRegisterData): Either<Exception, Account> {
        val account = accountService.findAccountById(accountRegister.accountId)

        if (!crmService.contactExists(account.accountId)) {
            updateUserOnCrm(accountRegister)
            throw CrmContactNotFoundException(account.accountId)
        }

        accountService.deletePartialAccount(accountRegister.accountId)

        val groups = mutableListOf(OnboardingTestPixService.randomAccountGroup(), AccountGroup.CHATBOT_AI_NEW_USERS)
        val accountWithGroups = accountService.addGroupsToAccount(account.accountId, groups)
        val updatedAccount = updateActivatedAccount(accountWithGroups)

        registerInstrumentationService.activated(account.accountId, accountRegister.registrationType)
        crmService.upsertContact(updatedAccount)
        userJourneyService.registerAsync(account)
        systemActivityService.setAccountActivated(accountId = account.accountId)
        adService.publishAccountActivated(accountId = account.accountId)

        createWhatsappLogin(account)

        LOG.info(
            append("accountId", updatedAccount.accountId)
                .andAppend("status", updatedAccount.status)
                .andAppend("created", updatedAccount.created),
            "doActivateAccount",
        )

        return if (accountRegister.shouldEnableCreditCard) {
            accountService.enableCreditCardUsage(
                accountId = updatedAccount.accountId,
                quota = 2_000_00,
                sendNotification = false,
            )
        } else {
            updatedAccount.right()
        }
    }

    private fun updateActivatedAccount(account: Account): Account {
        val tmpAccount = account.copy(
            status = AccountStatus.ACTIVE,
            configuration = account.configuration.copy(
                receiveDDANotification = true,
            ),
            activated = getZonedDateTime(),
        )
        accountService.save(tmpAccount)
        val hasChatBotEnabled = accountService.getChatbotType(account).checkHasAIChatbotEnabled()
        if (hasChatBotEnabled) {
            chatBotNotificationService.notifyWelcomeMessage(
                account = account,
                chatBotWelcomeMessageType = WelcomeMessageType.WELCOME_CHATBOT_ONBOARDING_SINGLE_PIX,
            )
        } else {
            notificationAdapter.notifyUserActivated(account)
        }
        return tmpAccount
    }

    open fun setupAccountInternally(
        accountId: AccountId,
        email: EmailAddress,
        document: Document,
        phone: MobilePhone,
        name: String,
        groups: List<AccountGroup>,
        externalId: ExternalId?,
        type: UserAccountType,
        subscriptionType: SubscriptionType,
        bankAccount: BankAccount,
        creditCardQuota: Long? = null,
    ): Account {
        val newAccount = Account(
            accountId = accountId,
            name = name,
            emailAddress = email,
            document = document.value,
            documentType = document.type.value,
            mobilePhone = phone.msisdn,
            created = getZonedDateTime(),
            updated = getZonedDateTime(),
            status = AccountStatus.APPROVED,
            configuration = LegacyAccountConfiguration(
                creditCardConfiguration = CreditCardConfiguration(),
                defaultWalletId = null,
                receiveDDANotification = false,
                receiveNotification = true,
                groups = groups,
                externalId = externalId,
            ),
            activated = null,
            type = type,
            imageUrlSmall = null,
            imageUrlLarge = null,
            subscriptionType = subscriptionType,
        )
        accountService.create(newAccount)

        val accountPaymentMethod = accountService.createAccountPaymentMethod(
            accountId,
            bankAccount.copy(document = document.value),
            1,
        )

        val wallet = walletService.createPrimaryWallet(newAccount, accountPaymentMethod.id)

        walletBillCategoryService.createDefaultWalletCategories(wallet.id)

        val updatedNewAccount = newAccount.copy(
            configuration = newAccount.configuration.copy(
                defaultWalletId = wallet.id,
                creditCardConfiguration = newAccount.configuration.creditCardConfiguration.copy(
                    quota = creditCardQuota ?: newAccount.configuration.creditCardConfiguration.quota,
                ),
            ),
        )

        accountService.save(updatedNewAccount)

        return updatedNewAccount
    }

    private fun setupAccountInternally(
        accountRegister: AccountRegisterData,
        partialAccount: PartialAccount,
        bankAccount: BankAccount,
    ) {
        setupAccountInternally(
            accountId = accountRegister.accountId,
            email = accountRegister.emailAddress,
            document = accountRegister.documentInfo?.cpf?.let { Document(it) } ?: throw RegisterIncompleteException("documentInfo"),
            phone = accountRegister.mobilePhone?.msisdn?.let { MobilePhone(it) } ?: throw RegisterIncompleteException("mobilePhone"),
            name = accountRegister.getName(),
            groups = partialAccount.groups,
            externalId = accountRegister.externalId,
            type = accountRegister.registrationType.toUserAccountType(),
            subscriptionType = partialAccount.subscriptionType,
            bankAccount = bankAccount,
        )
    }

    fun registerPixKey(
        accountId: AccountId,
        document: Document,
        name: String,
    ) {
        val bankAccount =
            accountService.findPhysicalBankAccountByAccountId(accountId)
                .single().method as InternalBankAccount

        messagePublisher.sendMessage(
            registerPixKeyQueueName,
            RegisterPixKeyCommand(
                accountNo = AccountNumber(bankAccount.accountNo.toBigInteger(), bankAccount.accountDv),
                key = PixKey(value = "${document.value}@$pixKeyEmailDomain", type = PixKeyType.EMAIL),
                document = document.value,
                name = name,
                walletId = WalletId(accountId.value),
            ),
        )

        messagePublisher.sendMessage(
            registerPixKeyQueueName,
            RegisterPixKeyCommand(
                accountNo = AccountNumber(bankAccount.accountNo.toBigInteger(), bankAccount.accountDv),
                key = PixKey(value = "", type = PixKeyType.EVP),
                document = document.value,
                name = name,
                walletId = WalletId(accountId.value),
            ),
        )
    }

    private fun registerPixKeyAndDDA(
        accountRegister: AccountRegisterData,
    ): SetupAccountResult {
        val cpf = accountRegister.getCPF()!!
        val name = accountRegister.getName()

        val ddaStatus = ddaService.register(accountRegister.accountId, cpf)
        if (ddaStatus.optedOut()) {
            return SetupAccountResult.RegisterDDAFailed
        }

        registerPixKey(accountRegister.accountId, Document(cpf), name)

        return SetupAccountResult.AccountApproved
    }

    open fun submitRegisterForInternalReview(accountId: AccountId): Either<Exception, Unit> {
        return try {
            val partialAccount = accountService.findPartialAccountById(accountId)
            if (partialAccount.status != AccountStatus.UNDER_REVIEW) {
                return Either.Left(AccountIsNotUnderReview(partialAccount.status))
            }

            val accountRegisterData = accountRegisterRepository.findByAccountId(accountId)

            val (kycFile, kycDossier) = kycService.generate(accountRegisterData).getOrElse {
                Pair(null, null)
            }

            val accountRegisterDataUpdated = accountRegisterRepository.save(
                accountRegisterData.copy(
                    kycFile = kycFile,
                    politicallyExposed = accountRegisterData.politicallyExposed!!.copy(query = kycDossier?.pep),
                ),
            )

            notificationSenderService.sendRawEmail(
                from,
                String.format(pendingInternalApproveConfiguration.subject, accountId.value),
                String.format(
                    pendingInternalApproveConfiguration.message,
                    accountId.value,
                    accountRegisterDataUpdated.documentInfo?.name,
                    accountRegisterDataUpdated.emailAddress,
                ),
                pendingInternalApproveConfiguration.recipients,
            )

            sendEmailWithUserDocuments(
                accountRegisterData = accountRegisterDataUpdated,
                subject = String.format(
                    pendingInternalApproveConfiguration.subject,
                    accountRegisterData.accountId.value,
                ),
                message = String.format(
                    pendingInternalApproveConfiguration.message,
                    accountRegisterData.accountId.value,
                    accountRegisterData.documentInfo?.name,
                    accountRegisterData.emailAddress,
                ),
                recipient = pendingInternalApproveConfiguration.sensitiveRecipients,
            )

            Either.Right(Unit)
        } catch (e: Exception) {
            LOG.error(append("ACTION", "VERIFY"), "submitRegisterForInternalReview", e)
            Either.Left(e)
        }
    }

    open fun submitUpgradeForInternalReview(accountId: AccountId): Either<Exception, Unit> {
        return try {
            val account = accountService.findAccountById(accountId)

            if (account.upgradeStatus != UpgradeStatus.UNDER_REVIEW) {
                return Either.Left(UpgradeIsNotUnderReview(account.upgradeStatus))
            }

            val accountRegisterData = accountRegisterRepository.findByAccountId(accountId)

            val (kycFile, kycDossier) = kycService.generate(accountRegisterData).getOrElse {
                Pair(null, null)
            }

            val accountRegisterDataUpdated = accountRegisterRepository.save(
                accountRegisterData.copy(
                    kycFile = kycFile,
                    politicallyExposed = accountRegisterData.politicallyExposed!!.copy(query = kycDossier?.pep),
                ),
            )

            notificationSenderService.sendRawEmail(
                from,
                String.format(pendingUpgradeInternalApproveConfiguration.subject, accountId.value),
                String.format(
                    pendingUpgradeInternalApproveConfiguration.message,
                    accountId.value,
                    accountRegisterDataUpdated.documentInfo?.name,
                    accountRegisterDataUpdated.emailAddress,
                ),
                pendingUpgradeInternalApproveConfiguration.recipients,
            )

            sendEmailWithUserDocuments(
                accountRegisterData = accountRegisterDataUpdated,
                subject = String.format(
                    pendingUpgradeInternalApproveConfiguration.subject,
                    accountRegisterData.accountId.value,
                ),
                message = String.format(
                    pendingUpgradeInternalApproveConfiguration.message,
                    accountRegisterData.accountId.value,
                    accountRegisterData.documentInfo?.name,
                    accountRegisterData.emailAddress,
                ),
                recipient = pendingUpgradeInternalApproveConfiguration.sensitiveRecipients,
            )

            Either.Right(Unit)
        } catch (e: Exception) {
            LOG.error(append("ACTION", "VERIFY"), "submitUpgradeForInternalReview", e)
            Either.Left(e)
        }
    }

    fun getContract(accountId: AccountId): InputStream {
        val accountRegisterData = accountRegisterRepository.findByAccountId(accountId)
        val fileCreated = ByteArrayOutputStream()
        agreementFilesService.createContract(
            stream = fileCreated,
            contractForm = accountRegisterData.toContractForm(),
            clientIP = accountRegisterData.clientIP!!,
        )
        return fileCreated.toByteArray().inputStream()
    }

    private fun StoredObject.getExtension() = key.substringAfterLast('.', "")

    private fun StoredObject.toAccountRegisterDocument(name: String): AccountRegisterDocument {
        return accountRegisterRepository.getDocumentInputStream(this)
            .use {
                AccountRegisterDocument(
                    content = it.readAllBytes(),
                    extension = this.getExtension(),
                    name = name,
                )
            }
    }

    private fun ImageContent.toAccountRegisterDocument(name: String): AccountRegisterDocument {
        return AccountRegisterDocument(
            content = content,
            name = name,
            extension = extension,
        )
    }

    private fun buildInsertRequest(register: AccountRegisterData): SendAccountRegisterDocumentsRequest {
        return SendAccountRegisterDocumentsRequest(
            id = register.accountId.value,
            name = register.getName(),
            document = register.getCPF()!!,
            emailAddress = register.emailAddress,
        ).apply {
            this.userContract = register.agreementData!!.userContractFile.toAccountRegisterDocument("Contrato - ${register.accountId.value}")
            this.userSelfie = register.uploadedSelfie!!.toAccountRegisterDocument("Selfie - ${register.accountId.value} ")
            this.userDocument = getUserDocuments(register)
            this.userKYC = register.kycFile?.toAccountRegisterDocument("KYC - ${register.accountId.value}")
            this.declarationOfResidency = register.agreementData.declarationOfResidencyFile!!.toAccountRegisterDocument("Declaracao de residencia - ${register.accountId.value}")
        }
    }

    private fun buildUpgradeRequest(register: AccountRegisterData): SendUpgradeDocumentsRequest {
        return SendUpgradeDocumentsRequest(
            id = register.accountId.value,
            name = register.getName(),
            document = register.getCPF()!!,
            emailAddress = register.emailAddress,
            userContract = register.upgradeAgreementData!!.userContractFile.toAccountRegisterDocument("Contrato - ${register.accountId.value}"),
            userDocument = getUserDocuments(register),
            declarationOfResidency = register.upgradeAgreementData.declarationOfResidencyFile!!.toAccountRegisterDocument("Declaracao de residencia - ${register.accountId.value}"),
        )
    }

    private fun getUserDocuments(register: AccountRegisterData): List<AccountRegisterDocument> {
        val scanned = documentScanService.getImage(register.accountId).getOrElse {
            when (it) {
                is GetImageError.Incomplete -> null
                is GetImageError.AccountNotFound,
                is GetImageError.Unexpected,
                -> throw it
            }
        }

        return when (scanned) {
            is DocumentScanImageWithType.SingleSided -> listOf(
                scanned.content.toAccountRegisterDocument("${scanned.type} - ${register.accountId.value}"),
            )

            is DocumentScanImageWithType.DoubleSided -> listOf(
                scanned.front.toAccountRegisterDocument("${scanned.type} - Frente - ${register.accountId.value}"),
                scanned.back.toAccountRegisterDocument("${scanned.type} - Verso - ${register.accountId.value}"),
            )

            null -> listOfNotNull(
                register.uploadedCNH?.toAccountRegisterDocument("CNH - ${register.accountId.value}"),
                register.uploadedDocument?.front?.toAccountRegisterDocument("RG - Frente - ${register.accountId.value}"),
                register.uploadedDocument?.back?.toAccountRegisterDocument("RG - Verso - ${register.accountId.value}"),
            )
        }
    }

    private fun checkUserBlockedForEdition(accountId: AccountId): Boolean {
        val account = accountService.findAccountByIdOrNull(accountId)

        if (account != null) {
            val blockedForUpgrade = account.upgradeStatus != null && account.upgradeStatus != UpgradeStatus.INCOMPLETE

            return account.type != UserAccountType.BASIC_ACCOUNT || blockedForUpgrade
        }

        val partialAccount = accountService.findPartialAccountById(accountId)
        return partialAccount.status != AccountStatus.REGISTER_INCOMPLETE
    }

    private fun buildObjectPath(accountId: AccountId, prefix: String, extension: String) =
        "${userFilesConfiguration.path}/${accountId.value}/$prefix${getZonedDateTime().toEpochSecond()}.$extension"

    private fun createContractPdf(
        accountRegisterData: AccountRegisterData,
        clientIP: String,
    ): StoredObject {
        val fileCreated = ByteArrayOutputStream()
        agreementFilesService.createContract(
            stream = fileCreated,
            contractForm = accountRegisterData.toContractForm(),
            clientIP = clientIP,
        )
        with(userFilesConfiguration) {
            return createStoredObject(
                fileContent = fileCreated.toByteArray(),
                key = "$path/${accountRegisterData.accountId.value}/${contractPrefix}${getZonedDateTime().toEpochSecond()}.pdf",
            )
        }
    }

    private fun createDeclarationOfResidencyPdf(
        accountRegisterData: AccountRegisterData,
        clientIP: String,
    ): StoredObject {
        val fileCreated = ByteArrayOutputStream()
        agreementFilesService.createDeclarationOfResidency(
            stream = fileCreated,
            accountRegisterData = accountRegisterData,
            clientIP = clientIP,
        )
        with(userFilesConfiguration) {
            return createStoredObject(
                fileContent = fileCreated.toByteArray(),
                key = "$path/${accountRegisterData.accountId.value}/${declarationOfResidencyPrefix}${getZonedDateTime().toEpochSecond()}.pdf",
            )
        }
    }

    private fun createStoredObject(
        fileContent: ByteArray,
        key: String,
        mediaType: MediaType = MediaType.APPLICATION_PDF_TYPE,
    ): StoredObject {
        with(userFilesConfiguration) {
            val storedObject = StoredObject(region, bucket, key)
            accountRegisterRepository.putDocument(storedObject, ByteArrayInputStream(fileContent), mediaType)

            return storedObject
        }
    }

    private fun buildMobilePhone(mobilePhone: String): MobilePhone {
        return if (mobilePhone.startsWith("+")) {
            MobilePhone(mobilePhone)
        } else {
            MobilePhone("+$mobilePhone")
        }
    }

    fun findPartialAccountByStatus(accountStatus: AccountStatus): List<Pair<PartialAccount, AccountRegisterData?>> {
        return accountService.findPartialAccountByStatus(accountStatus = accountStatus).map {
            val accountRegister = try {
                accountRegisterRepository.findByAccountId(it.id)
            } catch (e: ItemNotFoundException) {
                null
            }
            Pair(it, accountRegister)
        }
    }

    private fun InputStream.toByteArray() = use {
        it.readAllBytes()
    }

    fun upgradeAccount(accountId: AccountId): Either<Exception, Unit> {
        val account = accountService.findAccountById(accountId)
        if (account.type == UserAccountType.FULL_ACCOUNT) {
            return Either.Right(Unit)
        }

        return upgradeAccount(account)
    }

    private fun upgradeAccount(account: Account): Either<Exception, Unit> {
        val updatedAccount = account.copy(
            type = UserAccountType.FULL_ACCOUNT,
            upgradeStatus = UpgradeStatus.COMPLETED,
        )

        accountService.save(updatedAccount)
        crmService.upsertContact(updatedAccount)
        userJourneyService.registerAsync(updatedAccount)
        walletLimitsService.setDefaultFullAccountLimits(account.accountId)
        notificationAdapter.notifyUpgradeCompleted(updatedAccount)

        return Either.Right(Unit)
    }

    private fun createWhatsappLogin(account: Account) {
        try {
            loginRepository.createLogin(
                providerUser = ProviderUser(
                    id = account.accountId.value,
                    providerName = ProviderName.WHATSAPP,
                    username = "",
                    emailAddress = createWhatsappEmail(MobilePhone(account.mobilePhone)),
                ),
                id = account.accountId,
                role = Role.OWNER,
            )
        } catch (e: Exception) {
            LOG.warn(append("accountId", account.accountId), "RegisterService#createWhatsappLogin")
        }
    }

    companion object {
        internal val LOG = LoggerFactory.getLogger(RegisterService::class.java)
    }
}

data class User(val username: String)

sealed class SetupAccountResult() : PrintableSealedClassV2() {
    data object AccountApproved : SetupAccountResult()
    data object AccountRejected : SetupAccountResult()
    data object AddPixKeyFailed : SetupAccountResult()
    data object RegisterDDAFailed : SetupAccountResult()
}

object ImageOptimizer {
    fun optimize(inputStream: InputStream, extension: String): ByteArrayOutputStream {
        val image = ImageIO.read(inputStream)

        val height = if (image.height > 1080) {
            1080
        } else {
            image.height
        }
        val resizedImage: BufferedImage = Scalr.resize(
            image,
            Scalr.Method.ULTRA_QUALITY,
            Scalr.Mode.FIT_TO_HEIGHT,
            0,
            height,
        )
        val fileCreated = ByteArrayOutputStream()
        ImageIO.write(resizedImage, extension, fileCreated)
        image.flush()
        resizedImage.flush()

        LOG.info(append("compressedFileSize", fileCreated.size()), "Optimize")

        return fileCreated
    }

    private val LOG = LoggerFactory.getLogger(ImageOptimizer::class.java)
}

data class ExternalAccount(
    val documentInfo: DocumentInfo,
    val address: Address,
    val email: String,
    val mobilePhone: MobilePhone,
    val politicallyExposed: PoliticallyExposed,
    val monthlyIncome: MonthlyIncome,
    val calculatedGender: Gender,
)

data class SendAccountRegisterDocumentsRequest(
    override val id: String,
    override val name: String,
    override val document: String,
    override val emailAddress: EmailAddress,
) : SendSimpleSignUpDocumentsRequest(id, name, document, emailAddress) {
    lateinit var userDocument: List<AccountRegisterDocument>
    lateinit var declarationOfResidency: AccountRegisterDocument
}

data class SendUpgradeDocumentsRequest(
    val id: String,
    val name: String,
    val document: String,
    val emailAddress: EmailAddress,
    val userContract: AccountRegisterDocument,
    val userDocument: List<AccountRegisterDocument>,
    val declarationOfResidency: AccountRegisterDocument,
)

data class NotifyUpgradeStatusRequest(
    val document: String,
    val status: ExternalRegisterStatus,
)

open class SendSimpleSignUpDocumentsRequest(
    open val id: String,
    open val name: String,
    open val document: String,
    open val emailAddress: EmailAddress,
) {
    lateinit var userContract: AccountRegisterDocument
    lateinit var userSelfie: AccountRegisterDocument
    var userKYC: AccountRegisterDocument? = null
}

data class AccountRegisterDocument(
    val content: ByteArray,
    val name: String,
    val extension: String,
)

data class SendDocumentsResponse(val success: Boolean, val status: String)

data class NotifyUpgradeStatusResponse(val success: Boolean, val status: String)

data class RegisterNaturalPersonResponse(val success: Boolean, val accountNumber: Long?, val accountDv: String?)

@EachProperty("email.new-account")
class NewAccountEmailConfiguration @ConfigurationInject constructor(
    @param:Parameter val name: String,
    val recipients: String,
    val sensitiveRecipients: String,
    val subject: String,
    val message: String,
)

@ConfigurationProperties("email.newUpgradeAccount")
class NewUpgradeAccountEmailConfiguration @ConfigurationInject constructor(
    val recipients: String,
    val sensitiveRecipients: String,
    val subject: String,
    val message: String,
)

@ConfigurationProperties("fraud-list")
class FraudListMicronautConfiguration
@ConfigurationInject constructor(
    override val documents: List<String>,
) : FraudListConfiguration

@ConfigurationProperties("email.newAccountReport")
class NewAccountReportConfiguration @ConfigurationInject constructor(
    val recipients: String,
    val subject: String,
    val tableStyle: String,
    val thStyle: String,
    val tdStyle: String,
)

data class DocumentQuality(
    val typeOfDocument: DocumentType,
    val containsFace: Boolean,
)

enum class ExternalRegisterStatus {
    APPROVED, REJECTED
}

sealed class UserPoolEnableUserError {
    data object UserNotFound : UserPoolEnableUserError()
    class Unknown(val exception: Exception) : UserPoolEnableUserError()
}

sealed class UserPoolSetMfaPreferenceError {
    data object UserNotFound : UserPoolSetMfaPreferenceError()
    class Unknown(val exception: Exception) : UserPoolSetMfaPreferenceError()
}

data class DocumentDetails(
    val documentNumber: DocumentNumber,
    val motherName: String,
    val fatherName: String?,
    val birthCity: String,
    val birthState: String,
    val orgEmission: String,
    val expeditionDate: LocalDate,
)

enum class DocumentDetailsType {
    IDENTITY,
    DRIVERS_LICENSE,
}

data class DocumentNumber(
    val type: DocumentDetailsType,
    val value: String,
)