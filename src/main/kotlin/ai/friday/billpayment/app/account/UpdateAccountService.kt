package ai.friday.billpayment.app.account

import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.CrmService
import ai.friday.billpayment.app.integrations.DocumentType
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.LoginRepository
import ai.friday.billpayment.app.integrations.UserPoolAdapter
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.journey.UserJourneyService
import ai.friday.billpayment.app.login.ProviderName
import ai.friday.billpayment.app.login.ProviderUser
import ai.friday.billpayment.app.msisdnauth.createTemporaryEmail
import ai.friday.billpayment.app.wallet.MemberStatus
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.cognitoidentityprovider.model.InvalidParameterException
import software.amazon.awssdk.services.cognitoidentityprovider.model.UserNotFoundException

@Singleton
class UpdateAccountService(
    private val accountRepository: AccountRepository,
    private val userPoolAdapter: UserPoolAdapter,
    private val crmService: CrmService,
    private val accountRegisterRepository: AccountRegisterRepository,
    private val loginRepository: LoginRepository,
    private val userJourneyService: UserJourneyService,
    private val walletRepository: WalletRepository,
) {

    fun updatePhoneNumber(
        accountId: AccountId,
        newPhoneNumber: MobilePhone,
        currentPhoneNumber: MobilePhone,
    ): Either<UpdateAccountError, Unit> {
        val markers = Markers.append("accountId", accountId.value)
            .andAppend("phoneNumber", newPhoneNumber.msisdn)
            .andAppend("currentPhoneNumber", currentPhoneNumber.msisdn)
        try {
            val mobilePhoneList = accountRegisterRepository.findByVerifiedMobilePhone(newPhoneNumber)

            if (mobilePhoneList.isNotEmpty()) {
                return UpdateAccountError.PhoneNumberAlreadyExists.left()
            }

            accountRepository.findById(accountId).let {
                if (it.mobilePhone == newPhoneNumber.msisdn) {
                    LOG.info(markers.andAppend("status", "no change"), "updatePhoneNumber")
                    return Unit.right()
                }

                if (it.mobilePhone != currentPhoneNumber.msisdn) {
                    LOG.info(
                        markers.andAppend("updatePhoneNumberError", "Telefone atual inválido"),
                        "updatePhoneNumber",
                    )
                    return UpdateAccountError.InvalidPhoneNumber.left()
                }
                markers.andAppend("oldPhoneNumber", it.mobilePhone)

                try {
                    userPoolAdapter.updatePhoneNumber(it.document, newPhoneNumber)
                } catch (e: UserNotFoundException) {
                    LOG.warn(markers.andAppend("updatePhoneNumberError", "Usuário não encontrado no Cognito"), "updatePhoneNumber")
                }

                val updatedAccount = it.copy(mobilePhone = newPhoneNumber.msisdn)
                accountRepository.save(updatedAccount)
                crmService.upsertContact(updatedAccount)
            }

            accountRegisterRepository.findByAccountId(accountId).let {
                accountRegisterRepository.save(it.copy(mobilePhone = newPhoneNumber))
            }

            loginRepository.findUserLogin(
                ProviderUser(
                    id = accountId.value,
                    providerName = ProviderName.WHATSAPP,
                    username = "",
                    emailAddress = EmailAddress(email = ""),
                ),
            )?.let {
                val whatsappIdSuffix = "@wa.gw.msging.net"
                loginRepository.createLogin(
                    ProviderUser(
                        id = it.accountId.value,
                        providerName = ProviderName.WHATSAPP,
                        username = "",
                        emailAddress = EmailAddress(
                            email = "${newPhoneNumber.msisdn.replace("+", "")}$whatsappIdSuffix",
                        ), // FIXME não é possivel pegar apenas o userId no momento
                    ),
                    id = it.accountId,
                    role = it.role,
                )
            }

            loginRepository.createLogin(
                ProviderUser(
                    id = accountId.value,
                    providerName = ProviderName.MSISDN,
                    username = "",
                    emailAddress = createTemporaryEmail(newPhoneNumber),
                ),
                id = accountId,
                role = Role.GUEST,
            )

            LOG.info(markers, "updatePhoneNumber")

            return Unit.right()
        } catch (e: AccountNotFoundException) {
            LOG.warn(markers, "updatePhoneNumber", e)
            return UpdateAccountError.AccountNotFound.left()
        } catch (e: InvalidParameterException) {
            LOG.warn(markers, "updatePhoneNumber", e)
            return UpdateAccountError.InvalidPhoneNumber.left()
        } catch (e: Exception) {
            LOG.error(markers, "updatePhoneNumber", e)
            return UpdateAccountError.Unknown(e).left()
        }
    }

    fun updateEmailAddress(
        accountId: AccountId,
        newEmailAddress: EmailAddress,
        currentEmailAddress: EmailAddress,
    ): Either<UpdateAccountError, Unit> {
        val markers = Markers.append("accountId", accountId.value)
            .andAppend("emailAddress", newEmailAddress)
            .andAppend("currentEmailAddress", currentEmailAddress)

        try {
            accountRepository.findById(accountId).let {
                if (it.emailAddress.value != currentEmailAddress.value) {
                    LOG.info(markers.andAppend("updateEmailAddressError", "Email atual inválido"), "updateEmailAddress")
                    return UpdateAccountError.InvalidEmailAddress.left()
                }
                if (it.emailAddress.value == newEmailAddress.value) {
                    LOG.info(markers.andAppend("status", "no change"), "updateEmailAddress")
                    return Unit.right()
                }

                val updatedAccount = it.copy(emailAddress = newEmailAddress)

                if (userPoolAdapter.doesUserExist(it.document)) {
                    userPoolAdapter.updateEmailAddress(it.document, newEmailAddress)
                }

                userJourneyService.registerAsync(updatedAccount)

                loginRepository.updateEmail(accountId, it.emailAddress, newEmailAddress)

                accountRepository.save(updatedAccount)

                crmService.upsertContact(updatedAccount)
            }

            try {
                accountRegisterRepository.findByAccountId(accountId).let {
                    accountRegisterRepository.save(it.copy(emailAddress = newEmailAddress))
                }
            } catch (e: ItemNotFoundException) {
                LOG.warn(markers.andAppend("context", "accountRegister não encontrado. se for PJ não existe mesmo"), "updateEmailAddress", e)
            }

            val allWallets = walletRepository.findWallets(accountId = accountId, memberStatus = MemberStatus.ACTIVE) +
                walletRepository.findWallets(accountId = accountId, memberStatus = MemberStatus.REMOVED)

            allWallets.forEach { wallet ->
                walletRepository.upsertMember(
                    wallet.id,
                    wallet.getMember(accountId).copy(emailAddress = newEmailAddress),
                )
            }

            LOG.info(markers, "updateEmailAddress")

            return Unit.right()
        } catch (e: AccountNotFoundException) {
            LOG.warn(markers, "updateEmailAddress", e)
            return UpdateAccountError.AccountNotFound.left()
        } catch (e: InvalidParameterException) {
            LOG.warn(markers, "updateEmailAddress", e)
            return UpdateAccountError.InvalidEmailAddress.left()
        } catch (e: Exception) {
            LOG.error(markers, "updateEmailAddress", e)
            return UpdateAccountError.Unknown(e).left()
        }
    }

    fun updateName(
        accountId: AccountId,
        newName: String,
        currentName: String,
    ): Either<UpdateAccountError, Unit> {
        val markers = Markers.append("accountId", accountId.value)
            .andAppend("newName", newName)
            .andAppend("currentName", currentName)

        try {
            accountRepository.findById(accountId).let {
                if (it.name != currentName) {
                    LOG.info(markers.andAppend("updateNameError", "Nome atual inválido"), "updateName")
                    return UpdateAccountError.InvalidName.left()
                }
                if (it.name == newName) {
                    LOG.info(markers.andAppend("status", "no change"), "updateName")
                    return Unit.right()
                }

                val updatedAccount = it.copy(name = newName)

                userJourneyService.registerAsync(updatedAccount)

                accountRepository.save(updatedAccount)

                crmService.upsertContact(updatedAccount)

                if (it.documentType != DocumentType.CNPJ.value) {
                    accountRegisterRepository.findByAccountId(accountId).let { accountRegister ->
                        accountRegisterRepository.save(accountRegister.copy(documentInfo = accountRegister.documentInfo?.copy(name = newName), nickname = newName))
                    }
                }
            }

            val allWallets = walletRepository.findWallets(accountId = accountId, memberStatus = MemberStatus.ACTIVE) +
                walletRepository.findWallets(accountId = accountId, memberStatus = MemberStatus.REMOVED)

            allWallets.forEach { wallet ->
                if (wallet.checkPrimary(accountId)) {
                    walletRepository.save(wallet.copy(name = newName))
                }

                walletRepository.upsertMember(
                    wallet.id,
                    wallet.getMember(accountId).copy(name = newName),
                )
            }

            LOG.info(markers, "updateName")

            return Unit.right()
        } catch (e: AccountNotFoundException) {
            LOG.warn(markers, "updateName", e)
            return UpdateAccountError.AccountNotFound.left()
        } catch (e: InvalidParameterException) {
            LOG.warn(markers, "updateName", e)
            return UpdateAccountError.InvalidEmailAddress.left()
        } catch (e: Exception) {
            LOG.error(markers, "updateName", e)
            return UpdateAccountError.Unknown(e).left()
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(UpdateAccountService::class.java)
    }
}

sealed class UpdateAccountError : PrintableSealedClassV2() {
    data object AccountNotFound : UpdateAccountError()
    data object InvalidPhoneNumber : UpdateAccountError()
    data object PhoneNumberAlreadyExists : UpdateAccountError()
    data object InvalidEmailAddress : UpdateAccountError()
    data object InvalidName : UpdateAccountError()
    data object InvalidSubscriptionType : UpdateAccountError()
    class Unknown(val reason: Exception) : UpdateAccountError()
}