package ai.friday.billpayment.app.account

import ai.friday.billpayment.adapters.lock.issuedTokenLockProvider
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.SmsSender
import ai.friday.billpayment.app.integrations.TokenData
import ai.friday.billpayment.app.integrations.TokenDataWithExpiration
import ai.friday.billpayment.app.integrations.TokenRepository
import ai.friday.billpayment.app.integrations.TokenType
import ai.friday.billpayment.app.liveness.LivenessId
import ai.friday.billpayment.app.metrics.AbstractFridayCountMetrics
import ai.friday.billpayment.app.metrics.metricRegister
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import jakarta.inject.Named
import jakarta.inject.Singleton
import java.time.Duration
import java.time.Instant
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import kotlin.math.min
import kotlin.random.Random
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
open class FridayTokenService(
    tokenRepository: TokenRepository,
    smsSender: SmsSender,
    notificationAdapter: NotificationAdapter,
    configuration: TokenOnboardingConfiguration,
    @Named(issuedTokenLockProvider) issuedTokenLock: InternalLock,
) : TokenService(
    tokenRepository,
    smsSender,
    notificationAdapter,
    configuration,
    issuedTokenLock,
)

abstract class TokenService(
    private val tokenRepository: TokenRepository,
    private val smsSender: SmsSender,
    private val notificationAdapter: NotificationAdapter,
    private val configuration: TokenOnboardingConfiguration,
    private val issuedTokenLock: InternalLock,
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    open fun issuePasswordRecoveryToken(
        email: EmailAddress,
        accountId: AccountId,
        document: String,
    ): Either<Exception, IssuedToken> {
        val result = issueToken(
            tokenData = TokenData.of(email),
            accountId = accountId,
            duration = Duration.ofSeconds(configuration.passwordRecoveryDuration),
        ).getOrElse { return it.left() }

        notificationAdapter.notifyForgotPasswordToken(
            accountId = accountId,
            emailAddress = email,
            document = document,
            token = result.token,
            duration = result.duration,
        )

        return IssuedToken(
            duration = result.duration,
            cooldown = result.cooldown,
            accountId = accountId,
        ).right()
    }

    open fun issueToken(
        email: EmailAddress,
        accountId: AccountId,
    ): Either<Exception, IssuedToken> {
        val result = issueToken(
            tokenData = TokenData.of(email),
            accountId = accountId,
            duration = Duration.ofSeconds(configuration.emailVerificationDuration),
        ).getOrElse { return it.left() }

        notificationAdapter.notifyEmailVerificationToken(
            accountId = accountId,
            emailAddress = email,
            token = result.token,
            duration = result.duration,
        )

        return IssuedToken(
            duration = result.duration,
            cooldown = result.cooldown,
            accountId = accountId,
        ).right()
    }

    open fun issueToken(
        mobilePhone: MobilePhone,
        accountId: AccountId,
        channel: TokenChannel,
    ): Either<Exception, IssuedToken> {
        metricRegister(
            TokenCountMetrics(),
            "accountId" to accountId.value,
            "mobilePhone" to mobilePhone.msisdn,
        )
        val lock = issuedTokenLock.acquireLock(lockName = "${accountId.value}#${channel.name}", simultaneousLock = 20)
        if (lock == null) {
            logger.warn(
                Markers.append("reason", "máximo de tentativas de notificação de token excedidas para o período")
                    .andAppend("accountId", accountId.value),
                "sendIssuedTokenNotification",
            )
            return Either.Left(IssueTokenMaxLimitExceeded())
        }

        val token = if (mobilePhone.isTestMobilePhone()) {
            getZonedDateTime().format(DateTimeFormatter.ofPattern("ddMMyy"))
        } else if (mobilePhone.isAppleMobilePhone()) {
            configuration.appleTokenVerify
        } else {
            TokenGenerator.generate(Random, configuration.tokenSize)
        }

        val configDuration = when (channel) {
            TokenChannel.SMS -> Duration.ofSeconds(configuration.phoneVerificationDuration)
            TokenChannel.WHATSAPP -> Duration.ofSeconds(configuration.whatsappVerificationDuration)
        }

        val tokenData = TokenData.of(mobilePhone, channel)

        val result = issueToken(
            tokenData = tokenData,
            accountId = accountId,
            duration = configDuration,
            token = token,
        ).getOrElse {
            tokenRepository.retrieveNotExpiredTokenKey(accountId, tokenData.type).map { tokenKey ->
                sendIssuedTokenNotification(mobilePhone, channel, accountId, tokenKey.value)
            }

            return it.left()
        }

        sendIssuedTokenNotification(mobilePhone, channel, accountId, token)

        return IssuedToken(
            duration = result.duration,
            cooldown = result.cooldown,
            accountId = accountId,
        ).right()
    }

    private fun sendIssuedTokenNotification(
        mobilePhone: MobilePhone,
        channel: TokenChannel,
        accountId: AccountId,
        token: String,

    ) {
        if (!mobilePhone.isTestMobilePhone() && !mobilePhone.isAppleMobilePhone()) {
            when (channel) {
                TokenChannel.WHATSAPP -> notificationAdapter.notifyToken(accountId, mobilePhone, token)
                TokenChannel.SMS -> smsSender.send(mobilePhone, tokenMessage(token))
            }
        }
    }

    fun issueToken(livenessId: LivenessId, accountId: AccountId): Either<Exception, IssuedToken> {
        val result = issueToken(
            tokenData = TokenData.of(livenessId),
            accountId = accountId,
            duration = Duration.ofSeconds(configuration.livenessVerificationDuration),
            token = livenessId.value,
        ).getOrElse { return it.left() }

        return IssuedToken(
            duration = result.duration,
            cooldown = result.cooldown,
            accountId = accountId,
        ).right()
    }

    fun validateToken(
        tokenKey: TokenKey,
        tokenType: TokenType,
    ): Either<Exception, TokenData> {
        val markers = Markers.append("accountId", tokenKey.accountId.value)
            .andAppend("token", tokenKey.value.mask())
        return try {
            tokenRepository.retrieveValidated(tokenKey, tokenType, configuration.maxErrorCount).map { tokenData ->
                markers.andAppend("tokenData", tokenData)
                tokenRepository.delete(tokenKey, tokenData)
                logger.info(markers, "validateToken")
                tokenData.right()
            }.getOrElse {
                logger.warn(markers.andAppend("reason", it.reason.name), "validateToken")
                tokenRepository.incrementErrorCount(tokenKey.accountId)
                it.left()
            }
        } catch (e: Exception) {
            logger.error(markers, "validateToken", e)
            Either.Left(e)
        }
    }

    fun findIssuedToken(
        accountId: AccountId,
        tokenType: TokenType,
    ): Either<InvalidTokenException, TokenDataWithExpiration> {
        return tokenRepository.retrieveNotExpired(accountId, tokenType)
    }

    fun issueToken(
        tokenData: TokenData,
        accountId: AccountId,
        duration: Duration,
        token: String = TokenGenerator.generate(Random, configuration.tokenSize),
    ): Either<Exception, IssueTokenResult> {
        val markers = Markers.append("accountId", accountId.value)
            .andAppend("tokenData", tokenData)
            .andAppend("duration", duration.seconds)

        try {
            val expiration = getZonedDateTime().plusSeconds(duration.seconds).toEpochSecond()
            val cooldown = Duration.ofSeconds(
                when (tokenData.type) {
                    TokenType.EMAIL -> min(configuration.maxCooldownDuration, configuration.emailVerificationDuration)
                    TokenType.MOBILE_PHONE -> min(configuration.maxCooldownDuration, configuration.phoneVerificationDuration)
                    TokenType.LIVENESS_ID -> configuration.livenessVerificationDuration
                    TokenType.WHATSAPP -> min(configuration.maxCooldownDuration, configuration.whatsappVerificationDuration)
                },
            )

            markers.andAppend("cooldown", cooldown.seconds)
                .andAppend("expiration", expiration)

            tokenRepository.retrieveNotExpired(accountId, tokenData.type)
                .map {
                    val now = getZonedDateTime().truncatedTo(ChronoUnit.SECONDS)
                    val expiresAt = ZonedDateTime.ofInstant(Instant.ofEpochSecond(it.expiration), brazilTimeZone)
                    val createdAt = expiresAt.minusSeconds(duration.seconds) // NOTE: assume-se que o tempo de duração não mudou após ser salvo no banco
                    val coolsDownAt = createdAt.plusSeconds(cooldown.seconds)

                    markers.andAppend("cooledDown", now > coolsDownAt)

                    if (now <= coolsDownAt) {
                        logger.warn(markers, "issueToken")
                        return TokenStillValidException(accountId, duration = Duration.between(now, expiresAt), cooldown = Duration.between(now, coolsDownAt)).left()
                    }

                    tokenRepository.retrieveNotExpiredTokenKey(accountId, tokenData.type)
                        .map { key ->
                            tokenRepository.delete(key, it)
                        }
                }

            markers.andAppend("token", token.mask())

            tokenRepository.save(TokenKey(accountId, token), TokenDataWithExpiration(tokenData, expiration))

            logger.info(markers, "issueToken")
            return IssueTokenResult(token, duration = duration, cooldown = cooldown).right()
        } catch (e: Exception) {
            logger.error(markers, "issueToken", e)
            return Either.Left(e)
        }
    }

    private fun MobilePhone.isTestMobilePhone() = msisdn in configuration.testMobilePhones

    private fun MobilePhone.isAppleMobilePhone() = msisdn in configuration.appleMobilePhones

    private fun tokenMessage(token: String) = configuration.message.format(token)

    private fun String.mask() = replaceRange(1, Math.max(length - 1, 1), "*".repeat(Math.max(length - 2, 0)))
}

interface TokenOnboardingConfiguration {
    val tokenSize: Int
    val maxCooldownDuration: Long
    val phoneVerificationDuration: Long
    val emailVerificationDuration: Long
    val whatsappVerificationDuration: Long
    val passwordRecoveryDuration: Long
    val livenessVerificationDuration: Long
    val maxErrorCount: Int
    val message: String
    val testMobilePhones: List<String>
    val appleMobilePhones: List<String>
    val appleTokenVerify: String
}

object TokenGenerator {
    fun generate(random: Random, size: Int) = buildString {
        repeat(size) { append(random.nextInt(0, 9)) }
    }
}

data class IssuedToken(
    val duration: Duration,
    val cooldown: Duration,
    val accountId: AccountId,
)

data class TokenKey(
    val accountId: AccountId,
    val value: String,
)

enum class TokenChannel {
    WHATSAPP, SMS
}

data class IssueTokenResult(
    val token: String,
    val duration: Duration,
    val cooldown: Duration,
)

class TokenCountMetrics : AbstractFridayCountMetrics()