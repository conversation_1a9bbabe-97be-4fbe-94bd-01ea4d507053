package ai.friday.billpayment.app.account

import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.app.IntegrationError
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus.ACTIVE
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus.PENDING
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.AcquirerService
import ai.friday.billpayment.app.integrations.CreditCardChallengeRepository
import ai.friday.billpayment.app.integrations.CreditCardInformationService
import ai.friday.billpayment.app.integrations.CreditCardScore
import ai.friday.billpayment.app.integrations.CreditCardScoreService
import ai.friday.billpayment.app.integrations.CreditCardScoreValidationRequest
import ai.friday.billpayment.app.integrations.CreditCardService
import ai.friday.billpayment.app.integrations.FeatureConfiguration
import ai.friday.billpayment.app.integrations.GeneralCreditCardConfiguration
import ai.friday.billpayment.app.integrations.HMacService
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.limit.LimitIncrementResult
import ai.friday.billpayment.app.limit.LimitService
import ai.friday.billpayment.app.metrics.AbstractFridayCountMetrics
import ai.friday.billpayment.app.metrics.push
import ai.friday.billpayment.app.payment.CreditCardAuthorization
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.flatMap
import arrow.core.getOrElse
import arrow.core.right
import jakarta.inject.Named
import jakarta.inject.Singleton
import java.time.Duration
import java.util.UUID
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.time.delay
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

const val hmacLockTag = "hmac"

object CreditCardAddMetric : AbstractFridayCountMetrics()
object CreditCardChallengeMetric : AbstractFridayCountMetrics()
object CreditCardChallengeValidationMetric : AbstractFridayCountMetrics()

@Singleton
open class DefaultCreditCardService(
    private val accountRepository: AccountRepository,
    private val acquirerService: AcquirerService,
    private val creditCardInformationService: CreditCardInformationService?,
    private val featureConfiguration: FeatureConfiguration,
    private val creditCardChallengeRepository: CreditCardChallengeRepository,
    private val generalCreditCardConfiguration: GeneralCreditCardConfiguration,
    private val creditCardScoreService: CreditCardScoreService,
    private val limitService: LimitService,
    private val hmacService: HMacService,
    @Named(hmacLockTag) private val lockProvider: InternalLock,
) : CreditCardService {

    fun add(
        accountId: AccountId,
        cardNumber: String,
        expirationDate: String,
        brand: CreditCardBrand,
        cvv: String,
    ): AddCreditCardResult = addCreditCard(accountId, cardNumber, expirationDate, brand, cvv)
        .also {
            val tags = buildMap {
                put("result", it::class.java.simpleName)
                put("brand", brand.name)
                if (it is AddCreditCardResult.HighRiskCreditCard) {
                    put("riskLevel", it.riskLevel.name)
                }
            }
            CreditCardAddMetric.push(tags)
        }

    private fun addCreditCard(
        accountId: AccountId,
        cardNumber: String,
        expirationDate: String,
        brand: CreditCardBrand,
        cvv: String,
    ): AddCreditCardResult {
        val bin = cardNumber.take(8)
        val lastFourDigits = cardNumber.takeLast(4)
        val markers = Markers.append("accountId", accountId.value)
            .andAppend("bin", bin)
            .andAppend("lastFourDigits", lastFourDigits)

        val hmac = hmacService.sign(cardNumber, expirationDate)
        val lock = lockProvider.acquireLock(hmac)
            ?: return AddCreditCardResult.ServerError(IllegalStateException("Could not acquire lock"))
        try {
            val existingCreditCards = accountRepository.findAccountPaymentMethodsByHMac(hmac)
            if (existingCreditCards.isNotEmpty()) {
                return AddCreditCardResult.CreditCardAlreadyInUse
            }
            val account = accountRepository.findById(accountId)

            val riskLevel = checkRiskLevel(
                account = account,
                bin = bin,
                lastFourDigits = lastFourDigits,
            ).getOrElse {
                logger.error(markers, "CreditCardService#add", it)
                return AddCreditCardResult.ServerError(it)
            }
            markers.andAppend("riskLevel", riskLevel)

            if (riskLevel.level > account.creditCardConfiguration.allowedRisk.level) {
                logger.warn(markers, "CreditCardService#add")
                return AddCreditCardResult.HighRiskCreditCard(riskLevel = riskLevel)
            }

            val paymentMethods = accountRepository.findAccountPaymentMethodsByAccountId(accountId)
            val creditCards = paymentMethods.filter { it.isCreditCard() }

            val creditCardBinDetails = this.retrieveBinDetails(bin)

            val originalCard =
                CreditCard(
                    brand = brand,
                    expiryDate = expirationDate,
                    pan = cardNumber,
                    binDetails = creditCardBinDetails,
                    riskLevel = riskLevel,
                )

            val activeOrPendingCreditCards =
                creditCards.filter { it.status in listOf(ACTIVE, PENDING) }

            val recentsCreditCard = creditCards.filter { getLocalDate() == it.created?.toLocalDate() }

            val periodRecentsCreditCard = creditCards.filter {
                it.created?.isAfter(getZonedDateTime().minus(generalCreditCardConfiguration.periodLimit)) ?: false
            }

            (recentsCreditCard - activeOrPendingCreditCards.toSet())
                .find { (it.method as CreditCard).isSameCard(originalCard) }
                ?.let { return AddCreditCardResult.CreditCardAlreadyAddedToday }

            activeOrPendingCreditCards.find { (it.method as CreditCard).isSameCard(originalCard) }
                ?.let { return AddCreditCardResult.Created(it) }

            if (!accountId.hasEarlyAccess() && periodRecentsCreditCard.size >= generalCreditCardConfiguration.maxPeriodLimit) {
                return AddCreditCardResult.PeriodLimitReached
            }

            if (activeOrPendingCreditCards.size >= generalCreditCardConfiguration.maxLimit) {
                return AddCreditCardResult.MaxLimitReached
            }

            val holderName = accountRepository.findById(accountId).name

            val token = acquirerService.tokenize(
                cardNumber = cardNumber,
                expirationDate = expirationDate,
                brand = brand,
                holderName = holderName,
                uid = AccountId().value,
            ).getOrElse {
                return when (it) {
                    is IntegrationError.ServerError -> AddCreditCardResult.ServerError(it.e)
                    is IntegrationError.ClientError -> AddCreditCardResult.InvalidCreditCard
                }
            }
            val tokenizedCard = CreditCard(
                brand = brand,
                token = token,
                expiryDate = expirationDate,
                holderName = holderName,
                pan = cardNumber,
                binDetails = creditCardBinDetails,
                riskLevel = riskLevel,
                hmac = hmacService.sign(cardNumber, expirationDate),
            )

            if (featureConfiguration.zeroAuthEnabled) {
                val validCard = acquirerService.validate(token = tokenizedCard.token!!, cvv = cvv).getOrElse {
                    return when (it) {
                        is IntegrationError.ServerError -> AddCreditCardResult.ServerError(it.e)
                        else -> AddCreditCardResult.ServerError()
                    }
                }

                if (!validCard) {
                    return AddCreditCardResult.UnauthorizedCreditCard
                }
            }

            val status =
                if (featureConfiguration.creditCardChallenge || account.isAlphaGroup()) PENDING else ACTIVE

            val accountPaymentMethod = accountRepository.createAccountPaymentMethod(
                accountId = accountId,
                creditCard = tokenizedCard,
                position = paymentMethods.size + 1,
                status = status,
            )
            markers.andAppend("accountPaymentMethodId", accountPaymentMethod.id.value)

            logger.warn(markers, "CreditCardService#add")
            return AddCreditCardResult.Created(accountPaymentMethod)
        } finally {
            lock.unlock()
        }
    }

    private fun checkRiskLevel(
        account: Account,
        bin: String,
        lastFourDigits: String,
    ): Either<Exception, RiskLevel> {
        return riskLevel(account, bin, lastFourDigits)
            .flatMap {
                when (it) {
                    CreditCardScore.MATCH -> RiskLevel.LOW
                    CreditCardScore.MANUAL_CHECK -> RiskLevel.MEDIUM
                    CreditCardScore.NO_MATCH -> RiskLevel.HIGH
                }.right()
            }
    }

    private fun riskLevel(account: Account, bin: String, lastFourDigits: String): Either<Exception, CreditCardScore> {
        return creditCardScoreService.creditcardScore(
            CreditCardScoreValidationRequest(
                bin = bin,
                lastFourDigits = lastFourDigits,
                cpf = account.document,
                totalValue = "0",
            ),
        )
    }

    fun delete(accountId: AccountId, accountPaymentMethodId: AccountPaymentMethodId): DeleteCreditCardResult {
        return accountRepository.deleteAccountPaymentMethod(accountId, accountPaymentMethodId)
    }

    fun find(accountId: AccountId, accountPaymentMethodId: AccountPaymentMethodId): CreditCard {
        return accountRepository.findAccountPaymentMethodByIdAndAccountId(
            accountId = accountId,
            accountPaymentMethodId = accountPaymentMethodId,
        ).method as CreditCard
    }

    fun retrieveBinDetails(bin: String): CreditCardBinDetails? {
        return creditCardInformationService?.retrieveBinDetails(bin) ?: getDefaultBinDetails(bin)
    }

    private fun getDefaultBinDetails(bin: String): CreditCardBinDetails? {
        val providerByBin = getProviderByBin(bin) ?: return null
        return CreditCardBinDetails(
            provider = providerByBin.name,
            cardType = "Crédito",
            foreignCard = false,
            corporateCard = "false",
            issuer = "UNKNOWN",
            issuerCode = "UNKNOWN",
            prepaid = "UNKNOWN",
            status = "00",
        )
    }

    private fun getProviderByBin(bin: String): CreditCardBrand? {
        val binInt = bin.take(6).toIntOrNull() ?: return null

        return when {
            binInt in 340000..349999 || binInt in 370000..379999 -> CreditCardBrand.AMEX
            binInt in 401178..401179 || binInt in 431274..431274 || binInt in 438935..438935 ||
                binInt in 451416..451416 || binInt in 457393..457393 || binInt in 457631..457632 ||
                binInt in 504175..504175 || binInt in 506699..506778 || binInt in 509000..509999 ||
                binInt in 650031..650033 || binInt in 650035..650051 || binInt in 650405..650439 ||
                binInt in 650485..650538 || binInt in 650541..650598 || binInt in 650700..650718 ||
                binInt in 650720..650727 || binInt in 650901..650978 -> CreditCardBrand.ELO

            binInt in 300000..305999 || binInt in 309500..309599 || binInt in 360000..369999 ||
                binInt in 380000..399999 -> CreditCardBrand.DINERS

            binInt in 601100..601199 || binInt in 644000..649999 || binInt in 650000..659999 -> CreditCardBrand.DISCOVER
            binInt in 352800..358999 -> CreditCardBrand.JCB
            binInt in 606282..606282 || binInt in 637095..637095 || binInt in 637568..637599 -> CreditCardBrand.HIPERCARD
            binInt in 510000..559999 || binInt in 222100..272099 -> CreditCardBrand.MASTERCARD
            binInt in 400000..499999 -> CreditCardBrand.VISA
            binInt in 507860..507899 || binInt in 636297..636297 -> CreditCardBrand.AURA
            else -> null
        }
    }

    fun generatePairOfRandom(): Pair<Long, Long> {
        val maxAmount: Long = generalCreditCardConfiguration.challenge.maxAmount
        val minAmount: Long = generalCreditCardConfiguration.challenge.minAmount
        val firstMaxAmount: Long = maxAmount - minAmount
        val firstRange = minAmount..firstMaxAmount
        val firstRandom: Long = firstRange.random()
        val secondRange = (minAmount..(maxAmount - firstRandom)).filter { it != firstRandom }
        val secondRandom = secondRange.random()
        return firstRandom to secondRandom
    }

    fun createChallenge(
        accountId: AccountId,
        paymentMethodId: AccountPaymentMethodId,
    ): CreditCardChallengeResponse = createCreditCardChallenge(accountId, paymentMethodId).also {
        CreditCardChallengeMetric.push(mapOf("result" to it::class.java.simpleName))
    }

    private fun createCreditCardChallenge(
        accountId: AccountId,
        paymentMethodId: AccountPaymentMethodId,
    ): CreditCardChallengeResponse {
        try {
            val orderId = UUID.randomUUID().toString()
            val alternativeOrderId = UUID.randomUUID().toString()

            val (randomAmount, alternativeRandomAmount) = generatePairOfRandom()

            val paymentMethod = accountRepository.findAccountPaymentMethodByIdAndAccountId(
                accountId = accountId,
                accountPaymentMethodId = paymentMethodId,
            )

            if (paymentMethod.status != PENDING) {
                return CreditCardChallengeResponse.InvalidCreditCardStatus(paymentMethod.status)
            }

            val challenge = creditCardChallengeRepository.find(paymentMethodId = paymentMethodId)

            if (challenge != null) {
                if (challenge.status == CreditCardChallengeStatus.SUCCESS) {
                    return CreditCardChallengeResponse.InvalidChallengeStatus(challenge.status)
                }
                if (!challenge.isExpired()) {
                    return CreditCardChallengeResponse.ChallengeAlreadyExists
                }
            }

            val creditCard = paymentMethod.method as CreditCard

            when (limitService.increment(accountId, paymentMethod.id.value)) {
                LimitIncrementResult.Locked, LimitIncrementResult.Exceeded -> return CreditCardChallengeResponse.LimitReached
                LimitIncrementResult.NotFound -> limitService.createLimit(accountId, paymentMethod.id.value)
                LimitIncrementResult.Ok -> {}
            }
            val result = acquirerService.authorize(
                accountId = accountId,
                orderId = orderId,
                amount = randomAmount,
                creditCard = creditCard,
                softDescriptor = generalCreditCardConfiguration.challenge.softDescriptor,
            )

            if (result.status != CreditCardPaymentStatus.AUTHORIZED) {
                return handleChallengeAuthorizationError(result, orderId)
            }

            runBlocking {
                delay(Duration.ofSeconds(1))
            }

            val alternativeResult = acquirerService.authorize(
                accountId = accountId,
                orderId = alternativeOrderId,
                amount = alternativeRandomAmount,
                creditCard = creditCard,
                softDescriptor = generalCreditCardConfiguration.challenge.softDescriptor,
            )

            if (alternativeResult.status != CreditCardPaymentStatus.AUTHORIZED) {
                return handleChallengeAuthorizationError(alternativeResult, orderId, alternativeOrderId)
            }

            createChallenge(
                paymentMethodId = paymentMethodId,
                accountId = accountId,
                token = result.amount!!,
                alternativeToken = alternativeResult.amount!!,
                authorizationCode = result.acquirerReturnCode.orEmpty(),
                orderId = orderId,
                alternativeOrderId = alternativeOrderId,
            )

            return CreditCardChallengeResponse.Success
        } catch (e: Exception) {
            return CreditCardChallengeResponse.UnknownError(e)
        }
    }

    private fun handleChallengeAuthorizationError(
        result: CreditCardAuthorization,
        vararg orderIds: String,
    ): CreditCardChallengeResponse.CreditCardAutorizationError {
        if (result.status in listOf(CreditCardPaymentStatus.ABORTED, CreditCardPaymentStatus.DENIED)) {
            orderIds.forEach { orderId ->
                acquirerService.cancel(orderId) // Se der erro cancelando uma transacao que foi negada vai voltar erro desconhecido pro usuário
            }
        }

        return CreditCardChallengeResponse.CreditCardAutorizationError(
            status = result.status,
            acquirerReturnCode = result.acquirerReturnCode.orEmpty(),
            acquirerReturnMessage = result.acquirerReturnMessage,
        )
    }

    fun validateChallenge(
        accountId: AccountId,
        paymentMethodId: AccountPaymentMethodId,
        token: Long,
    ): ValidateChallengeResult = validateCreditCardChallenge(accountId, paymentMethodId, token).also {
        CreditCardChallengeValidationMetric.push(mapOf("result" to it::class.java.simpleName))
    }

    fun validateCreditCardChallenge(
        accountId: AccountId,
        paymentMethodId: AccountPaymentMethodId,
        token: Long,
    ): ValidateChallengeResult {
        try {
            val challengeResult = getChallenge(accountId, paymentMethodId)
            val paymentMethod = accountRepository.findAccountPaymentMethodByIdAndAccountId(
                accountPaymentMethodId = paymentMethodId,
                accountId = accountId,
            )

            if (paymentMethod.status !== PENDING) {
                return ValidateChallengeResult.CreditCardInvalidStatus(paymentMethod.status)
            }

            return when (challengeResult) {
                GetChallengeResult.Expired -> return ValidateChallengeResult.Expired
                is GetChallengeResult.InvalidStatus -> return ValidateChallengeResult.ChallengeInvalidStatus(
                    challengeResult.status,
                )

                GetChallengeResult.MaxAttemptsReached -> ValidateChallengeResult.AttemptsExceeded
                GetChallengeResult.NotFound -> return ValidateChallengeResult.NotFound
                is GetChallengeResult.Success -> {
                    val challenge = challengeResult.challenge

                    val updatedChallenge = challenge.increaseAttempt()

                    if (token != challenge.value && token != challenge.alternativeValue) {
                        creditCardChallengeRepository.save(updatedChallenge)
                        return ValidateChallengeResult.ChallengeMismatch
                    }

                    creditCardChallengeRepository.save(updatedChallenge.asSuccess())

                    val updatedPaymentMethod = paymentMethod.asActive()
                    accountRepository.activateAccountPaymentMethod(
                        accountId = accountId,
                        accountPaymentMethodId = updatedPaymentMethod.id,
                    )

                    try {
                        acquirerService.cancel(challenge.acquirerOrderId)
                        if (challenge.alternativeAcquirerOrderId != null) {
                            acquirerService.cancel(challenge.alternativeAcquirerOrderId)
                        }
                    } catch (e: Exception) {
                        return ValidateChallengeResult.FailedOnAcquirerVoid(e)
                    }

                    return ValidateChallengeResult.Success
                }
            }
        } catch (e: Exception) {
            return ValidateChallengeResult.UnknownError(e)
        }
    }

    private fun createChallenge(
        paymentMethodId: AccountPaymentMethodId,
        accountId: AccountId,
        token: Long,
        alternativeToken: Long,
        authorizationCode: String,
        orderId: String,
        alternativeOrderId: String,
    ) {
        val now = getZonedDateTime()
        val challenge = CreditCardChallenge(
            paymentMethodId = paymentMethodId,
            accountId = accountId,
            value = token,
            alternativeValue = alternativeToken,
            attempts = 0,
            status = CreditCardChallengeStatus.ACTIVE,
            createdAt = now,
            updatedAt = now,
            expiresAt = now.plus(generalCreditCardConfiguration.challenge.expiration),
            authorizationCode = authorizationCode,
            acquirerOrderId = orderId,
            alternativeAcquirerOrderId = alternativeOrderId,
        )
        creditCardChallengeRepository.save(challenge)
    }

    fun expireChallenge(creditCardChallenge: CreditCardChallenge) {
        creditCardChallengeRepository.save(creditCardChallenge.copy(status = CreditCardChallengeStatus.EXPIRED))
        acquirerService.cancel(creditCardChallenge.acquirerOrderId)
        if (creditCardChallenge.alternativeAcquirerOrderId != null) {
            acquirerService.cancel(creditCardChallenge.alternativeAcquirerOrderId)
        }
    }

    fun getChallenge(accountId: AccountId, paymentMethodId: AccountPaymentMethodId): GetChallengeResult {
        val challenge =
            creditCardChallengeRepository.find(paymentMethodId = paymentMethodId) ?: return GetChallengeResult.NotFound

        if (challenge.accountId != accountId) {
            return GetChallengeResult.NotFound
        }

        if (challenge.isExpired()) {
            return GetChallengeResult.Expired
        }

        if (challenge.status == CreditCardChallengeStatus.SUCCESS) {
            return GetChallengeResult.InvalidStatus(challenge.status)
        }

        if (challenge.attempts >= generalCreditCardConfiguration.challenge.maxAttempts) {
            return GetChallengeResult.MaxAttemptsReached
        }

        return GetChallengeResult.Success(challenge)
    }

    override fun findActiveAndPendingCreditCards(accountId: AccountId): List<AccountPaymentMethod> {
        return accountRepository.findAccountPaymentMethodsByAccountId(accountId).filter {
            it.method is CreditCard && it.status in listOf(ACTIVE, PENDING)
        }
    }

    fun findActiveAndPendingCreditCards(pan: String, expirationDate: String): List<AccountPaymentMethod> {
        val hmac = hmacService.sign(pan, expirationDate)
        return accountRepository.findAccountPaymentMethodsByHMac(hmac).filter {
            it.method is CreditCard && it.status in listOf(ACTIVE, PENDING)
        }
    }

    private val logger = LoggerFactory.getLogger(DefaultCreditCardService::class.java)
}

sealed class ValidateChallengeResult : PrintableSealedClassV2() {
    data object NotFound : ValidateChallengeResult()

    data object Expired : ValidateChallengeResult()

    class ChallengeInvalidStatus(val status: CreditCardChallengeStatus) : ValidateChallengeResult()

    data object ChallengeMismatch : ValidateChallengeResult()

    data object AttemptsExceeded : ValidateChallengeResult()

    class CreditCardInvalidStatus(val status: AccountPaymentMethodStatus) : ValidateChallengeResult()

    data object Success : ValidateChallengeResult()

    class FailedOnAcquirerVoid(val e: Exception) : ValidateChallengeResult()

    class UnknownError(val e: Exception) : ValidateChallengeResult()
}

sealed class GetChallengeResult : PrintableSealedClassV2() {
    data object NotFound : GetChallengeResult()

    data object Expired : GetChallengeResult()

    class InvalidStatus(val status: CreditCardChallengeStatus) : GetChallengeResult()

    data object MaxAttemptsReached : GetChallengeResult()

    class Success(val challenge: CreditCardChallenge) : GetChallengeResult()
}

sealed class AddCreditCardResult : PrintableSealedClassV2() {
    data object PeriodLimitReached : AddCreditCardResult()
    data object MaxLimitReached : AddCreditCardResult()
    data object CreditCardAlreadyAddedToday : AddCreditCardResult()
    data object CreditCardAlreadyInUse : AddCreditCardResult()
    class ServerError(val e: Exception? = null) : AddCreditCardResult()
    data object InvalidCreditCard : AddCreditCardResult()
    data object UnauthorizedCreditCard : AddCreditCardResult()
    data class HighRiskCreditCard(val riskLevel: RiskLevel) : AddCreditCardResult()
    class Created(val accountPaymentMethod: AccountPaymentMethod) : AddCreditCardResult()
}

sealed class DeleteCreditCardResult : PrintableSealedClassV2() {
    class ServerError(val e: Exception) : DeleteCreditCardResult()
    data object NotFound : DeleteCreditCardResult()
    data class Deleted(val accountPaymentMethodId: AccountPaymentMethodId) : DeleteCreditCardResult()
}

sealed class UpdateCreditCardMethodRiskResult : PrintableSealedClassV2() {
    data object NotFound : UpdateCreditCardMethodRiskResult()

    data class Updated(val accountPaymentMethodId: AccountPaymentMethodId) : UpdateCreditCardMethodRiskResult()
}

sealed class CreditCardChallengeResponse : PrintableSealedClassV2() {
    data object Success : CreditCardChallengeResponse()
    class InvalidCreditCardStatus(val status: AccountPaymentMethodStatus) : CreditCardChallengeResponse()
    class CreditCardAutorizationError(
        val status: CreditCardPaymentStatus,
        val acquirerReturnCode: String,
        val acquirerReturnMessage: String,
    ) : CreditCardChallengeResponse()

    data object ChallengeAlreadyExists : CreditCardChallengeResponse()
    class InvalidChallengeStatus(val status: CreditCardChallengeStatus) : CreditCardChallengeResponse()

    class UnknownError(val exception: Exception) : CreditCardChallengeResponse()
    data object LimitReached : CreditCardChallengeResponse()
}