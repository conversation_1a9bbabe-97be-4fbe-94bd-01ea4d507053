package ai.friday.billpayment.app.account

import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.BankAccountMode
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.chatbot.ChatbotMessagePublisher
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.CrmService
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.liveness.LivenessId
import ai.friday.billpayment.app.liveness.LivenessSelfieError
import ai.friday.billpayment.app.liveness.LivenessService
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.request
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.Property
import io.micronaut.http.MediaType
import jakarta.inject.Singleton
import java.io.ByteArrayInputStream
import java.time.Duration
import java.time.LocalDate
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
open class AccountService(
    private val accountConfigurationService: AccountConfigurationService,
    private val accountRepository: AccountRepository,
    private val chatbotMessagePublisher: ChatbotMessagePublisher,
    private val crmService: CrmService,
    private val notificationAdapter: NotificationAdapter,
    private val walletRepository: WalletRepository,
) {

    open fun save(account: Account) {
        accountRepository.save(account = account)
    }

    open fun create(account: Account): Account {
        return accountRepository.create(userAccount = account)
    }

    open fun createAccountPaymentMethod(
        accountId: AccountId,
        bankAccount: BankAccount,
        position: Int,
        mode: BankAccountMode = BankAccountMode.PHYSICAL,
    ): AccountPaymentMethod {
        return accountRepository.createAccountPaymentMethod(
            accountId = accountId,
            bankAccount = bankAccount,
            position = position,
            mode = mode,
        )
    }

    open fun findAccountById(accountId: AccountId): Account {
        return accountRepository.findById(accountId)
    }

    open fun findAccountByIdOrNull(accountId: AccountId): Account? {
        return accountRepository.findByIdOrNull(accountId)
    }

    open fun findAccountByDocument(document: String): Account {
        return accountRepository.findAccountByDocument(document)
    }

    open fun findAccountByDocumentOrNull(document: String): Account? {
        return accountRepository.findAccountByDocumentOrNull(document)
    }

    fun findLastAccountByDocument(document: String): Account {
        return accountRepository.listAccountsByDocument(document).maxByOrNull { it.created }
            ?: throw AccountNotFoundException(document)
    }

    open fun findPartialAccountById(accountId: AccountId) =
        accountRepository.findPartialAccountById(accountId)

    fun findPartialAccountByIdOrNull(accountId: AccountId) =
        accountRepository.findPartialAccountByIdOrNull(accountId)

    fun findPartialAccountByStatus(accountStatus: AccountStatus) =
        accountRepository.findPartialAccountByStatus(accountStatus)

    fun findAccountPaymentMethodsByAccountId(
        accountId: AccountId,
        status: AccountPaymentMethodStatus?,
    ): List<AccountPaymentMethod> {
        return status?.let {
            accountRepository.findAccountPaymentMethodsByAccountIdAndStatus(accountId, status)
        } ?: accountRepository.findAccountPaymentMethodsByAccountId(accountId)
    }

    fun findActiveAndPendingCreditCards(accountId: AccountId): List<AccountPaymentMethod> {
        return accountRepository.findAccountPaymentMethodsByAccountId(accountId).filter {
            it.method is CreditCard && it.status in listOf(
                AccountPaymentMethodStatus.ACTIVE,
                AccountPaymentMethodStatus.PENDING,
            )
        }
    }

    fun findPhysicalAccountPaymentMethod(bankNo: Long, routingNo: Long, fullAccountNo: String) =
        accountRepository.findPhysicalAccountPaymentMethod(bankNo, routingNo, AccountNumber(fullAccountNo))

    fun findAccountIdByPhysicalBankAccountNo(bankNo: Long, routingNo: Long, accountNo: AccountNumber) =
        accountRepository.findAccountIdByPhysicalBankAccountNo(bankNo, routingNo, accountNo)

    fun findVirtualBankAccountByDocument(document: String) =
        accountRepository.findVirtualBankAccountByDocument(document)

    fun checkAccountExists(externalId: ExternalId): Boolean {
        return accountRepository.findByExternalId(externalId)
            .any { it.status != AccountStatus.CLOSED && it.status != AccountStatus.DENIED }
    }

    fun findLastAccountByExternalId(externalId: ExternalId): Account {
        return accountRepository.findByExternalId(externalId).maxByOrNull { it.created }
            ?: throw ItemNotFoundException("Account not found")
    }

    fun findLastAccountByExternalIdOrNull(externalId: ExternalId): Account? {
        return accountRepository.findByExternalId(externalId).maxByOrNull { it.created }
    }

    fun findAllPhysicalBalanceAccount() = accountRepository.findAllPhysicalBalanceAccount()

    fun findAllVirtualBankAccount() = accountRepository.findAllVirtualBankAccount()

    open fun findAllAccountsByDocument(document: String): List<Account> {
        return accountRepository.listAccountsByDocument(document)
    }

    // FIXME walletId - separar metodos de pagamento da carteira (balance) dos metodos de pagamento do usuário (cartao de credito) ???
    fun findPhysicalBankAccountByAccountId(accountId: AccountId) =
        accountRepository.findPhysicalBankAccountByAccountId(accountId)

    suspend fun findNameAndEmail(accountId: AccountId, role: Role): Either<Exception, Pair<String, EmailAddress>> =
        accountRepository.findNameAndEmail(accountId, role)

    fun findAccountPaymentMethodByIdAndAccountId(accountPaymentMethodId: AccountPaymentMethodId, accountId: AccountId) =
        accountRepository.findAccountPaymentMethodByIdAndAccountId(accountPaymentMethodId, accountId)

    fun attemptToUpdateAccountStatus(request: UpdateAccountStatusRequest): Result<UpdateAccountStatusResult> = runCatching {
        val current = accountRepository.findByIdOrNull(request.userAccountId)

        if (current == null) {
            UpdateAccountStatusResult.AccountNotFound
        } else {
            val updated = current.attemptToUpdateStatus(request)

            if (updated != null) {
                accountRepository.save(updated)
                chatbotMessagePublisher.publishStateUpdate(updated, updated.paymentStatus, updated.status)
                crmService.upsertContact(updated)

                UpdateAccountStatusResult.Updated(updated)
            } else {
                UpdateAccountStatusResult.Unchanged(current)
            }
        }
    }

    open fun updateUpgradeStatus(accountId: AccountId, status: UpgradeStatus): Account {
        val updatedAccount = accountRepository.findById(accountId).copy(upgradeStatus = status)
        accountRepository.save(updatedAccount)
        return updatedAccount
    }

    open fun updatePartialAccountStatus(accountId: AccountId, status: AccountStatus) {
        accountRepository.updatePartialAccountStatus(accountId, status)
    }

    open fun deletePartialAccount(accountId: AccountId) {
        accountRepository.deletePartialAccount(accountId = accountId)
    }

    open fun addGroupsToPartialAccount(accountId: AccountId, groups: List<AccountGroup>): List<AccountGroup> {
        val partialAccount = accountRepository.findPartialAccountById(accountId)
        val groupsSet = (partialAccount.groups + groups).toSet()
        return setUserGroups(partialAccount, groupsSet)
    }

    open fun addGroupsToAccount(accountId: AccountId, groups: List<AccountGroup>): Account {
        val account = accountRepository.findById(accountId)
        val groupsSet = (account.configuration.groups + groups).toSet()
        return setUserGroups(account, groupsSet)
    }

    open fun addGroups(accountId: AccountId, groups: List<AccountGroup>): List<AccountGroup> {
        return try {
            addGroupsToAccount(accountId, groups).configuration.groups
        } catch (e: AccountNotFoundException) {
            addGroupsToPartialAccount(accountId, groups)
        }
    }

    open fun removeGroups(accountId: AccountId, groups: List<AccountGroup>): List<AccountGroup> {
        return removeGroups(accountId, groups.toSet())
    }

    open fun isGuestAccount(accountId: AccountId): Boolean {
        val role = accountRepository.checkAccountRole(accountId, Role.GUEST)
        return role != null
    }

    open fun updatePartialAccountEmail(accountId: AccountId, newEmailAddress: EmailAddress) {
        accountRepository.updatePartialAccountEmail(accountId, newEmailAddress)
    }

    open fun updatePartialAccountName(accountId: AccountId, newName: String) {
        accountRepository.updatePartialAccountName(accountId, newName)
    }

    fun updatePartialAccountRegistrationType(accountId: AccountId, registrationType: RegistrationType) {
        accountRepository.updatePartialAccountRegistrationType(accountId, registrationType)
    }

    private fun removeGroups(accountId: AccountId, groups: Set<AccountGroup>): List<AccountGroup> {
        val account = accountRepository.findByIdOrNull(accountId)

        return if (account != null) {
            val updatedGroups = account.configuration.groups.toSet() - groups
            setUserGroups(account, updatedGroups).configuration.groups
        } else {
            val partialAccount = accountRepository.findPartialAccountById(accountId)
            val updatedGroups = partialAccount.groups.toSet() - groups
            setUserGroups(partialAccount, updatedGroups)
        }
    }

    private fun setUserGroups(account: Account, groups: Set<AccountGroup>): Account {
        if (groups != account.configuration.groups.toSet()) {
            val updatedAccount = account.copy(
                configuration = account.configuration.copy(groups = groups.toList()),
            )
            accountRepository.save(updatedAccount)
            crmService.upsertContact(updatedAccount)

            return updatedAccount
        }
        return account
    }

    private fun setUserGroups(partialAccount: PartialAccount, groups: Set<AccountGroup>): List<AccountGroup> {
        if (groups != partialAccount.groups.toSet()) {
            val groupList = groups.toList()
            val updatedPartialAccount = accountRepository.updatePartialAccountGroups(partialAccount.id, groupList)

            crmService.upsertContact(updatedPartialAccount)

            return groupList
        }
        return partialAccount.groups
    }

    fun createAccount(command: CreateAccountCommand): Account {
        val account = Account(
            accountId = AccountId(),
            name = command.name,
            emailAddress = EmailAddress(command.email),
            document = command.document,
            documentType = "CPF",
            mobilePhone = command.mobilePhone.msisdn,
            created = getZonedDateTime(),
            updated = getZonedDateTime(),
            status = AccountStatus.REGISTER_INCOMPLETE,
            configuration = LegacyAccountConfiguration(
                creditCardConfiguration = CreditCardConfiguration(
                    quota = 0,
                ),
                defaultWalletId = null,
                receiveDDANotification = false,
                receiveNotification = command.receiveNotification,
                externalId = command.externalId,
                notificationGateway = command.notificationGateway,
            ),
            activated = null,
            channel = command.channel,
            type = UserAccountType.FULL_ACCOUNT,
            imageUrlSmall = command.imageUrlSmall,
            imageUrlLarge = command.imageUrlLarge,
            subscriptionType = command.subscriptionType,
        )
        accountRepository.save(account)
        return account
    }

    fun updateAccountConfig(
        accountId: AccountId,
        name: AccountConfigurationName,
        value: String,
    ): Either<UpdateAccountConfigError, Unit> {
        val account = accountRepository.findById(accountId)
        val result = when (name) {
            AccountConfigurationName.DEFAULT_WALLET_ID -> updateDefaultWalletId(account, WalletId(value))
            AccountConfigurationName.ACCESS_TOKEN -> updateAccessToken(account, value)
            AccountConfigurationName.REFRESH_TOKEN -> updateRefreshToken(account, value)
            AccountConfigurationName.RECEIVE_NOTIFICATION -> updateReceiveNotification(account, value)
            AccountConfigurationName.RECEIVE_DDA_NOTIFICATION -> updateReceiveDDANotification(account, value)
            AccountConfigurationName.NOTIFICATION_GATEWAY -> updateNotificationGateway(account, value)
        }
        return result.map {
            accountRepository.save(it)
        }
    }

    fun findAllUsersWithMonthlyAccountStatementEnabled(): List<Account> {
        return accountRepository.findAllAccountsActivatedSince(LocalDate.EPOCH)
            .filter { account -> accountConfigurationService.find(account.accountId).receiveMonthlyStatement }
    }

    fun findAllUsersWithAlphaGroup(): List<Account> {
        return accountRepository.findAllAccountsActivatedSince(LocalDate.EPOCH)
            .filter { account -> account.isAlphaGroup() }
    }

    fun createExternalAccountPaymentMethod(account: Account, providerName: AccountProviderName, position: Int) =
        accountRepository.createExternalAccountPaymentMethod(
            accountId = account.accountId,
            providerName = providerName,
            position = position,
        )

    private fun updateDefaultWalletId(account: Account, walletId: WalletId): Either<UpdateAccountConfigError, Account> {
        val wallet = walletRepository.findWalletOrNull(walletId)
        if (wallet == null) {
            return UpdateAccountConfigError.WalletNotFound.left()
        }
        if (!wallet.hasActiveMember(account.accountId)) {
            return UpdateAccountConfigError.UserNotAllowed.left()
        }
        if (!account.canSetAsDefault(wallet)) {
            return UpdateAccountConfigError.WalletCannotBeSetAsDefault.left()
        }
        return account.copy(configuration = account.configuration.copy(defaultWalletId = walletId)).right()
    }

    private fun updateAccessToken(account: Account, accessToken: String): Either<UpdateAccountConfigError, Account> {
        return account.copy(
            configuration = account.configuration.copy(
                accessToken = accessToken,
                receiveNotification = true,
                receiveDDANotification = true,
            ),
        ).right()
    }

    private fun updateNotificationGateway(
        account: Account,
        newGateway: String,
    ): Either<UpdateAccountConfigError, Account> {
        return account.copy(configuration = account.configuration.copy(notificationGateway = NotificationGateways.valueOf(newGateway)))
            .right()
    }

    private fun updateReceiveNotification(
        account: Account,
        receiveNotification: String,
    ): Either<UpdateAccountConfigError, Account> {
        return account.copy(configuration = account.configuration.copy(receiveNotification = receiveNotification.toBoolean()))
            .right()
    }

    private fun updateReceiveDDANotification(
        account: Account,
        receiveDDANotification: String,
    ): Either<UpdateAccountConfigError, Account> {
        return account.copy(configuration = account.configuration.copy(receiveDDANotification = receiveDDANotification.toBoolean()))
            .right()
    }

    private fun updateRefreshToken(account: Account, refreshToken: String): Either<UpdateAccountConfigError, Account> {
        return account.copy(configuration = account.configuration.copy(refreshToken = refreshToken)).right()
    }

    fun enableCreditCardUsage(
        accountId: AccountId,
        quota: Long? = null,
        sendNotification: Boolean? = true,
    ): Either<Exception, Account> {
        return try {
            val account = accountRepository.findById(accountId)
            val hasCreditCardEnabled = account.hasCreditCardEnabled()
            val currentCreditCardConfiguration = account.creditCardConfiguration

            if (!account.isOpen()) {
                return IllegalStateException("Account should be open").left()
            }

            val quotaToUse = when {
                quota != null -> quota
                hasCreditCardEnabled -> currentCreditCardConfiguration.quota
                else -> 500_00L
            }

            if (quotaToUse <= 0L) {
                return IllegalArgumentException("Quota should greater than zero").left()
            }

            val updatedCreditCardConfiguration = CreditCardConfiguration(
                quota = quotaToUse,
                allowedRisk = currentCreditCardConfiguration.allowedRisk,
            )

            if (updatedCreditCardConfiguration == currentCreditCardConfiguration) {
                return account.right()
            }

            accountRepository.save(
                account.copy(
                    configuration = account.configuration.copy(
                        creditCardConfiguration = updatedCreditCardConfiguration,
                    ),
                ),
            )

            val accountUpdated = addGroupsToAccount(account.accountId, listOf(AccountGroup.CREDIT_CARD_STANDARD_PLAN))

            if (!hasCreditCardEnabled) {
                LOG.info(
                    Markers.append("creditCardEnabled", true)
                        .andAppend("accountId", account.accountId.value),
                    "accountService#enableCreditCardUsage",
                )
                if (sendNotification == true) {
                    notificationAdapter.notifyCreditCardEnabled(account.accountId, updatedCreditCardConfiguration.quota)
                }
            }

            accountUpdated.right()
        } catch (e: Exception) {
            e.left()
        }
    }

    fun updateCreditCardAllowedRisk(
        accountId: AccountId,
        allowedRisk: RiskLevel,
    ): Either<Exception, Account> {
        return try {
            val account = accountRepository.findById(accountId)

            if (!account.isOpen()) {
                return IllegalStateException("Account should be open").left()
            }

            if (account.creditCardConfiguration.allowedRisk == allowedRisk) {
                return account.right()
            }

            return account.copy(
                configuration = account.configuration.copy(
                    creditCardConfiguration = account.configuration.creditCardConfiguration.copy(
                        allowedRisk = allowedRisk,
                    ),
                ),
            ).also {
                accountRepository.save(it)
            }.right()
        } catch (e: Exception) {
            e.left()
        }
    }

    fun disableCreditCardUsage(accountId: AccountId): Either<Exception, Unit> {
        return try {
            val account = accountRepository.findById(accountId)
            accountRepository.save(
                account.copy(
                    configuration = account.configuration.copy(
                        creditCardConfiguration = account.creditCardConfiguration.copy(
                            quota = 0,
                        ),
                    ),
                ),
            )

            removeGroups(account.accountId, listOf(AccountGroup.CREDIT_CARD_STANDARD_PLAN))
            Unit.right()
        } catch (e: Exception) {
            e.left()
        }
    }

    fun findAllUsersWithGroup(group: AccountGroup): List<Account> {
        return accountRepository.findByGroup(group)
    }

    fun getChatbotType(accountId: AccountId): ChatbotType {
        return try {
            getChatbotType(accountRepository.findById(accountId))
        } catch (ex: AccountNotFoundException) {
            val partialAccount = accountRepository.findPartialAccountById(accountId)
            if (partialAccount.status in listOf(AccountStatus.UNDER_REVIEW, AccountStatus.UNDER_EXTERNAL_REVIEW, AccountStatus.APPROVED)) {
                ChatbotType.CHABOT_AI
            } else {
                ChatbotType.CHATBOT_LEGACY
            }
        } catch (e: Exception) {
            LOG.warn("Error getting chatbot type for account $accountId", e)
            ChatbotType.CHATBOT_LEGACY
        }
    }

    fun getChatbotType(account: Account): ChatbotType {
        return when {
            account.hasAnyGroup(AccountGroup.CHATBOT_HUMAN_FIRST) -> ChatbotType.HUMAN_FIRST
            account.hasAnyGroup(AccountGroup.CHATBOT_LEGACY) -> ChatbotType.CHATBOT_LEGACY
            else -> ChatbotType.CHABOT_AI
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(AccountService::class.java)
    }
}

sealed class UpdateAccountStatusRequest(accountId: AccountId) {
    abstract fun apply(account: Account): Account?

    data class Block(val accountId: AccountId, val expiredFor: Duration) : UpdateAccountStatusRequest(accountId) {
        override fun apply(account: Account): Account? {
            val desiredPaymentStatus = if (expiredFor.toDays() >= 7) AccountPaymentStatus.Overdue else AccountPaymentStatus.PastDue

            return when (account.status) {
                AccountStatus.ACTIVE ->
                    account.copy(status = AccountStatus.BLOCKED, paymentStatus = desiredPaymentStatus)

                AccountStatus.CLOSED,
                AccountStatus.BLOCKED,
                AccountStatus.PENDING_CLOSE,
                ->
                    if (account.paymentStatus != desiredPaymentStatus) {
                        account.copy(paymentStatus = desiredPaymentStatus)
                    } else {
                        null
                    }

                AccountStatus.REGISTER_INCOMPLETE,
                AccountStatus.DENIED,
                AccountStatus.UNDER_REVIEW,
                AccountStatus.UNDER_EXTERNAL_REVIEW,
                AccountStatus.APPROVED,
                ->
                    null
            }?.copy(updated = getZonedDateTime())
        }
    }

    data class Unblock(val accountId: AccountId) : UpdateAccountStatusRequest(accountId) {
        override fun apply(account: Account): Account? {
            return when (account.status) {
                AccountStatus.BLOCKED ->
                    account.copy(status = AccountStatus.ACTIVE, paymentStatus = AccountPaymentStatus.UpToDate)

                AccountStatus.ACTIVE,
                AccountStatus.CLOSED,
                AccountStatus.PENDING_CLOSE,
                ->
                    if (account.paymentStatus != AccountPaymentStatus.UpToDate) {
                        account.copy(paymentStatus = AccountPaymentStatus.UpToDate)
                    } else {
                        null
                    }

                AccountStatus.REGISTER_INCOMPLETE,
                AccountStatus.DENIED,
                AccountStatus.UNDER_REVIEW,
                AccountStatus.UNDER_EXTERNAL_REVIEW,
                AccountStatus.APPROVED,
                ->
                    null
            }?.copy(updated = getZonedDateTime())
        }
    }

    data class Approve(val accountId: AccountId) : UpdateAccountStatusRequest(accountId) {
        override fun apply(account: Account): Account? {
            return when (account.status) {
                AccountStatus.REGISTER_INCOMPLETE ->
                    account.copy(status = AccountStatus.APPROVED)

                AccountStatus.ACTIVE,
                AccountStatus.CLOSED,
                AccountStatus.BLOCKED,
                AccountStatus.PENDING_CLOSE,
                AccountStatus.DENIED,
                AccountStatus.UNDER_REVIEW,
                AccountStatus.UNDER_EXTERNAL_REVIEW,
                AccountStatus.APPROVED,
                ->
                    null
            }?.copy(updated = getZonedDateTime())
        }
    }

    data class Close(val accountId: AccountId) : UpdateAccountStatusRequest(accountId) {
        override fun apply(account: Account): Account? {
            return when (account.status) {
                AccountStatus.ACTIVE,
                AccountStatus.BLOCKED,
                AccountStatus.PENDING_CLOSE,
                ->
                    account.copy(status = AccountStatus.CLOSED)

                AccountStatus.CLOSED,
                AccountStatus.REGISTER_INCOMPLETE,
                AccountStatus.DENIED,
                AccountStatus.UNDER_REVIEW,
                AccountStatus.UNDER_EXTERNAL_REVIEW,
                AccountStatus.APPROVED,
                ->
                    null
            }?.copy(updated = getZonedDateTime())
        }
    }

    val userAccountId = accountId
}

sealed class UpdateAccountStatusResult {
    data class Updated(val account: Account) : UpdateAccountStatusResult()
    data class Unchanged(val account: Account) : UpdateAccountStatusResult()
    data object AccountNotFound : UpdateAccountStatusResult()
}

@Singleton
open class AccountRegisterService(
    private val accountRegisterRepository: AccountRegisterRepository,
    private val livenessService: LivenessService,
    private val userFilesConfiguration: UserFilesConfiguration,
) {

    @field:Property(name = "token.onboarding.testMobilePhones")
    lateinit var signUpMobilePhones: List<String>

    fun readAccountCity(accountId: AccountId): String {
        val accountCity = try {
            accountRegisterRepository.findByAccountId(accountId).address?.city.takeIf {
                !it.isNullOrBlank()
            }
        } catch (e: ItemNotFoundException) {
            null
        }
        return accountCity ?: "RIO DE JANEIRO"
    }

    fun checkIsTestAccount(mobilePhone: MobilePhone): Boolean {
        return signUpMobilePhones.contains(mobilePhone.msisdn)
    }

    fun checkIsTestAccount(accountId: AccountId): Boolean {
        return try {
            val accountRegisterData = accountRegisterRepository.findByAccountId(accountId)
            accountRegisterData.mobilePhone?.let { checkIsTestAccount(it) } ?: false
        } catch (e: ItemNotFoundException) {
            false
        }
    }

    fun processSelfie(accountId: AccountId, livenessId: LivenessId): Either<Exception, StoredObject> {
        val selfieByteArray = livenessService.retrieveEnrollmentSelfie(livenessId).getOrElse {
            return if (it is LivenessSelfieError.Error) it.e.left() else Exception("NO_SELFIE_AVAILABLE").left()
        }

        val key = buildObjectPath(
            accountId,
            userFilesConfiguration.selfiePrefix,
            "jpg",
        )

        val storedObject = StoredObject(userFilesConfiguration.region, userFilesConfiguration.bucket, key)

        try {
            accountRegisterRepository.putDocument(storedObject, ByteArrayInputStream(selfieByteArray), MediaType.IMAGE_JPEG_TYPE)
        } catch (e: Exception) {
            return e.left()
        }

        return storedObject.right()
    }

    private fun buildObjectPath(accountId: AccountId, prefix: String, extension: String) =
        "${userFilesConfiguration.path}/${accountId.value}/" +
            "$prefix${getZonedDateTime().toEpochSecond()}.$extension"

    companion object {
        private val LOG = LoggerFactory.getLogger(AccountRegisterService::class.java)
    }
}

fun Account.canSetAsDefault(wallet: Wallet) = wallet.getActiveMember(accountId).type != MemberType.ASSISTANT

sealed class UpdateAccountConfigError : PrintableSealedClassV2() {
    data object WalletNotFound : UpdateAccountConfigError()
    data object UserNotAllowed : UpdateAccountConfigError()
    data object WalletCannotBeSetAsDefault : UpdateAccountConfigError()
}

enum class AccountConfigurationName {
    DEFAULT_WALLET_ID, ACCESS_TOKEN, REFRESH_TOKEN, RECEIVE_NOTIFICATION, RECEIVE_DDA_NOTIFICATION, NOTIFICATION_GATEWAY
}

enum class SignInPendingAction {
    NONE, CREATE_PASSWORD_REQUIRED, DEVICE_BINDING_REQUIRED, PASSWORD_AUTHENTICATION_REQUIRED
}

enum class ChatbotType(val value: String) {
    CHABOT_AI("CHATBOT_AI"),
    CHATBOT_LEGACY("CHATBOT_LEGACY"),
    HUMAN_FIRST("HUMAN_FIRST"),
    ;

    fun checkHasAIChatbotEnabled(): Boolean {
        return this == CHABOT_AI
    }
}