package ai.friday.billpayment.app.name

import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.BigDataService
import ai.friday.billpayment.app.integrations.DocumentBelongsToAMinorException
import ai.friday.billpayment.app.integrations.DocumentNotFoundException
import ai.friday.billpayment.app.isValidCnpj
import ai.friday.billpayment.app.isValidCpf
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import jakarta.inject.Singleton

@Singleton
class NameService(
    private val bigDataService: BigDataService,
    private val accountRegisterRepository: AccountRegisterRepository,
) {
    fun getName(accountId: AccountId): Either<GetNameError, String> {
        return kotlin.runCatching {
            val accountRegisterData = accountRegisterRepository.findByAccountId(accountId)
            accountRegisterData.document?.let { getName(it) } ?: GetNameError.DocumentNotFound.left()
        }.getOrElse(handleError)
    }

    fun getName(document: Document): Either<GetNameError, String> {
        val function = if (document.isValidCpf()) {
            bigDataService::getPersonName
        } else if (document.isValidCnpj()) {
            bigDataService::getCompanyName
        } else {
            return GetNameError.InvalidDocument.left()
        }

        return function(document.value).fold(
            ifLeft = handleError,
            ifRight = { it.right() },
        )
    }

    private val handleError: (e: Throwable) -> Either<GetNameError, String> = {
        when (it) {
            is DocumentNotFoundException -> GetNameError.DocumentNotFound.left()
            is DocumentBelongsToAMinorException -> GetNameError.DocumentBelongsToAMinor.left()
            else -> GetNameError.UnknownError(it).left()
        }
    }
}

sealed class GetNameError {
    data object InvalidDocument : GetNameError()
    data object DocumentNotFound : GetNameError()
    data object DocumentBelongsToAMinor : GetNameError()
    data class UnknownError(val e: Throwable) : GetNameError()
}