package ai.friday.billpayment.app.caf

import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import jakarta.inject.Singleton
import java.time.LocalDateTime
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

interface CafAdapterInterface {
    fun createTransaction(templateId: String, files: List<FileData>): Either<Exception, CreateTransactionResponse>
    fun listTransactions(): Either<Exception, ListTransactionsResponse>
    fun getTransaction(transactionId: String): Either<Exception, GetTransactionResponse>
}

interface CafServiceInterface {
    fun createTransaction(templateId: String, files: List<FileData>): Either<Exception, CreateTransactionResponse>
    fun listTransactions(): Either<Exception, ListTransactionsResponse>
    fun getTransaction(transactionId: String): Either<Exception, GetTransactionResponse>
}

@Singleton
class CafService(
    private val cafAdapter: CafAdapterInterface,
) : CafServiceInterface {
    override fun createTransaction(templateId: String, files: List<FileData>): Either<Exception, CreateTransactionResponse> {
        val markers = append("templateId", templateId)

        return cafAdapter.createTransaction(templateId, files).fold(
            ifLeft = {
                logger.error(markers, "CafService#createTransaction", it)
                it.left()
            },
            ifRight = { response ->
                logger.info(
                    markers.andAppend("transactionId", response.id),
                    "CafService#createTransaction",
                )
                response.right()
            },
        )
    }

    override fun listTransactions(): Either<Exception, ListTransactionsResponse> {
        return cafAdapter.listTransactions().fold(
            ifLeft = {
                logger.error("CafService#listTransactions", it)
                it.left()
            },
            ifRight = { response ->
                logger.info(
                    append("totalItems", response.totalItems),
                    "CafService#listTransactions",
                )
                response.right()
            },
        )
    }

    override fun getTransaction(transactionId: String): Either<Exception, GetTransactionResponse> {
        val markers = append("transactionId", transactionId)

        return cafAdapter.getTransaction(transactionId).fold(
            ifLeft = {
                logger.error(markers, "CafService#getTransaction", it)
                it.left()
            },
            ifRight = { response ->
                logger.info(
                    markers.andAppend("status", response.status),
                    "CafService#getTransaction",
                )
                response.right()
            },
        )
    }

    companion object {
        private val logger = LoggerFactory.getLogger(CafService::class.java)
    }
}

data class FileData(
    val data: String,
    val type: String,
)

data class CreateTransactionResponse(
    val requestId: String,
    val id: String?,
    val status: String?,
)

data class ListTransactionsResponse(
    val requestId: String,
    val items: List<TransactionItem>,
    val totalItems: Int,
)

data class TransactionItem(
    val id: String,
    val status: String,
    val createdAt: LocalDateTime,
    val data: TransactionData?,
)

data class TransactionData(
    val cpf: String?,
    val birthDate: String?,
    val name: String?,
)

data class GetTransactionResponse(
    val requestId: String,
    val id: String,
    val status: String,
)

enum class TransactionStatus {
    APPROVED,
    REJECTED,
    PENDING,
}

enum class FileType {
    SELFIE,
    DOCUMENT_FRONT,
    DOCUMENT_BACK,
}