package ai.friday.billpayment.app.login

import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountNotFoundException
import ai.friday.billpayment.app.account.RegistrationType
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.CrmService
import ai.friday.billpayment.app.integrations.LoginRepository
import ai.friday.billpayment.app.subscription.SubscriptionType
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import java.util.UUID

@Singleton
open class LoginService(
    private val loginRepository: LoginRepository,
    private val accountRepository: AccountRepository,
    private val accountRegisterRepository: AccountRegisterRepository,
    private val crmService: CrmService,
    @Property(name = "features.automaticUserRegister") private val automaticUserRegister: Boolean,
    @Property(name = "features.inAppSubscription", defaultValue = "0") private val inAppSubscription: Float,
) {
    open fun resolveLogin(providerUser: ProviderUser): LoginResult {
        tryToFindLogin(providerUser)?.let {
            return LoginResult(it, false)
        }

        tryToFindLoginByEmail(providerUser.emailAddress)?.let {
            return LoginResult(createLogin(providerUser, it.accountId, it.role), false)
        }

        return LoginResult(processFirstLogin(providerUser), true)
    }

    open fun remove(emailAddress: EmailAddress) {
        loginRepository.findActiveUserLoginProvider(emailAddress).forEach { providerUser ->
            loginRepository.remove(providerUser)
        }
    }

    open fun remove(accountId: AccountId) {
        loginRepository.remove(accountId)
    }

    fun removeByAccountIDAndProviderName(account: AccountId, providerName: ProviderName) {
        // username e emailAddress não são utilizados durante o fluxo de remoção
        // @see ai.friday.billpayment.adapters.dynamodb.LoginDbRepository#remove
        loginRepository.remove(
            ProviderUser(
                id = account.value,
                providerName = providerName,
                username = "",
                emailAddress = EmailAddress(""),
            ),
        )
    }

    private fun tryToFindLoginByEmail(emailAddress: EmailAddress): Login? {
        return loginRepository.findUserLogin(emailAddress)
            .ifEmpty { return null }
            .first()
    }

    private fun tryToFindLogin(providerUser: ProviderUser): Login? {
        return loginRepository.findUserLogin(providerUser)
    }

    open fun findProviderUser(emailAddress: EmailAddress, providerName: ProviderName): ProviderUser? {
        return loginRepository.findActiveUserLoginProvider(
            emailAddress = emailAddress,
        ).sortedBy {
            it.created
        }.reversed().firstOrNull() {
            it.providerName == providerName
        }
    }

    open fun createLogin(providerUser: ProviderUser, accountId: AccountId, role: Role): Login {
        loginRepository.createLogin(providerUser, accountId, role)
        return Login(accountId = accountId, emailAddress = providerUser.emailAddress, role = role)
    }

    private fun ProviderUser.buildAccountId(): AccountId {
        return when (providerName) {
            else -> AccountId("ACCOUNT-${UUID.randomUUID()}")
        }
    }

    private fun processFirstLogin(providerUser: ProviderUser): Login {
        val loginResult = if (automaticUserRegister) {
            val account = accountRepository.create(
                providerUser.username,
                providerUser.emailAddress,
                providerUser.buildAccountId(),
                RegistrationType.BASIC,
                subscriptionType = SubscriptionType.random(inAppSubscription),
            )

            loginRepository.createLogin(providerUser, account.id, account.role)
            accountRegisterRepository.create(
                accountId = account.id,
                emailAddress = providerUser.emailAddress,
                emailAddressVerified = true,
                username = providerUser.username,
                registrationType = RegistrationType.BASIC,
            )

            crmService.upsertContact(account)
            Login(account.id, providerUser.emailAddress, account.role)
        } else {
            throw AccountNotFoundException(providerUser.id)
        }

        return loginResult
    }
}

class LoginNotFoundException(message: String?) : RuntimeException(message) {
    constructor(
        accountId: AccountId,
        emailAddress: EmailAddress,
    ) : this("AccountId: ${accountId.value} - Email: ${emailAddress.value}")
}