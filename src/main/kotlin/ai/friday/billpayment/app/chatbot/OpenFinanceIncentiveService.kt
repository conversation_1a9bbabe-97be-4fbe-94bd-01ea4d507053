package ai.friday.billpayment.app.chatbot

import ai.friday.billpayment.app.FindError
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.PaymentMethodNotFound
import ai.friday.billpayment.app.account.SystemActivityService
import ai.friday.billpayment.app.account.SystemActivityType
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.OpenFinanceConsentService
import ai.friday.billpayment.app.integrations.OpenFinanceIncentiveType
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Singleton
open class OpenFinanceIncentiveService(
    private val accountService: AccountService,
    private val walletService: WalletService,
    private val chatBotNotificationService: ChatbotNotificationService,
    private val openFinanceConsentService: OpenFinanceConsentService,
    private val systemActivityService: SystemActivityService,
    private val billEventRepository: BillEventRepository,
    private val billRepository: BillRepository,
) {

    fun processCashIn(cashIn: CashInReceivedTO) {
        val logName = "OpenFinanceIncentiveService#processCashIn"

        val markers = append("accountNo", cashIn.accountNo.fullAccountNumber)
            .andAppend("routingNo", cashIn.routingNo)
            .andAppend("senderBankIspb", cashIn.senderBankIspb)

        val account = try {
            val paymentMethod = getPaymentMethod(bankNo = cashIn.bankNo, routingNo = cashIn.routingNo, accountNumber = cashIn.accountNo)
            accountService.findAccountById(paymentMethod.accountId)
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return
        }

        val userOptedOut = systemActivityService.getSystemActivityFlag(account.accountId, SystemActivityType.PromotedSweepingAccountOptOut)

        val bank = openFinanceConsentService.getParticipants().filter {
            it.restrictedTo.isNullOrEmpty()
        }.firstOrNull {
            it.ispb.toLong() == cashIn.senderBankIspb
        }

        if (bank != null) {
            chatBotNotificationService.notifyOpenFinanceIncentive(account, OpenFinanceIncentiveType.CASH_IN, userOptedOut, bank.name)
        }

        logger.info(
            markers.andAppend("accountId", account.accountId.value)
                .andAppend("mobilePhone", account.mobilePhone)
                .andAppend("userOptedOut", userOptedOut)
                .andAppend("bank", bank),
            logName,
        )
    }

    fun processDDA(billId: BillId) {
        val bill = billEventRepository.getBillById(billId).getOrElse {
            throw IllegalArgumentException("bill not found")
        }

        val billView = billRepository.findBill(bill.billId, bill.walletId)

        val wallet = walletService.findWallet(bill.walletId)

        val account = accountService.findAccountById(wallet.founder.accountId)

        val userOptedOut = systemActivityService.getSystemActivityFlag(account.accountId, SystemActivityType.PromotedSweepingAccountOptOut)

        // TODO: Filtrar banco
        // val bank = sweepingAccountService.getParticipants().firstOrNull { it.ispb?.toLong() == cashIn.senderBankIspb }

        chatBotNotificationService.notifyOpenFinanceDDAIncentive(account, wallet, userOptedOut, bill = billView)

        logger.info(
            append("billId", bill.billId.value)
                .andAppend("accountId", account.accountId.value)
                .andAppend("mobilePhone", account.mobilePhone)
                .andAppend("userOptedOut", userOptedOut),
            "OpenFinanceIncentiveService#processDDA",
        )
    }

    private fun getPaymentMethod(
        routingNo: Long,
        accountNumber: AccountNumber,
        bankNo: Long,
    ) = accountService.findPhysicalAccountPaymentMethod(bankNo, routingNo, accountNumber.fullAccountNumber)
        .getOrElse {
            when (it) {
                FindError.MultipleItemsFound -> throw IllegalStateException("Payment method list size must be 1")
                FindError.NotFound -> throw PaymentMethodNotFound(bankNo, routingNo, accountNumber)
                is FindError.ServerError -> throw it.exception
            }
        }

    companion object {
        private val logger = LoggerFactory.getLogger(OpenFinanceIncentiveService::class.java)
    }
}

data class CashInReceivedTO(
    val accountNo: AccountNumber,
    val routingNo: Long,
    val bankNo: Long,
    val senderBankIspb: Long,
)