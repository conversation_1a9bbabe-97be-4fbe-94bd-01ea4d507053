package ai.friday.billpayment.app.bill

import ai.friday.billpayment.ServerError
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.hasEarlyAccess
import ai.friday.billpayment.app.bill.schedule.CancelSchedulePaymentService
import ai.friday.billpayment.app.contact.ContactId
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.BillTrackingRepository
import ai.friday.billpayment.app.integrations.BillValidationService
import ai.friday.billpayment.app.integrations.BoletoSettlementService
import ai.friday.billpayment.app.integrations.EventPublisher
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.MailboxWalletDataRepository
import ai.friday.billpayment.app.mailbox.MailboxListType
import ai.friday.billpayment.app.payment.AmountCalculationModel
import ai.friday.billpayment.app.payment.BillChargesUtils
import ai.friday.billpayment.app.payment.BillValidationResponse
import ai.friday.billpayment.app.payment.BillValidationStatus
import ai.friday.billpayment.app.payment.BoletoSettlementException
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.app.payment.UnableToValidateReason
import ai.friday.billpayment.app.usage.WalletLimitsService
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.app.wallet.canSchedule
import ai.friday.billpayment.app.wallet.canView
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.zonedDateTimeFromMillis
import ai.friday.morning.date.dateFormat
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.Property
import jakarta.inject.Named
import jakarta.inject.Singleton
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZonedDateTime
import kotlin.jvm.optionals.getOrElse
import kotlin.math.abs
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.runBlocking
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

const val billLockProvider = "bill"
const val walletLockProviderTag = "wallet"

@Singleton
open class UpdateBillService(
    private val billEventRepository: BillEventRepository,
    private val billRepository: BillRepository,
    private val billTrackingRepository: BillTrackingRepository,
    private val eventPublisher: EventPublisher,
    private val boletoSettlementService: BoletoSettlementService,
    private val billValidationService: BillValidationService,
    private val walletLimitsService: WalletLimitsService,
    private val walletService: WalletService,
    @Named(billLockProvider) private val lockProvider: InternalLock,
    private val approveBillService: UpdateBillStateService<ApproveUpdateBillRequest>,
    private val denyBillService: UpdateBillStateService<DenyUpdateBillRequest>,
    private val resolveScheduledBill: ResolveScheduledBill,
    private val cancelSchedulePaymentService: CancelSchedulePaymentService,
    private val mailboxWalletDataRepository: MailboxWalletDataRepository,
) {
    @field:Property(name = "bill.ted.limitTime")
    lateinit var tedLimitTime: String

    fun updateBillNotPayable(bill: Bill) {
        if (bill.isActive()) {
            publishRegisterUpdatedEvent(UpdatedRegisterData.NotPayableBill, bill)
        }
    }

    fun synchronizeBill(bill: Bill, billValidationResponse: BillValidationResponse): SynchronizeBillResponse {
        val previousAmountTotal = bill.amountTotal
        val ignoreAmountPaid = billValidationResponse.billRegisterData?.paidDate?.let { paidAt ->
            val createdAt = zonedDateTimeFromMillis(bill.created).toLocalDate()
            billValidationResponse.isCreditCard() && paidAt.isBefore(createdAt)
        } ?: false

        val billValidationStatus = billValidationResponse.getStatus(ignoreAmountPaid)
        return when (billValidationStatus) {
            is BillValidationStatus.Payable -> synchronizePayableBill(bill, billValidationResponse)
            is BillValidationStatus.AlreadyPaid -> synchronizeAlreadyPaidBill(bill, billValidationResponse)
            is BillValidationStatus.NotPayable -> synchronizeNotPayableBill(bill, billValidationResponse)
            is BillValidationStatus.UnableToValidate -> synchronizeUnableToValidateBill(
                bill,
                billValidationResponse,
                billValidationStatus,
            )

            else -> SynchronizeBillResponse(
                BillSynchronizationStatus.UnableToValidate(),
                SyncErrorMessages.UnableToValidate,
            )
        }.also {
            val updatedAmountTotal = billValidationResponse.billRegisterData?.amountTotal ?: 0L

            val markers = append("synchronizeBill.isRetryable", billValidationResponse.isRetryable())
                .andAppend("billValidationResponse", it).andAppend("barcode", bill.barcode?.digitable)
                .andAppend("billValidationStatus", billValidationStatus)
                .andAppend("billId", bill.billId.value)
                .andAppend("previousAmountTotal", previousAmountTotal)
                .andAppend("updatedAmountTotal", updatedAmountTotal)
                .andAppend("valueDifference", abs(previousAmountTotal - updatedAmountTotal))
            LOG.info(markers, "DDAServiceSynchronizeBill")
        }
    }

    fun synchronizeBill(billId: BillId, billValidationResponse: BillValidationResponse): SynchronizeBillResponse {
        return billEventRepository.getBillById(billId).map {
            if (it.status == BillStatus.ACTIVE || it.status == BillStatus.WAITING_BENEFICIARY_UPDATE) {
                synchronizeBill(it, billValidationResponse)
            } else {
                SynchronizeBillResponse(status = BillSynchronizationStatus.BillIsNotActive(it.status))
            }
        }.getOrElse { SynchronizeBillResponse(status = BillSynchronizationStatus.UnableToValidate()) }
    }

    fun synchronizeBill(bill: Bill): Either<Exception, Bill> {
        with(bill) {
            return if ((isActive() || needsBeneficaryUpdate()) && billType.needValidation()) {
                val billValidationResponse: BillValidationResponse = try {
                    if (this.barcode!!.checkIsConcessionaria()) {
                        boletoSettlementService.validateBill(this)
                    } else {
                        billValidationService.validate(this)
                    }
                } catch (e: BoletoSettlementException) {
                    return Either.Left(e)
                }

                val synchronizeBillResponse = synchronizeBill(bill, billValidationResponse)
                when (synchronizeBillResponse.status) {
                    BillSynchronizationStatus.BillNotModified -> Either.Right(this)

                    is BillSynchronizationStatus.BillStatusUpdated,
                    is BillSynchronizationStatus.BillAmountTotalUpdated,
                    is BillSynchronizationStatus.BillAmountTotalUpdatedToLess,
                    is BillSynchronizationStatus.BillAmountCalculationValidUntilUpdated,
                    -> billEventRepository.getBill(
                        walletId,
                        billId,
                    )

                    is BillSynchronizationStatus.BillIsNotActive, is BillSynchronizationStatus.UnableToValidate -> Either.Left(
                        Exception(),
                    )
                }
            } else {
                Either.Right(this)
            }
        }
    }

    fun getBill(billId: BillId): Bill { // TODO precisa mesmo?
        return billEventRepository.getBillById(billId).getOrNull()!!
    }

    fun getBill(walletId: WalletId, billId: BillId): Either<Exception, Bill> {
        return billEventRepository.getBill(walletId, billId)
    }

    @Deprecated("in favor of BillEventPublisher. @see lines 689 and 698")
    fun storeEvent(bill: Bill, event: BillEvent) {
        billEventRepository.save(event)
        bill.apply(event)
        if (event is BillIgnored && event.removeFromListing) {
            billRepository.remove(bill.billId, bill.walletId)
        } else {
            billRepository.save(bill)
        }
    }

    @Deprecated("in favor of BillEventPublisher. @see lines 689 and 698")
    open fun publishEvent(bill: Bill, event: BillEvent) {
        storeEvent(bill, event)
        try {
            eventPublisher.publish(event, bill.billType)
        } catch (e: Exception) {
            LOG.error(append("event", event), "Error on EventPublisher", e)
        }
    }

    fun reactivateBill(billId: BillId, walletId: WalletId, actionSource: ActionSource): Either<Exception, Bill> {
        return billEventRepository.getBill(walletId, billId).map { bill ->
            return when (bill.status) {
                BillStatus.ACTIVE -> Either.Right(bill)
                BillStatus.IGNORED -> {
                    bill.barcode?.let {
                        billEventRepository.findLastBill(it, walletId).map { lastBill ->
                            if (lastBill.billId != bill.billId) {
                                return Either.Left(
                                    NewerBillExistsException(
                                        newerBillId = lastBill.billId,
                                        billId = bill.billId,
                                    ),
                                )
                            }
                        }
                    }
                    val event = BillReactivated(
                        billId = billId,
                        walletId = walletId,
                        actionSource = actionSource,
                    )
                    publishEvent(bill, event)
                    synchronizeBill(bill).mapLeft {
                        LOG.warn(
                            append("errorDescription", "Unable to validate bill after been reactivated")
                                .andAppend("walletId", walletId.value)
                                .andAppend("billId", billId.value),
                            "ReactivateBill",
                            it,
                        )
                        Either.Left(it)
                    }
                    Either.Right(bill)
                }

                else -> Either.Left(Exception())
            }
        }
    }

    fun reactivateBill(
        billId: BillId,
        walletId: WalletId,
        member: Member,
        actionSource: ActionSource,
    ): Either<Exception, Bill> {
        return billEventRepository.getBill(walletId, billId).map { bill ->
            if (!member.canView(bill)) {
                return MemberNotAllowedException(member.accountId, bill.billId).left()
            }
            return when (bill.status) {
                BillStatus.ACTIVE -> Either.Right(bill)
                BillStatus.IGNORED -> {
                    bill.barcode?.let {
                        billEventRepository.findLastBill(it, bill.walletId).map { lastBill ->
                            if (lastBill.billId != bill.billId) {
                                return Either.Left(
                                    NewerBillExistsException(
                                        newerBillId = lastBill.billId,
                                        billId = bill.billId,
                                    ),
                                )
                            }
                        }
                    }
                    val event =
                        BillReactivated(
                            billId = billId,
                            walletId = bill.walletId,
                            actionSource = actionSource,
                        )
                    publishEvent(bill, event)
                    synchronizeBill(bill).mapLeft {
                        LOG.warn(
                            append("errorDescription", "Unable to validate bill after been reactivated")
                                .andAppend("walletId", bill.walletId.value)
                                .andAppend("billId", billId.value),
                            "ReactivateBill",
                            it,
                        )
                        Either.Left(it)
                    }
                    Either.Right(bill)
                }

                else -> Either.Left(Exception())
            }
        }
    }

    fun updateContactAlias(contactId: ContactId, actionSource: ActionSource, alias: String) {
        runBlocking {
            billRepository.findByContactId(contactId).forEach {
                async(Dispatchers.Default) {
                    updateContactAlias(it.walletId, it.billId, actionSource, alias)
                }
            }
        }
    }

    fun updateContactAlias(
        walletId: WalletId,
        billId: BillId,
        actionSource: ActionSource,
        alias: String,
    ): Either<Exception, Bill> {
        return billEventRepository.getBill(walletId = walletId, billId = billId).map {
            val billRecipientUpdated = BillRecipientUpdated(
                billId = billId,
                walletId = walletId,
                actionSource = actionSource,
                recipient = it.recipient!!.copy(alias = alias),
            )
            publishEvent(it, billRecipientUpdated)
            it
        }
    }

    fun updateContact(
        contactId: ContactId,
        actionSource: ActionSource,
        oldBankAccount: BankAccount,
        newBankAccount: BankAccount,
    ) {
        runBlocking {
            billRepository.findByContactId(contactId).forEach {
                async(Dispatchers.Default) {
                    updateContact(it.walletId, it.billId, actionSource, oldBankAccount, newBankAccount)
                }
            }
        }
    }

    fun updateContact(
        walletId: WalletId,
        billId: BillId,
        actionSource: ActionSource,
        oldBankAccount: BankAccount,
        newBankAccount: BankAccount,
    ): Either<Exception, Bill> {
        return billEventRepository.getBill(walletId = walletId, billId = billId).map {
            if (it.status == BillStatus.ACTIVE && it.recipient?.bankAccount == oldBankAccount) {
                val billRecipientUpdated = BillRecipientUpdated(
                    billId = billId,
                    walletId = walletId,
                    actionSource = actionSource,
                    recipient = it.recipient!!.copy(bankAccount = newBankAccount),
                )
                publishEvent(it, billRecipientUpdated)
            }
            it
        }
    }

    fun updateDescription(
        billId: BillId,
        walletId: WalletId,
        description: String,
        member: Member,
        actionSource: ActionSource,
    ): Either<Exception, Bill> {
        return billEventRepository.getBill(walletId, billId).map { bill ->
            if (!member.canView(bill)) {
                return MemberNotAllowedException(member.accountId, bill.billId).left()
            }
            val event = DescriptionUpdated(
                billId = billId,
                walletId = bill.walletId,
                description = description,
                actionSource = actionSource,
            )
            publishEvent(bill, event)
            bill
        }
    }

    // NOTE: Somente usado pelo backoffice para edição de data de vencimento
    fun updateDueDate(
        billId: BillId,
        walletId: WalletId,
        dueDate: LocalDate,
    ): Either<Exception, Bill> {
        return billEventRepository.getBill(walletId, billId).map { bill ->

            val event = RegisterUpdated(
                billId = billId,
                walletId = walletId,
                updatedRegisterData = UpdatedRegisterData.NewDueDate(
                    dueDate = dueDate,
                    effectiveDueDate = calculateEffectiveDate(
                        dueDate = dueDate,
                        billType = bill.billType,
                        barcode = bill.barcode,
                    ),
                ),
            )
            publishEvent(bill, event)
            bill
        }
    }

    fun ignoreBill(
        billId: BillId,
        walletId: WalletId,
        member: Member,
        actionSource: ActionSource,
    ): Either<Exception, Bill> {
        val lock = lockProvider.acquireLock(billId.value)
            ?: return BillAlreadyLockedException(billId).left()
        try {
            return billEventRepository.getBill(walletId, billId)
                .fold(
                    ifLeft = { Either.Left(it) },
                    ifRight = { bill ->
                        if (!member.canView(bill)) {
                            return MemberNotAllowedException(member.accountId, bill.billId).left()
                        }
                        when {
                            bill.status == BillStatus.IGNORED -> {
                                Either.Right(bill)
                            }

                            bill.billType == BillType.AUTOMATIC_PIX -> {
                                // TODO: vamos precisar implementar o ignore para automatic pix fazendo uma chamada para o Arbi.
                                Either.Right(bill)
                            }

                            bill.isIgnorable() -> {
                                val event = BillIgnored(
                                    billId = billId,
                                    walletId = bill.walletId,
                                    actionSource = actionSource,
                                )
                                publishEvent(bill, event)

                                val billSource = bill.source
                                if (billSource is ActionSource.WalletMailBox) {
                                    mailboxWalletDataRepository.remove(
                                        walletId = bill.walletId,
                                        type = MailboxListType.ALLOWED,
                                        email = EmailAddress(billSource.from),
                                    )
                                }

                                Either.Right(bill)
                            }

                            else -> Either.Left(BillNotIgnorableException(bill))
                        }
                    },
                )
        } finally {
            lock.unlock()
        }
    }

    fun cancelMarkAsPaid(
        billId: BillId,
        walletId: WalletId,
        member: Member,
        actionSource: ActionSource,
    ): Either<Exception, Bill> {
        val lock = lockProvider.acquireLock(billId.value)
            ?: return BillAlreadyLockedException(billId).left()
        try {
            return billEventRepository.getBill(walletId, billId)
                .fold(
                    ifLeft = { it.left() },
                    ifRight = { bill ->
                        if (!member.canView(bill)) {
                            return MemberNotAllowedException(member.accountId, bill.billId).left()
                        }

                        if (bill.history.last() is BillMarkedAsPaidCanceled) {
                            return bill.right()
                        }

                        if (bill.status != BillStatus.ALREADY_PAID) {
                            return InvalidBillStateChangeException(
                                billId,
                                bill.status,
                            ).left()
                        }

                        if (!bill.isMarkAsPaidCancelable()) {
                            return InvalidBillStateChangeException(
                                billId,
                                bill.status,
                            ).left()
                        }

                        val event = BillMarkedAsPaidCanceled(
                            billId = bill.billId,
                            walletId = walletId,
                            actionSource = actionSource,
                        )

                        publishEvent(bill, event)

                        synchronizeBill(bill).mapLeft {
                            LOG.warn(
                                append("errorDescription", "Unable to validate bill after marked as paid is canceled")
                                    .andAppend("walletId", walletId.value)
                                    .andAppend("billId", billId.value),
                                "CancelMarkAsPaid",
                                it,
                            )
                            return it.left()
                        }
                        bill.right()
                    },
                )
        } finally {
            lock.unlock()
        }
    }

    fun markAsPaid(
        billId: BillId,
        walletId: WalletId,
        member: Member,
        amountPaid: Long?,
        actionSource: ActionSource.Api,
    ): Either<Exception, Bill> {
        val lock = lockProvider.acquireLock(billId.value)
            ?: return BillAlreadyLockedException(billId).left()
        try {
            return billEventRepository.getBill(walletId, billId)
                .fold(
                    ifLeft = { Either.Left(it) },
                    ifRight = { bill ->
                        val markers =
                            append("status", bill.status).andAppend("isPaymentSchedule", bill.isPaymentScheduled())
                                .andAppend("billId", bill.billId)

                        if (!member.canView(bill)) {
                            return MemberNotAllowedException(member.accountId, bill.billId).left()
                        }

                        if (bill.status == BillStatus.ALREADY_PAID) {
                            return bill.right()
                        }

                        if (bill.billType == BillType.INVESTMENT) {
                            return bill.right()
                        }

                        if (bill.billType == BillType.AUTOMATIC_PIX) {
                            return bill.right()
                        }

                        if (bill.status !in listOf(
                                BillStatus.ACTIVE,
                                BillStatus.NOT_PAYABLE,
                            ) || bill.isPaymentScheduled()
                        ) {
                            LOG.warn(markers, "UpdateBillService#markAsPaid")
                            return InvalidBillStateChangeException(
                                billId,
                                bill.status,
                            ).left()
                        }

                        val event = BillMarkedAsPaid(
                            billId = bill.billId,
                            walletId = walletId,
                            actionSource = actionSource,
                            amountPaid = amountPaid,
                        )

                        publishEvent(bill, event)
                        bill.right()
                    },
                )
        } finally {
            lock.unlock()
        }
    }

    fun approve(
        billId: BillId,
        walletId: WalletId,
        member: Member,
        alwaysAllowSender: Boolean,
        actionSource: ActionSource.Api,
    ): Either<ServerError, Bill> = this.approveBillService.update(
        ApproveUpdateBillRequest(
            billId = billId,
            walletId = walletId,
            member = member,
            alwaysAllowSender = alwaysAllowSender,
            actionSource = actionSource,
        ),
    )

    fun deny(
        billId: BillId,
        walletId: WalletId,
        member: Member,
        actionSource: ActionSource.Api,
    ): Either<ServerError, Bill> = this.denyBillService.update(
        DenyUpdateBillRequest(
            billId = billId,
            walletId = walletId,
            member = member,
            actionSource = actionSource,
        ),
    )

    @Deprecated("in favor of CancelSchedulePaymentService")
    fun cancelScheduledPayment(
        walletId: WalletId,
        billId: BillId,
        reason: ScheduleCanceledReason,
    ) = cancelSchedulePaymentService.cancelScheduledPayment(walletId, billId, reason)

    @Deprecated("in favor of CancelSchedulePaymentService")
    fun userCancelScheduledPayment(
        walletId: WalletId,
        billId: BillId,
        member: Member,
        actionSource: ActionSource,
    ) = cancelSchedulePaymentService.userCancelScheduledPayment(walletId, billId, member, actionSource)

    private fun synchronizePayableBill(
        bill: Bill,
        billValidationResponse: BillValidationResponse,
    ): SynchronizeBillResponse {
        if (bill.registrationUpdateNumber != null && billValidationResponse.isRegistrationResponseOlderThan(bill.registrationUpdateNumber!!)) {
            return SynchronizeBillResponse(
                BillSynchronizationStatus.UnableToValidate(),
                SyncErrorMessages.OldRegistrationUpdateNumber,
            )
        }

        if (!AccountId(bill.walletId.value).hasEarlyAccess() && billValidationResponse.isEmptyAmount()) {
            if (!bill.isCreditCard()) {
                if (bill.amountCalculationModel == AmountCalculationModel.BENEFICIARY_ONLY) {
                    publishRegisterUpdatedEvent(billValidationResponse.toNewTotalAmount(), bill)
                    return SynchronizeBillResponse(BillSynchronizationStatus.BillAmountTotalUpdated)
                }
                return SynchronizeBillResponse(
                    BillSynchronizationStatus.UnableToValidate(false),
                    SyncErrorMessages.ZeroAmount,
                )
            }

            publishRegisterUpdatedEvent(
                UpdatedRegisterData.AlreadyPaidBill(
                    amountPaid = billValidationResponse.billRegisterData!!.amountPaid,
                ),
                bill,
            )
            return SynchronizeBillResponse(
                BillSynchronizationStatus.BillStatusUpdated(BillStatus.ALREADY_PAID),
                SyncErrorMessages.ZeroAmount,
            )
        }

        if (this.hasCalculationDataChanged(bill, billValidationResponse)) {
            publishRegisterUpdatedEvent(billValidationResponse.toNewAmountCalculationData(), bill)
        }

        if (billValidationResponse.billRegisterData != null) {
            when {
                bill.amountTotal < billValidationResponse.billRegisterData!!.amountTotal -> {
                    publishRegisterUpdatedEvent(billValidationResponse.toNewTotalAmount(), bill)
                    return SynchronizeBillResponse(
                        BillSynchronizationStatus.BillAmountTotalUpdated,
                        SyncErrorMessages.DivergentAmount,
                    )
                }

                bill.amountTotal > billValidationResponse.billRegisterData!!.amountTotal -> {
                    publishRegisterUpdatedEvent(billValidationResponse.toNewTotalAmount(), bill)
                    return SynchronizeBillResponse(
                        BillSynchronizationStatus.BillAmountTotalUpdatedToLess,
                    )
                }

                bill.amountCalculationValidUntil != billValidationResponse.billRegisterData?.amountCalculationValidUntil -> {
                    publishRegisterUpdatedEvent(billValidationResponse.toNewAmountCalculationValidUntil(), bill)
                    return SynchronizeBillResponse(BillSynchronizationStatus.BillAmountCalculationValidUntilUpdated)
                }
            }
        }

        return SynchronizeBillResponse(BillSynchronizationStatus.BillNotModified)
    }

    private fun BillValidationResponse.toNewTotalAmount() = UpdatedRegisterData.NewTotalAmount(
        amount = billRegisterData!!.amount,
        abatement = billRegisterData!!.rebate ?: 0L,
        discount = billRegisterData!!.discount,
        interest = billRegisterData!!.interest,
        fine = billRegisterData!!.fine,
        amountTotal = billRegisterData!!.amountTotal,
        lastSettleDate = billRegisterData!!.settleDate!!.format(dateFormat),
        registrationUpdateNumber = billRegisterData!!.registrationUpdateNumber,
        source = gateway,
        amountCalculationValidUntil = billRegisterData!!.amountCalculationValidUntil,
    )

    private fun BillValidationResponse.toNewAmountCalculationData() = UpdatedRegisterData.NewAmountCalculationData(
        fineData = billRegisterData!!.fineData,
        interestData = billRegisterData!!.interestData,
        discountData = billRegisterData!!.discountData,
    )

    private fun BillValidationResponse.toNewAmountCalculationValidUntil() =
        UpdatedRegisterData.NewAmountCalculationValidUntil(
            amountCalculationValidUntil = billRegisterData!!.amountCalculationValidUntil,
        )

    private fun synchronizeAlreadyPaidBill(
        bill: Bill,
        billValidationResponse: BillValidationResponse,
    ): SynchronizeBillResponse {
        publishRegisterUpdatedEvent(
            UpdatedRegisterData.AlreadyPaidBill(
                amountPaid = billValidationResponse.billRegisterData?.amountPaid,
            ),
            bill,
        )

        return SynchronizeBillResponse(
            BillSynchronizationStatus.BillStatusUpdated(BillStatus.ALREADY_PAID),
            SyncErrorMessages.GenericSyncErrorMessage(billValidationResponse.errorDescription.orEmpty()),
        )
    }

    private fun synchronizeNotPayableBill(
        bill: Bill,
        billValidationResponse: BillValidationResponse,
    ): SynchronizeBillResponse {
        publishRegisterUpdatedEvent(UpdatedRegisterData.NotPayableBill, bill)
        return SynchronizeBillResponse(
            BillSynchronizationStatus.BillStatusUpdated(BillStatus.NOT_PAYABLE),
            SyncErrorMessages.GenericSyncErrorMessage(billValidationResponse.errorDescription.orEmpty()),
        )
    }

    private fun synchronizeUnableToValidateBill(
        bill: Bill,
        billValidationResponse: BillValidationResponse,
        billValidationStatus: BillValidationStatus.UnableToValidate,
    ): SynchronizeBillResponse {
        if (billValidationStatus.reason == UnableToValidateReason.BARCODE_NOT_FOUND) {
            publishRegisterUpdatedEvent(UpdatedRegisterData.NotPayableBill, bill)

            return SynchronizeBillResponse(
                BillSynchronizationStatus.BillStatusUpdated(billStatus = BillStatus.NOT_PAYABLE),
                SyncErrorMessages.GenericSyncErrorMessage("BarCodeNotFound"),
            )
        }

        return SynchronizeBillResponse(
            BillSynchronizationStatus.UnableToValidate(isRetryable = billValidationResponse.isRetryable()),
            SyncErrorMessages.UnableToValidate,
        )
    }

    private fun publishRegisterUpdatedEvent(
        updatedRegisterData: UpdatedRegisterData,
        bill: Bill,
        actionSource: ActionSource = ActionSource.System,
    ) {
        val registerUpdated = RegisterUpdated(
            billId = bill.billId,
            walletId = bill.walletId,
            updatedRegisterData = updatedRegisterData,
            actionSource = actionSource,
        )
        publishEvent(bill, registerUpdated)
        resolveScheduledBill.resolve(updatedRegisterData, bill)
    }

    private fun hasCalculationDataChanged(bill: Bill, billValidationResponse: BillValidationResponse): Boolean {
        val billTracking = billTrackingRepository.findById(bill.billId).blockOptional().getOrElse {
            return false
        }

        val data = billValidationResponse.billRegisterData ?: return false

        return billTracking.fineData != data.fineData ||
            billTracking.interestData != data.interestData ||
            billTracking.discountData != data.discountData
    }

    open fun refund(
        walletId: WalletId,
        billId: BillId,
        transactionId: TransactionId,
        type: BillType,
        amount: Long,
        originalPaidDate: ZonedDateTime,
    ) {
        billRepository.refund(
            walletId = walletId,
            billId = billId,
            transactionId = transactionId,
            type = type,
            amount = amount,
            originalPaidDate = originalPaidDate,
        )
    }

    enum class UnscheduleValidation { FAIL, ALLOW }

    fun updateAmount(
        billId: BillId,
        amount: Long,
        actionSource: ActionSource.GoalInvestment,
    ): Either<Exception, Bill> {
        return doUpdateAmount(billId, amount, actionSource, UnscheduleValidation.ALLOW, actionSource.accountId)
    }

    fun updateAmount(
        billId: BillId,
        amount: Long,
        actionSource: ActionSource.Api,
        unscheduleValidation: UnscheduleValidation = UnscheduleValidation.FAIL,
    ): Either<Exception, Bill> {
        return doUpdateAmount(billId, amount, actionSource, unscheduleValidation, actionSource.accountId)
    }

    private fun doUpdateAmount(
        billId: BillId,
        amount: Long,
        actionSource: ActionSource.WalletActionSource,
        unscheduleValidation: UnscheduleValidation,
        accountId: AccountId,
    ): Either<Exception, Bill> {
        val lock = lockProvider.acquireLock(billId.value) ?: return BillAlreadyLockedException(billId).left()
        try {
            val bill = billEventRepository.getBillById(billId).getOrElse { return it.left() }
            val wallet = walletService.findWallet(bill.walletId)

            val member = wallet.getActiveMemberOrNull(accountId) ?: return MemberNotAllowedException(
                accountId,
                billId,
            ).left()

            if (!member.canView(bill)) {
                return MemberNotAllowedException(member.accountId, bill.billId).left()
            }

            val shouldUnscheduleBill =
                bill.isPaymentScheduled() && !member.canSchedule(actionSource = bill.source, visibleBy = bill.visibleBy)

            if (shouldUnscheduleBill && unscheduleValidation == UnscheduleValidation.FAIL) {
                return MemberNotAllowedException(member.accountId, bill.billId).left()
            }

            if (!bill.isAmountEditable()) {
                return BillAmountIsNotEditable(billId = billId).left()
            }

            if (!bill.canUpdateValueTo(amount)) {
                return BillValidationException("this Bill cant be updated to informed amount").left()
            }

            val checkTransactionLimits = bill.expectedPaymentDate() == getLocalDate()
            if (checkTransactionLimits && bill.billType != BillType.INVESTMENT) {
                if (walletLimitsService.getDailyLimit(walletId = bill.walletId) < amount) {
                    return BillValidationException("Bill amount is above the limit").left()
                }

                val deltaAmount = if (bill.isPaymentScheduled()) {
                    amount - bill.amountTotal
                } else {
                    amount
                }

                if (walletLimitsService.getAvailableLimit(
                        walletId = bill.walletId,
                        countScheduledBankTransfer = true,
                        date = bill.expectedPaymentDate(),
                    ) < deltaAmount
                ) {
                    return BillValidationException("Bill amount is above the available limit").left()
                }
            }

            if (shouldUnscheduleBill && unscheduleValidation == UnscheduleValidation.ALLOW) {
                val scheduleCancelEvent = BillPaymentScheduleCanceled(
                    billId = bill.billId,
                    walletId = bill.walletId,
                    actionSource = actionSource,
                    reason = ScheduleCanceledReason.USER_REQUEST,
                    batchSchedulingId = bill.schedule!!.batchSchedulingId,
                )
                publishEvent(bill, scheduleCancelEvent)
            }

            publishEvent(
                bill = bill,
                event = AmountUpdated(
                    billId = bill.billId,
                    walletId = wallet.id,
                    actionSource = actionSource,
                    amount = amount,
                ),
            )
            return bill.right()
        } finally {
            lock.unlock()
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(UpdateBillService::class.java)
    }
}

fun calculateScheduledDate(originalScheduleDate: LocalDate, limitDateTime: LocalTime): LocalDate { // FIXME mover
    val now = getZonedDateTime()
    val dateToCalculate = if (originalScheduleDate == now.toLocalDate() && now.toLocalTime().isAfter(limitDateTime)) {
        originalScheduleDate.plusDays(1)
    } else {
        originalScheduleDate
    }

    return BillChargesUtils.calculateClosestWorkingDay(dateToCalculate)
}