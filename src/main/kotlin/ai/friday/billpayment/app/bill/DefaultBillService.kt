package ai.friday.billpayment.app.bill

import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.BillService
import ai.friday.billpayment.app.integrations.MailBoxService
import ai.friday.billpayment.app.wallet.WalletId
import arrow.core.getOrElse
import io.micronaut.cache.annotation.CacheConfig
import io.micronaut.cache.annotation.Cacheable
import jakarta.inject.Singleton
import java.time.LocalDate
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

const val descriptionMaxLength = 140

@Singleton
@CacheConfig("update-trackable-bill")
open class DefaultFindBillServiceCache {

    @Cacheable(parameters = ["billId", "processingDate"])
    open fun storeBillId(billId: BillId, processingDate: LocalDate, token: String): String {
        return token
    }
}

@Singleton
class DefaultBillService(
    private val repository: BillRepository,
    private val mailBoxService: MailBoxService,
    private val accountRepository: AccountRepository,
) : BillService {

    override fun addBillsFromBackoffice(
        userEmail: EmailAddress,
        from: EmailAddress,
        subject: String,
        bills: List<EmailBill>,
    ): List<Pair<BarCode, MailboxAddBillError?>> {
        return bills.map { bill ->
            val barCode = BarCode.ofDigitable(bill.digitable)
            val isConcessionaria = barCode.checkIsConcessionaria()

            val document = userEmail.recipient.split(".").last()

            val account = try {
                accountRepository.findAccountByDocument(document)
            } catch (e: Exception) {
                return@map Pair(barCode, MailboxAddBillError.MailboxAccountNotFoundError())
            }

            val billAdded = mailBoxService.addBill(
                walletId = account.defaultWalletId(),
                barCode = barCode,
                dueDate = if (isConcessionaria) LocalDate.parse(bill.dueDate, DateTimeFormatter.ISO_DATE) else null,
                from = from,
                subject = subject,
                shouldNotifyOnRetryableError = false,
            ).map { null }.getOrElse { it }

            Pair(barCode, billAdded)
        }
    }

    override fun markBillComingDueMessageSent(billId: BillId, walletId: WalletId) {
        repository.setBillComingDueMessageSent(billId, walletId, true)
    }

    override fun sumAmountUntilEachDate(
        walletId: WalletId,
        dates: List<LocalDate>,
        filter: ((BillView) -> Boolean)?,
    ): List<Long> {
        fun BillView.mapTuple() = List(dates.size) { index ->
            getAmountIfBefore(dates[index])
        }

        val bills = if (filter == null) {
            repository.findByWalletAndStatus(walletId, BillStatus.ACTIVE)
        } else {
            repository.findByWalletAndStatus(walletId, BillStatus.ACTIVE).filter { filter.invoke(it) }
        }

        logger.info(append("bills", bills.map { it.billId }), "SumAmmountUntilEachDate")

        return bills.map {
            it.mapTuple()
        }.fold(
            List(dates.size) {
                0L
            },
        ) { aggregate, current -> aggregate plus current }
    }

    override fun getPaidBills(walletId: WalletId, startDate: ZonedDateTime, endDate: ZonedDateTime, types: List<BillType>?): List<BillView> {
        return repository.getPaidBills(walletId, startDate, endDate, types)
    }

    private val logger = LoggerFactory.getLogger(javaClass)
}

private fun BillView.getAmountIfBefore(localDate: LocalDate) =
    if (this isExpectedToBePaidBefore localDate) amountTotal else 0

private infix fun BillView.isExpectedToBePaidBefore(localDate: LocalDate) =
    expectedPaymentDate.isBefore(localDate)

private infix fun List<Long>.plus(other: List<Long>) = List(this.size) {
    this[it] + other[it]
}

data class EmailBill(
    val digitable: String,
    val dueDate: String?,
)