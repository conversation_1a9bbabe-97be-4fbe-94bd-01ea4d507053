package ai.friday.billpayment.app.bill

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.duplication.PossibleDuplicateBillService
import ai.friday.billpayment.app.bill.tracking.BillTrackingCalculateHandlerService
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.FindBillsCriteria
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.canView
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import datadog.trace.api.Trace
import jakarta.inject.Singleton

interface ListBillsToTimeline {
    fun findBills(
        accountId: AccountId,
        currentWalletId: WalletId,
        criteria: FindBillsCriteria = FindBillsCriteria(),
    ): List<BillViewWrapper>
}

@Singleton
class DefaultListBillsToTimeline(
    private val billRepository: BillRepository,
    private val walletRepository: WalletRepository,
    private val possibleDuplicateBillService: PossibleDuplicateBillService,
    private val billTrackingService: BillTrackingCalculateHandlerService,
    private val updateAmountBill: UpdateAmountBill,
) : ListBillsToTimeline {

    private val limitDateToFilterSubscriptions = 32L

    @Trace
    override fun findBills(accountId: AccountId, currentWalletId: WalletId, criteria: FindBillsCriteria): List<BillViewWrapper> {
        val wallet = walletRepository.findWallet(currentWalletId)
        val member = wallet.getActiveMember(accountId)

        val walletBills = billRepository.findByWallet(wallet.id, criteria)
            .filter { it.isVisible() }

        val possibleDuplicateBills =
            runCatching { possibleDuplicateBillService.check(walletBills) }.getOrElse { mapOf() }

        return walletBills
            .filter { member.canView(it) }
            .map { billView ->
                updateAmountBill.updateAmountIfExpired(billView)
            }
            .map { billView ->
                BillViewWrapper(
                    bill = billView,
                    possibleDuplicateBills = possibleDuplicateBills[billView]?.toList() ?: listOf(),
                    trackingOutdated = billTrackingService.isBillOutdated(billView),
                    participants = null,
                )
            }
    }

    private fun BillView.isVisible(): Boolean {
        return if (status == BillStatus.MOVED) {
            false
        } else {
            !subscriptionFee || dueDate.isBefore(getLocalDate().plusDays(limitDateToFilterSubscriptions))
        }
    }
}