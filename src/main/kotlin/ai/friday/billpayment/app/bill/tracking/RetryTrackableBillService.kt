package ai.friday.billpayment.app.bill.tracking

import ai.friday.billpayment.Err
import ai.friday.billpayment.ServerError
import ai.friday.billpayment.app.bill.BillEvent
import ai.friday.billpayment.app.integrations.BillTrackingRepository
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import jakarta.inject.Singleton

interface RetryTrackableBillService {
    fun retry(event: BillEvent): Either<Err, Unit>
}

@Singleton
class DefaultRetryTrackableBillService(private val repository: BillTrackingRepository) : RetryTrackableBillService {
    override fun retry(event: BillEvent): Either<Err, Unit> {
        return try {
            val trackable = repository.findById(event.billId).block() ?: return Unit.right()

            repository.update(trackable, getLocalDate().plusDays(1))

            Unit.right()
        } catch (ex: Exception) {
            ServerError(ex).left()
        }
    }
}