package ai.friday.billpayment.app.bill

import ai.friday.billpayment.app.account.isClosed
import ai.friday.billpayment.app.account.isLegalPerson
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.BillComingDueRepository
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.FindBillService
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import io.reactivex.Flowable
import jakarta.inject.Singleton
import java.time.LocalDate
import java.time.LocalTime

@Singleton
class DefaultFindBillService(
    private val accountRepository: AccountRepository,
    private val walletService: WalletService,
    private val repository: BillRepository,
    private val billsComingDueRepository: BillComingDueRepository,
) : FindBillService {

    override fun findActiveAndWaitingApprovalBills(walletId: WalletId): List<BillView> {
        return repository.findByWalletAndStatus(walletId, BillStatus.ACTIVE) + repository.findByWalletAndStatus(walletId, BillStatus.WAITING_APPROVAL)
    }

    override fun find(walletId: WalletId, billId: BillId): BillView {
        return repository.findBill(billId = billId, walletId = walletId)
    }

    override fun findByWalletId(walletId: WalletId): List<BillView> {
        return repository.findByWallet(walletId)
    }

    override fun findOverdueBills(walletId: WalletId): List<BillView> {
        return repository.findOverdueBills(walletId = walletId)
    }

    override fun findBillsWaitingFunds(walletId: WalletId): List<BillView> {
        return repository.findBillsWaitingFunds(walletId = walletId)
    }

    override fun findAllWallets(filterAccountsThatReceiveNotification: Boolean, filterActiveAccounts: Boolean): List<Wallet> {
        return accountRepository.findAllAccountsActivatedSince(LocalDate.MIN, filterAccountsThatReceiveNotification)
            .filter { account ->
                !filterActiveAccounts || !account.isClosed()
            }
            .flatMap { account ->
                walletService.findAllFounderWallets(account.accountId)
            }
    }

    override fun findBillsComingDue(walletId: WalletId): List<BillView> {
        return repository.findBillsComingDue(walletId = walletId)
    }

    override fun findBillsByDueDateAsync(dueDate: LocalDate): Flowable<BillComingDue> {
        return billsComingDueRepository.findByEffectiveDueDateAsync(dueDate)
    }

    override fun findAllWalletsThatReceiveNotification(): List<Wallet> {
        return accountRepository.findAllAccountsActivatedSince(LocalDate.MIN)
            .filter { account ->
                account.isLegalPerson() || account.configuration.receiveNotification
            }
            .filter { !it.isClosed() }
            .flatMap { account ->
                walletService.findAllFounderWallets(account.accountId)
            }
    }

    override fun findOpenBills(dueDate: LocalDate, paymentLimitTime: LocalTime): Flowable<BillComingDue> {
        return billsComingDueRepository.findOpenBills(dueDate, paymentLimitTime)
    }
}