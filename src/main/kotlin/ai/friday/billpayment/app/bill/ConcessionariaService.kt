package ai.friday.billpayment.app.bill

import ai.friday.billpayment.FileUtils
import ai.friday.billpayment.adapters.parsers.parseListFrom
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.BoletoSettlementService
import ai.friday.billpayment.app.payment.BillValidationResponse
import ai.friday.billpayment.app.payment.BillValidationStatus
import ai.friday.billpayment.app.utilityaccount.Utility
import ai.friday.billpayment.app.wallet.canView
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getEpochMilli
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.timeFormat
import ai.friday.morning.log.andAppend
import jakarta.inject.Singleton
import java.time.LocalDate
import java.time.LocalTime
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

interface ConcessionariaServiceInterface {
    fun createConcessionaria(
        request: ConcessionariaRequestWithDueDate,
        preemptValidation: BillValidationResponse? = null,
        dryRun: Boolean = false,
    ): CreateBillResult

    fun handlePaid(request: PaidConcessionariaRequest): Result<BillId>
}

@Singleton
open class ConcessionariaService(
    private val boletoSettlementService: BoletoSettlementService,
    private val billEventRepository: BillEventRepository,
    private val billRepository: BillRepository,
    private val updateBillService: UpdateBillService,
    private val resolveScheduledBill: ResolveScheduledBill,
) : ConcessionariaServiceInterface {
    override fun createConcessionaria(
        request: ConcessionariaRequestWithDueDate,
        preemptValidation: BillValidationResponse?,
        dryRun: Boolean,
    ): CreateBillResult {
        val utility = request.barcode.utility()
        val effectiveDueDate =
            calculateEffectiveDate(
                dueDate = request.dueDate,
                billType = BillType.CONCESSIONARIA,
                barcode = request.barcode,
            )
        val markers =
            Markers.append("request", request)
                .andAppend("preemptValidation", preemptValidation)
                .andAppend("segmentAndCovenantCode", request.barcode.segmentAndCovenantCode())
                .andAppend("utility", utility)

        billEventRepository.findLastBill(request.barcode, request.walletId).map { duplicatedBill ->
            markers.andAppend("existingBillId", duplicatedBill.billId.value)
            val result = calculateBillDuplicatedResult(duplicatedBill, request, dryRun)
            LOG.info(markers.andAppend("result", result.javaClass.simpleName), "createConcessionaria")
            return result
        }

        request.externalBillId?.let { externalBillId ->
            billEventRepository.getBillByExternalId(externalBillId)
                .map {
                    val result = calculateBillDuplicatedResult(it, request, dryRun)
                    LOG.info(markers.andAppend("result", result.javaClass.simpleName), "createConcessionaria")
                    return result
                }
        }

        val validation =
            preemptValidation ?: boletoSettlementService.validateBill(request)
        markers.andAppend("validationStatus", validation.getStatus().javaClass.simpleName)

        findDuplicatedBillByCustomerCode(utility, validation.billRegisterData?.amountTotal, request, effectiveDueDate)?.let { duplicatedBill ->
            markers.andAppend("existingBillId", duplicatedBill.billId.value)
            val result = calculateBillDuplicatedResult(duplicatedBill, request, dryRun)
            LOG.info(markers.andAppend("result", result.javaClass.simpleName), "createConcessionaria")
            return result
        }

        return when (validation.getStatus()) {
            BillValidationStatus.Payable -> {
                val billAdded = buildBillAdded(validation, request, effectiveDueDate, validation.billRegisterData!!.amountTotal, request.dueDate)
                if (!dryRun) {
                    updateBillService.publishEvent(Bill.build(), billAdded)
                }
                CreateBillResult.SUCCESS(Bill.build(billAdded))
            }

            BillValidationStatus.NotPayable -> {
                if (request is ForcedConcessionariaRequest) {
                    publishNotPayableBill(validation, request, effectiveDueDate, dryRun)
                }

                CreateBillResult.FAILURE.BillNotPayable(
                    description = validation.errorDescription.orEmpty(),
                    billRegisterData = validation.billRegisterData,
                )
            }

            BillValidationStatus.PaymentNotAuthorized -> {
                if (request is ForcedConcessionariaRequest) {
                    publishNotPayableBill(validation, request, effectiveDueDate, dryRun)
                }

                CreateBillResult.FAILURE.BillNotPayable(
                    code = AddBillError.PAYMENT_NOT_AUTHORIZED.code,
                    description = AddBillError.PAYMENT_NOT_AUTHORIZED.description,
                    billRegisterData = validation.billRegisterData,
                )
            }

            BillValidationStatus.AlreadyPaid -> {
                validation.billRegisterData?.let {
                    CreateBillResult.FAILURE.AlreadyPaid.WithData(
                        description = validation.errorDescription.orEmpty(),
                        billRegisterData = it,
                    )
                } ?: CreateBillResult.FAILURE.AlreadyPaid.WithoutData(
                    description = validation.errorDescription.orEmpty(),
                    barCode = request.barcode,
                    dueDate = request.dueDate,
                )
            }

            is BillValidationStatus.UnableToValidate ->
                CreateBillResult.FAILURE.ServerError(
                    BillValidationException(validation.errorDescription.orEmpty()),
                )
        }.also {
            LOG.info(markers.andAppend("result", it.javaClass.simpleName), "createConcessionaria")
        }
    }

    override fun handlePaid(request: PaidConcessionariaRequest): Result<BillId> = kotlin.runCatching {
        billEventRepository.findLastBill(request.barcode, request.walletId)
            .map { duplicatedBill ->
                if (!duplicatedBill.isActive()) {
                    return Result.success(duplicatedBill.billId)
                }

                val registerUpdated = RegisterUpdated(
                    billId = duplicatedBill.billId,
                    created = getEpochMilli(),
                    walletId = duplicatedBill.walletId,
                    actionSource = request.source,
                    updatedRegisterData = UpdatedRegisterData.AlreadyPaidBill(request.amount),
                )

                updateBillService.publishEvent(duplicatedBill, registerUpdated)

                resolveScheduledBill.resolve(duplicatedBill.apply(registerUpdated), ScheduleCanceledReason.BILL_ALREADY_PAID)

                return Result.success(duplicatedBill.billId)
            }

        if (request.dueDate == null) {
            return Result.failure(IllegalArgumentException("dueDate is required"))
        }

        val effectiveDueDate =
            calculateEffectiveDate(
                dueDate = request.dueDate,
                billType = BillType.CONCESSIONARIA,
                barcode = request.barcode,
            )

        val validation = boletoSettlementService.validateBill(request)

        val billAdded = buildBillAdded(
            validation,
            request,
            effectiveDueDate,
            request.amount,
            request.dueDate,
        )

        val registerUpdated = RegisterUpdated(
            billId = billAdded.billId,
            created = billAdded.created + 1,
            walletId = billAdded.walletId,
            actionSource = request.source,
            updatedRegisterData = UpdatedRegisterData.AlreadyPaidBill(request.amount),
        )

        updateBillService.publishEvent(Bill.build(), billAdded)

        updateBillService.publishEvent(Bill.build(billAdded), registerUpdated)

        return Result.success(billAdded.billId)
    }

    private fun publishNotPayableBill(
        validation: BillValidationResponse,
        request: ForcedConcessionariaRequest,
        effectiveDueDate: LocalDate,
        dryRun: Boolean,
    ) {
        val billAdded = buildBillAdded(validation, request, effectiveDueDate, request.amount, request.dueDate)

        val registerUpdated = RegisterUpdated(
            billId = billAdded.billId,
            created = billAdded.created + 1,
            walletId = billAdded.walletId,
            actionSource = request.source,
            updatedRegisterData = UpdatedRegisterData.NotPayableBill,
        )

        if (!dryRun) {
            updateBillService.publishEvent(Bill.build(), billAdded)
            updateBillService.publishEvent(Bill.build(billAdded), registerUpdated)
        }
    }

    private fun buildBillAdded(
        validation: BillValidationResponse,
        request: ConcessionariaRequest,
        effectiveDueDate: LocalDate,
        amountTotal: Long,
        dueDate: LocalDate,
    ): BillAdded {
        val billAdded =
            with(validation) {
                BillAdded(
                    billId = request.id,
                    created = request.createdOn.toInstant().toEpochMilli(),
                    walletId = request.walletId,
                    description = request.description,
                    dueDate = dueDate,
                    amount = amountTotal,
                    billType = BillType.CONCESSIONARIA,
                    amountTotal = amountTotal,
                    discount = billRegisterData?.discount ?: 0,
                    fine = billRegisterData?.fine ?: 0,
                    interest = billRegisterData?.interest ?: 0,
                    barcode = request.barcode,
                    assignor = getAssignor(request),
                    document = billRegisterData?.payerDocument,
                    paymentLimitTime = getLimitTime(request),
                    lastSettleDate = billRegisterData?.settleDate?.format(dateFormat),
                    actionSource = request.source,
                    effectiveDueDate = effectiveDueDate,
                    securityValidationResult = request.securityValidationErrors.map { it.reason },
                    requestedDueDate = request.dueDate,
                    externalId = request.externalBillId,
                )
            }
        return billAdded
    }

    private fun BillValidationResponse.getAssignor(request: ConcessionariaRequest): String {
        billRegisterData?.assignor
            ?.let { return it }
            ?: request.takeIf { it is ForcedConcessionariaRequest }
                ?.let { return request.description }
            ?: request.takeIf { it is PaidConcessionariaRequest }
                ?.let { return request.description } ?: throw IllegalStateException("Assignor not found")
    }

    private fun BillValidationResponse.getLimitTime(request: ConcessionariaRequest) =
        billRegisterData?.paymentLimitTime?.let { LocalTime.parse(it, timeFormat) }
            ?: convenios[request.barcode.segmentAndCovenantCode()]?.horarioCorte
            ?: LocalTime.of(16, 0)

    private fun calculateBillDuplicatedResult(
        existingBill: Bill,
        request: ConcessionariaRequestWithDueDate,
        dryRun: Boolean,
    ): CreateBillResult {
        return if (request.member.canView(existingBill)) {
            if (existingBill.status == BillStatus.IGNORED) {
                val bill = reactivateConcessionaria(existingBill, request, dryRun)
                CreateBillResult.SUCCESS(bill)
            } else {
                CreateBillResult.FAILURE.BillAlreadyExists(existingBill)
            }
        } else {
            val billPermissionUpdated = request.toBillPermissionUpdate(existingBill.billId)

            if (dryRun) {
                existingBill.apply(billPermissionUpdated)
            } else {
                updateBillService.publishEvent(existingBill, billPermissionUpdated)
            }

            if (existingBill.status == BillStatus.IGNORED) {
                val bill = reactivateConcessionaria(existingBill, request, dryRun)
                CreateBillResult.SUCCESS(bill)
            } else {
                CreateBillResult.SUCCESS(
                    bill = existingBill,
                    warningCode = WarningCode.NOT_VISIBLE_BILL_ALREADY_EXISTS,
                )
            }
        }
    }

    private fun findDuplicatedBillByCustomerCode(
        utility: Utility,
        amountTotal: Long?,
        request: ConcessionariaRequest,
        effectiveDueDate: LocalDate,
    ): Bill? {
        val clientNumber = utility.extractClientNumberFrom(request.barcode)
        if (clientNumber != null) {
            val foundBill =
                billRepository.findByWalletAndEffectiveDueDate(request.walletId, effectiveDueDate).firstOrNull { billView ->
                    val foundUtility = billView.barCode?.utility()
                    utility == foundUtility &&
                        clientNumber == foundUtility.extractClientNumberFrom(billView.barCode) && amountTotal == billView.amountTotal
                }

            if (foundBill != null) {
                return billEventRepository.getBill(request.walletId, foundBill.billId).getOrNull()
            }
        }
        return null
    }

    private fun CreateBoletoRequest.toBillPermissionUpdate(billId: BillId): BillPermissionUpdated {
        return BillPermissionUpdated(
            billId = billId,
            created = createdOn.toInstant().toEpochMilli(),
            walletId = walletId,
            actionSource = source,
            permissionUpdated = PermissionUpdated.VisibilityAdded(toSourceAccountId()),
        )
    }

    private fun reactivateConcessionaria(
        bill: Bill,
        request: ConcessionariaRequestWithDueDate,
        dryRun: Boolean,
    ): Bill {
        val billReactivated =
            BillReactivated(
                billId = bill.billId,
                walletId = bill.walletId,
                actionSource = request.source,
            )
        val registerUpdated =
            RegisterUpdated(
                billId = bill.billId,
                created = billReactivated.created + 1, // FIXME deveria ter um jeito melhor de garantir a não colisão de scankey
                walletId = bill.walletId,
                actionSource = request.source,
                updatedRegisterData =
                UpdatedRegisterData.NewDueDate(
                    dueDate = request.dueDate,
                    effectiveDueDate =
                    calculateEffectiveDate(
                        dueDate = request.dueDate,
                        billType = BillType.CONCESSIONARIA,
                        barcode = request.barcode,
                    ),
                ),
            )
        if (dryRun) {
            bill.apply(billReactivated)
            if (bill.dueDate != request.dueDate) {
                bill.apply(registerUpdated)
            }
        } else {
            updateBillService.publishEvent(bill, billReactivated)
            if (bill.dueDate != request.dueDate) {
                updateBillService.publishEvent(bill, registerUpdated)
            }
        }
        return bill
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(ConcessionariaService::class.java)
    }
}

private val convenios by lazy {
    parseListFrom<Convenio>(FileUtils.readLocalFile("concessionaria/convenios.json"))
        .associateBy { "${it.codigoSegmento.substring(1, 2)}${it.codigo.padStart(4, '0')}" }
}

private data class Convenio(
    val nome: String,
    val codigo: String,
    val segmento: String,
    val codigoSegmento: String,
    val horarioCorte: LocalTime,
)