package ai.friday.billpayment.app.bill.tracking

import ai.friday.billpayment.Err
import ai.friday.billpayment.ServerError
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.TrackableBill
import ai.friday.billpayment.app.integrations.BillTrackingRepository
import ai.friday.billpayment.app.payment.DiscountData
import ai.friday.billpayment.app.payment.FineData
import ai.friday.billpayment.app.payment.InterestData
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import jakarta.inject.Singleton
import java.time.LocalDate

interface UpdateTrackableBillService {
    fun updateDueDateByBillID(id: BillId, date: LocalDate): Either<Err, Unit>
    fun updateAmountByBillID(id: BillId, amount: Long): Either<Err, Unit>
    fun updatePaymentCalculationDataByBillID(id: BillId, fineData: FineData?, interestData: InterestData?, discountData: DiscountData?): Either<Err, Unit>
}

@Singleton
class DefaultUpdateTrackableBillService(
    private val repository: BillTrackingRepository,
) : UpdateTrackableBillService {
    override fun updateDueDateByBillID(id: BillId, date: LocalDate): Either<Err, Unit> = this.update(id) {
        return@update it.copy(dueDate = date)
    }

    override fun updateAmountByBillID(id: BillId, amount: Long): Either<Err, Unit> = this.update(id) {
        return@update it.copy(amount = amount)
    }

    override fun updatePaymentCalculationDataByBillID(id: BillId, fineData: FineData?, interestData: InterestData?, discountData: DiscountData?): Either<Err, Unit> = this.update(id) {
        return@update it.copy(
            interestData = interestData ?: it.interestData,
            fineData = fineData ?: it.fineData,
            discountData = discountData ?: it.discountData,
        )
    }

    private fun update(id: BillId, fn: (TrackableBill) -> TrackableBill): Either<Err, Unit> {
        return try {
            val trackable = repository.findById(id).block() ?: return UntrackableEntity.left()

            val result = fn(trackable)

            repository.update(result, getLocalDate().plusDays(1))

            Unit.right()
        } catch (ex: Exception) {
            ServerError(ex).left()
        }
    }
}