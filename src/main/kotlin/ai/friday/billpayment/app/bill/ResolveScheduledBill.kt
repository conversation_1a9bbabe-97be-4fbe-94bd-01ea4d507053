package ai.friday.billpayment.app.bill

import ai.friday.billpayment.app.integrations.BillEventPublisher
import jakarta.inject.Singleton

@Singleton
open class ResolveScheduledBill(
    private val billEventPublisher: BillEventPublisher,
) {
    fun resolve(updatedRegisterData: UpdatedRegisterData, bill: Bill) {
        if (updatedRegisterData is UpdatedRegisterData.NotPayableBill || updatedRegisterData is UpdatedRegisterData.AlreadyPaidBill) {
            resolve(
                bill = bill,
                reason = if (updatedRegisterData is UpdatedRegisterData.NotPayableBill) ScheduleCanceledReason.BILL_NOT_PAYABLE else ScheduleCanceledReason.BILL_ALREADY_PAID,
            )
        }
    }

    fun resolve(bill: Bill, reason: ScheduleCanceledReason) {
        if (bill.isPaymentScheduled()) {
            val scheduleCancelEvent = BillPaymentScheduleCanceled(
                billId = bill.billId,
                walletId = bill.walletId,
                actionSource = ActionSource.System,
                reason = reason,
                batchSchedulingId = bill.schedule!!.batchSchedulingId,
            )
            billEventPublisher.publish(bill, scheduleCancelEvent)
        }
    }
}