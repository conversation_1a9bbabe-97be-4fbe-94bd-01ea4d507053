package ai.friday.billpayment.app.bill.schedule

import ai.friday.billpayment.app.AsyncUtils.callAsync
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.PaymentMethodNotFound
import ai.friday.billpayment.app.account.PaymentMethodType
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BatchSchedulingId
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillAlreadyLockedException
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillPaymentScheduled
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.InvalidBillStateChangeException
import ai.friday.billpayment.app.bill.InvalidScheduleBillType
import ai.friday.billpayment.app.bill.InvalidSchedulePaymentAmount
import ai.friday.billpayment.app.bill.InvalidSchedulePaymentMethod
import ai.friday.billpayment.app.bill.InvalidSchedulePaymentMethodDate
import ai.friday.billpayment.app.bill.MemberNotAllowedException
import ai.friday.billpayment.app.bill.billLockProvider
import ai.friday.billpayment.app.bill.checkCanBePaidWith
import ai.friday.billpayment.app.bill.schedule.security.ScheduleBillSecurityRequest
import ai.friday.billpayment.app.bill.schedule.security.ScheduleBillSecurityService
import ai.friday.billpayment.app.integrations.BatchScheduledPublisher
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.BillInstrumentationService
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.ScheduledBillPaymentService
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.payment.BillChargesUtils
import ai.friday.billpayment.app.payment.PaymentMethodsDetail
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithBalance
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithCreditCard
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithExternalPayment
import ai.friday.billpayment.app.payment.PaymentSchedulingService
import ai.friday.billpayment.app.payment.ScheduleStrategy
import ai.friday.billpayment.app.payment.ScheduleTo
import ai.friday.billpayment.app.payment.ScheduledBill
import ai.friday.billpayment.app.payment.pinCode.PinCode
import ai.friday.billpayment.app.payment.pinCode.PinCodeService
import ai.friday.billpayment.app.security.Fingerprint
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.log
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.Property
import jakarta.inject.Named
import jakarta.inject.Provider
import jakarta.inject.Singleton
import java.time.LocalDate
import java.time.LocalTime
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Suppress("UNUSED_EXPRESSION")
@Singleton
open class ScheduleBillService(
    private val billEventRepository: BillEventRepository,
    private val accountService: AccountService,
    private val paymentSchedulingService: PaymentSchedulingService,
    private val scheduledBillPaymentServiceProvider: Provider<ScheduledBillPaymentService>,
    private val walletRepository: WalletRepository,
    private val billInstrumentationService: BillInstrumentationService,
    private val billEventPublisher: BillEventPublisher,
    private val scheduleBillSecurityService: ScheduleBillSecurityService,
    private val batchSchedulePublisher: BatchScheduledPublisher,
    private val pinCodeService: PinCodeService,
    @Named(billLockProvider) private val lockProvider: InternalLock,
    @Property(name = "features.batchScheduleNotification.enabled") private val isBatchScheduleNotificationEnabled: Boolean,
) {
    @field:Property(name = "bill.ted.limitTime")
    lateinit var tedLimitTime: String

    open fun processScheduleAsync(walletId: WalletId) {
        callAsync {
            scheduledBillPaymentServiceProvider.get().process(walletId)
        }
    }

    fun schedulePayments(
        paymentWallet: Wallet,
        billIdsWithMethods: List<Pair<BillId, PaymentMethodsDetail?>>,
        accountId: AccountId,
        actionSource: ActionSource,
        scheduleStrategy: ScheduleStrategy,
        fingerprint: Fingerprint? = null,
        pinCode: PinCode? = null,
    ): ScheduleResult {
        val schedulingId = BatchSchedulingId()

        val pinCodeResult = pinCodeService.validate(accountId, pinCode).getOrNull()

        if (pinCodeResult != null && pinCodeResult.maxAttemptsReached) {
            return ScheduleResult.PinCodeMaxAttemptsReached
        }

        if (pinCodeResult == null || !pinCodeResult.valid) {
            return ScheduleResult.InvalidPinCode
        }

        val billsWithStatus = billIdsWithMethods.map { (billId, detail) ->
            val markers = log("billId" to billId.value, "walletId" to paymentWallet.id.value)
            billEventRepository.getBillById(billId).map { bill ->
                val billWallet = walletRepository.findWallet(bill.walletId)
                val member = billWallet.activeMembers.first { it.accountId == accountId }

                val scheduleResult = schedulePayment(
                    batchSchedulingId = schedulingId,
                    paymentWallet = paymentWallet,
                    bill = bill,
                    member = member,
                    actionSource = actionSource,
                    scheduleStrategy = scheduleStrategy,
                    paymentMethodsDetail = detail ?: PaymentMethodsDetailWithBalance(
                        amount = bill.amountTotal,
                        paymentMethodId = paymentWallet.paymentMethodId,
                        calculationId = null,
                    ),
                    fingerprint = fingerprint,
                )

                scheduleResult
                    .map { scheduledBill -> billId to scheduledBill }
                    .getOrElse { LOG.warn(markers, "SchedulePayment", it); billId to null }
            }.getOrElse { LOG.warn(markers, "SchedulePayment", it); billId to null }
        }

        val scheduledBills = billsWithStatus.filter { (_, scheduledBill) -> scheduledBill != null }

        if (scheduledBills.isNotEmpty()) {
            processScheduleAsync(paymentWallet.id)
            billInstrumentationService.scheduled(
                // TODO - isso deveria estar no controller para marcar que o usuário agendou varias contas ao mesmo tempo?
                wallet = paymentWallet,
                accountId = accountId,
                scheduledBills.size,
            )
        }

        if (isBatchScheduleNotificationEnabled) {
            batchSchedulePublisher.publish(schedulingId, scheduledBills, scheduleStrategy)
        }

        return ScheduleResult.Success(billsWithStatus.map { (billId, scheduledBill) -> billId to (scheduledBill != null) })
    }

    fun schedulePayment(
        paymentWallet: Wallet,
        billId: BillId,
        member: Member,
        actionSource: ActionSource,
        scheduleStrategy: ScheduleStrategy,
        paymentMethodsDetail: PaymentMethodsDetail,
        fingerprint: Fingerprint? = null,
    ): Either<Exception, Bill> {
        val result = billEventRepository.getBillById(billId)
        val bill = result.getOrElse { return ItemNotFoundException(billId).left() }
        return schedulePayment(
            paymentWallet = paymentWallet,
            bill = bill,
            member = member,
            actionSource = actionSource,
            scheduleStrategy = scheduleStrategy,
            paymentMethodsDetail = paymentMethodsDetail,
            fingerprint = fingerprint,
        )
    }

    fun schedulePayment(
        batchSchedulingId: BatchSchedulingId = BatchSchedulingId(),
        paymentWallet: Wallet,
        bill: Bill,
        member: Member,
        actionSource: ActionSource,
        scheduleStrategy: ScheduleStrategy,
        paymentMethodsDetail: PaymentMethodsDetail = PaymentMethodsDetailWithBalance(
            amount = bill.amountTotal,
            paymentMethodId = paymentWallet.paymentMethodId,
            calculationId = null,
        ),
        fingerprint: Fingerprint? = Fingerprint(),
    ): Either<Exception, Bill> {
        val lock = lockProvider.acquireLock(bill.billId.value)
            ?: return BillAlreadyLockedException(bill.billId).left()
        try {
            scheduleBillSecurityService.canSchedule(ScheduleBillSecurityRequest(bill, member, paymentMethodsDetail))
                .getOrElse {
                    LOG.error(
                        append("billId", bill.billId.value)
                            .andAppend("accountId", member.accountId.value)
                            .andAppend("walletId", paymentWallet.id.value),
                        "schedulePaymentNotAllowed",
                    )
                    return MemberNotAllowedException(member.accountId, bill.billId).left()
                }

            if (bill.status != BillStatus.ACTIVE) {
                return InvalidBillStateChangeException(bill.billId, bill.status).left()
            }

            try {
                paymentMethodsDetail.retrieveAllPaymentMethodsDetail()
                    .forEach { detail ->
                        val (expectedType, paymentMethodOwner) = when (detail) {
                            is PaymentMethodsDetailWithBalance -> PaymentMethodType.BALANCE to paymentWallet.founder
                            is PaymentMethodsDetailWithExternalPayment -> PaymentMethodType.EXTERNAL to paymentWallet.founder
                            is PaymentMethodsDetailWithCreditCard -> PaymentMethodType.CREDIT_CARD to member
                        }

                        detail.retrievePaymentMethodIds().map { accountPaymentMethodId ->
                            val accountPaymentMethod = accountService.findAccountPaymentMethodByIdAndAccountId(
                                accountPaymentMethodId,
                                paymentMethodOwner.accountId,
                            )

                            if (accountPaymentMethod.method.type != expectedType) {
                                throw PaymentMethodNotFound(accountPaymentMethodId, paymentMethodOwner.accountId)
                            }

                            if (!bill.checkCanBePaidWith(accountPaymentMethod.method.type, paymentMethodOwner)) {
                                return InvalidSchedulePaymentMethod(accountPaymentMethod.method.type).left()
                            }
                        }
                    }
            } catch (e: PaymentMethodNotFound) {
                LOG.error(
                    append("paymentMethodId", paymentWallet.paymentMethodId.value)
                        .and<LogstashMarker>(append("accountId", paymentWallet.founder.accountId.value))
                        .and(append("walletId", paymentWallet.id.value)),
                    "schedulePaymentMethodNotFound",
                    e,
                )
                return e.left()
            }

            if (paymentMethodsDetail.internalSettlement()) {
                val scheduledNetAmountTotal = paymentMethodsDetail.netAmountTotal()
                if (scheduledNetAmountTotal != bill.amountTotal) {
                    return InvalidSchedulePaymentAmount(
                        billAmount = bill.amountTotal,
                        scheduledNetAmount = scheduledNetAmountTotal,
                    ).left()
                }
            }

            val currentScheduleDate = scheduleStrategy.calculateScheduleDate(bill.effectiveDueDate)
            val scheduleDate = if (paymentMethodsDetail.internalSettlement()) {
                when (bill.billType) {
                    BillType.CONCESSIONARIA, BillType.FICHA_COMPENSACAO, BillType.INVESTMENT -> calculateScheduledDate(
                        currentScheduleDate,
                        bill.paymentLimitTime,
                    )

                    BillType.INVOICE -> calculateScheduledDate(currentScheduleDate, LocalTime.parse(tedLimitTime))
                    BillType.PIX -> currentScheduleDate
                    BillType.AUTOMATIC_PIX -> return InvalidScheduleBillType(bill.billType).left()
                    BillType.OTHERS -> currentScheduleDate
                }
            } else {
                currentScheduleDate
            }

            if (bill.isPaymentScheduled() && bill.schedule?.date == scheduleDate) {
                return bill.right()
            }

            if (paymentMethodsDetail.mustSettlePaymentImmediately() && scheduleDate.isAfter(getZonedDateTime().toLocalDate())) {
                return InvalidSchedulePaymentMethodDate(scheduleDate).left()
            }

            val event = BillPaymentScheduled(
                batchSchedulingId = batchSchedulingId,
                billId = bill.billId,
                walletId = bill.walletId,
                actionSource = actionSource,
                scheduledDate = scheduleDate,
                amount = bill.amountTotal,
                paymentLimitTime = bill.paymentLimitTime,
                infoData = paymentMethodsDetail.toBillPaymentScheduledInfo(),
                fingerprint = fingerprint,
                paymentWalletId = paymentWallet.id,
            )
            billEventPublisher.publish(bill, event)

            if (paymentMethodsDetail.internalSettlement()) {
                paymentSchedulingService.schedule(
                    bill.toScheduleBill(
                        paymentWalletId = paymentWallet.id,
                        paymentWalletFounder = paymentWallet.founder,
                        scheduleDate = scheduleDate,
                        scheduleTo = scheduleStrategy.schedulePriority,
                        paymentMethodsDetail = paymentMethodsDetail,
                    ),
                )
            }

            return if (bill.isPaymentScheduled()) {
                bill.right()
            } else {
                IllegalStateException("bill should be scheduled").left()
            }
        } catch (e: Exception) {
            return e.left()
        } finally {
            lock.unlock()
        }
    }

    fun getOrderedScheduledBills(walletId: WalletId, scheduledDate: LocalDate): List<ScheduledBill> {
        return paymentSchedulingService.getOrderedScheduledBills(walletId, scheduledDate)
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(ScheduleBillService::class.java)
    }
}

fun calculateScheduledDate(originalScheduleDate: LocalDate, limitDateTime: LocalTime): LocalDate { // FIXME mover
    val now = getZonedDateTime()
    val dateToCalculate = if (originalScheduleDate == now.toLocalDate() && now.toLocalTime().isAfter(limitDateTime)) {
        originalScheduleDate.plusDays(1)
    } else {
        originalScheduleDate
    }

    return BillChargesUtils.calculateClosestWorkingDay(dateToCalculate)
}

fun Bill.toScheduleBill(
    paymentWalletId: WalletId,
    paymentWalletFounder: Member,
    scheduleDate: LocalDate,
    scheduleTo: ScheduleTo,
    paymentMethodsDetail: PaymentMethodsDetail,
): ScheduledBill {
    return ScheduledBill(
        walletId = paymentWalletId,
        billId = billId,
        scheduledDate = scheduleDate,
        billType = billType,
        amount = amountTotal,
        scheduleTo = scheduleTo,
        paymentLimitTime = paymentLimitTime,
        expires = !subscriptionFee,
        paymentMethodsDetail = paymentMethodsDetail,
        batchSchedulingId = schedule?.batchSchedulingId,
        isSelfTransfer = checkSelfTransfer(paymentWalletFounder),
    )
}

sealed class ScheduleResult {
    data class Success(val billIds: List<Pair<BillId, Boolean>>) : ScheduleResult()
    object InvalidPinCode : ScheduleResult()
    object PinCodeMaxAttemptsReached : ScheduleResult()
}