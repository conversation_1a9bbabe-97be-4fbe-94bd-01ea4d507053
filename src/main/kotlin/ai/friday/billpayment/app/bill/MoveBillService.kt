package ai.friday.billpayment.app.bill

import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.dda.DDABill
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.BillRecurrenceRepository
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.DDARepository
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithExternalPayment
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.app.wallet.canView
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import jakarta.inject.Named
import jakarta.inject.Singleton
import java.time.Instant
import java.util.UUID
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

sealed class MoveAllBillsResult : PrintableSealedClassV2() {
    data class Finished(
        val originWalletId: WalletId,
        val destinationWalletId: WalletId,
        val successes: List<Pair<BillId, BillId>>,
        val errors: List<BillId>,
    ) : MoveAllBillsResult()

    data class IncompatibleWalletOwnersError(
        val originDocument: String,
        val destinationDocument: String,
    ) : MoveAllBillsResult()

    data class UnknownError(val ex: Exception) : MoveAllBillsResult()
}

@Singleton
class MoveBillService(
    private val billEventRepository: BillEventRepository,
    private val ddaRepository: DDARepository,
    private val recurrenceRepository: BillRecurrenceRepository,
    private val updateBillService: UpdateBillService,
    private val billRepository: BillRepository,
    private val accountRepository: AccountRepository,
    private val walletService: WalletService,
    @Named(billLockProvider) private val lockProvider: InternalLock,
) {

    fun moveBills(
        accountId: AccountId,
        originWalletId: WalletId,
        destinationWalletId: WalletId,
        billIds: List<BillId>,
    ): Either<MoveBillsError, List<MoveBillResult>> {
        if (billIds.size > billIds.toSet().size) {
            return MoveBillsError.InvalidBillList.left()
        }

        val originWalletMember = findMemberOnWallet(originWalletId, accountId).getOrElse { return it.left() }
        findMemberOnWallet(destinationWalletId, accountId).mapLeft { return it.left() }

        return billIds.map { billId ->
            moveBill(billId, originWalletId, destinationWalletId, originWalletMember)
        }.right()
    }

    fun moveAllActiveBills(
        originWalletId: WalletId,
        destinationWalletId: WalletId,
    ): MoveAllBillsResult {
        val (originWallet, destinationWallet) = try {
            Pair(
                walletService.findWallet(walletId = originWalletId),
                walletService.findWallet(walletId = destinationWalletId),
            )
        } catch (ex: Exception) {
            return MoveAllBillsResult.UnknownError(ex)
        }

        if (originWallet.founder.document != destinationWallet.founder.document) {
            return MoveAllBillsResult.IncompatibleWalletOwnersError(
                originWallet.founder.document,
                destinationWallet.founder.document,
            )
        }

        return moveBills(
            originWallet = originWallet,
            destinationWallet = destinationWallet,
            bills = billRepository.findByWalletAndStatus(originWalletId, BillStatus.ACTIVE),
        )
    }

    fun moveAllBills(
        originWalletId: WalletId,
        destinationWalletId: WalletId,
    ): MoveAllBillsResult {
        val (originWallet, destinationWallet) = try {
            Pair(
                walletService.findWallet(walletId = originWalletId),
                walletService.findWallet(walletId = destinationWalletId),
            )
        } catch (ex: Exception) {
            return MoveAllBillsResult.UnknownError(ex)
        }

        if (originWallet.founder.document != destinationWallet.founder.document) {
            return MoveAllBillsResult.IncompatibleWalletOwnersError(
                originWallet.founder.document,
                destinationWallet.founder.document,
            )
        }

        return moveBills(
            originWallet = originWallet,
            destinationWallet = destinationWallet,
            bills = billRepository.findByWallet(originWalletId),
        )
    }

    private fun moveBills(
        originWallet: Wallet,
        destinationWallet: Wallet,
        bills: List<BillView>,
    ): MoveAllBillsResult.Finished {
        val originWalletId = originWallet.id
        val destinationWalletId = destinationWallet.id

        val account = accountRepository.findById(originWallet.founder.accountId)
        val importedBills = mutableListOf<Pair<BillId, BillId>>()
        val notImportedBills = mutableListOf<BillId>()

        bills.forEach { billView ->
            val billId = billView.billId
            val lock = lockProvider.acquireLock(billId.value) ?: kotlin.run { return@forEach }
            try {
                val bill =
                    billEventRepository.getBill(originWalletId, billId).getOrNull() ?: throw IllegalStateException()

                if (bill.status == BillStatus.MOVED) return@forEach

                val billAdded = bill.history.single { it is BillAdded || it is FichaCompensacaoAdded }
                val actionSource =
                    if (billAdded.actionSource is ActionSource.DirectDebit) billAdded.actionSource else null

                val newBillAdded =
                    buildNewBillAdded(billAdded, bill, destinationWalletId, destinationWallet.founder, actionSource)

                updateBillService.publishEvent(Bill.build(), newBillAdded)

                val billMoved = buildBillMoved(billAdded, bill, originWalletId, originWallet.founder)
                updateBillService.publishEvent(bill, billMoved)

                if (billAdded.actionSource is ActionSource.DDA) {
                    updateDDABill(billEvent = newBillAdded, bill.status)
                }

                // code smell alert
                val events: List<BillEvent> =
                    bill.history.filterNot {
                        it is BillAdded || it is FichaCompensacaoAdded || it is BillMoved
                    }.map { event ->
                        event::class.java.declaredFields.let { fields ->
                            fields.firstOrNull { it.name == "walletId" }?.let { f ->
                                f.isAccessible = true
                                f.set(event, destinationWalletId)
                            }

                            fields.firstOrNull { it.name == "billId" }?.let { f ->
                                f.isAccessible = true
                                f.set(event, newBillAdded.billId)
                            }

                            fields.firstOrNull { it.name == "created" }?.let { f ->
                                f.isAccessible = true
                                f.set(event, getZonedDateTime().toInstant().toEpochMilli())
                            }
                        }
                        event
                    }

                var newBill = Bill.build(newBillAdded)
                events.forEach {
                    newBill = newBill.apply(it)
                    updateBillService.publishEvent(newBill, it)
                }

                if (newBillAdded.actionSource is ActionSource.DirectDebit && bill.schedule != null &&
                    bill.schedule!!.date.isAfter(getLocalDate())
                ) {
                    billEventRepository.getBill(destinationWalletId, newBillAdded.billId)
                        .getOrElse { throw ItemNotFoundException(billId) }.also { debit ->
                            BillPaymentScheduled(
                                billId = debit.billId,
                                walletId = debit.walletId,
                                actionSource = debit.source,
                                scheduledDate = debit.dueDate,
                                amount = debit.amount,
                                paymentLimitTime = debit.paymentLimitTime,
                                infoData = PaymentMethodsDetailWithExternalPayment(account.configuration.externalId!!.providerName).toBillPaymentScheduledInfo(),
                                batchSchedulingId = bill.schedule!!.batchSchedulingId,
                            ).also { updateBillService.publishEvent(debit, it) }
                        }
                }

                importedBills.add(billId to newBillAdded.billId)
            } catch (ex: Exception) {
                notImportedBills.add(billId)
            } finally {
                lock.unlock()
            }
        }

        return MoveAllBillsResult.Finished(
            originWalletId = originWalletId,
            destinationWalletId = destinationWalletId,
            successes = importedBills,
            errors = notImportedBills,
        )
    }

    private fun moveBill(
        billId: BillId,
        originWalletId: WalletId,
        destinationWalletId: WalletId,
        member: Member,
    ): MoveBillResult {
        val lock = lockProvider.acquireLock(billId.value)
        if (lock == null) {
            LOG.warn(
                Markers.append("billId", billId.value)
                    .and(Markers.append("result", MoveBillErrorReason.BILL_ALREADY_LOCKED)),
                "MoveBill",
            )
            return MoveBillResult(billId, false, MoveBillErrorReason.BILL_ALREADY_LOCKED)
        }
        try {
            return tryToMoveBill(
                billId = billId,
                originWalletId = originWalletId,
                destinationWalletId = destinationWalletId,
                member = member,
            )
        } finally {
            lock.unlock()
        }
    }

    private fun tryToMoveBill(
        billId: BillId,
        originWalletId: WalletId,
        destinationWalletId: WalletId,
        member: Member,
    ): MoveBillResult {
        return billEventRepository.getBill(walletId = originWalletId, billId = billId).map { bill ->
            when {
                !member.canView(bill) -> MoveBillResult(billId, false, MoveBillErrorReason.USER_NOT_ALLOWED)
                !bill.isActive() || bill.isPaymentScheduled() -> MoveBillResult(
                    billId,
                    false,
                    MoveBillErrorReason.INVALID_STATUS,
                )
                bill.billType == BillType.INVESTMENT -> MoveBillResult(
                    billId,
                    false,
                    MoveBillErrorReason.INVESTMENT_CANT_BE_MOVED,
                )

                else -> doMoveBill(bill, originWalletId, destinationWalletId, member)
            }
        }.getOrElse {
            when (it) {
                is ItemNotFoundException -> MoveBillResult(billId, false, MoveBillErrorReason.BILL_NOT_FOUND)
                else -> {
                    LOG.error(
                        Markers.append("billId", billId.value)
                            .and(Markers.append("result", MoveBillErrorReason.SERVER_ERROR)),
                        "MoveBill",
                        it,
                    )
                    MoveBillResult(billId, false, MoveBillErrorReason.SERVER_ERROR)
                }
            }
        }
    }

    private fun doMoveBill(
        bill: Bill,
        originWalletId: WalletId,
        destinationWalletId: WalletId,
        member: Member,
    ): MoveBillResult {
        if (bill.billType.isBoleto()) {
            billEventRepository.findLastBill(bill.barcode!!, destinationWalletId, bill.dueDate).map { lastBill ->

                if (lastBill.status == BillStatus.IGNORED) {
                    billEventRepository.deleteUniqueConstraint(bill.barcode!!, destinationWalletId, bill.dueDate)

                    lastBill.removeFromTimeline()
                } else {
                    val destinationWalletMember = findMemberOnWallet(destinationWalletId, member.accountId)
                        .getOrElse { return MoveBillResult(bill.billId, false, MoveBillErrorReason.USER_NOT_ALLOWED) }

                    if (!destinationWalletMember.canView(lastBill)) {
                        val billPermissionUpdated = BillPermissionUpdated(
                            billId = lastBill.billId,
                            walletId = destinationWalletId,
                            actionSource = ActionSource.System,
                            permissionUpdated = PermissionUpdated.VisibilityAdded(member.accountId),
                        )
                        updateBillService.publishEvent(lastBill, billPermissionUpdated)
                    }
                    return MoveBillResult(bill.billId, false, MoveBillErrorReason.BILL_ALREADY_EXISTS)
                }
            }
        }
        val billAdded = bill.history.single { it is BillAdded || it is FichaCompensacaoAdded }

        val newBillAdded = buildNewBillAdded(billAdded, bill, destinationWalletId, member)
        updateBillService.publishEvent(Bill.build(), newBillAdded)

        val billMoved = buildBillMoved(billAdded, bill, originWalletId, member)
        updateBillService.publishEvent(bill, billMoved)

        if (billAdded.actionSource is ActionSource.DDA) {
            updateDDABill(billEvent = newBillAdded, bill.status)
        }

        resolveRecurrence(billAdded)

        return MoveBillResult(bill.billId, true, null)
    }

    private fun Bill.removeFromTimeline() {
        val billIgnored = BillIgnored(
            billId = billId,
            walletId = walletId,
            actionSource = ActionSource.System,
            removeFromListing = true,
            removeFromRecurrence = false,
        )
        updateBillService.publishEvent(this, billIgnored)
    }

    private fun resolveRecurrence(billAdded: BillEvent) {
        val source = billAdded.actionSource
        if (source is ActionSource.WalletRecurrence) {
            val recurrence = recurrenceRepository.find(source.recurrenceId, billAdded.walletId)
            recurrenceRepository.save(recurrence.ignoreBillId(billAdded.billId))
        }
    }

    private fun findMemberOnWallet(walletId: WalletId, accountId: AccountId): Either<MoveBillsError, Member> {
        val wallet = walletService.findWalletOrNull(walletId)
            ?: return MoveBillsError.WalletNotFound(walletId).left()

        if (!wallet.hasActiveMember(accountId)) {
            return MoveBillsError.UserNotAllowed.left()
        }
        return wallet.getActiveMember(accountId).right()
    }

    private fun buildNewBillAdded(
        billEvent: BillEvent,
        bill: Bill,
        destinationWalletId: WalletId,
        member: Member,
        actionSource: ActionSource? = null,
    ): BillEvent {
        return when (billEvent) {
            is BillAdded -> billEvent.copy(
                walletId = destinationWalletId,
                billId = BillId("BILL-${UUID.randomUUID()}"),
                created = Instant.now().toEpochMilli(),
                description = bill.description,
                amount = bill.amount,
                amountTotal = bill.amountTotal,
                recipient = bill.recipient,
                actionSource = actionSource ?: ActionSource.Api(member.accountId, billEvent.billId),
                recurrenceRule = null,
            )

            is FichaCompensacaoAdded -> billEvent.copy(
                walletId = destinationWalletId,
                billId = BillId("BILL-${UUID.randomUUID()}"),
                created = Instant.now().toEpochMilli(),
                description = bill.description,
                amount = bill.amount,
                amountTotal = bill.amountTotal,
                fine = bill.fine,
                interest = bill.interest,
                discount = bill.discount,
                recipient = bill.recipient!!,
                actionSource = ActionSource.Api(member.accountId, billEvent.billId),
            )

            else -> throw IllegalStateException()
        }
    }

    private fun buildBillMoved(
        billEvent: BillEvent,
        bill: Bill,
        destinationWalletId: WalletId,
        member: Member,
    ): BillMoved {
        val (barCode, dueDate, idNumber) = when (billEvent) {
            is BillAdded -> Triple(billEvent.barcode, billEvent.dueDate, null)
            is FichaCompensacaoAdded -> Triple(billEvent.barcode, billEvent.dueDate, billEvent.idNumber)
            else -> Triple(null, null, null)
        }

        return BillMoved(
            billId = bill.billId,
            walletId = bill.walletId,
            destinationWalletId = destinationWalletId,
            actionSource = ActionSource.Api(member.accountId),
            barCode = barCode,
            dueDate = dueDate,
            idNumber = idNumber,
        )
    }

    private fun updateDDABill(billEvent: BillEvent, billStatus: BillStatus) {
        val ddaBill = when (billEvent) {
            is BillAdded -> DDABill(
                barcode = billEvent.barcode!!,
                document = billEvent.document!!,
                dueDate = billEvent.dueDate,
                walletId = billEvent.walletId,
                billId = billEvent.billId,
                lastStatus = BillStatus.ACTIVE,
            )

            is FichaCompensacaoAdded -> DDABill(
                barcode = billEvent.barcode,
                document = billEvent.document,
                dueDate = billEvent.dueDate,
                walletId = billEvent.walletId,
                billId = billEvent.billId,
                lastStatus = billStatus,
            )

            else -> throw IllegalStateException()
        }

        ddaRepository.save(ddaBill)
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(MoveBillService::class.java)
    }
}

enum class MoveBillErrorReason {
    INVALID_STATUS, USER_NOT_ALLOWED, BILL_ALREADY_EXISTS, BILL_NOT_FOUND, BILL_ALREADY_LOCKED, SERVER_ERROR, INVESTMENT_CANT_BE_MOVED
}

data class MoveBillResult(val billId: BillId, val moved: Boolean, val reason: MoveBillErrorReason?)

sealed class MoveBillsError {
    data object InvalidBillList : MoveBillsError()
    class WalletNotFound(val walletId: WalletId) : MoveBillsError()
    data object UserNotAllowed : MoveBillsError()
}