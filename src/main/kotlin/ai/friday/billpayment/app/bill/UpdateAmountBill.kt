package ai.friday.billpayment.app.bill

import ai.friday.billpayment.app.bill.tracking.BillTrackingCalculateHandlerService
import ai.friday.billpayment.app.bill.tracking.BillTrackingCalculateResult
import ai.friday.billpayment.app.getOrFalse
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.FeatureConfiguration
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import arrow.core.getOrElse
import jakarta.inject.Singleton
import java.time.LocalDate
import java.util.*
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
class UpdateAmountBill(
    private val billRepository: BillRepository,
    private val billTrackingService: BillTrackingCalculateHandlerService,
    private val featureConfiguration: FeatureConfiguration,
    private val defaultFindBillServiceCache: DefaultFindBillServiceCache,
    private val billEventRepository: BillEventRepository,
) {
    private val logger = LoggerFactory.getLogger(UpdateAmountBill::class.java)

    fun updateAmountIfExpired(billView: BillView): BillView {
        if (!featureConfiguration.updateAmountAfterPaymentWindow) return billView

        val processingDate = billView.processingDate()

        if (!billView.shouldUpdateAmountFor(processingDate)) return billView

        val markers = Markers.append("billId", billView.billId)
            .andAppend("walletId", billView.walletId.value)

        val currentToken = UUID.randomUUID().toString()

        val cachedToken = defaultFindBillServiceCache.storeBillId(billView.billId, processingDate, currentToken)

        markers.andAppend("currentToken", currentToken)
            .andAppend("cachedToken", cachedToken)

        if (currentToken != cachedToken) {
            logger.warn(markers, "BillView#updateAmountCacheHit")
            return billView
        }

        val trackableBill = billTrackingService.findById(billView.billId).block() ?: return billView

        val calculateResult = billTrackingService.execute(
            track = trackableBill,
            origin = BillTrackingCalculateOptions.CALCULATE,
            processingDate = processingDate,
        ).getOrElse {
            logger.warn(markers.andAppend("result", it), "BillView#updateAmountIfExpired")
            return billView
        }

        // codigo temporario para corrigir conta que so tem FichaCompensacaoAdded com amountCalculationValidUntil com valor errado
        if (calculateResult == BillTrackingCalculateResult.NOT_MODIFIED) {
            billEventRepository.getBillById(trackableBill.billId).map {
                logger.warn(markers, "BillView#rebuildBillView")
                billRepository.save(it)
            }.getOrElse {
                logger.error(markers, "BillView#rebuildBillView", it)
            }
        }

        return billRepository.findBill(billView.billId, billView.walletId).also { updatedBillView ->
            markers.andAppend("amountCalculationValidUntil", updatedBillView.amountCalculationValidUntil)
                .andAppend("amountTotal", updatedBillView.amountTotal)
                .andAppend("calculateResult", calculateResult)
                .andAppend("status", billView.status)
            logger.info(markers, "BillView#updateAmountIfExpired")
        }
    }

    private fun BillView.processingDate(): LocalDate {
        val limit = paymentLimitTime
        val now = getZonedDateTime()
        return if (now.toLocalTime() > limit) {
            now.toLocalDate().plusDays(1)
        } else {
            now.toLocalDate()
        }
    }

    private fun BillView.shouldUpdateAmountFor(processingDate: LocalDate): Boolean {
        return this.status in listOf(
            BillStatus.ACTIVE,
            BillStatus.WAITING_APPROVAL,
            BillStatus.WAITING_BENEFICIARY_UPDATE,
        ) && amountCalculationOption == BillTrackingCalculateOptions.CALCULATE && amountCalculationValidUntil?.isBefore(processingDate).getOrFalse()
    }
}