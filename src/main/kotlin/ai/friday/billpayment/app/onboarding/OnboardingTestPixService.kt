package ai.friday.billpayment.app.onboarding

import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountNotFoundException
import ai.friday.billpayment.app.account.AdService
import ai.friday.billpayment.app.account.SystemActivityService
import ai.friday.billpayment.app.account.SystemActivityService.OnboardingTestPixSystemActivityValue
import ai.friday.billpayment.app.balance.BalanceService
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillIgnored
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillTag.ONBOARDING_TEST_PIX
import ai.friday.billpayment.app.bill.BillTagAdded
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.bill.CreateBillResult
import ai.friday.billpayment.app.bill.CreateBillService
import ai.friday.billpayment.app.bill.CreatePixRequest
import ai.friday.billpayment.app.bill.RecipientRequest
import ai.friday.billpayment.app.bill.RegisterUpdated
import ai.friday.billpayment.app.bill.UpdatedRegisterData
import ai.friday.billpayment.app.bill.and
import ai.friday.billpayment.app.chatbot.ChatbotNotificationService
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.BankAccountService
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.OnboardingTestPixRepository
import ai.friday.billpayment.app.integrations.PixKeyManagement
import ai.friday.billpayment.app.integrations.TestPixReminderType
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.journey.UserJourneyService
import ai.friday.billpayment.app.onboarding.instrumentation.OnboardingInstrumentationService
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.log.andAppend
import arrow.core.Either
import arrow.core.flatMap
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.ConfigurationProperties
import jakarta.inject.Singleton
import java.time.LocalDate
import java.time.Period
import java.time.ZonedDateTime
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

const val TRI_PIX_DURATION_IN_DAYS = 7

@Singleton
class OnboardingTestPixService(
    private val pixKeyManagement: PixKeyManagement,
    private val accountRepository: AccountRepository,
    private val createBillService: CreateBillService,
    private val bankAccountService: BankAccountService,
    private val walletRepository: WalletRepository,
    private val configuration: OnboardingTestPixConfiguration,
    private val systemActivityService: SystemActivityService,
    private val billEventPublisher: BillEventPublisher,
    private val userJourneyService: UserJourneyService,
    private val onboardingTestPixRepository: OnboardingTestPixRepository,
    private val billEventRepository: BillEventRepository,
    private val notificationAdapter: NotificationAdapter,
    private val adService: AdService,
    private val balanceService: BalanceService,
    private val billRepository: BillRepository,
    private val chatBotNotificationService: ChatbotNotificationService,
    private val onboardingInstrumentationService: OnboardingInstrumentationService,
) {
    private val logger = LoggerFactory.getLogger(OnboardingTestPixService::class.java)

    fun createChatbotExamplePayment(accountId: AccountId, pixKey: PixKey? = null): Either<CreateOnboardingTestPixError, BillView> {
        val logName = "OnboardingTestPixService#createChatbotExamplePayment"
        val marker = Markers.append("accountId", accountId)

        return findAccountOrBill(accountId)
            .flatMap { account ->
                if (pixKey == null) {
                    account.withValidPixKey()
                } else {
                    pixKeyManagement.findKeyDetails(pixKey, account.document).fold(
                        ifLeft = { CreateOnboardingTestPixError.NoPixKeyFound.left() },
                        ifRight = {
                            if (it.pixKeyDetails.owner.document == account.document) {
                                Pair(account, pixKey).right()
                            } else {
                                CreateOnboardingTestPixError.PixKeyFromDifferentOwner.left()
                            }
                        },
                    )
                }
            }.flatMap { (account, pixKey) ->
                marker.andAppend("pixKey", pixKey)
                transferFunds(
                    account = account,
                    pixKey = pixKey,
                    marker = marker,
                    amount = 1,
                )
            }.flatMap { (account, pixKey) ->
                createPix(
                    account = account,
                    pixKey = pixKey,
                    marker = marker,
                    dates = listOf(getLocalDate()),
                )
            }.map { billsCreated ->
                marker.andAppend("totalPixCreated", billsCreated.size)
                systemActivityService.setOnboardingTestPixCreated(accountId)
                adService.publishOnboardingTestPixCreated(accountId)
                onboardingInstrumentationService.singlePixCreated(accountId)
                onboardingTestPixRepository.save(
                    onboardingTestPix =
                    OnboardingTestPix(
                        accountId = accountId,
                        createdAt = getZonedDateTime(),
                        updatedAt = getZonedDateTime(),
                        billIds = billsCreated.map { it.billId },
                        status = OnboardingTestPixStatus.IN_PROGRESS,
                        billPaidCounter = 0,
                    ),
                )
                logger.info(marker, logName)

                val bill = billsCreated.single()
                return billRepository.findBill(bill.billId, bill.walletId).right()
            }.getOrElse { error ->
                marker.andAppend("error", error)
                if (error !is CreateOnboardingTestPixError.PixAlreadyCreated) {
                    systemActivityService.setOnboardingTestPixError(accountId, error)
                }
                logger.error(marker, logName)
                error.left()
            }
    }

    fun refundFriday(
        wallet: Wallet,
        amount: Long,
    ): Either<RefundOnboardingTestPixError, Unit> {
        fun findAccountToRefund(accountId: AccountId): Either<RefundOnboardingTestPixError, Account> {
            if (systemActivityService.getOnboardingTestPix(accountId) == null) {
                return RefundOnboardingTestPixError.CONFLICT.left()
            }

            val account = accountRepository.findByIdOrNull(accountId)

            return if (account == null || !account.hasGroup(AccountGroup.ONBOARDING_TEST_PIX)) {
                RefundOnboardingTestPixError.CONFLICT.left()
            } else {
                account.right()
            }
        }

        val logName = "OnboardingTestPixService#refundFriday"
        val marker = Markers.append("accountId", wallet.founder.accountId.value)
        return findAccountToRefund(
            accountId = wallet.founder.accountId,
        ).flatMap {
            if (transferRefundFriday(wallet = wallet, amount = amount, marker = marker)) {
                it.right()
            } else {
                RefundOnboardingTestPixError.FUNDS_NOT_TRANSFERRED.left()
            }
        }.map {
            logger.info(marker, logName)
            Unit.right()
        }.getOrElse { error ->
            marker.andAppend("error", error)
            logger.error(marker, logName)
            error.left()
        }
    }

    fun synchronizeAll(): Int {
        return onboardingTestPixRepository.findByStatus(
            onboardingTestPixStatus = OnboardingTestPixStatus.IN_PROGRESS,
        ).map { onboardingTestPix ->
            synchronize(onboardingTestPix).also { updated ->
                onboardingTestPixRepository.save(updated)
            }
        }.size
    }

    fun findPixKey(accountId: AccountId): Result<PixKey?> = runCatching {
        val primaryWallet = walletRepository.findWallets(accountId).single { wallet -> wallet.checkPrimary(accountId) }

        onboardingTestPixRepository.findByAccountId(accountId)?.billIds?.firstOrNull()?.let { billId ->
            val bill = billRepository.findBill(billId = billId, walletId = primaryWallet.id)

            bill.recipient?.pixKeyDetails?.key
        }
    }

    private fun findAccount(accountId: AccountId): Either<CreateOnboardingTestPixError, Account> {
        if (systemActivityService.getOnboardingTestPix(accountId) != null) {
            return CreateOnboardingTestPixError.Conflict.left()
        }

        val account = accountRepository.findByIdOrNull(accountId)

        return if (account == null || !account.hasGroup(AccountGroup.ONBOARDING_TEST_PIX)) {
            CreateOnboardingTestPixError.AccountIdNotEligible.left()
        } else {
            account.right()
        }
    }

    private fun findAccountOrBill(accountId: AccountId): Either<CreateOnboardingTestPixError, Account> {
        val logName = "OnboardingTestPixService#findAccountOrBill"
        val marker = Markers.append("accountId", accountId.value)

        val account = accountRepository.findByIdOrNull(accountId) ?: return CreateOnboardingTestPixError.AccountIdNotEligible.left().also {
            logger.error(marker.andAppend("errorType", it).andAppend("error", "Account não encontrado"), logName)
        }

        val systemActivityValue = systemActivityService.getOnboardingTestPix(accountId)
        if (systemActivityValue != null && systemActivityValue in listOf(OnboardingTestPixSystemActivityValue.CREATED.name, CreateOnboardingTestPixError.Conflict.name)) {
            val testPix = onboardingTestPixRepository.findByAccountId(accountId) ?: return CreateOnboardingTestPixError.Conflict.left().also {
                logger.error(marker.andAppend("errorType", it).andAppend("error", "Já fez o fluxo de onboarding com falha"), logName)
            }

            marker.andAppend("onboardingTestPix", testPix)

            if (testPix.billIds.isNotEmpty()) {
                val billView = billRepository.findBill(testPix.billIds.first(), account.defaultWalletId())

                marker.andAppend("billView", billView)

                return if (billView.status == BillStatus.ACTIVE) {
                    CreateOnboardingTestPixError.PixAlreadyCreated(billView).left().also {
                        logger.error(marker.andAppend("errorType", it).andAppend("error", "A bill do single pix está ativa"), logName)
                    }
                } else {
                    CreateOnboardingTestPixError.Conflict.left().also {
                        logger.error(marker.andAppend("errorType", it).andAppend("error", "A bill do single pix foi paga"), logName)
                    }
                }
            }
        }

        return if (!account.hasGroup(AccountGroup.ONBOARDING_TEST_PIX)) {
            CreateOnboardingTestPixError.AccountIdNotEligible.left().also {
                logger.error(marker.andAppend("errorType", it).andAppend("error", "O usuário não está elegível ao single pix"), logName)
            }
        } else {
            logger.info(marker.andAppend("account", account), logName)
            account.right()
        }
    }

    private fun Account.withValidPixKey(): Either<CreateOnboardingTestPixError, Pair<Account, PixKey>> {
        val pixKeys =
            listOf(
                PixKey(value = document, type = PixKeyType.CPF),
                PixKey(value = mobilePhone, type = PixKeyType.PHONE),
                PixKey(value = emailAddress.value, type = PixKeyType.EMAIL),
            )

        val pixKey =
            pixKeys.firstOrNull { pixKey ->
                val details =
                    pixKeyManagement.findKeyDetails(
                        key = pixKey,
                        document = document,
                    )

                details.map {
                    it.pixKeyDetails.owner.document == document
                }.getOrElse {
                    false
                }
            }

        return pixKey?.let { Pair(this, it).right() } ?: CreateOnboardingTestPixError.NoPixKeyFound.left()
    }

    private fun createPix(
        account: Account,
        pixKey: PixKey,
        marker: LogstashMarker,
        dates: List<LocalDate>,
    ): Either<CreateOnboardingTestPixError, List<Bill>> {
        val logName = "OnboardingTestPixService#createPix"
        val requests =
            dates.map { dueDate ->
                CreatePixRequest(
                    description = "Bônus novo cadastro",
                    dueDate = dueDate,
                    amount = 1,
                    recipient =
                    RecipientRequest(
                        accountId = account.accountId,
                        name = account.name,
                        document = account.document,
                        pixKey = pixKey,
                        qrCode = null,
                    ),
                    source = ActionSource.Api(accountId = account.accountId),
                    walletId = account.defaultWalletId(),
                    automaticPixAuthorizationMaximumAmount = null,
                    automaticPixData = null,
                )
            }

        marker.andAppend("requests", requests)

        val billsCreated =
            requests.mapIndexed { idx, request ->
                createBillService.createPix(request, false).also {
                    if (it is CreateBillResult.SUCCESS) {
                        marker.andAppend("billId_$idx", it.bill.billId.value)
                        billEventPublisher.publish(
                            bill = it.bill,
                            event =
                            BillTagAdded(
                                billId = it.bill.billId,
                                walletId = account.defaultWalletId(),
                                actionSource = ActionSource.System,
                                tag = ONBOARDING_TEST_PIX,
                            ),
                        )
                    } else {
                        marker.andAppend("createBillResult_$idx", it)
                    }
                }
            }.mapNotNull {
                if (it is CreateBillResult.SUCCESS) {
                    it.bill
                } else {
                    null
                }
            }

        return if (billsCreated.isEmpty()) {
            CreateOnboardingTestPixError.PixNotCreated.left().also {
                logger.error(marker.andAppend("errorType", it), logName)
            }
        } else {
            logger.info(marker.andAppend("billsCreated", billsCreated), logName)
            billsCreated.right()
        }
    }

    private fun transferFunds(
        account: Account,
        pixKey: PixKey,
        marker: LogstashMarker,
        amount: Long,
    ): Either<CreateOnboardingTestPixError, Pair<Account, PixKey>> {
        val wallet = walletRepository.findWallet(account.defaultWalletId())

        val paymentMethod =
            accountRepository.findAccountPaymentMethodByIdAndAccountId(
                accountPaymentMethodId = wallet.paymentMethodId,
                accountId = account.accountId,
            )

        val bankTransferResult =
            bankAccountService.transfer(
                originAccountNo = configuration.originAccountNo,
                targetAccountNo = (paymentMethod.method as InternalBankAccount).buildFullAccountNumber(),
                amount = amount,
            )

        marker.andAppend("walletId", wallet.id.value)
            .andAppend("originAccountNo", configuration.originAccountNo)
            .andAppend("paymentMethod", paymentMethod)
            .andAppend("bankTransferResult", bankTransferResult)

        return if (bankTransferResult.status == BankOperationStatus.SUCCESS) {
            balanceService.invalidate(wallet.paymentMethodId)
            logger.info(marker, "OnboardingTestPixService#transferFunds")
            Pair(account, pixKey).right()
        } else {
            CreateOnboardingTestPixError.FundsNotTransferred.left().also {
                logger.error(marker.andAppend("error", it), "OnboardingTestPixService#transferFunds")
            }
        }
    }

    private fun transferRefundFriday(
        wallet: Wallet,
        amount: Long,
        marker: LogstashMarker,
    ): Boolean {
        val paymentMethod =
            accountRepository.findAccountPaymentMethodByIdAndAccountId(
                accountPaymentMethodId = wallet.paymentMethodId,
                accountId = wallet.founder.accountId,
            )

        val bankTransferResult =
            bankAccountService.transfer(
                originAccountNo = (paymentMethod.method as InternalBankAccount).buildFullAccountNumber(),
                targetAccountNo = configuration.originAccountNo,
                amount = amount,
            )
        marker.andAppend("walletId", wallet.id.value)
            .andAppend("targetAccountNo", configuration.originAccountNo)
            .andAppend("paymentMethod", paymentMethod)
            .andAppend("bankTransferResult", bankTransferResult)

        return bankTransferResult.status == BankOperationStatus.SUCCESS
    }

    private fun synchronize(onboardingTestPix: OnboardingTestPix): OnboardingTestPix {
        val logName = "OnboardingTestPixService#synchronize"
        val markers = Markers.append("input", onboardingTestPix)

        val bills =
            onboardingTestPix.billIds.map { billId ->
                billEventRepository.getBillById(billId).map { bill ->
                    bill
                }.getOrElse {
                    logger.error(markers, logName, it)
                    return onboardingTestPix
                }
            }
        markers.andAppend("bills", bills.map { mapOf("billId" to it.billId, "status" to it.status, "schedule" to it.schedule) })

        val billsPaidCounter = bills.filter { it.status == BillStatus.PAID }.size
        val allBillsArePaid = billsPaidCounter == bills.size

        val status =
            when {
                allBillsArePaid -> OnboardingTestPixStatus.COMPLETED
                onboardingTestPix.expired() && !bills.hasScheduled() -> OnboardingTestPixStatus.EXPIRED
                else -> OnboardingTestPixStatus.IN_PROGRESS
            }

        markers.andAppend("status", status)

        val notified =
            when (status) {
                OnboardingTestPixStatus.EXPIRED -> {
                    bills.ignore()
                    onboardingTestPix.notifyExpired()
                    false
                }

                OnboardingTestPixStatus.IN_PROGRESS -> {
                    bills.updateDueDate()
                    onboardingTestPix.notifyReminder(bills)
                }

                OnboardingTestPixStatus.COMPLETED -> {
                    false
                }
            }

        markers.andAppend("status", status)

        return onboardingTestPix.copy(
            status = status,
            billPaidCounter = billsPaidCounter,
            updatedAt = getZonedDateTime(),
            remindedAt = if (notified) getZonedDateTime() else onboardingTestPix.remindedAt,
        ).also {
            markers.andAppend("output", it)
            logger.info(markers, logName)
        }
    }

    private fun OnboardingTestPix.notifyReminder(bills: List<Bill>): Boolean {
        val markers = Markers.append("accountId", accountId)
            .andAppend("status", status)
            .andAppend("remindedAt", remindedAt)

        val allPendingBills = bills.all {
            it.status == BillStatus.ACTIVE && it.schedule == null
        }
        markers.andAppend("allPendingBills", allPendingBills)

        val createdInDays = Period.between(createdAt.toLocalDate(), getLocalDate()).days
        val durationInDaysUntilLastDay = TRI_PIX_DURATION_IN_DAYS - 2

        markers.andAppend("createdInDays", createdInDays)

        val reminder = when {
            createdInDays == 0 -> null
            createdInDays < durationInDaysUntilLastDay && remindedAt == null -> TriPixReminder.NEXT_DAY
            createdInDays == durationInDaysUntilLastDay && remindedAt != null -> TriPixReminder.LAST_DAY
            else -> null
        }
        markers.andAppend("reminder", reminder)

        val shouldSendNotification = status == OnboardingTestPixStatus.IN_PROGRESS && allPendingBills && reminder != null
        markers.andAppend("shouldSendNotification", shouldSendNotification)

        if (shouldSendNotification) {
            val account = accountRepository.findByIdOrNull(accountId) ?: throw AccountNotFoundException(accountId.value)
            val wallet = walletRepository.findWallet(account.defaultWalletId())
            val billViews = bills.map { billRepository.findBill(it.billId, it.walletId) }
            val reminderType = if (reminder == TriPixReminder.LAST_DAY) TestPixReminderType.LAST_DAY else TestPixReminderType.NEXT_DAY

            chatBotNotificationService.notifyTestPixReminder(account, wallet, billViews, reminderType)
        }

        logger.info(markers, "OnboardingTestPixService#notifyReminder")
        return shouldSendNotification
    }

    private fun OnboardingTestPix.notifyExpired() {
        val account = accountRepository.findByIdOrNull(accountId)

        if (account == null) {
            logger.error(Markers.append("accountId", accountId).and("ACTION" to "VERIFY"), "OnboardingTestPixService#notifyLastCall")
            return
        }

        val wallet = walletRepository.findWallet(account.defaultWalletId())
        val bills = billIds.map { billRepository.findBill(it, wallet.id) }

        chatBotNotificationService.notifyTestPixReminder(account, wallet, bills, TestPixReminderType.EXPIRED)
    }

    private fun OnboardingTestPix.expired(): Boolean {
        val expiresAt = createdAt.toLocalDate().plusDays(TRI_PIX_DURATION_IN_DAYS - 1L)
        return getLocalDate().isEqual(expiresAt) || getLocalDate().isAfter(expiresAt)
    }

    private fun List<Bill>.hasScheduled() = any { it.status == BillStatus.ACTIVE && it.schedule != null }

    private fun List<Bill>.ignore() {
        forEach { bill ->
            if (bill.status == BillStatus.ACTIVE) {
                billEventPublisher.publish(
                    bill = bill,
                    event =
                    BillIgnored(
                        billId = bill.billId,
                        walletId = bill.walletId,
                        actionSource = ActionSource.System,
                    ),
                )
            }
        }
    }

    private fun List<Bill>.updateDueDate() {
        val today = getLocalDate()
        val dueDates =
            listOf(
                today,
                today,
                today.plusDays(1),
            )

        forEachIndexed { index, bill ->
            if (bill.status == BillStatus.ACTIVE && bill.schedule == null) {
                billEventPublisher.publish(
                    bill = bill,
                    event =
                    RegisterUpdated(
                        billId = bill.billId,
                        walletId = bill.walletId,
                        updatedRegisterData =
                        UpdatedRegisterData.NewDueDate(
                            dueDate = dueDates[index],
                            effectiveDueDate = dueDates[index],
                        ),
                        actionSource = ActionSource.System,
                    ),
                )
            }
        }
    }

    companion object {
        fun randomAccountGroup(): AccountGroup {
            // NOTE: esse método ficou caso retorne algum teste com o tri-Pix que exiga grupo de controle
            return AccountGroup.ONBOARDING_TEST_PIX
        }
    }
}

@ConfigurationProperties("onboardingTestPix")
interface OnboardingTestPixConfiguration {
    val originAccountNo: String
}

data class OnboardingTestPix(
    val accountId: AccountId,
    val createdAt: ZonedDateTime,
    val updatedAt: ZonedDateTime,
    val remindedAt: ZonedDateTime? = null,
    val billIds: List<BillId>,
    val status: OnboardingTestPixStatus,
    val billPaidCounter: Int,
)

enum class OnboardingTestPixStatus {
    IN_PROGRESS,
    COMPLETED,
    EXPIRED,
}

sealed class CreateOnboardingTestPixError(val name: String) : PrintableSealedClassV2() {
    data object Conflict : CreateOnboardingTestPixError("CONFLICT")
    data object AccountIdNotEligible : CreateOnboardingTestPixError("ACCOUNT_ID_NOT_ELIGIBLE")
    data object NoPixKeyFound : CreateOnboardingTestPixError("NO_PIX_KEY_FOUND")
    data object PixKeyFromDifferentOwner : CreateOnboardingTestPixError("PIX_KEY_FROM_DIFFERENT_OWNER")
    data object PixNotCreated : CreateOnboardingTestPixError("PIX_KEY_NOT_CREATED")
    data object FundsNotTransferred : CreateOnboardingTestPixError("FUNDS_NOT_TRANSFERRED")
    data class PixAlreadyCreated(val billView: BillView) : CreateOnboardingTestPixError("PIX_ALREADY_CREATED")
}

enum class RefundOnboardingTestPixError {
    CONFLICT,
    FUNDS_NOT_TRANSFERRED,
}

internal enum class TriPixReminder {
    NEXT_DAY,
    LAST_DAY,
}