package ai.friday.billpayment.app.account

import ai.friday.billpayment.app.integrations.EventPublisher
import ai.friday.billpayment.app.integrations.UserPoolAdapter
import ai.friday.billpayment.app.integrations.UserPoolUsernameExistsException
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.AccountFixture
import arrow.core.getOrElse
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.fail

class RegisterServiceCreateOwnerUserTest {
    val accountId = AccountId(ACCOUNT_ID)

    val account = AccountFixture.createAccount().copy(accountId = accountId)

    private val accountService = AccountService(
        accountConfigurationService = mockk(),
        accountRepository = mockk(relaxed = true),
        chatbotMessagePublisher = mockk(),
        crmService = mockk(),
        notificationAdapter = mockk(),
        walletRepository = mockk(),
    )

    private val accountRegisterService = mockk<AccountRegisterService>()

    private val userPoolAdapter: UserPoolAdapter = mockk {
        every {
            createUser(any(), any(), any(), any(), any(), any(), any(), any())
        } just runs

        every {
            setAccountId(any(), any())
        } just runs

        every {
            enableUser(any())
        } returns Unit.right()
    }

    private val activityService: SystemActivityService = mockk {
        every {
            setCreatedPassword(any(), any())
        } returns Unit
    }

    private val mockedEventPublisher = mockk<EventPublisher>(relaxed = true)

    private val registerServiceWithMocks =
        RegisterService(
            tokenService = mockk(),
            accountRegisterRepository = mockk(relaxed = true),
            accountService = accountService,
            accountRegisterService = accountRegisterService,
            documentOCRParser = mockk(),
            userFilesConfiguration = mockk(),
            agreementFilesService = mockk(),
            notificationSenderService = mockk(),
            ddaService = mockk(),
            notificationAdapter = mockk(),
            userPoolAdapter = userPoolAdapter,
            kycService = mockk(),
            externalAccountRegister = mockk(),
            ecmProvider = mockk(),
            walletService = mockk(),
            walletLimitsService = mockk(),
            crmService = mockk(),
            registerInstrumentationService = mockk(),
            pdfFileParser = mockk(),
            pendingInternalApproveConfiguration = mockk(),
            pendingInternalReviewConfiguration = mockk(),
            pendingActivationConfiguration = mockk(),
            accountStatusLockProvider = mockk(),
            bigDataService = mockk(),
            userJourneyService = mockk(relaxUnitFun = true),
            closeAccountService = mockk(relaxed = true),
            systemActivityService = mockk(),
            livenessService = mockk(),
            eventPublisher = mockedEventPublisher,
            fraudList = mockk(relaxed = true),
            pendingUpgradeInternalApproveConfiguration = mockk(relaxed = true),
            messagePublisher = mockk(),
            adService = mockk(relaxed = true),
            chatBotNotificationService = mockk(relaxed = true),
            walletBillCategoryService = mockk(relaxed = true),
            documentScanService = mockk(relaxed = true),
            activityService = activityService,
            loginRepository = mockk(relaxed = true),
        )

    @Nested
    @DisplayName("Teste de criação de usuário owner")
    inner class CreateUserTest {

        @Test
        internal fun `deve ativar e setar o novo account id no cognito caso esse usuário já tiver uma conta desativada`() {
            every {
                accountService.findAccountById(any())
            } returns account

            every {
                userPoolAdapter.doesUserExist(any())
            } returns true

            every {
                userPoolAdapter.isUserEnabled(any())
            } returns false

            every {
                userPoolAdapter.setUserPassword(any(), any())
            } just Runs

            val result = registerServiceWithMocks.createSimpleOwnerUser(accountId, "123")

            verify(exactly = 0) {
                userPoolAdapter.createUser(any(), any(), any(), any(), any(), any(), any(), any())
            }

            verify(exactly = 1) {
                userPoolAdapter.enableUser(any())
            }

            verify(exactly = 1) {
                userPoolAdapter.setAccountId(any(), any())
            }

            verify(exactly = 1) {
                mockedEventPublisher.publish(ofType<UserCreated>())
            }

            verify(exactly = 1) {
                activityService.setCreatedPassword(any(), any())
            }

            result.isRight() shouldBe true
            result.getOrNull()?.username shouldBe account.document
        }

        @Test
        internal fun `deve retornar erro caso já exista uma conta ativa para o documento no cognito`() {
            every {
                accountService.findAccountById(any())
            } returns account

            every {
                userPoolAdapter.doesUserExist(any())
            } returns true

            every {
                userPoolAdapter.isUserEnabled(any())
            } returns true

            registerServiceWithMocks.createSimpleOwnerUser(accountId, "123").mapLeft {
                it.shouldBeTypeOf<UserPoolUsernameExistsException>()
            }

            verify(exactly = 0) {
                userPoolAdapter.createUser(
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                )
            }
        }

        @Test
        internal fun `deve criar a conta normalmente caso não exista usuário para esse documento no cognito`() {
            every {
                accountService.findAccountById(any())
            } returns account

            every {
                userPoolAdapter.doesUserExist(any())
            } returns false

            val result = registerServiceWithMocks.createSimpleOwnerUser(accountId, "123").getOrElse {
                fail("should not fail")
            }

            verify(exactly = 1) {
                userPoolAdapter.createUser(any(), any(), any(), any(), any(), any(), any(), any())
            }

            result.username shouldBe account.document
        }
    }

    @Test
    internal fun `não deve interromper o fluxo caso ocorra erro ao salvar a senha`() {
        every {
            activityService.setCreatedPassword(any(), any())
        } throws Exception()

        every {
            accountService.findAccountById(any())
        } returns account

        every {
            userPoolAdapter.doesUserExist(any())
        } returns true

        every {
            userPoolAdapter.isUserEnabled(any())
        } returns false

        every {
            userPoolAdapter.setUserPassword(any(), any())
        } just Runs

        val result = registerServiceWithMocks.createSimpleOwnerUser(accountId, "123")

        verify(exactly = 1) {
            activityService.setCreatedPassword(any(), any())
        }

        result.isRight() shouldBe true
        result.getOrNull()?.username shouldBe account.document
    }
}