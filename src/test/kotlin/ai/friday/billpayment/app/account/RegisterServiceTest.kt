package ai.friday.billpayment.app.account

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.accountRegisterCompleted
import ai.friday.billpayment.accountRegisterData
import ai.friday.billpayment.accountRegisterDataWithAddress
import ai.friday.billpayment.accountRegisterDataWithPhoneVerified
import ai.friday.billpayment.accountRegisterMissingOnlyAgreement
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.AccountRegisterDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountRegisterDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_TABLE_NAME
import ai.friday.billpayment.adapters.dynamodb.LoginDbRepository
import ai.friday.billpayment.adapters.dynamodb.LoginDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.OriginalOcrAndPersonDataDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.originalOcrKey
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.chatbot.ChatbotNotificationService
import ai.friday.billpayment.app.dda.DDAProvider
import ai.friday.billpayment.app.dda.DDARegister
import ai.friday.billpayment.app.dda.DDAService
import ai.friday.billpayment.app.dda.DDAStatus
import ai.friday.billpayment.app.documentscan.DocumentScanService
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.BigDataService
import ai.friday.billpayment.app.integrations.CrmContact
import ai.friday.billpayment.app.integrations.CrmService
import ai.friday.billpayment.app.integrations.DocumentOCRParser
import ai.friday.billpayment.app.integrations.DocumentOCRParserException
import ai.friday.billpayment.app.integrations.DocumentType
import ai.friday.billpayment.app.integrations.EventPublisher
import ai.friday.billpayment.app.integrations.InvalidDocumentImageException
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.ObjectRepository
import ai.friday.billpayment.app.integrations.PersonBasicInfo
import ai.friday.billpayment.app.integrations.RegisterInstrumentationService
import ai.friday.billpayment.app.integrations.TokenData
import ai.friday.billpayment.app.integrations.TokenDataWithExpiration
import ai.friday.billpayment.app.integrations.UserPoolAdapter
import ai.friday.billpayment.app.liveness.LivenessEnrollmentVerification
import ai.friday.billpayment.app.liveness.LivenessErrors
import ai.friday.billpayment.app.liveness.LivenessId
import ai.friday.billpayment.app.liveness.LivenessSelfieError
import ai.friday.billpayment.app.liveness.LivenessService
import ai.friday.billpayment.app.login.ProviderName
import ai.friday.billpayment.app.login.ProviderUser
import ai.friday.billpayment.app.msisdnauth.createWhatsappEmail
import ai.friday.billpayment.app.onboarding.OnboardingTestPixService
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.basicAccountRegisterData
import ai.friday.billpayment.buildKycDossier
import ai.friday.billpayment.cnhDocumentInfo
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.DynamoDBUtils
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.rgDocumentInfo
import ai.friday.billpayment.withGivenImageOptimizer
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateTimeFormat
import ai.friday.morning.date.withGivenDateTime
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.collections.shouldContainInOrder
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.kotest.matchers.string.shouldEndWith
import io.kotest.matchers.string.shouldStartWith
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.Runs
import io.mockk.called
import io.mockk.clearMocks
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.runs
import io.mockk.slot
import io.mockk.spyk
import io.mockk.unmockkObject
import io.mockk.verify
import io.via1.communicationcentre.app.integrations.EmailSenderService
import io.via1.communicationcentre.app.integrations.PdfFileParser
import java.awt.image.BufferedImage
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.io.FileInputStream
import java.io.InputStream
import java.time.LocalDate
import java.time.ZonedDateTime
import java.util.Optional
import java.util.stream.Stream
import javax.imageio.ImageIO
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class RegisterServiceTest {

    var dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val objectRepository: ObjectRepository = mockk {
        every { putObject(any(), any(), any()) } just runs
        every {
            loadObject(any(), any())
        } returns InputStream.nullInputStream()
    }

    private val accountRegisterRepository = spyk(
        AccountRegisterDbRepository(
            AccountRegisterDynamoDAO(getDynamoDB()),
            OriginalOcrAndPersonDataDynamoDAO(getDynamoDB()),
            objectRepository,
        ),
    )

    private val ddaService: DDAService = mockk()
    private val notificationAdapter: NotificationAdapter = mockk()
    private val registerInstrumentationService: RegisterInstrumentationService = mockk() {
        every { completed(any(), any()) } just Runs
    }

    private val accountDAO = AccountDynamoDAO(getDynamoDB())
    private val partialAccountDAO = PartialAccountDynamoDAO(getDynamoDB())
    private val paymentMethodDAO = PaymentMethodDynamoDAO(getDynamoDB())
    private val nsuDAO = NSUDynamoDAO(getDynamoDB())
    private val transactionDynamo = TransactionDynamo(getDynamoDB())

    private val accountRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )

    private val crmServiceMock = mockk<CrmService>(relaxUnitFun = true)

    private val testMobilePhone = MobilePhone("+*************")
    private val accountRegisterService = AccountRegisterService(
        accountRegisterRepository = accountRegisterRepository,
        livenessService = mockk(),
        userFilesConfiguration = mockk(),
    ).apply {
        signUpMobilePhones = listOf(testMobilePhone.msisdn)
    }

    private val accountService =
        AccountService(
            accountConfigurationService = mockk(),
            accountRepository = accountRepository,
            chatbotMessagePublisher = mockk(),
            crmService = crmServiceMock,
            notificationAdapter = notificationAdapter,
            walletRepository = mockk(),
        )

    private val agreementFilesService: AgreementFilesService = mockk(relaxUnitFun = true)

    private val tokenService: TokenService = mockk(relaxUnitFun = true)

    private val emailSenderService: EmailSenderService = mockk()

    private val documentOCRParser: DocumentOCRParser = mockk()

    private val userFilesConfiguration = UserFilesConfiguration().apply {
        bucket = "test"
        path = "pathtest"
        documentPrefix = "prefix_"
        region = "region"
        selfiePrefix = "selfie_"
        contractPrefix = "contract_"
        declarationOfResidencyPrefix = "residency_"
    }

    private val bigDataService = mockk<BigDataService>(relaxUnitFun = true) {
        every {
            getPersonBasicInfo(any())
        } returns
            PersonBasicInfo(
                name = cnhDocumentInfo.name,
                birthDate = cnhDocumentInfo.birthDate,
                motherName = cnhDocumentInfo.motherName,
                fatherName = cnhDocumentInfo.fatherName,
            ).right()
    }

    private val documentScanServiceMock: DocumentScanService = mockk {
        every { getResult(any()) } returns null.right()
        every { getImage(any()) } returns null.right()
    }

    private val systemActivityService: SystemActivityService = mockk()
    private val userPoolAdapter: UserPoolAdapter = mockk(relaxed = true)
    private val enhancedClient = getDynamoDB()
    private val loginRepository = LoginDbRepository(LoginDynamoDAO(enhancedClient), TransactionDynamo(enhancedClient))

    private val pdfFileParser: PdfFileParser = mockk()
    private val livenessService: LivenessService = mockk(relaxed = true)

    private val eventPublisher: EventPublisher = mockk()

    private val closeAccountService = CloseAccountService(
        accountService = accountService,
        accountRegisterRepository = accountRegisterRepository,
        accountRegisterService = accountRegisterService,
        walletService = mockk(relaxed = true),
        backOfficeAccountService = mockk(),
        loginService = mockk(),
        transactionRepository = mockk(),
        balanceService = mockk(),
        crmService = crmServiceMock,
        subscriptionService = mockk(),
        livenessService = livenessService,
        statementService = mockk(),
        closeAccountStepExecutors = emptyList(),
        messagePublisher = mockk(),
        closeAccountQueueName = "close_Account",
        onboardingTestPixService = mockk(),
        externalAccountService = mockk(), closeAccountStepDiscovery = listOf(), closeWalletStepDiscovery = listOf(),
    )

    private val chatBotNotificationService: ChatbotNotificationService = mockk(relaxed = true)

    private val registerService: RegisterService =
        RegisterService(
            tokenService = tokenService,
            accountRegisterRepository = accountRegisterRepository,
            accountService = accountService,
            accountRegisterService = accountRegisterService,
            documentOCRParser = documentOCRParser,
            userFilesConfiguration = userFilesConfiguration,
            agreementFilesService = agreementFilesService,
            notificationSenderService = emailSenderService,
            ddaService = ddaService,
            notificationAdapter = notificationAdapter,
            userPoolAdapter = userPoolAdapter,
            kycService = mockk(),
            externalAccountRegister = mockk(),
            ecmProvider = mockk(),
            walletService = mockk(relaxed = true),
            walletLimitsService = mockk(),
            crmService = crmServiceMock,
            registerInstrumentationService = registerInstrumentationService,
            pdfFileParser = pdfFileParser,
            pendingInternalApproveConfiguration = mockk(),
            pendingInternalReviewConfiguration = mockk(),
            pendingActivationConfiguration = mockk(),
            accountStatusLockProvider = mockk(),
            systemActivityService = systemActivityService,
            bigDataService = bigDataService,
            userJourneyService = mockk(relaxUnitFun = true),
            closeAccountService = closeAccountService,
            livenessService = livenessService,
            eventPublisher = eventPublisher,
            fraudList = mockk(relaxed = true),
            pendingUpgradeInternalApproveConfiguration = mockk(relaxed = true),
            messagePublisher = mockk(),
            adService = mockk(relaxed = true),
            chatBotNotificationService = chatBotNotificationService,
            walletBillCategoryService = mockk(relaxed = true),
            documentScanService = documentScanServiceMock,
            activityService = mockk(relaxed = true),
            loginRepository = loginRepository,
        ).apply {
            maxDocumentSize = 10
        }

    private lateinit var accountId: AccountId
    private lateinit var otherAccountId: AccountId

    private val imageFileExtension = "jpeg"
    private val pdfFileExtension = "pdf"
    private lateinit var partialAccount: PartialAccount
    private lateinit var otherPartialAccount: PartialAccount

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        partialAccount = accountRepository.create(
            "fake_user",
            EmailAddress("<EMAIL>"),
            registrationType = RegistrationType.BASIC,
        )
        otherPartialAccount = accountRepository.create(
            "fake_user",
            EmailAddress("<EMAIL>"),
            registrationType = RegistrationType.BASIC,
        )

        accountId = partialAccount.id
        otherAccountId = otherPartialAccount.id
        accountRegisterRepository.save(accountRegisterData.copy(accountId = accountId))
        every {
            userPoolAdapter.doesUserExist(any())
        } returns true
        every {
            userPoolAdapter.isUserEnabled(any())
        } returns true
    }

    @Test
    fun `deve ativar cartão de crédito quando usuário se registrar com CNH`() {
        mockkObject(OnboardingTestPixService) {
            every { OnboardingTestPixService.randomAccountGroup() } returns AccountGroup.ONBOARDING_TEST_PIX

            every { userPoolAdapter.doesUserExist(any()) } returns true
            every { userPoolAdapter.removeUserFromGroup(any(), any()) } just Runs
            every { userPoolAdapter.addUserToGroup(any(), any()) } just Runs
            every { notificationAdapter.notifyUserActivated(any()) } just Runs
            every { notificationAdapter.notifyCreditCardEnabled(any(), any()) } just Runs
            every { registerInstrumentationService.activated(any(), any()) } just Runs
            every { systemActivityService.setAccountActivated(any(), any()) } just Runs

            every {
                ddaService.register(any(), any(), any())
            } returns DDAStatus.ACTIVE
            every { ddaService.findByAccount(any()) } returns DDARegister(accountId = partialAccount.id, document = DOCUMENT, created = ZonedDateTime.now(), status = DDAStatus.ACTIVE, lastUpdated = ZonedDateTime.now(), lastSuccessfullExecution = null, provider = DDAProvider.ARBI, migrated = null)

            every {
                crmServiceMock.contactExists(any())
            } returns true

            every { crmServiceMock.upsertContact(any<Account>()) } returns mockk()

            every { crmServiceMock.findContact(accountId = any()) } returns CrmContact(
                accountId = partialAccount.id,
                emailAddress = partialAccount.emailAddress,
                role = partialAccount.role,
                name = partialAccount.name,
                mobilePhone = null,
                document = null,
                removed = false,
                groups = listOf(),
                accountType = UserAccountType.FULL_ACCOUNT,
                subscriptionType = SubscriptionType.PIX,
            )

            val partialAccount = accountRepository.create(
                username = accountRegisterCompleted.nickname,
                emailAddress = accountRegisterCompleted.emailAddress,
                registrationType = RegistrationType.BASIC,
            )

            accountRepository.updatePartialAccountStatus(
                partialAccount.id,
                AccountStatus.APPROVED,
            )

            accountRepository.save(
                account = Account(
                    accountId = partialAccount.id,
                    name = partialAccount.name,
                    emailAddress = partialAccount.emailAddress,
                    document = "12345",
                    documentType = "CNH",
                    mobilePhone = "2313132",
                    created = getZonedDateTime(),
                    updated = getZonedDateTime(),
                    activated = null,
                    status = AccountStatus.APPROVED,
                    firstLoginAsOwner = null,
                    channel = null,
                    configuration = LegacyAccountConfiguration(
                        accountId = partialAccount.id,
                        defaultWalletId = null,
                        receiveDDANotification = false,
                        receiveNotification = false,
                        accessToken = null,
                        refreshToken = null,
                        externalId = null,
                        groups = listOf(),
                        notificationGateway = NotificationGateways.WHATSAPP,
                        creditCardConfiguration = CreditCardConfiguration(
                            quota = 0,
                        ),
                    ),
                    imageUrlSmall = null,
                    imageUrlLarge = null,
                    subscriptionType = SubscriptionType.PIX,
                ),
            )

            loginRepository.createLogin(
                providerUser = ProviderUser(
                    id = "12345",
                    providerName = ProviderName.MSISDN,
                    username = accountRegisterCompleted.nickname,
                    emailAddress = accountRegisterCompleted.emailAddress,
                ),
                id = partialAccount.id,
                role = Role.GUEST,
            )

            loginRepository.createLogin(
                providerUser = ProviderUser(
                    id = "12345",
                    providerName = ProviderName.WHATSAPP,
                    username = accountRegisterCompleted.nickname,
                    emailAddress = createWhatsappEmail(MobilePhone("2313132")),
                ),
                id = partialAccount.id,
                role = Role.GUEST,
            )

            val accountRegisterData = accountRegisterRepository.save(
                accountRegisterData.copy(
                    accountId = partialAccount.id,
                    uploadedCNH = StoredObject(
                        region = "",
                        bucket = "",
                        key = "",
                    ),
                    documentInfo = cnhDocumentInfo,
                ),
            )

            val account = registerService.activateAccount(accountRegisterData.accountId)

            account.isRight() shouldBe true
            account.map { it: Account ->
                it.hasGroup(AccountGroup.CREDIT_CARD_STANDARD_PLAN) shouldBe true
                it.hasGroup(AccountGroup.ONBOARDING_TEST_PIX) shouldBe true
                it.hasCreditCardEnabled() shouldBe true
                it.creditCardConfiguration.quota shouldBe 2_000_00
            }
            verify(exactly = 0) {
                notificationAdapter.notifyCreditCardEnabled(any(), any())
            }
        }
    }

    @Test
    fun `should create account register with correct default values`() {
        val emailS = "FAKE_EMAIL"
        val username = "FAKE_USERNAME"
        registerService.createAccountRegister(
            accountId = accountId,
            emailAddress = EmailAddress(emailS),
            username = username,
            mobilePhone = null,
            registrationType = RegistrationType.FULL,
        )

        val register = accountRegisterRepository.findByAccountId(accountId)
        with(register) {
            mobilePhone shouldBe null
            mobilePhoneVerified shouldBe false
            emailAddress shouldBe EmailAddress(emailS)
            nickname shouldBe username
            calculatedGender shouldBe null
            registrationType shouldBe RegistrationType.FULL
        }
    }

    @Test
    fun `should return either left when some exception occurs`() {
        val accountRegisterRepository = mockk<AccountRegisterRepository>()
        val registerServiceWithMocks =
            RegisterService(
                tokenService = mockk(),
                accountRegisterRepository = accountRegisterRepository,
                accountService = accountService,
                accountRegisterService = accountRegisterService,
                documentOCRParser = documentOCRParser,
                userFilesConfiguration = userFilesConfiguration,
                agreementFilesService = agreementFilesService,
                notificationSenderService = emailSenderService,
                ddaService = ddaService,
                notificationAdapter = mockk(),
                userPoolAdapter = mockk(),
                kycService = mockk(),
                externalAccountRegister = mockk(),
                ecmProvider = mockk(),
                walletService = mockk(),
                walletLimitsService = mockk(),
                crmService = mockk(),
                registerInstrumentationService = mockk(),
                pdfFileParser = pdfFileParser,
                pendingInternalApproveConfiguration = mockk(),
                pendingInternalReviewConfiguration = mockk(),
                pendingActivationConfiguration = mockk(),
                accountStatusLockProvider = mockk(),
                bigDataService = mockk(),
                userJourneyService = mockk(relaxUnitFun = true),
                closeAccountService = mockk(relaxed = true),
                systemActivityService = mockk(),
                livenessService = mockk(),
                eventPublisher = eventPublisher,
                fraudList = mockk(relaxed = true),
                pendingUpgradeInternalApproveConfiguration = mockk(relaxed = true),
                messagePublisher = mockk(),
                adService = mockk(relaxed = true),
                chatBotNotificationService = mockk(relaxed = true),
                walletBillCategoryService = mockk(relaxed = true),
                documentScanService = documentScanServiceMock,
                activityService = mockk(relaxed = true),
                loginRepository = loginRepository,
            )

        every {
            accountRegisterRepository.create(
                any(),
                any(),
                any(),
                any(),
                registrationType = any(),
            )
        } throws Exception()

        assertThrows<Exception> {
            registerServiceWithMocks.createAccountRegister(
                accountId,
                EmailAddress("email"),
                "username",
                null,
                RegistrationType.FULL,
            )
        }
    }

    @Test
    fun `should return DocumentOCRParserException when bigdata call fails`() {
        every { documentOCRParser.parseDocument(any()) } returns Either.Left(DocumentOCRParserException(Exception()))
        val result = withGivenImageOptimizer(ByteArrayOutputStream()) {
            registerService.processCNHDocumentFile(accountId, InputStream.nullInputStream(), imageFileExtension)
        }
        result.isLeft() shouldBe true
        result.mapLeft { it.shouldBeTypeOf<DocumentOCRParserException>() }
    }

    @Test
    fun `quando check de ocr for false e for uma CNH deve retornar ocrParseResult vazio`() {
        val result = withGivenImageOptimizer(ByteArrayOutputStream()) {
            registerService.processCNHDocumentFile(
                accountId = accountId,
                document = InputStream.nullInputStream(),
                extension = imageFileExtension,
                ocr = false,
            )
        }

        verify(exactly = 0) { documentOCRParser.parseDocument(any()) }
        verify { accountRegisterRepository.save(any()) }
        result.isRight() shouldBe true
    }

    @Test
    fun `quando check de ocr for false e for um RG deve retornar ocrParseResult vazio`() {
        val result = withGivenImageOptimizer(ByteArrayOutputStream()) {
            registerService.processRGDocumentFile(
                accountId = accountId,
                document = InputStream.nullInputStream(),
                extension = imageFileExtension,
                ocr = false,
                frontSide = true,
            )
        }

        verify(exactly = 0) { documentOCRParser.fetchDocumentQuality(any()) }
        verify { accountRegisterRepository.save(any()) }
        result.isRight() shouldBe true
    }

    @Test
    fun `quando check de ocr for false e for um RG com a parte de trás deve retornar ocrParseResult vazio`() {
        val frontSide = withGivenImageOptimizer(ByteArrayOutputStream()) {
            registerService.processRGDocumentFile(
                accountId = accountId,
                document = InputStream.nullInputStream(),
                extension = imageFileExtension,
                ocr = false,
                frontSide = true,
            )
        }.map {
            accountRegisterRepository.save(it)
        }

        val result = withGivenImageOptimizer(ByteArrayOutputStream()) {
            registerService.processRGDocumentFile(
                accountId = accountId,
                document = InputStream.nullInputStream(),
                extension = imageFileExtension,
                ocr = false,
                frontSide = false,
            )
        }

        verify(exactly = 0) { documentOCRParser.parseDocument(any()) }
        verify { accountRegisterRepository.save(any()) }
        result.isRight() shouldBe true
    }

    @Test
    fun `should return InvalidDocumentImageException when document is not an valid document image`() {
        every { documentOCRParser.parseDocument(any()) } returns Either.Left(InvalidDocumentImageException("FAKE_EXCEPTION"))

        val result = withGivenImageOptimizer(ByteArrayOutputStream()) {
            registerService.processCNHDocumentFile(accountId, InputStream.nullInputStream(), imageFileExtension)
        }
        result.isLeft() shouldBe true
        result.mapLeft { it.shouldBeTypeOf<InvalidDocumentImageException>() }
    }

    @Test
    fun `should return InvalidDocumentImageException when document is an valid document image but not an CNH`() {
        every { documentOCRParser.parseDocument(any()) } returns Either.Right(rgDocumentInfo)

        val result = withGivenImageOptimizer(ByteArrayOutputStream()) {
            registerService.processCNHDocumentFile(accountId, InputStream.nullInputStream(), imageFileExtension)
        }
        result.isLeft() shouldBe true
        result.mapLeft { it.shouldBeTypeOf<InvalidDocumentImageException>() }
    }

    @Test
    fun `deve tratar como CNH documentos do tipo CNHV2`() {
        every { documentOCRParser.parseDocument(any()) } returns Either.Right(
            cnhDocumentInfo.copy(
                docType = DocumentType.of(
                    "CNHV2",
                ),
            ),
        )

        val result = withGivenImageOptimizer(ByteArrayOutputStream()) {
            registerService.processCNHDocumentFile(accountId, InputStream.nullInputStream(), imageFileExtension)
        }

        result.isRight() shouldBe true
    }

    @Test
    fun `deve retornar sucesso mesmo quando o CPF não for encontrado na imagem da CNH`() {
        withGivenImageOptimizer(ByteArrayOutputStream()) {
            every { documentOCRParser.parseDocument(any()) } returns DocumentInfo(
                name = "",
                cpf = "",
                birthDate = null,
                fatherName = "",
                motherName = "",
                rg = "",
                docType = DocumentType.CNH,
                cnhNumber = null,
                orgEmission = "",
                expeditionDate = null,
                birthCity = "",
                birthState = "",
            ).right()

            val originalByteArray = "[original-document-file]".toByteArray()

            val result = registerService.processCNHDocumentFile(
                accountId = accountId,
                document = ByteArrayInputStream(originalByteArray),
                extension = imageFileExtension,
            )

            verify(exactly = 1) { documentOCRParser.parseDocument(any()) }

            result.isRight() shouldBe true
            result.map { accountRegisterData ->
                accountRegisterData.uploadedCNH.shouldNotBeNull()
                accountRegisterData.uploadedDocument.shouldBeNull()
                accountRegisterData.documentInfo.shouldNotBeNull()
                accountRegisterData.documentInfo!!.cpf shouldBe ""
                accountRegisterData.documentInfo!!.docType.value shouldBe "CNH"
            }

            verify(exactly = 1) {
                crmServiceMock.upsertContact(any<AccountRegisterData>())
                accountRegisterRepository.saveOriginalOcrAndPersonData(accountId, any(), any(), any())
            }
        }
    }

    /*
        @Disabled("Esperar a bigdatacorp resolver o problema")
        @Test
        fun `deve retornar FaceNotFoundException quando a primeira imagem não contenha uma foto`() {
            every { documentOCRParser.fetchDocumentQuality(any()) } returns DocumentQuality(
                typeOfDocument = DocumentType.CNH,
                containsFace = false,
            ).right()

            val result = withGivenImageOptimizer(ByteArrayOutputStream()) {
                registerService.processRGDocumentFile(
                    accountId = accountId,
                    document = InputStream.nullInputStream(),
                    frontSide = true,
                    extension = imageFileExtension,
                )
            }

            result.isLeft() shouldBe true
            result.mapLeft { it.shouldBeTypeOf<FaceNotFoundException>() }

            verify(exactly = 0) { documentOCRParser.parseDocument(any()) }
        }

        @Disabled("Esperar a bigdatacorp resolver o problema")
        @Test
        fun `deve retornar DocumentOCRParserException quando a validação da primeira imagem falhar`() {
            every { documentOCRParser.fetchDocumentQuality(any()) } returns NoStackTraceException("DEU RUIM").left()

            val result = withGivenImageOptimizer(ByteArrayOutputStream()) {
                registerService.processRGDocumentFile(
                    accountId = accountId,
                    document = InputStream.nullInputStream(),
                    frontSide = true,
                    extension = imageFileExtension,
                )
            }
            result.isLeft() shouldBe true
            result.mapLeft { it.shouldBeTypeOf<DocumentOCRParserException>() }

            verify(exactly = 0) { documentOCRParser.parseDocument(any()) }
        }

        @Disabled("Esperar a bigdatacorp resolver o problema")
        @Test
        fun `deve retornar corretamente quando a validação da primeira imagem funcionar e gravar no banco`() {
            val docType = DocumentType.OTHER("OUTRO_QQ")
            every { documentOCRParser.fetchDocumentQuality(any()) } returns DocumentQuality(
                typeOfDocument = docType,
                containsFace = true,
            ).right()

            val result = withGivenImageOptimizer(ByteArrayOutputStream()) {
                registerService.processRGDocumentFile(
                    accountId = accountId,
                    document = InputStream.nullInputStream(),
                    frontSide = true,
                    extension = imageFileExtension,
                )
            }
            result.isRight() shouldBe true

            verify(exactly = 0) { documentOCRParser.parseDocument(any()) }

            val accountRegisterData = accountRegisterRepository.findByAccountId(accountId)

            with(accountRegisterData) {
                documentInfo.shouldBeNull()
                uploadedCNH.shouldBeNull()
                uploadedDocument.shouldNotBeNull()
                uploadedDocument!!.front.shouldNotBeNull()
                uploadedDocument!!.back.shouldBeNull()
                uploadedDocument!!.documentType.value shouldBe docType.value
                calculatedGender shouldBe Gender.F
            }
        }
    */

    @Test
    fun `deve retornar corretamente tipo RG quando a validação disser que o documento é CNH`() {
        val docType = DocumentType.CNH
        every { documentOCRParser.fetchDocumentQuality(any()) } returns DocumentQuality(
            typeOfDocument = docType,
            containsFace = true,
        ).right()

        val result = withGivenImageOptimizer(ByteArrayOutputStream()) {
            registerService.processRGDocumentFile(
                accountId = accountId,
                document = InputStream.nullInputStream(),
                frontSide = true,
                extension = imageFileExtension,
            )
        }
        result.isRight() shouldBe true

        verify(exactly = 0) { documentOCRParser.parseDocument(any()) }

        val accountRegisterData = accountRegisterRepository.findByAccountId(accountId)

        with(accountRegisterData) {
            documentInfo.shouldBeNull()
            uploadedCNH.shouldBeNull()
            uploadedDocument.shouldNotBeNull()
            uploadedDocument!!.front.shouldNotBeNull()
            uploadedDocument!!.back.shouldBeNull()
            uploadedDocument!!.documentType.value shouldBe DocumentType.RG.value
            calculatedGender shouldBe Gender.F
        }
    }

    @Test
    fun `deve retornar sucesso mesmo quando o CPF não for encontrado na imagem do verso do RG`() {
        withGivenImageOptimizer(ByteArrayOutputStream()) {
            every { documentOCRParser.parseDocument(any(), any(), any()) } returns DocumentInfo(
                name = "",
                cpf = "",
                birthDate = null,
                fatherName = "",
                motherName = "",
                rg = "",
                docType = DocumentType.RG,
                cnhNumber = null,
                orgEmission = "",
                expeditionDate = null,
                birthCity = "",
                birthState = "",
            ).right()

            val frontStoredObject = StoredObject(
                userFilesConfiguration.region,
                userFilesConfiguration.bucket,
                "${userFilesConfiguration.path}/${accountId.value}/${userFilesConfiguration.documentPrefix}",
            )

            every {
                objectRepository.loadObject(frontStoredObject.bucket, frontStoredObject.key)
            } returns InputStream.nullInputStream()

            val accountRegisterUpdated = accountRegisterRepository.findByAccountId(accountId).copy(
                uploadedDocument = UploadedDocumentImages(
                    front = frontStoredObject,
                    documentType = DocumentType.RG,
                    back = null,
                ),
            )

            accountRegisterRepository.save(accountRegisterUpdated)

            val result = registerService.processRGDocumentFile(
                accountId = accountId,
                document = InputStream.nullInputStream(),
                frontSide = false,
                extension = imageFileExtension,
            )

            verify(exactly = 1) { documentOCRParser.parseDocument(any(), any(), any()) }
            verify(exactly = 0) { documentOCRParser.fetchDocumentQuality(any()) }

            result.isRight() shouldBe true
            result.map { accountRegisterData ->
                accountRegisterData.uploadedDocument.shouldNotBeNull()
                accountRegisterData.uploadedDocument!!.back.shouldNotBeNull()
                accountRegisterData.uploadedDocument!!.back!!.key.contains("_BACK")
                accountRegisterData.uploadedCNH.shouldBeNull()
                accountRegisterData.documentInfo.shouldNotBeNull()
                accountRegisterData.documentInfo!!.cpf shouldBe ""
                accountRegisterData.documentInfo!!.docType.value shouldBe "RG"
            }

            verify(exactly = 1) {
                crmServiceMock.upsertContact(any<AccountRegisterData>())
                accountRegisterRepository.saveOriginalOcrAndPersonData(accountId, any(), any(), any())
            }
        }
    }

    @Test
    fun `deve retornar sucesso com cpf vazio quando o CPF indicado pelo ocr for inválido`() {
        withGivenImageOptimizer(ByteArrayOutputStream()) {
            every { documentOCRParser.parseDocument(any(), any(), any()) } returns DocumentInfo(
                name = "",
                cpf = "***********",
                birthDate = null,
                fatherName = "",
                motherName = "",
                rg = "",
                docType = DocumentType.RG,
                cnhNumber = null,
                orgEmission = "",
                expeditionDate = null,
                birthCity = "",
                birthState = "",
            ).right()

            val frontStoredObject = StoredObject(
                userFilesConfiguration.region,
                userFilesConfiguration.bucket,
                "${userFilesConfiguration.path}/${accountId.value}/${userFilesConfiguration.documentPrefix}",
            )

            every {
                objectRepository.loadObject(frontStoredObject.bucket, frontStoredObject.key)
            } returns InputStream.nullInputStream()

            val accountRegisterUpdated = accountRegisterRepository.findByAccountId(accountId).copy(
                uploadedDocument = UploadedDocumentImages(
                    front = frontStoredObject,
                    documentType = DocumentType.RG,
                    back = null,
                ),
            )

            accountRegisterRepository.save(accountRegisterUpdated)

            val result = registerService.processRGDocumentFile(
                accountId = accountId,
                document = InputStream.nullInputStream(),
                frontSide = false,
                extension = imageFileExtension,
            )

            verify(exactly = 1) { documentOCRParser.parseDocument(any(), any(), any()) }
            verify(exactly = 0) { documentOCRParser.fetchDocumentQuality(any()) }

            result.isRight() shouldBe true
            result.map { accountRegisterData ->
                accountRegisterData.uploadedDocument.shouldNotBeNull()
                accountRegisterData.uploadedDocument!!.back.shouldNotBeNull()
                accountRegisterData.uploadedDocument!!.back!!.key.contains("_BACK")
                accountRegisterData.uploadedCNH.shouldBeNull()
                accountRegisterData.documentInfo.shouldNotBeNull()
                accountRegisterData.documentInfo!!.cpf shouldBe ""
                accountRegisterData.documentInfo!!.docType.value shouldBe "RG"
            }

            verify(exactly = 1) {
                crmServiceMock.upsertContact(any<AccountRegisterData>())
                accountRegisterRepository.saveOriginalOcrAndPersonData(accountId, any(), any(), any())
            }
        }
    }

    @Test
    fun `deve retornar erro se o usuário enviar a segunda imagem e nao existir a primeira armazenada`() {
        val result = withGivenImageOptimizer(ByteArrayOutputStream()) {
            registerService.processRGDocumentFile(
                accountId = accountId,
                document = InputStream.nullInputStream(),
                frontSide = false,
                extension = imageFileExtension,
            )
        }

        verify(exactly = 0) {
            documentOCRParser.parseDocument(any(), any(), any())
            documentOCRParser.fetchDocumentQuality(any())
        }

        result.isLeft() shouldBe true
        result.mapLeft { it.shouldBeTypeOf<InvalidDocumentImageException>() }
    }

    @Test
    fun `deve retornar sucesso e gravar a segunda imagem (sem apagar a primeira) quando o CPF for encontrado`() {
        val cpf = "***********"
        val docType = DocumentType.OTHER("QUALQUER_OUTRO")
        accountRegisterRepository.save(
            accountRegisterData.copy(
                accountId = accountId,
                uploadedCNH = StoredObject(
                    region = "",
                    bucket = "",
                    key = "",
                ),
            ),
        )
        every { documentOCRParser.parseDocument(any(), any(), any()) } answers {
            DocumentInfo(
                name = "",
                cpf = cpf,
                birthDate = null,
                fatherName = "",
                motherName = "",
                rg = "",
                docType = thirdArg(),
                cnhNumber = null,
                orgEmission = "",
                expeditionDate = null,
                birthCity = "",
                birthState = "",
            ).right()
        }

        val frontStoredObject = StoredObject(
            userFilesConfiguration.region,
            userFilesConfiguration.bucket,
            "${userFilesConfiguration.path}/${accountId.value}/${userFilesConfiguration.documentPrefix}",
        )

        every {
            objectRepository.loadObject(frontStoredObject.bucket, frontStoredObject.key)
        } returns InputStream.nullInputStream()

        val accountRegisterUpdated = accountRegisterRepository.findByAccountId(accountId).copy(
            uploadedDocument = UploadedDocumentImages(front = frontStoredObject, back = null, documentType = docType),
        )
        accountRegisterRepository.save(accountRegisterUpdated)

        val result = withGivenImageOptimizer(ByteArrayOutputStream()) {
            registerService.processRGDocumentFile(
                accountId = accountId,
                document = InputStream.nullInputStream(),
                frontSide = false,
                extension = imageFileExtension,
            )
        }

        verify(exactly = 0) { documentOCRParser.fetchDocumentQuality(any()) }

        verify(exactly = 1) {
            documentOCRParser.parseDocument(any(), any(), any())
        }

        result.isRight() shouldBe true
        result.map { accountRegisterData ->
            accountRegisterData.uploadedDocument.shouldNotBeNull()
            accountRegisterData.uploadedDocument!!.back.shouldNotBeNull()
            accountRegisterData.uploadedDocument!!.back!!.key.contains("_BACK")
            accountRegisterData.uploadedCNH.shouldBeNull()
            accountRegisterData.documentInfo.shouldNotBeNull()
            accountRegisterData.documentInfo!!.cpf shouldBe cpf
            accountRegisterData.documentInfo!!.docType.value shouldBe docType.value
        }

        verify(exactly = 1) {
            crmServiceMock.upsertContact(any<AccountRegisterData>())
            accountRegisterRepository.saveOriginalOcrAndPersonData(accountId, any(), any(), any())
        }
    }

    @Test
    fun `should return AccountRegisterData and save document information on database`() {
        mockkObject(ImageOptimizer)
        val originalByteArray = "[original-document-file]".toByteArray()
        val compressedByteArray = "[compressed-document-file]".toByteArray()
        val outputStream = ByteArrayOutputStream()
        outputStream.write(compressedByteArray)
        every { ImageOptimizer.optimize(any(), any()) } returns outputStream
        val compressedStoredObject = StoredObject(
            userFilesConfiguration.region,
            userFilesConfiguration.bucket,
            "${userFilesConfiguration.path}/${accountId.value}/${userFilesConfiguration.documentPrefix}",
        )
        every { documentOCRParser.parseDocument(any()) } returns Either.Right(cnhDocumentInfo)

        val accountRegisterBefore = accountRegisterRepository.findByAccountId(accountId)
        withGivenDateTime(getZonedDateTime().minusDays(1)) {
            val result = registerService.processCNHDocumentFile(
                accountId,
                ByteArrayInputStream(originalByteArray),
                imageFileExtension,
            )

            result.isRight() shouldBe true
            result.map { register ->
                register.documentInfo shouldBe cnhDocumentInfo
                with(accountRegisterRepository.findByAccountId(accountId)) {
                    documentInfo shouldBe cnhDocumentInfo
                    isDocumentEdited shouldBe false
                    calculatedGender shouldNotBe null
                    accountRegisterBefore.lastUpdated shouldNotBe lastUpdated
                    register.lastUpdated.format(dateTimeFormat) shouldBe lastUpdated.format(
                        dateTimeFormat,
                    )
                    register.uploadedCNH?.bucket shouldBe compressedStoredObject.bucket
                    register.uploadedCNH?.key shouldStartWith compressedStoredObject.key
                    register.uploadedCNH?.key shouldEndWith ".$imageFileExtension"
                    register.uploadedCNH?.region shouldBe compressedStoredObject.region

                    val slot = slot<AccountRegisterData>()
                    verify {
                        crmServiceMock.upsertContact(capture(slot))
                    }
                    slot.captured shouldBe register
                }
            }
        }
        val slots = mutableListOf<ByteArray>()
        verify(exactly = 1) {
            ImageOptimizer.optimize(any(), any())
        }

        verify(exactly = 2) {
            objectRepository.putObject(any(), capture(slots), any())
        }

        verify(exactly = 1) {
            crmServiceMock.upsertContact(any<AccountRegisterData>())
            accountRegisterRepository.saveOriginalOcrAndPersonData(accountId, any(), any(), any())
        }

        slots.map {
            String(it)
        } shouldContainInOrder listOf(String(originalByteArray), String(compressedByteArray))

        val item = DynamoDBUtils.getItem(dynamoDB, BILL_PAYMENT_TABLE_NAME, accountId.value, originalOcrKey)

        item.getRawMap("OriginalOcr") shouldNotBe null

        unmockkObject(ImageOptimizer)
    }

    @ParameterizedTest
    @MethodSource("cnhOrRgDocument")
    fun `Quando o nome do cliente, ou nome da mae pai for igual ao do validador deverá retornar o valor obtido no OCR`(
        cnhOrRGDocumentInfo: DocumentInfo,
    ) {
        every {
            bigDataService.getPersonBasicInfo(any())
        } returns PersonBasicInfo(
            name = cnhOrRGDocumentInfo.name,
            birthDate = cnhOrRGDocumentInfo.birthDate,
            motherName = cnhOrRGDocumentInfo.motherName,
            fatherName = cnhOrRGDocumentInfo.fatherName,
        ).right()

        val result = processCNHorBackRGDocumentFile(cnhOrRGDocumentInfo)

        result.isRight() shouldBe true
        result.map {
            it.documentInfo?.name shouldBe cnhOrRGDocumentInfo.name
            it.documentInfo?.motherName shouldBe cnhOrRGDocumentInfo.motherName
            it.documentInfo?.fatherName shouldBe cnhOrRGDocumentInfo.fatherName
            it.documentInfo?.birthDate shouldBe cnhOrRGDocumentInfo.birthDate
        }

        verify(exactly = 1) {
            bigDataService.getPersonBasicInfo(any())
        }

        result.map {
            it.validateValidatedDocumentInfo()
        }
    }

    @ParameterizedTest
    @MethodSource("cnhOrRgDocument")
    fun `quando os dados do validador forem diferentes dos dados obtidos via OCR os campos deverão ser desconsiderados`(
        cnhOrRGDocumentInfo: DocumentInfo,
    ) {
        every {
            bigDataService.getPersonBasicInfo(any())
        } returns PersonBasicInfo(
            name = "NOME DIFERENTE",
            birthDate = getZonedDateTime().toLocalDate(),
            motherName = "NOME DA MAE",
            fatherName = "NOME DO PAI",
        ).right()

        val result = processCNHorBackRGDocumentFile(
            cnhOrRGDocumentInfo,
        )

        result.isRight() shouldBe true
        result.map {
            it.documentInfo?.name shouldBe ""
            it.documentInfo?.motherName shouldBe ""
            it.documentInfo?.fatherName shouldBe ""
            it.documentInfo?.birthDate.shouldBeNull()
        }

        verify(exactly = 1) {
            bigDataService.getPersonBasicInfo(any())
        }

        result.map {
            it.validateValidatedDocumentInfo()
        }
    }

    @ParameterizedTest
    @MethodSource("cnhOrRgDocument")
    fun `quando o dado retornado pelo validador estiver contido no nome do cliente, ou nome da mae pai deverá ajustar o valor do nome lido no OCR para o do validador`(
        cnhOrRGDocumentInfo: DocumentInfo,
    ) {
        every {
            bigDataService.getPersonBasicInfo(any())
        } returns PersonBasicInfo(
            name = cnhOrRGDocumentInfo.name,
            birthDate = cnhOrRGDocumentInfo.birthDate,
            motherName = cnhOrRGDocumentInfo.motherName,
            fatherName = cnhOrRGDocumentInfo.fatherName,
        ).right()

        val result = processCNHorBackRGDocumentFile(
            cnhOrRGDocumentInfo.copy(
                name = "ALGO${cnhOrRGDocumentInfo.name}A MAIS AQUI",
                fatherName = "as vezes ${cnhOrRGDocumentInfo.fatherName} tem um lixo",
                motherName = "vindo ${cnhOrRGDocumentInfo.motherName} no campo",
            ),
        )

        result.isRight() shouldBe true
        result.map {
            it.documentInfo?.name shouldBe cnhOrRGDocumentInfo.name
            it.documentInfo?.motherName shouldBe cnhOrRGDocumentInfo.motherName
            it.documentInfo?.fatherName shouldBe cnhOrRGDocumentInfo.fatherName
            it.documentInfo?.birthDate shouldBe cnhOrRGDocumentInfo.birthDate
        }

        verify(exactly = 1) {
            bigDataService.getPersonBasicInfo(any())
        }

        result.map {
            it.validateValidatedDocumentInfo()
        }
    }

    @ParameterizedTest
    @MethodSource("cnhOrRgDocument")
    fun `quando a consulta a validação dos dados falhar o cadastro deve seguir validar apenas as datas de nascimento e emissao do documento`(
        cnhOrRGDocumentInfo: DocumentInfo,
        datesShouldBeNull: Boolean,
    ) {
        every {
            bigDataService.getPersonBasicInfo(any())
        } returns NoStackTraceException().left()

        val result = processCNHorBackRGDocumentFile(cnhOrRGDocumentInfo)

        result.isRight() shouldBe true
        result.map {
            it.documentInfo?.name shouldBe cnhOrRGDocumentInfo.name
            it.documentInfo?.motherName shouldBe cnhOrRGDocumentInfo.motherName
            it.documentInfo?.fatherName shouldBe cnhOrRGDocumentInfo.fatherName
            if (datesShouldBeNull) {
                it.documentInfo?.birthDate.shouldBeNull()
            } else {
                it.documentInfo?.birthDate shouldBe cnhOrRGDocumentInfo.birthDate
            }
        }

        verify(exactly = 1) {
            bigDataService.getPersonBasicInfo(any())
        }

        result.map {
            it.validateValidatedDocumentInfo()
        }
    }

    @ParameterizedTest
    @MethodSource("cnhOrRgDocument")
    fun `deve retornar o gênero do BigBoost quando for um gênero conhecido`(cnhOrRGDocumentInfo: DocumentInfo) {
        every {
            bigDataService.getPersonBasicInfo(any())
        } returns PersonBasicInfo(
            name = cnhOrRGDocumentInfo.name,
            birthDate = cnhOrRGDocumentInfo.birthDate,
            motherName = cnhOrRGDocumentInfo.motherName,
            fatherName = cnhOrRGDocumentInfo.fatherName,
            gender = "F",
        ).right()

        val result = processCNHorBackRGDocumentFile(cnhOrRGDocumentInfo)

        result.isRight() shouldBe true
        result.map {
            it.calculatedGender shouldBe Gender.F
        }
    }

    @ParameterizedTest
    @MethodSource("cnhOrRgDocument")
    fun `deve retornar genero baseado pelo nome quando o BigBoost nao conhecer o genero`(cnhOrRGDocumentInfo: DocumentInfo) {
        every {
            bigDataService.getPersonBasicInfo(any())
        } returns PersonBasicInfo(
            name = cnhOrRGDocumentInfo.name,
            birthDate = cnhOrRGDocumentInfo.birthDate,
            motherName = cnhOrRGDocumentInfo.motherName,
            fatherName = cnhOrRGDocumentInfo.fatherName,
            gender = "U",
        ).right()

        val result = processCNHorBackRGDocumentFile(cnhOrRGDocumentInfo)

        result.isRight() shouldBe true
        result.map {
            it.calculatedGender shouldBe Gender.M
        }
    }

    @ParameterizedTest
    @MethodSource("cnhOrRgDocument")
    fun `deve retornar genero baseado pelo nome quando o BigBoost nao devolver um genero`(cnhOrRGDocumentInfo: DocumentInfo) {
        every {
            bigDataService.getPersonBasicInfo(any())
        } returns PersonBasicInfo(
            name = cnhOrRGDocumentInfo.name,
            birthDate = cnhOrRGDocumentInfo.birthDate,
            motherName = cnhOrRGDocumentInfo.motherName,
            fatherName = cnhOrRGDocumentInfo.fatherName,
            gender = null,
        ).right()

        val result = processCNHorBackRGDocumentFile(cnhOrRGDocumentInfo)

        result.isRight() shouldBe true
        result.map {
            it.calculatedGender shouldBe Gender.M
        }
    }

    @Test
    fun `deve ignorar data de expedição no futuro`() {
        val originalDocumentInfo = cnhDocumentInfo.copy(expeditionDate = getLocalDate().plusDays(1))
        val basicInfo = PersonBasicInfo(
            name = null,
            birthDate = null,
            motherName = null,
            fatherName = null,
            gender = null,
        )

        val validatedDocumentInfo = registerService.validateOcrResult(originalDocumentInfo, basicInfo)

        validatedDocumentInfo.expeditionDate shouldBe null
    }

    @Test
    fun `deve ignorar data de expedição com mais de 70 anos`() {
        val originalDocumentInfo = cnhDocumentInfo.copy(expeditionDate = getLocalDate().minusYears(70).minusDays(1))
        val basicInfo = PersonBasicInfo(
            name = null,
            birthDate = null,
            motherName = null,
            fatherName = null,
            gender = null,
        )

        val validatedDocumentInfo = registerService.validateOcrResult(originalDocumentInfo, basicInfo)

        validatedDocumentInfo.expeditionDate shouldBe null
    }

    @Test
    fun `deve manter uma data de expedição válida`() {
        val originalDocumentInfo = cnhDocumentInfo
        val basicInfo = PersonBasicInfo(
            name = null,
            birthDate = null,
            motherName = null,
            fatherName = null,
            gender = null,
        )

        val validatedDocumentInfo = registerService.validateOcrResult(originalDocumentInfo, basicInfo)

        validatedDocumentInfo.expeditionDate shouldBe originalDocumentInfo.expeditionDate
    }

    private fun processCNHorBackRGDocumentFile(documentOcrData: DocumentInfo): Either<Exception, AccountRegisterData> {
        mockkObject(ImageOptimizer)
        every { ImageOptimizer.optimize(any(), any()) } returns ByteArrayOutputStream(0)
        return if (documentOcrData.cnhNumber != null) {
            every {
                documentOCRParser.parseDocument(any())
            } returns documentOcrData.right()

            registerService.processCNHDocumentFile(
                accountId,
                ByteArrayInputStream(InputStream.nullInputStream().readAllBytes()),
                imageFileExtension,
            )
        } else {
            every {
                documentOCRParser.parseDocument(any(), any(), any())
            } returns documentOcrData.right()

            val frontStoredObject = StoredObject(
                userFilesConfiguration.region,
                userFilesConfiguration.bucket,
                "${userFilesConfiguration.path}/${accountId.value}/${userFilesConfiguration.documentPrefix}",
            )

            every {
                objectRepository.loadObject(frontStoredObject.bucket, frontStoredObject.key)
            } returns InputStream.nullInputStream()

            val accountRegisterUpdated = accountRegisterRepository.findByAccountId(accountId).copy(
                uploadedDocument = UploadedDocumentImages(
                    front = frontStoredObject,
                    documentType = DocumentType.RG,
                    back = null,
                ),
            )
            accountRegisterRepository.save(accountRegisterUpdated)

            registerService.processRGDocumentFile(
                accountId,
                ByteArrayInputStream(InputStream.nullInputStream().readAllBytes()),
                false,
                imageFileExtension,
            )
        }
    }

    private fun AccountRegisterData.validateValidatedDocumentInfo() {
        val slot = slot<DocumentInfo>()
        verify {
            accountRegisterRepository.saveOriginalOcrAndPersonData(
                accountId = any(),
                originalDocumentInfo = any(),
                personBasicInfo = any(),
                validatedDocumentInfo = capture(slot),
            )
        }

        val documentInfo = slot.captured
        documentInfo.expeditionDate shouldBe this.documentInfo?.expeditionDate
        documentInfo.docType shouldBe this.documentInfo?.docType
        documentInfo.name shouldBe this.documentInfo?.name
        documentInfo.cpf shouldBe this.documentInfo?.cpf
        documentInfo.birthCity shouldBe this.documentInfo?.birthCity
        documentInfo.birthDate shouldBe this.documentInfo?.birthDate
        documentInfo.birthState shouldBe this.documentInfo?.birthState
        documentInfo.cnhNumber shouldBe this.documentInfo?.cnhNumber
        documentInfo.fatherName shouldBe this.documentInfo?.fatherName
        documentInfo.motherName shouldBe this.documentInfo?.motherName
        documentInfo.orgEmission shouldBe this.documentInfo?.orgEmission
        documentInfo.rg shouldBe this.documentInfo?.rg
    }

    @ParameterizedTest
    @MethodSource("pdfUploadedCNH")
    fun `should return AccountRegisterData and save PDF information on database`(
        pdfToImageData: BufferedImage,
        shouldOptimize: Boolean,
    ) {
        mockkObject(ImageOptimizer)
        accountRegisterRepository.save(
            accountRegisterData.copy(
                accountId = accountId,
                uploadedDocument = UploadedDocumentImages(
                    front = StoredObject(region = "", bucket = "", key = ""),
                    documentType = DocumentType.RG,
                    back = StoredObject(region = "", bucket = "", key = ""),
                ),
            ),
        )
        val expectedByteArray = "[compressed-document-file]".toByteArray()
        val storedObject = StoredObject(
            userFilesConfiguration.region,
            userFilesConfiguration.bucket,
            "${userFilesConfiguration.path}/${accountId.value}/${userFilesConfiguration.documentPrefix}",
        )
        every { documentOCRParser.parseDocument(any()) } returns Either.Right(cnhDocumentInfo)
        val expectedImage = Optional.of(pdfToImageData)
        every { pdfFileParser.parseToImage(any(), any()) } returns expectedImage

        val accountRegisterBefore = accountRegisterRepository.findByAccountId(accountId)
        withGivenDateTime(getZonedDateTime().minusDays(1)) {
            val result = registerService.processCNHDocumentFile(
                accountId,
                ByteArrayInputStream(expectedByteArray),
                pdfFileExtension,
            )

            result.isRight() shouldBe true
            result.map { register ->
                register.documentInfo shouldBe cnhDocumentInfo
                with(accountRegisterRepository.findByAccountId(accountId)) {
                    documentInfo shouldBe cnhDocumentInfo
                    isDocumentEdited shouldBe false
                    calculatedGender shouldNotBe null
                    accountRegisterBefore.lastUpdated shouldNotBe lastUpdated
                    register.lastUpdated.format(dateTimeFormat) shouldBe lastUpdated.format(
                        dateTimeFormat,
                    )
                    register.uploadedCNH?.bucket shouldBe storedObject.bucket
                    register.uploadedCNH?.key shouldStartWith storedObject.key
                    register.uploadedCNH?.key shouldEndWith ".jpeg"
                    register.uploadedCNH?.region shouldBe storedObject.region
                    register.uploadedDocument.shouldBeNull()

                    val slot = slot<AccountRegisterData>()
                    verify {
                        crmServiceMock.upsertContact(capture(slot))
                    }
                    slot.captured shouldBe register
                }
            }
        }

        verify(exactly = 1) {
            pdfFileParser.parseToImage(any(), any())
        }
        val inputStreamSlot = mutableListOf<ByteArray>()
        val storedObjectSlot = mutableListOf<StoredObject>()
        verify(exactly = 2) {
            objectRepository.putObject(capture(storedObjectSlot), capture(inputStreamSlot), any())
        }

        if (shouldOptimize) {
            verify(exactly = 1) {
                ImageOptimizer.optimize(any(), any())
            }
        } else {
            verify(exactly = 0) {
                ImageOptimizer.optimize(any(), any())
            }
        }

        storedObjectSlot.last().key shouldEndWith "jpeg"
        storedObjectSlot.first().key shouldEndWith "pdf"

        String(inputStreamSlot.first()) shouldBe String(expectedByteArray)
        unmockkObject(ImageOptimizer)
    }

    /*
        @Test
        @Disabled("estamos otimizando sempre")
        fun `should return AccountRegisterData and save IMAGE information on database without optimization`() {
            mockkObject(ImageOptimizer)
            val expectedByteArray = "[small]".toByteArray()
            val storedObject = StoredObject(
                userFilesConfiguration.region,
                userFilesConfiguration.bucket,
                "${userFilesConfiguration.path}/${accountId.value}/${userFilesConfiguration.documentPrefix}",
            )
            every { documentOCRParser.parseDocument(any()) } returns Either.Right(cnhDocumentInfo)

            val accountRegisterBefore = accountRegisterRepository.findByAccountId(accountId)
            withGivenDateTime(getZonedDateTime().minusDays(1)) {
                val result = registerService.processCNHDocumentFile(
                    accountId,
                    ByteArrayInputStream(expectedByteArray),
                    imageFileExtension,
                )

                result.isRight() shouldBe true
                result.map { resultData ->
                    resultData.documentInfo shouldBe cnhDocumentInfo
                    with(accountRegisterRepository.findByAccountId(accountId)) {
                        documentInfo shouldBe cnhDocumentInfo
                        isDocumentEdited shouldBe false
                        calculatedGender shouldBe Gender.F
                        accountRegisterBefore.lastUpdated shouldNotBe lastUpdated
                        resultData.lastUpdated shouldBe lastUpdated
                        resultData.uploadedCNH?.bucket shouldBe storedObject.bucket
                        resultData.uploadedCNH?.key shouldStartWith storedObject.key
                        resultData.uploadedCNH?.key shouldEndWith ".$imageFileExtension"
                        resultData.uploadedCNH?.region shouldBe storedObject.region
                    }
                }
            }
            val slot = slot<InputStream>()
            verify(exactly = 1) {
                objectRepository.putObject(any(), capture(slot), any())
            }
            verify(exactly = 0) {
                ImageOptimizer.optimize(any(), any())
            }
            String(slot.captured.readAllBytes()) shouldBe String(expectedByteArray)
            unmockkObject(ImageOptimizer)
        }
    */

    @Test
    fun `should return left and not call document OCR neither save to database`() {
        every { objectRepository.putObject(any(), any(), any()) } throws Exception()

        val result =
            registerService.processCNHDocumentFile(accountId, InputStream.nullInputStream(), imageFileExtension)
        result.isLeft() shouldBe true

        verify {
            documentOCRParser wasNot called
        }

        with(accountRegisterRepository.findByAccountId(accountId)) {
            documentInfo shouldBe null
            isDocumentEdited shouldBe false
            calculatedGender shouldBe Gender.F
        }
    }

    @Test
    fun `should return left AccountIsBlockedForEdition when uploading document and user is not allowed to change data`() {
        accountRepository.updatePartialAccountStatus(accountId, AccountStatus.UNDER_REVIEW)

        val result =
            registerService.processCNHDocumentFile(accountId, InputStream.nullInputStream(), imageFileExtension)
        result.isLeft() shouldBe true
        result.mapLeft { it.shouldBeTypeOf<AccountIsBlockedForEdition>() }
    }

    @Test
    fun `should return mobilePhone and expiration when token exists`() {
        val expiration = getZonedDateTime().plusMinutes(1).toEpochSecond()
        every {
            tokenService.findIssuedToken(
                any(),
                any(),
            )
        } returns TokenDataWithExpiration(
            TokenData.of(MobilePhone("+*************"), TokenChannel.SMS),
            expiration,
        ).right()
        val result = registerService.findByAccountId(accountId)
        result.isRight() shouldBe true
        result.map {
            it.mobilePhone shouldBe MobilePhone("+*************")
            it.mobilePhoneVerified shouldBe false
            it.mobilePhoneTokenExpiration shouldBe expiration
        }
    }

    @Test
    fun `should return accountRegister when token does not exists`() {
        accountRegisterRepository.save(accountRegisterDataWithPhoneVerified.copy(accountId = accountId))
        every {
            tokenService.findIssuedToken(
                any(),
                any(),
            )
        } returns InvalidTokenException(reason = InvalidTokenReason.NOT_FOUND).left()
        val result = registerService.findByAccountId(accountId)
        result.isRight() shouldBe true
        result.map {
            it.mobilePhone shouldBe accountRegisterDataWithPhoneVerified.mobilePhone
            it.mobilePhoneVerified shouldBe accountRegisterDataWithPhoneVerified.mobilePhoneVerified
            it.mobilePhoneTokenExpiration shouldBe null
        }
    }

    @Test
    fun `quando for um usuário ativo fazendo upgrade, deve salvar dados do documento no banco`() {
        val account = ACCOUNT.copy(
            accountId = AccountId("upgrade-account-id"),
            status = AccountStatus.ACTIVE,
            type = UserAccountType.BASIC_ACCOUNT,
        )

        accountRepository.save(account)
        accountRegisterRepository.save(accountRegisterData.copy(accountId = account.accountId))

        val result = withGivenImageOptimizer(ByteArrayOutputStream()) {
            registerService.processCNHDocumentFile(
                accountId = account.accountId,
                document = InputStream.nullInputStream(),
                extension = imageFileExtension,
                ocr = false,
            )
        }

        verify { accountRegisterRepository.save(any()) }
        result.isRight() shouldBe true
    }

    @Test
    fun `quando for um usuário full, não deve salvar os dados do documento no banco`() {
        val account = ACCOUNT.copy(
            accountId = AccountId("full-account-id"),
            status = AccountStatus.ACTIVE,
            type = UserAccountType.FULL_ACCOUNT,
        )

        accountRepository.save(account)

        // o BeforeEach chama esse mock, o que altera o contador de chamadas dele
        clearMocks(accountRegisterRepository)

        val result = withGivenImageOptimizer(ByteArrayOutputStream()) {
            registerService.processCNHDocumentFile(
                accountId = account.accountId,
                document = InputStream.nullInputStream(),
                extension = imageFileExtension,
                ocr = false,
            )
        }

        verify(exactly = 0) { accountRegisterRepository.save(any()) }
        result.mapLeft { it.shouldBeTypeOf<AccountIsBlockedForEdition>() }
    }

    @Test
    fun `quando for um usuário basic e o status de upgrade for diferente de incompleto, não deve salvar os dados do documento no banco`() {
        val account = ACCOUNT.copy(
            accountId = AccountId("upgrade-under-review-account-id"),
            status = AccountStatus.ACTIVE,
            upgradeStatus = UpgradeStatus.UNDER_REVIEW,
            type = UserAccountType.BASIC_ACCOUNT,
        )

        accountRepository.save(account)

        // o BeforeEach chama esse mock, o que altera o contador de chamadas dele
        clearMocks(accountRegisterRepository)

        val result = withGivenImageOptimizer(ByteArrayOutputStream()) {
            registerService.processCNHDocumentFile(
                accountId = account.accountId,
                document = InputStream.nullInputStream(),
                extension = imageFileExtension,
                ocr = false,
            )
        }

        verify(exactly = 0) { accountRegisterRepository.save(any()) }
        result.mapLeft { it.shouldBeTypeOf<AccountIsBlockedForEdition>() }
    }

    @Test
    fun `deve atualizar o document info quando editar os detalhes do documento`() {
        accountRegisterRepository.save(
            accountRegisterCompleted.copy(
                accountId = accountId,
                document = Document("***********"),
            ),
        )

        val documentDetails = DocumentDetails(
            documentNumber = DocumentNumber(
                type = DocumentDetailsType.DRIVERS_LICENSE,
                value = "********",
            ),
            motherName = "Nome da mae",
            fatherName = "Nome do pai",
            birthCity = "Rio de Janeiro",
            birthState = "RJ",
            orgEmission = "DETRAN",
            expeditionDate = LocalDate.of(2016, 5, 10),
        )

        val result = registerService.processDocumentDetails(accountId, documentDetails)

        result.isRight() shouldBe true
        result.map {
            it.documentInfo!!.motherName shouldBe documentDetails.motherName
            it.documentInfo!!.fatherName shouldBe documentDetails.fatherName
            it.documentInfo!!.birthCity shouldBe documentDetails.birthCity
            it.documentInfo!!.birthState shouldBe documentDetails.birthState
            it.documentInfo!!.orgEmission shouldBe documentDetails.orgEmission
            it.documentInfo!!.expeditionDate shouldBe documentDetails.expeditionDate
        }
    }

    @Test
    fun `deve atualizar o monthly income quando a renda for enviada`() {
        accountRegisterRepository.save(
            accountRegisterCompleted.copy(
                accountId = accountId,
                document = Document("***********"),
            ),
        )

        val monthlyIncome = MonthlyIncome(lowerBound = 4_000_01L, upperBound = 10_000_00L)

        val result = registerService.processMonthlyIncome(accountId, monthlyIncome)

        result.isRight() shouldBe true
        result.map {
            it.monthlyIncome!!.lowerBound shouldBe monthlyIncome.lowerBound
            it.monthlyIncome!!.upperBound shouldBe monthlyIncome.upperBound
        }
    }

    @Test
    fun `deve atualizar UpgradeStatus e data de upgrade para usuário que não possui`() {
        val account = ACCOUNT.copy(
            accountId = AccountId("basic-account-id"),
            status = AccountStatus.ACTIVE,
            type = UserAccountType.BASIC_ACCOUNT,
            upgradeStatus = null,
        )

        accountRepository.save(account)

        accountRegisterRepository.save(
            basicAccountRegisterData.copy(
                accountId = account.accountId,
                upgradeStarted = null,
            ),
        )

        val timestamp = ZonedDateTime.parse("2023-01-02 03:04:05", dateTimeFormat)
        withGivenDateTime(timestamp) {
            registerService.startUpgrade(account.accountId)

            val savedRegisterData = accountRegisterRepository.findByAccountId(account.accountId)
            val savedAccount = accountRepository.findById(account.accountId)

            savedRegisterData.upgradeStarted shouldBe timestamp
            savedAccount.upgradeStatus shouldBe UpgradeStatus.INCOMPLETE
        }
    }

    @ParameterizedTest
    @MethodSource("upgradeStatusStarted")
    fun `não deve atualizar UpgradeStatus e data de upgrade para usuário que já iniciou o upgrade`(status: UpgradeStatus) {
        val account = ACCOUNT.copy(
            accountId = AccountId("basic-account-id"),
            status = AccountStatus.ACTIVE,
            type = UserAccountType.BASIC_ACCOUNT,
            upgradeStatus = status,
        )

        accountRepository.save(account)

        val timestamp = ZonedDateTime.parse("2023-01-02 03:04:05", dateTimeFormat)

        accountRegisterRepository.save(
            basicAccountRegisterData.copy(
                accountId = account.accountId,
                upgradeStarted = timestamp,
            ),
        )

        registerService.startUpgrade(account.accountId)

        val savedRegisterData = accountRegisterRepository.findByAccountId(account.accountId)
        val savedAccount = accountRepository.findById(account.accountId)

        savedRegisterData.upgradeStarted shouldBe timestamp
        savedAccount.upgradeStatus shouldBe status
    }

    @Test
    fun `deve gerar o ugrade agreement quando o usuário estiver pronto para aceitar os termos`() {
        val account = ACCOUNT.copy(
            accountId = AccountId("basic-account-id-upgrade-incomplete"),
            status = AccountStatus.ACTIVE,
            type = UserAccountType.BASIC_ACCOUNT,
            upgradeStatus = UpgradeStatus.INCOMPLETE,
        )

        accountRepository.save(account)

        accountRegisterRepository.save(
            accountRegisterCompleted.copy(
                accountId = account.accountId,
                upgradeStarted = getZonedDateTime(),
                agreementData = accountRegisterCompleted.agreementData?.copy(declarationOfResidencyFile = null),
                upgradeAgreementData = null,
            ),
        )

        val agreementData = registerService.updateAgreementFiles(account.accountId, "123")
        val accountRegister = accountRegisterRepository.findByAccountId(account.accountId)

        agreementData?.declarationOfResidencyFile shouldNotBe null
        accountRegister.upgradeAgreementData shouldNotBe null
    }

    @Nested
    @DisplayName("Teste de armazenamento dos dados de duplicação no account register data pelo processo de liveness")
    inner class LivenessDuplicationTest {

        val userContractSignature = accountRegisterMissingOnlyAgreement.toContractForm().generateSignatureKey()

        val registerServiceWithMocks =
            RegisterService(
                tokenService = mockk(),
                accountRegisterRepository = accountRegisterRepository,
                accountService = accountService,
                accountRegisterService = accountRegisterService,
                documentOCRParser = documentOCRParser,
                userFilesConfiguration = userFilesConfiguration,
                agreementFilesService = mockk {
                    every {
                        getContractSignature(any())
                    } returns userContractSignature
                    every {
                        createContract(any(), any(), any())
                    } just runs
                },
                notificationSenderService = emailSenderService,
                ddaService = mockk(),
                notificationAdapter = mockk(),
                userPoolAdapter = mockk(),
                kycService = mockk(),
                externalAccountRegister = mockk(),
                ecmProvider = mockk(),
                walletService = mockk(),
                walletLimitsService = mockk(),
                crmService = mockk(),
                registerInstrumentationService = mockk(),
                pdfFileParser = pdfFileParser,
                pendingInternalApproveConfiguration = mockk(),
                pendingInternalReviewConfiguration = mockk(),
                pendingActivationConfiguration = mockk(),
                accountStatusLockProvider = mockk(),
                bigDataService = mockk(),
                userJourneyService = mockk(relaxUnitFun = true),
                closeAccountService = mockk(relaxed = true),
                systemActivityService = mockk(),
                livenessService = livenessService,
                eventPublisher = eventPublisher,
                fraudList = mockk(relaxed = true),
                pendingUpgradeInternalApproveConfiguration = mockk(relaxed = true),
                messagePublisher = mockk(),
                adService = mockk(relaxed = true),
                chatBotNotificationService = mockk(relaxed = true),
                walletBillCategoryService = mockk(relaxed = true),
                documentScanService = documentScanServiceMock,
                activityService = mockk(relaxed = true),
                loginRepository = loginRepository,
            )

        @BeforeEach
        fun beforeEach() {
            accountRegisterRepository.save(
                accountRegisterCompleted.copy(
                    accountId = accountId,
                    documentInfo = rgDocumentInfo,
                    agreementData = AgreementData(
                        acceptedAt = null,
                        userContractFile = StoredObject("region", "bucket", "userContract.pdf"),
                        userContractSignature = userContractSignature,
                        declarationOfResidencyFile = StoredObject("region", "bucket", "declarationOfResidencyFile.pdf"),
                    ),
                    livenessId = LivenessId("123"),
                ),
            )

            accountRegisterRepository.save(
                accountRegisterCompleted.copy(
                    accountId = otherAccountId,
                    documentInfo = cnhDocumentInfo,
                    livenessId = null,
                    agreementData = AgreementData(
                        acceptedAt = null,
                        userContractFile = StoredObject("region", "bucket", "userContract.pdf"),
                        userContractSignature = userContractSignature,
                        declarationOfResidencyFile = StoredObject("region", "bucket", "declarationOfResidencyFile.pdf"),
                    ),
                ),
            )
        }

        @Test
        internal fun `deve salvar os dados da verificação de duplicação no account register`() {
            every {
                livenessService.verifyDuplication(any())
            } returns LivenessEnrollmentVerification(
                duplications = LivenessEnrollmentVerification.Result.Verified(
                    listOf(otherAccountId),
                ),
            ).right()

            val result = registerServiceWithMocks.processAgreement(accountId, "127.0.0.1")

            result.isRight() shouldBe true

            with(accountRegisterRepository.findByAccountId(accountId)) {
                livenessEnrollmentVerification shouldNotBe null
                livenessEnrollmentVerification.duplications shouldBe LivenessEnrollmentVerification.Result.Verified(
                    listOf(otherAccountId),
                )
            }
        }

        @Test
        internal fun `não deve salvar nada caso aconteça algum erro na consulta de verificação`() {
            every {
                livenessService.verifyDuplication(any())
            } returns Either.Left(LivenessErrors.DuplicationCheckUnavailable)

            val result = registerServiceWithMocks.processAgreement(accountId, "127.0.0.1")

            result.isRight() shouldBe true

            with(accountRegisterRepository.findByAccountId(accountId)) {
                livenessEnrollmentVerification.duplications shouldBe LivenessEnrollmentVerification.Result.NotVerified
            }
        }

        @Test
        internal fun `não deve fazer a requisição de verificação de duplicação caso o usuário não tenha liveness id`() {
            val result = registerServiceWithMocks.processAgreement(otherAccountId, "127.0.0.1")

            result.isRight() shouldBe true

            verify(exactly = 0) {
                livenessService.verifyDuplication(any())
            }
        }
    }

    @Nested
    @DisplayName("Teste da captura da selfie pelo processo do liveness")
    inner class LivenessSelfieTest {

        @Test
        fun `deve retornar o proprio registro quando não houver livenessId`() {
            val response = registerService.processLiveness(accountRegisterDataWithAddress.copy(accountId = accountId))

            response.isRight().shouldBeTrue()
            response.map {
                it.uploadedSelfie.shouldBeNull()
            }
            verify(exactly = 0) {
                livenessService.retrieveEnrollmentSelfie(any())
                accountRegisterRepository.putDocument(any(), any(), any())
            }
            accountRegisterRepository.findByAccountId(accountId).uploadedSelfie.shouldBeNull()
        }

        @Test
        fun `deve retornar erro quando a consulta a foto falhar`() {
            every {
                livenessService.retrieveEnrollmentSelfie(any())
            } returns LivenessSelfieError.Error(NoStackTraceException("")).left()

            val response = registerService.processLiveness(
                accountRegisterDataWithAddress.copy(
                    accountId = accountId,
                    livenessId = LivenessId("FAKE"),
                ),
            )

            response.isLeft().shouldBeTrue()
            verify(exactly = 0) {
                accountRegisterRepository.putDocument(any(), any(), any())
            }
            accountRegisterRepository.findByAccountId(accountId).uploadedSelfie.shouldBeNull()
        }

        @Test
        fun `deve retornar o proprio registro quando ainda não tiver foto`() {
            every {
                livenessService.retrieveEnrollmentSelfie(any())
            } returns LivenessSelfieError.Unavailable.left()

            val response = registerService.processLiveness(
                accountRegisterDataWithAddress.copy(
                    accountId = accountId,
                    livenessId = LivenessId("FAKE"),
                ),
            )

            response.isRight().shouldBeTrue()
            response.map {
                it.uploadedSelfie.shouldBeNull()
            }
            verify(exactly = 0) {
                accountRegisterRepository.putDocument(any(), any(), any())
            }
            accountRegisterRepository.findByAccountId(accountId).uploadedSelfie.shouldBeNull()
        }

        @Test
        fun `deve retornar o proprio registro quando a foto já estiver salva`() {
            val response = registerService.processLiveness(
                accountRegisterDataWithAddress.copy(
                    accountId = accountId,
                    livenessId = LivenessId("FAKE"),
                    uploadedSelfie = StoredObject("", "", ""),
                ),
            )

            response.isRight().shouldBeTrue()
            response.map {
                it.uploadedSelfie.shouldNotBeNull()
            }
            verify(exactly = 0) {
                accountRegisterRepository.putDocument(any(), any(), any())
                livenessService.retrieveEnrollmentSelfie(any())
            }
        }

        @Test
        fun `deve retornar a foto e salvar no S3 e salvar no banco a referencia para a foto`() {
            every {
                livenessService.retrieveEnrollmentSelfie(any())
            } returns "DADOS_DA_SELFIE".toByteArray().right()

            every {
                livenessService.verifyDuplication(any())
            } returns LivenessEnrollmentVerification(duplications = LivenessEnrollmentVerification.Result.NotVerified).right()

            val response = registerService.processLiveness(
                accountRegisterDataWithAddress.copy(
                    accountId = accountId,
                    livenessId = LivenessId("FAKE"),
                ),
            )

            response.isRight().shouldBeTrue()

            response.map {
                it.uploadedSelfie.shouldNotBeNull()
            }

            verify(exactly = 1) {
                accountRegisterRepository.save(
                    withArg { arg ->
                        arg.uploadedSelfie.shouldNotBeNull()
                    },
                )
                accountRegisterRepository.putDocument(any(), any(), any())
            }
        }
    }

    @Nested
    @DisplayName("ao negar um conta")
    inner class DenyRegister {

        lateinit var underReviewAccountId: AccountId

        val userContractSignature = accountRegisterMissingOnlyAgreement.toContractForm().generateSignatureKey()
        private val registerServiceWithMocks =
            RegisterService(
                tokenService = mockk(),
                accountRegisterRepository = accountRegisterRepository,
                accountService = accountService,
                accountRegisterService = accountRegisterService,
                documentOCRParser = documentOCRParser,
                userFilesConfiguration = userFilesConfiguration,
                agreementFilesService = mockk {
                    every {
                        getContractSignature(any())
                    } returns userContractSignature
                    every {
                        createContract(any(), any(), any())
                    } just runs
                },
                notificationSenderService = mockk() {
                    every { sendRawEmail(any(), any(), any(), any()) } just Runs
                    every { sendRawEmail(any(), any(), any(), any(), any()) } just Runs
                },
                ddaService = mockk(),
                notificationAdapter = mockk(),
                userPoolAdapter = mockk(),
                kycService = mockk() {
                    every { generate(any()) } returns Pair(
                        StoredObject(
                            region = "us-east-1",
                            bucket = "",
                            key = "",
                        ),
                        buildKycDossier(),
                    ).right()
                },
                externalAccountRegister = mockk(),
                ecmProvider = mockk(),
                walletService = mockk(),
                walletLimitsService = mockk(),
                crmService = mockk(),
                registerInstrumentationService = registerInstrumentationService,
                pdfFileParser = pdfFileParser,
                pendingInternalApproveConfiguration = NewAccountEmailConfiguration(
                    name = "teste",
                    recipients = "teste@test",
                    sensitiveRecipients = "teste",
                    subject = "teste",
                    message = "teste",
                ),
                pendingInternalReviewConfiguration = mockk(),
                pendingActivationConfiguration = NewAccountEmailConfiguration(
                    name = "",
                    recipients = "",
                    sensitiveRecipients = "",
                    subject = "",
                    message = "",
                ),
                accountStatusLockProvider = mockk(),
                bigDataService = mockk(),
                userJourneyService = mockk(relaxUnitFun = true),
                closeAccountService = closeAccountService,
                systemActivityService = mockk(),
                livenessService = livenessService,
                eventPublisher = eventPublisher,
                fraudList = mockk(relaxed = true),
                pendingUpgradeInternalApproveConfiguration = mockk(relaxed = true),
                messagePublisher = mockk(),
                adService = mockk(relaxed = true),
                chatBotNotificationService = mockk(relaxed = true),
                walletBillCategoryService = mockk(relaxed = true),
                documentScanService = documentScanServiceMock,
                activityService = mockk(relaxed = true),
                loginRepository = loginRepository,
            ).apply { from = "test" }

        @BeforeEach
        fun setup() {
            every { notificationAdapter.notifyRegisterDenied(any(), any()) } just Runs
            every { registerInstrumentationService.rejected(any(), any(), any()) } just Runs
            val underReviewAccount = accountRepository.create(
                "under_review_user",
                EmailAddress("<EMAIL>"),
                registrationType = RegistrationType.UPGRADED,
            )

            underReviewAccountId = underReviewAccount.id

            accountRegisterRepository.save(accountRegisterCompleted.copy(accountId = underReviewAccount.id))

            registerServiceWithMocks.processAgreement(underReviewAccountId, "*********")
        }
    }

    companion object {
        @JvmStatic
        fun cnhOrRgDocument(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(cnhDocumentInfo, false),
                Arguments.of(rgDocumentInfo, false),
                Arguments.of(
                    cnhDocumentInfo.copy(
                        birthDate = getLocalDate().plusDays(10),
                        expeditionDate = getLocalDate().plusMonths(10),
                    ),
                    true,
                ),
                Arguments.of(
                    rgDocumentInfo.copy(
                        birthDate = getLocalDate().plusDays(10),
                        expeditionDate = getLocalDate().plusMonths(10),
                    ),
                    true,
                ),
            )
        }

        @JvmStatic
        fun pdfUploadedCNH(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(BufferedImage(1, 1, 1), false),
                Arguments.of(
                    ImageIO.read(FileInputStream(Thread.currentThread().contextClassLoader.getResource("images/large_cnh_1MB.jpeg").path)),
                    true,
                ),
            )
        }

        @JvmStatic
        fun upgradeStatusStarted(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(UpgradeStatus.INCOMPLETE),
                Arguments.of(UpgradeStatus.UNDER_REVIEW),
                Arguments.of(UpgradeStatus.DENIED),
                Arguments.of(UpgradeStatus.COMPLETED),
            )
        }
    }
}