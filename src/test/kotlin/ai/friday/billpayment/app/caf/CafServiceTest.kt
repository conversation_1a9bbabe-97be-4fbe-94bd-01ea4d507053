package ai.friday.billpayment.app.caf

import arrow.core.left
import arrow.core.right
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.time.LocalDateTime
import org.junit.jupiter.api.Test

class CafServiceTest {

    private val cafAdapter = mockk<CafAdapterInterface>()
    private val cafService = CafService(cafAdapter)

    @Test
    fun `createTransaction should return success when adapter succeeds`() {
        val templateId = "template123"
        val files = listOf(
            FileData(data = "base64data", type = "SELFIE"),
            FileData(data = "base64document", type = "DOCUMENT_FRONT"),
        )
        val expectedResponse = CreateTransactionResponse(
            requestId = "request123",
            id = "transaction456",
            status = "PENDING",
        )

        every { cafAdapter.createTransaction(templateId, files) } returns expectedResponse.right()

        val result = cafService.createTransaction(templateId, files)

        result shouldBe expectedResponse.right()
        verify { cafAdapter.createTransaction(templateId, files) }
    }

    @Test
    fun `createTransaction should return error when adapter fails`() {
        val templateId = "template123"
        val files = listOf(FileData(data = "base64data", type = "SELFIE"))
        val expectedException = RuntimeException("CAF API error")

        every { cafAdapter.createTransaction(templateId, files) } returns expectedException.left()

        val result = cafService.createTransaction(templateId, files)

        result shouldBe expectedException.left()
        verify { cafAdapter.createTransaction(templateId, files) }
    }

    @Test
    fun `listTransactions should return success when adapter succeeds`() {
        val expectedResponse = ListTransactionsResponse(
            requestId = "request123",
            items = listOf(
                TransactionItem(
                    id = "transaction1",
                    status = "APPROVED",
                    createdAt = LocalDateTime.of(2023, 12, 1, 10, 0),
                    data = TransactionData(
                        cpf = "12345678901",
                        birthDate = "1990-01-01",
                        name = "João Silva",
                    ),
                ),
                TransactionItem(
                    id = "transaction2",
                    status = "REJECTED",
                    createdAt = LocalDateTime.of(2023, 12, 2, 11, 0),
                    data = null,
                ),
            ),
            totalItems = 2,
        )

        every { cafAdapter.listTransactions() } returns expectedResponse.right()

        val result = cafService.listTransactions()

        result shouldBe expectedResponse.right()
        verify { cafAdapter.listTransactions() }
    }

    @Test
    fun `listTransactions should return error when adapter fails`() {
        val expectedException = RuntimeException("CAF API error")

        every { cafAdapter.listTransactions() } returns expectedException.left()

        val result = cafService.listTransactions()

        result shouldBe expectedException.left()
        verify { cafAdapter.listTransactions() }
    }

    @Test
    fun `getTransaction should return success when adapter succeeds`() {
        val transactionId = "transaction123"
        val expectedResponse = GetTransactionResponse(
            requestId = "request123",
            id = "transaction123",
            status = "APPROVED",
        )

        every { cafAdapter.getTransaction(transactionId) } returns expectedResponse.right()

        val result = cafService.getTransaction(transactionId)

        result shouldBe expectedResponse.right()
        verify { cafAdapter.getTransaction(transactionId) }
    }

    @Test
    fun `getTransaction should return error when adapter fails`() {
        val transactionId = "transaction123"
        val expectedException = RuntimeException("Transaction not found")

        every { cafAdapter.getTransaction(transactionId) } returns expectedException.left()

        val result = cafService.getTransaction(transactionId)

        result shouldBe expectedException.left()
        verify { cafAdapter.getTransaction(transactionId) }
    }

    @Test
    fun `createTransaction should handle empty files list`() {
        val templateId = "template123"
        val files = emptyList<FileData>()
        val expectedResponse = CreateTransactionResponse(
            requestId = "request123",
            id = "transaction456",
            status = "PENDING",
        )

        every { cafAdapter.createTransaction(templateId, files) } returns expectedResponse.right()

        val result = cafService.createTransaction(templateId, files)

        result shouldBe expectedResponse.right()
        verify { cafAdapter.createTransaction(templateId, files) }
    }

    @Test
    fun `createTransaction should handle different file types`() {
        val templateId = "template123"
        val files = listOf(
            FileData(data = "selfie_data", type = "SELFIE"),
            FileData(data = "document_front_data", type = "DOCUMENT_FRONT"),
            FileData(data = "document_back_data", type = "DOCUMENT_BACK"),
        )
        val expectedResponse = CreateTransactionResponse(
            requestId = "request123",
            id = "transaction456",
            status = "PENDING",
        )

        every { cafAdapter.createTransaction(templateId, files) } returns expectedResponse.right()

        val result = cafService.createTransaction(templateId, files)

        result shouldBe expectedResponse.right()
        verify { cafAdapter.createTransaction(templateId, files) }
    }

    @Test
    fun `listTransactions should handle empty results`() {
        val expectedResponse = ListTransactionsResponse(
            requestId = "request123",
            items = emptyList(),
            totalItems = 0,
        )

        every { cafAdapter.listTransactions() } returns expectedResponse.right()

        val result = cafService.listTransactions()

        result shouldBe expectedResponse.right()
        verify { cafAdapter.listTransactions() }
    }

    @Test
    fun `getTransaction should handle different transaction statuses`() {
        val transactionId = "transaction123"
        val statuses = listOf("PENDING", "APPROVED", "REJECTED")

        statuses.forEach { status ->
            val expectedResponse = GetTransactionResponse(
                requestId = "request123",
                id = transactionId,
                status = status,
            )

            every { cafAdapter.getTransaction(transactionId) } returns expectedResponse.right()

            val result = cafService.getTransaction(transactionId)

            result shouldBe expectedResponse.right()
        }

        verify(exactly = statuses.size) { cafAdapter.getTransaction(transactionId) }
    }
}