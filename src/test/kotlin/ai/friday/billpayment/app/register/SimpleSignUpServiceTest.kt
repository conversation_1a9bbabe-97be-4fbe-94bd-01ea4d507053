package ai.friday.billpayment.app.register

import DynamoDBUtils.getDynamoDB
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.accountRegisterDataWithDocumentInfo
import ai.friday.billpayment.accountRegisterDataWithPhoneVerified
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.AccountRegisterDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountRegisterDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.NSUDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.OriginalOcrAndPersonDataDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PartialAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.AccountClosureDetails
import ai.friday.billpayment.app.account.AccountClosureReason
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountProviderName
import ai.friday.billpayment.app.account.AccountRegisterService
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.BasicRegisterService
import ai.friday.billpayment.app.account.CloseAccountService
import ai.friday.billpayment.app.account.DocumentInfo
import ai.friday.billpayment.app.account.ExternalId
import ai.friday.billpayment.app.account.ExternalRegisterStatus
import ai.friday.billpayment.app.account.IdentityValidationStatus
import ai.friday.billpayment.app.account.MonthlyIncome
import ai.friday.billpayment.app.account.PepQuery
import ai.friday.billpayment.app.account.PepQueryResult
import ai.friday.billpayment.app.account.PoliticallyExposed
import ai.friday.billpayment.app.account.RegistrationType
import ai.friday.billpayment.app.account.SendDocumentsResponse
import ai.friday.billpayment.app.account.SetupAccountResult
import ai.friday.billpayment.app.account.StoredObject
import ai.friday.billpayment.app.account.UserFilesConfiguration
import ai.friday.billpayment.app.integrations.CrmService
import ai.friday.billpayment.app.integrations.ECMProvider
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.LoginRepository
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.ObjectRepository
import ai.friday.billpayment.app.integrations.RegisterInstrumentationService
import ai.friday.billpayment.app.liveness.LivenessEnrollmentVerification
import ai.friday.billpayment.app.liveness.LivenessErrors
import ai.friday.billpayment.app.liveness.LivenessId
import ai.friday.billpayment.app.liveness.LivenessService
import ai.friday.billpayment.app.register.kyc.KycDossier
import ai.friday.billpayment.app.register.kyc.KycDossierSanctionType
import ai.friday.billpayment.app.register.kyc.KycDossierSanctions
import ai.friday.billpayment.app.register.kyc.KycService
import ai.friday.billpayment.buildKycDossier
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.withGivenDateTime
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.collections.shouldContainAll
import io.kotest.matchers.collections.shouldContainExactly
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.mockk.Called
import io.mockk.Runs
import io.mockk.called
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import java.io.ByteArrayInputStream
import java.time.LocalDate
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.stream.Stream
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.CsvSource

class SimpleSignUpServiceTest {

    var dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()

    private val dynamoDbEnhancedClient = getDynamoDB()
    private val objectRepository: ObjectRepository = mockk(relaxUnitFun = true) {
        every {
            loadObject(any(), any())
        } returns ByteArrayInputStream("mock".toByteArray())
    }
    private val accountDAO = AccountDynamoDAO(dynamoDbEnhancedClient)
    private val partialAccountDAO = PartialAccountDynamoDAO(dynamoDbEnhancedClient)
    private val paymentMethodDAO = PaymentMethodDynamoDAO(dynamoDbEnhancedClient)
    private val nsuDAO = NSUDynamoDAO(dynamoDbEnhancedClient)
    private val transactionDynamo = TransactionDynamo(dynamoDbEnhancedClient)

    private val accountDbRepository = AccountDbRepository(
        accountDAO = accountDAO,
        partialAccountDAO = partialAccountDAO,
        paymentMethodDAO = paymentMethodDAO,
        nsuDAO = nsuDAO,
        transactionDynamo = transactionDynamo,
    )
    private val accountRegisterDAO = AccountRegisterDynamoDAO(dynamoDbEnhancedClient)
    private val originalOcrAndPersonDataDAO = OriginalOcrAndPersonDataDynamoDAO(dynamoDbEnhancedClient)
    private val accountRegisterDbRepository = AccountRegisterDbRepository(accountRegisterDAO, originalOcrAndPersonDataDAO, objectRepository)
    private val livenessService: LivenessService = mockk {
        every { verifyDuplication(any()) } returns LivenessEnrollmentVerification(
            duplications = LivenessEnrollmentVerification.Result.Verified(
                emptyList(),
            ),
            fraudIndications = LivenessEnrollmentVerification.Result.Verified(
                emptyList(),
            ),
        ).right()
        every { markAsFraud(any()) } returns Unit.right()
    }

    private val storedObject = StoredObject(region = "us-east-1", bucket = "", key = "")
    private val kycService: KycService = mockk(relaxed = true)
    private val ecmProvider: ECMProvider = mockk()
    private val messagePublisher: MessagePublisher = mockk(relaxed = true)
    private val registerInstrumentationService: RegisterInstrumentationService = mockk(relaxed = true)
    private val crmService: CrmService = mockk(relaxed = true)

    private val accountRegisterService: AccountRegisterService = mockk(relaxed = true) {
        every {
            processSelfie(any(), any())
        } returns storedObject.right()
    }

    private val notificationAdapterMock: NotificationAdapter = mockk(relaxUnitFun = true)

    private val userFilesConfiguration = UserFilesConfiguration().apply {
        bucket = "user-documents-bucket"
        path = "pathtest"
        documentPrefix = "prefix_"
        region = "region"
        selfiePrefix = "selfie_"
        contractPrefix = "contract_"
    }

    private val simpleSignUpRequest = SimpleSignUpRequest(
        externalId = ExternalId(value = "ACCOUNT-***********", providerName = AccountProviderName.MOTOROLA),
        name = "John Doe",
        document = Document(accountRegisterDataWithDocumentInfo.documentInfo!!.cpf),
        birthDate = LocalDate.parse("1990-10-10", DateTimeFormatter.ISO_LOCAL_DATE),
        email = EmailAddress(email = "<EMAIL>"),
        mobilePhone = MobilePhone(msisdn = "+*************"),
        livenessId = LivenessId(value = "FAKE-LIVENESS-ID"),
        userContractKey = "USER-CONTRACT",
        userContractSignature = "SIGNATURE-KEY",
    )

    private val accountService = AccountService(
        accountConfigurationService = mockk(),
        accountRepository = accountDbRepository,
        chatbotMessagePublisher = mockk(),
        crmService = crmService,
        notificationAdapter = notificationAdapterMock,
        walletRepository = mockk(),
    )

    private val closeAccountService = CloseAccountService(
        accountService = accountService,
        accountRegisterRepository = accountRegisterDbRepository,
        accountRegisterService = mockk(relaxed = true),
        walletService = mockk(relaxed = true),
        backOfficeAccountService = mockk(),
        loginService = mockk(),
        transactionRepository = mockk(),
        balanceService = mockk(),
        crmService = crmService,
        subscriptionService = mockk(),
        livenessService = livenessService,
        statementService = mockk(),
        closeAccountStepExecutors = emptyList(),
        messagePublisher = mockk(),
        closeAccountQueueName = "close_Account",
        onboardingTestPixService = mockk(),
        externalAccountService = mockk(), closeAccountStepDiscovery = listOf(), closeWalletStepDiscovery = listOf(),

    )

    private val loginRepository: LoginRepository = mockk() {
        every {
            remove(any<AccountId>())
        } just Runs
    }

    private val basicRegisterService = BasicRegisterService(
        tokenService = mockk(),
        accountRegisterRepository = accountRegisterDbRepository,
        accountService = AccountService(
            accountConfigurationService = mockk(),
            accountRepository = accountDbRepository,
            chatbotMessagePublisher = mockk(),
            crmService = mockk(),
            notificationAdapter = notificationAdapterMock,
            walletRepository = mockk(),
        ),
        accountRegisterService = mockk(),
        userFilesConfiguration = mockk(),
        userAddressConfiguration = mockk(),
        agreementFilesService = mockk(),
        notificationSenderService = mockk(),
        loginRepository = loginRepository,
        userPoolAdapter = mockk(),
        externalAccountRegister = mockk(),
        walletService = mockk(),
        walletLimitsService = mockk(),
        crmService = mockk(),
        registerInstrumentationService = registerInstrumentationService,
        ddaService = mockk(),
        userJourneyService = mockk(),
        closeAccountService = closeAccountService,
        systemActivityService = mockk(),
        livenessService = livenessService,
        pendingInternalReviewConfiguration = mockk(),
        pendingActivationConfiguration = mockk(),
        accountStatusLockProvider = mockk(relaxed = true),
        messagePublisher = mockk(),
        documentValidationService = mockk(),
        fraudList = mockk(),
        notificationAdapter = notificationAdapterMock,
        adService = mockk(relaxed = true),
        chatbotNotificationService = mockk(relaxed = true),
        walletBillCategoryService = mockk(relaxed = true),
    )

    private val registerService: BasicRegisterService = spyk(basicRegisterService) {
        every { validateIdentity(any()) } answers {
            accountRegisterDbRepository.findByAccountId(firstArg())
                .copy(identityValidationStatus = IdentityValidationStatus.CHECKED).right()
        }
    }

    private val simpleSignUpService = spyk(
        SimpleSignUpService(
            accountRepository = accountDbRepository,
            accountRegisterRepository = accountRegisterDbRepository,
            livenessService = livenessService,
            kycService = kycService,
            ecmProvider = ecmProvider,
            userFilesConfiguration = userFilesConfiguration,
            objectRepository = objectRepository,
            messagePublisher = messagePublisher,
            registerService = registerService,
            inAppSubscription = 0.1f,
            accountRegisterService = accountRegisterService,
        ).apply {
            simpleSignUpQueueName = "simpleSignUpQueueName"
        },
    )

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
    }

    @Nested
    @DisplayName("na validacao dos dados de usuario")
    inner class ValidateUserData {
        private val userDataValidationRequestTO = UserDataValidationRequest(
            document = Document(ACCOUNT.document),
            mobilePhone = MobilePhone(ACCOUNT.mobilePhone),
        )

        @Test
        fun `deve retornar dados validos sem accountId quando nem documento nem telefone sao usados em uma conta aberta`() {
            val response = simpleSignUpService.validate(userDataValidationRequestTO)

            response.valid shouldBe true
            response.accountId shouldBe null
        }

        @Test
        fun `deve retornar dados validos com accountId quando o documento esta sendo usado em uma conta aberta e o telefone bate`() {
            accountDbRepository.save(ACCOUNT)

            val response = simpleSignUpService.validate(userDataValidationRequestTO)

            response.valid shouldBe true
            response.accountId shouldBe ACCOUNT.accountId
        }

        @Test
        fun `deve retornar dados invalidos quando o documento esta sendo usado em uma conta aberta e o telefone nao bate`() {
            accountDbRepository.save(ACCOUNT)

            val response = simpleSignUpService.validate(
                userDataValidationRequestTO.copy(mobilePhone = MobilePhone("123456")),
            )

            response.valid shouldBe false
            response.accountId shouldBe null
        }

        @Test
        fun `deve retornar dados invalidos quando o telefone esta sendo usado em uma conta aberta e o documento nao bate`() {
            accountRegisterDbRepository.save(accountRegisterDataWithPhoneVerified)

            val response = simpleSignUpService.validate(
                userDataValidationRequestTO.copy(
                    document = Document("***********"),
                    mobilePhone = accountRegisterDataWithPhoneVerified.mobilePhone!!,
                ),
            )

            response.valid shouldBe false
            response.accountId shouldBe null
        }
    }

    @Nested
    @DisplayName("ao continuar o processamento do signup")
    inner class ContinueSignUp {
        private val kycFileStoredObject = StoredObject("", "", "kyc-report.html")
        private val kycDossier = buildKycDossier()

        private val event = SimpleSignUpPendingMessage(
            livenessId = simpleSignUpRequest.livenessId.value,
            userContractKey = simpleSignUpRequest.userContractKey,
            userContractSignature = simpleSignUpRequest.userContractSignature,
            selfieCaptured = false,
            userContractCaptured = false,
            kycDossierGenerated = false,
            ecmDocumentsSent = false,
            accountId = "",
            hasPassedRiskAnalysis = false,
            accountActivated = false,
            deduplicationVerified = false,
            identityVerified = false,
        )

        private val accountId = AccountId()

        private fun loadUnderReviewPartialAccount() {
            accountDbRepository.updatePartialAccountStatus(accountId, AccountStatus.UNDER_REVIEW)
        }

        private fun loadFridayAccountRegister() {
            val accountRegister = accountRegisterDbRepository.findByAccountId(accountId)
            accountRegisterDbRepository.save(
                accountRegister.copy(
                    externalId = accountRegister.externalId?.copy(
                        providerName = AccountProviderName.FRIDAY,
                    ),
                ),
            )
        }

        @BeforeEach
        fun init() {
            every {
                livenessService.retrieveEnrollmentSelfie(any())
            } returns "selfie".toByteArray().right()

            every {
                objectRepository.copyObject(any(), any(), any(), any())
            } just Runs

            every {
                kycService.generate(any())
            } returns Pair(kycFileStoredObject, mockk<KycDossier>()).right()

            every {
                ecmProvider.sendSimpleSignUpDocuments(any())
            } returns SendDocumentsResponse(success = true, status = "OK")

            every {
                kycService.generate(any())
            } returns Pair(storedObject, kycDossier).right()

            val existingPartialAccount = accountDbRepository.create(
                username = simpleSignUpRequest.name,
                emailAddress = simpleSignUpRequest.email,
                accountId = accountId,
                registrationType = RegistrationType.BASIC,
            )

            accountRegisterDbRepository.create(
                accountId = existingPartialAccount.id,
                emailAddress = simpleSignUpRequest.email,
                username = simpleSignUpRequest.name,
                mobilePhone = simpleSignUpRequest.mobilePhone,
                mobilePhoneVerified = true,
                livenessId = simpleSignUpRequest.livenessId,
                externalId = simpleSignUpRequest.externalId,
                documentInfo = DocumentInfo(
                    name = simpleSignUpRequest.name,
                    cpf = simpleSignUpRequest.document.value,
                    birthDate = simpleSignUpRequest.birthDate,
                    fatherName = "",
                    motherName = "",
                    rg = "",
                    docType = simpleSignUpRequest.document.type,
                    cnhNumber = null,
                    orgEmission = simpleSignUpRequest.document.issuer,
                    expeditionDate = null,
                    birthCity = "",
                    birthState = simpleSignUpRequest.document.issuerRegion,
                ),
                monthlyIncome = MonthlyIncome(0, 2_000_00), registrationType = RegistrationType.BASIC,
                politicallyExposed = PoliticallyExposed(selfDeclared = false, query = null),
            )
        }

        @Test
        fun `não deve chamar a serpro caso já exista o resultado para o usuário`() {
            accountRegisterDbRepository.save(
                accountRegisterDbRepository.findByAccountId(accountId).copy(
                    externalId = ExternalId(value = "***********", providerName = AccountProviderName.FRIDAY),
                    identityValidationStatus = IdentityValidationStatus.CHECKED,
                    identityValidationPercentage = 1.0,
                ),
            )

            val result = simpleSignUpService.continueSignUp(event.copy(accountId = accountId.value))

            result.isRight() shouldBe true
            result.map { it: SimpleSignUpPendingMessage ->
                it.identityVerified.shouldBeTrue()
            }

            verify(exactly = 0) { registerService.validateIdentity(any()) }
        }

        @Test
        fun `nao deve republicar a mensagem na fila se o primeiro passo falhar`() {
            every {
                accountRegisterService.processSelfie(any(), any())
            } returns Exception().left()

            val response =
                simpleSignUpService.continueSignUp(event.copy(accountId = accountId.value))

            response.isLeft().shouldBeTrue()

            verify {
                messagePublisher wasNot Called
            }
        }

        @Test
        fun `deve salvar apenas a selfie se gerar o contrato falhar`() {
            every {
                objectRepository.copyObject(any(), any(), any(), any())
            } throws NoStackTraceException("Fake")

            val response =
                simpleSignUpService.continueSignUp(event.copy(accountId = accountId.value))

            response.isRight().shouldBeTrue()

            response.map { result ->
                result.selfieCaptured.shouldBeTrue()
                result.userContractCaptured.shouldBeFalse()
                result.kycDossierGenerated.shouldBeFalse()
                result.hasPassedRiskAnalysis.shouldBeFalse()
                result.ecmDocumentsSent.shouldBeFalse()

                verify {
                    messagePublisher.sendMessage(any(), result)
                }
            }

            val accountRegisterData = accountRegisterDbRepository.findByAccountId(accountId)
            accountRegisterData.uploadedSelfie.shouldNotBeNull()
        }

        @Test
        fun `deve negar o usuário se ele estiver na lista de fraudadores`() {
            accountRegisterDbRepository.save(
                accountRegisterDbRepository.findByAccountId(accountId).copy(fraudListMatch = true),
            )
            val now = ZonedDateTime.of(2023, 1, 1, 0, 0, 0, 0, brazilTimeZone)
            val result = withGivenDateTime(now) {
                simpleSignUpService.continueSignUp(event.copy(accountId = accountId.value))
            }

            val closureDetails = AccountClosureDetails.create(
                reason = AccountClosureReason.FRAUD,
                description = "Cadastro rejeitado por análise de risco",
                at = now,
            )

            result.isRight() shouldBe true

            result.map {
                it.hasPassedRiskAnalysis.shouldBeFalse()
            }

            accountDbRepository.findPartialAccountById(accountId).status shouldBe AccountStatus.DENIED

            with(accountRegisterDbRepository.findByAccountId(accountId, false)) {
                riskAnalysisFailedReasons shouldContainExactly listOf(RiskAnalysisFailedReason.FRAUD_LIST_MATCH)
                openForUserReview shouldBe false
                accountClosureDetails shouldBe closureDetails
            }
            verify {
                registerInstrumentationService.rejected(any(), any(), any())
                notificationAdapterMock.notifyRegisterDenied(any(), any())
                livenessService.markAsFraud(any())
                crmService.removeContactAsync(accountId)
            }

            verify {
                loginRepository.remove(accountId)
            }
        }

        @Test
        fun `nao deve republicar a mensagem na fila se o primeiro passo processado falhar`() {
            every {
                objectRepository.copyObject(any(), any(), any(), any())
            } throws NoStackTraceException("FAKE")

            val response =
                simpleSignUpService.continueSignUp(event.copy(accountId = accountId.value, selfieCaptured = true))

            response.isLeft().shouldBeTrue()

            verify {
                messagePublisher wasNot Called
                livenessService wasNot Called
            }
        }

        @Test
        fun `não deve executar a validacao na serpro quando já tive sido feita anteriormente`() {
            loadFridayAccountRegister()

            simpleSignUpService.continueSignUp(event.copy(accountId = accountId.value, identityVerified = true))

            verify(exactly = 0) { registerService.validateIdentity(accountId) }
        }

        @Test
        fun `deve salvar a selfie e o contrato se falhar na geração do KYC`() {
            every {
                kycService.generate(any())
            } returns NoStackTraceException("FAKE").left()
            val now = getZonedDateTime()

            val response = withGivenDateTime(now) {
                simpleSignUpService.continueSignUp(event.copy(accountId = accountId.value))
            }

            response.isRight().shouldBeTrue()

            val expectedUserContractKey = response.map { result ->
                result.selfieCaptured.shouldBeTrue()
                result.userContractCaptured.shouldBeTrue()
                result.kycDossierGenerated.shouldBeFalse()
                result.hasPassedRiskAnalysis.shouldBeFalse()
                result.ecmDocumentsSent.shouldBeFalse()

                verify {
                    messagePublisher.sendMessage(any(), result)
                }

                "${userFilesConfiguration.path}/${result.accountId}/${userFilesConfiguration.contractPrefix}${now.toEpochSecond()}.pdf"
            }.getOrElse {
                TODO("DEVERIA TER FUNCIONADO")
            }

            val accountRegisterData = accountRegisterDbRepository.findByAccountId(accountId)
            accountRegisterData.uploadedSelfie.shouldNotBeNull()
            accountRegisterData.agreementData.shouldNotBeNull()
            accountRegisterData.agreementData?.userContractSignature shouldBe simpleSignUpRequest.userContractSignature
            accountRegisterData.agreementData?.userContractFile?.bucket shouldBe userFilesConfiguration.bucket
            accountRegisterData.agreementData?.userContractFile?.region shouldBe userFilesConfiguration.region
            accountRegisterData.agreementData?.userContractFile?.key shouldBe expectedUserContractKey
        }

        @Test
        fun `deve gerar um kyc e salvar a duplicação e indicação de fraude com resultado do liveness`() {
            every {
                livenessService.verifyDuplication(any())
            } returns LivenessEnrollmentVerification(
                duplications = LivenessEnrollmentVerification.Result.create(
                    accountIds = listOf(AccountId("1234")),
                ),
                fraudIndications = LivenessEnrollmentVerification.Result.create(
                    accountIds = listOf(
                        AccountId("9876"),
                    ),
                ),
            ).right()

            every {
                ecmProvider.sendSimpleSignUpDocuments(any())
            } throws NoStackTraceException("FAKE")

            val now = getZonedDateTime()

            val response = withGivenDateTime(now) {
                simpleSignUpService.continueSignUp(event.copy(accountId = accountId.value, kycDossierGenerated = false))
            }

            response.isRight().shouldBeTrue()

            response.map { result ->
                result.selfieCaptured.shouldBeTrue()
                result.userContractCaptured.shouldBeTrue()
                result.kycDossierGenerated.shouldBeTrue()
                result.hasPassedRiskAnalysis.shouldBeFalse()
                result.ecmDocumentsSent.shouldBeFalse()

                verify(exactly = 0) { messagePublisher.sendMessage(any(), result) }
            }

            val accountRegisterData = accountRegisterDbRepository.findByAccountId(accountId)

            accountRegisterData.kycFile.shouldNotBeNull()
            accountRegisterData.livenessEnrollmentVerification.fraudIndications shouldBe LivenessEnrollmentVerification.Result.Verified(
                accountIds = listOf(AccountId("9876")),
            )
            accountRegisterData.livenessEnrollmentVerification.duplications shouldBe LivenessEnrollmentVerification.Result.Verified(
                accountIds = listOf(AccountId("1234")),
            )
        }

        @Test
        fun `deve salvar o PEP consultado ao gerar o KYC`() {
            val now = getZonedDateTime()

            every {
                kycService.generate(any())
            } returns Pair(
                storedObject,
                kycDossier.copy(
                    pep = PepQuery(
                        at = now,
                        result = PepQueryResult.RELATED_TO_PEP,
                    ),
                ),
            ).right()

            every {
                ecmProvider.sendSimpleSignUpDocuments(any())
            } throws NoStackTraceException("FAKE")

            with(accountRegisterDbRepository.findByAccountId(accountId)) {
                politicallyExposed shouldBe PoliticallyExposed(selfDeclared = false, query = null)
            }

            withGivenDateTime(now) {
                simpleSignUpService.continueSignUp(
                    event.copy(
                        accountId = accountId.value,
                        kycDossierGenerated = false,
                    ),
                )
            }

            with(accountRegisterDbRepository.findByAccountId(accountId)) {
                politicallyExposed?.selfDeclared shouldBe false
                politicallyExposed?.query?.result shouldBe PepQueryResult.RELATED_TO_PEP
            }
        }

        @Test
        fun `deve salvar a selfie, contrato, kyc e análise de risco`() {
            every {
                ecmProvider.sendSimpleSignUpDocuments(any())
            } throws NoStackTraceException("FAKE")

            val now = getZonedDateTime()

            val response = withGivenDateTime(now) {
                simpleSignUpService.continueSignUp(event.copy(accountId = accountId.value))
            }

            response.isRight().shouldBeTrue()

            response.map { result ->
                result.selfieCaptured.shouldBeTrue()
                result.userContractCaptured.shouldBeTrue()
                result.kycDossierGenerated.shouldBeTrue()
                result.hasPassedRiskAnalysis.shouldBeTrue()
                result.ecmDocumentsSent.shouldBeFalse()

                verify {
                    messagePublisher.sendMessage(any(), result)
                    ecmProvider.sendSimpleSignUpDocuments(any())
                }
            }

            val accountRegisterData = accountRegisterDbRepository.findByAccountId(accountId)
            accountRegisterData.kycFile.shouldNotBeNull()
        }

        @Test
        fun `deve salvar a selfie, contrato, kyc, análise de risco e ecm`() {
            val now = getZonedDateTime()

            val response = withGivenDateTime(now) {
                simpleSignUpService.continueSignUp(event.copy(accountId = accountId.value))
            }

            response.isRight().shouldBeTrue()

            response.map { result ->
                result.selfieCaptured.shouldBeTrue()
                result.userContractCaptured.shouldBeTrue()
                result.kycDossierGenerated.shouldBeTrue()
                result.hasPassedRiskAnalysis.shouldBeTrue()
                result.ecmDocumentsSent.shouldBeTrue()

                verify {
                    messagePublisher.sendMessage(any(), result)
                    ecmProvider.sendSimpleSignUpDocuments(any())
                }
            }
        }

        @Test
        fun `deve salvar a selfie, contrato, kyc, passar na análise de risco, enviar ecm, notificar ao banco parceiro que os documentos foram enviados e ativar o usuário`() {
            every {
                registerService.updateAccountStatus(
                    any(),
                    ExternalRegisterStatus.APPROVED,
                )
            } returns SetupAccountResult.AccountApproved.right()

            val now = getZonedDateTime()
            val response = withGivenDateTime(now) {
                simpleSignUpService.continueSignUp(event.copy(accountId = accountId.value))
            }

            response.isRight().shouldBeTrue()

            response.map { result ->
                result.selfieCaptured.shouldBeTrue()
                result.userContractCaptured.shouldBeTrue()
                result.kycDossierGenerated.shouldBeTrue()
                result.hasPassedRiskAnalysis.shouldBeTrue()
                result.ecmDocumentsSent.shouldBeTrue()
                result.accountActivated.shouldBeTrue()
                verify {
                    registerService.updateAccountStatus(any(), ExternalRegisterStatus.APPROVED)
                }
            }
        }

        @Test
        fun `deve reenviar a mensagem pra fila se usuário não tiver feito o liveness`() {
            every {
                livenessService.verifyDuplication(any())
            } returns LivenessErrors.EnrollmentUnavailable.left()

            val now = getZonedDateTime()
            val response = withGivenDateTime(now) {
                simpleSignUpService.continueSignUp(
                    event.copy(
                        accountId = accountId.value,
                        selfieCaptured = true,
                        userContractCaptured = true,
                        kycDossierGenerated = true,
                    ),
                )
            }

            response.isLeft().shouldBeTrue()

            response.map { result ->
                result.hasPassedRiskAnalysis.shouldBeFalse()
                result.ecmDocumentsSent.shouldBeFalse()

                verify(exactly = 1) { messagePublisher.sendMessage(any()) }
            }
        }

        @Test
        fun `deve reenviar a mensagem pra fila se o usuário ainda não fez o check de duplicação de liveness`() {
            every {
                livenessService.verifyDuplication(any())
            } returns LivenessEnrollmentVerification(
                duplications = LivenessEnrollmentVerification.Result.create(
                    accountIds = null,
                ),
            ).right()

            val now = getZonedDateTime()
            val response = withGivenDateTime(now) {
                simpleSignUpService.continueSignUp(
                    event.copy(
                        accountId = accountId.value,
                        selfieCaptured = true,
                        userContractCaptured = true,
                        kycDossierGenerated = true,
                    ),
                )
            }

            response.isLeft().shouldBeTrue()

            response.map { result ->
                result.hasPassedRiskAnalysis.shouldBeFalse()
                result.ecmDocumentsSent.shouldBeFalse()

                verify(exactly = 1) { messagePublisher.sendMessage(any()) }
            }
        }

        @Test
        fun `deve reenviar a mensagem pra fila se o usuário ainda não fez o check de rosto na lista de fraudadores`() {
            every {
                livenessService.verifyDuplication(any())
            } returns LivenessEnrollmentVerification(
                fraudIndications = LivenessEnrollmentVerification.Result.create(accountIds = null),
                duplications = LivenessEnrollmentVerification.Result.Verified(
                    accountIds = listOf(
                        AccountId("test"),
                    ),
                ),
            ).right()

            val now = getZonedDateTime()
            val response = withGivenDateTime(now) {
                simpleSignUpService.continueSignUp(
                    event.copy(
                        accountId = accountId.value,
                        selfieCaptured = true,
                        userContractCaptured = true,
                        kycDossierGenerated = true,
                    ),
                )
            }

            response.isLeft().shouldBeTrue()

            response.map { result ->
                result.hasPassedRiskAnalysis.shouldBeFalse()
                result.ecmDocumentsSent.shouldBeFalse()

                verify(exactly = 1) { messagePublisher.sendMessage(any()) }
            }
        }

        @Nested
        @DisplayName("quand o cadastro não for aprovado automaticamente e puder ser corrigido pelo usuário")
        inner class BasicRegisterReopenedWihtoutUpgrade {
            @Test
            fun `deve reabrir o cadastro automaticamente`() {
                accountDbRepository.updatePartialAccountStatus(accountId, AccountStatus.UNDER_REVIEW)
                every {
                    livenessService.verifyDuplication(any())
                } returns LivenessEnrollmentVerification(
                    duplications = LivenessEnrollmentVerification.Result.Verified(emptyList()),
                    fraudIndications = LivenessEnrollmentVerification.Result.Verified(
                        emptyList(),
                    ),
                ).right()

                every {
                    kycService.generate(any())
                } returns Pair(
                    storedObject,
                    kycDossier.copy(officialData = kycDossier.officialData.copy(name = "jane")),
                ).right()

                val now = getZonedDateTime()
                val response = withGivenDateTime(now) {
                    simpleSignUpService.continueSignUp(event.copy(accountId = accountId.value))
                }

                response.isRight() shouldBe true

                verify(exactly = 0) {
                    registerService.updateAccountStatus(any(), any())
                }

                with(accountDbRepository.findPartialAccountById(accountId)) {
                    status shouldBe AccountStatus.REGISTER_INCOMPLETE
                }

                with(accountRegisterDbRepository.findByAccountId(accountId)) {
                    riskAnalysisFailedReasons shouldContainExactly listOf(RiskAnalysisFailedReason.NAME_MISMATCH)
                    openForUserReview shouldBe true
                }

                verify {
                    registerInstrumentationService.reopened(any(), any(), any())
                }
            }
        }

        @Nested
        @DisplayName("ao concluir os passos de selfie, contrato, kyc, análise de risco, envio de documentos e notificação ao banco parceiro")
        inner class ActivateAccount {
            @Test
            fun `deve ativar a conta`() {
                every {
                    registerService.updateAccountStatus(any(), ExternalRegisterStatus.APPROVED)
                } returns SetupAccountResult.AccountApproved.right()

                val now = getZonedDateTime()
                val response = withGivenDateTime(now) {
                    simpleSignUpService.continueSignUp(event.copy(accountId = accountId.value))
                }

                response.isRight().shouldBeTrue()

                response.map { result ->
                    result.selfieCaptured.shouldBeTrue()
                    result.userContractCaptured.shouldBeTrue()
                    result.kycDossierGenerated.shouldBeTrue()
                    result.hasPassedRiskAnalysis.shouldBeTrue()
                    result.ecmDocumentsSent.shouldBeTrue()
                    result.accountActivated.shouldBeTrue()

                    verify(exactly = 0) {
                        messagePublisher.sendMessage(any(), result)
                    }
                }

                verify {
                    registerService.updateAccountStatus(any(), ExternalRegisterStatus.APPROVED)
                }
            }

            @Test
            fun `deve retornar erro caso a conta não seja aprovada`() {
                every {
                    registerService.updateAccountStatus(any(), ExternalRegisterStatus.APPROVED)
                } returns SetupAccountResult.AccountRejected.right()

                val now = getZonedDateTime()
                val response = withGivenDateTime(now) {
                    simpleSignUpService.continueSignUp(event.copy(accountId = accountId.value))
                }

                response.isRight().shouldBeTrue()

                response.map { result ->
                    result.selfieCaptured.shouldBeTrue()
                    result.userContractCaptured.shouldBeTrue()
                    result.kycDossierGenerated.shouldBeTrue()
                    result.hasPassedRiskAnalysis.shouldBeTrue()
                    result.ecmDocumentsSent.shouldBeTrue()
                    result.accountActivated.shouldBeFalse()

                    verify {
                        messagePublisher.sendMessage(any(), result)
                    }
                }
            }
        }

        @Nested
        @DisplayName("ao executar a análise de risco")
        inner class RiskAnalysis {

            @Nested
            @DisplayName("e passar")
            inner class RiskAnalysisSucessTest {

                @Test
                fun `deve considerar o pep autodeclarado`() {
                    val existingPartialAccount = accountDbRepository.create(
                        username = simpleSignUpRequest.name,
                        emailAddress = simpleSignUpRequest.email,
                        registrationType = RegistrationType.BASIC,
                    )
                    accountRegisterDbRepository.create(
                        accountId = existingPartialAccount.id,
                        emailAddress = simpleSignUpRequest.email,
                        username = simpleSignUpRequest.name,
                        mobilePhone = MobilePhone(ACCOUNT.mobilePhone),
                        mobilePhoneVerified = true,
                        livenessId = simpleSignUpRequest.livenessId,
                        externalId = simpleSignUpRequest.externalId,
                        documentInfo = DocumentInfo(
                            name = simpleSignUpRequest.name,
                            cpf = Document(ACCOUNT.document).value,
                            birthDate = simpleSignUpRequest.birthDate,
                            fatherName = "",
                            motherName = "",
                            rg = "",
                            docType = simpleSignUpRequest.document.type,
                            cnhNumber = null,
                            orgEmission = simpleSignUpRequest.document.issuer,
                            expeditionDate = null,
                            birthCity = "",
                            birthState = simpleSignUpRequest.document.issuerRegion,
                        ),
                        monthlyIncome = MonthlyIncome(0, 2_000_00), registrationType = RegistrationType.BASIC,
                        politicallyExposed = PoliticallyExposed(selfDeclared = true, query = null),
                    )

                    val now = getZonedDateTime()
                    val response = withGivenDateTime(now) {
                        simpleSignUpService.continueSignUp(event.copy(accountId = existingPartialAccount.id.value))
                    }

                    response.isRight().shouldBeTrue()

                    val accountRegister = accountRegisterDbRepository.findByAccountId(existingPartialAccount.id, false)

                    accountRegister.riskAnalysisFailedReasons.shouldNotBeNull()
                    accountRegister.riskAnalysisFailedReasons!![0] shouldBe RiskAnalysisFailedReason.POLITICALLY_EXPOSED_PERSON
                    accountRegister.accountRecoverable shouldBe true
                    accountRegister.agreementData shouldBe null

                    val account = accountDbRepository.findPartialAccountById(existingPartialAccount.id)

                    account.status shouldBe AccountStatus.REGISTER_INCOMPLETE

                    accountRegisterDbRepository.findByAccountId(
                        existingPartialAccount.id,
                        true,
                    ).registrationType shouldBe RegistrationType.UPGRADED

                    verify {
                        registerInstrumentationService.upgraded(any(), any(), any())
                        notificationAdapterMock.notifyRegisterUpgraded(any(), any())
                    }
                }

                @Test
                fun `não deve dar erro de name mismatch mesmo quando o nome da pessoa tiver acento`() {
                    val existingPartialAccount = accountDbRepository.create(
                        username = simpleSignUpRequest.name,
                        emailAddress = simpleSignUpRequest.email,
                        registrationType = RegistrationType.BASIC,
                    )

                    accountRegisterDbRepository.create(
                        accountId = existingPartialAccount.id,
                        emailAddress = simpleSignUpRequest.email,
                        username = simpleSignUpRequest.name,
                        mobilePhone = MobilePhone(ACCOUNT.mobilePhone),
                        mobilePhoneVerified = true,
                        livenessId = simpleSignUpRequest.livenessId,
                        externalId = simpleSignUpRequest.externalId,
                        documentInfo = DocumentInfo(
                            name = "João",
                            cpf = Document(ACCOUNT.document).value,
                            birthDate = simpleSignUpRequest.birthDate,
                            fatherName = "",
                            motherName = "",
                            rg = "",
                            docType = simpleSignUpRequest.document.type,
                            cnhNumber = null,
                            orgEmission = simpleSignUpRequest.document.issuer,
                            expeditionDate = null,
                            birthCity = "",
                            birthState = simpleSignUpRequest.document.issuerRegion,
                        ),
                        monthlyIncome = MonthlyIncome(0, 2_000_00), registrationType = RegistrationType.BASIC,
                    )

                    every {
                        kycService.generate(any())
                    } returns Pair(
                        storedObject,
                        kycDossier.copy(officialData = kycDossier.officialData.copy(name = "JOAO")),
                    ).right()

                    val now = getZonedDateTime()

                    withGivenDateTime(now) {
                        simpleSignUpService.continueSignUp(event.copy(accountId = existingPartialAccount.id.value))
                            .isRight().shouldBeTrue()
                    }

                    accountRegisterDbRepository.findByAccountId(
                        existingPartialAccount.id,
                        false,
                    ).riskAnalysisFailedReasons.shouldBeNull()
                }

                @Test
                fun `deve salvar o resultado da verificação 1 para n do liveness`() {
                    val existingPartialAccount = accountDbRepository.create(
                        username = simpleSignUpRequest.name,
                        emailAddress = simpleSignUpRequest.email,
                        registrationType = RegistrationType.BASIC,
                    )
                    accountRegisterDbRepository.create(
                        accountId = existingPartialAccount.id,
                        emailAddress = simpleSignUpRequest.email,
                        username = simpleSignUpRequest.name,
                        mobilePhone = MobilePhone(ACCOUNT.mobilePhone),
                        mobilePhoneVerified = true,
                        livenessId = simpleSignUpRequest.livenessId,
                        externalId = simpleSignUpRequest.externalId,
                        documentInfo = DocumentInfo(
                            name = simpleSignUpRequest.name,
                            cpf = "***********",
                            birthDate = simpleSignUpRequest.birthDate,
                            fatherName = "",
                            motherName = "",
                            rg = "",
                            docType = simpleSignUpRequest.document.type,
                            cnhNumber = null,
                            orgEmission = simpleSignUpRequest.document.issuer,
                            expeditionDate = null,
                            birthCity = "",
                            birthState = simpleSignUpRequest.document.issuerRegion,
                        ),
                        monthlyIncome = MonthlyIncome(0, 2_000_00), registrationType = RegistrationType.BASIC,
                        politicallyExposed = PoliticallyExposed(selfDeclared = false, query = null),
                    )
                    every {
                        livenessService.verifyDuplication(any())
                    } returns LivenessEnrollmentVerification(
                        duplications = LivenessEnrollmentVerification.Result.Verified(emptyList()),
                        fraudIndications = LivenessEnrollmentVerification.Result.Verified(emptyList()),
                    ).right()

                    val now = getZonedDateTime()
                    val response = withGivenDateTime(now) {
                        simpleSignUpService.continueSignUp(event.copy(accountId = existingPartialAccount.id.value))
                    }

                    response.isRight().shouldBeTrue()

                    response.map { result ->
                        result.selfieCaptured.shouldBeTrue()
                        result.userContractCaptured.shouldBeTrue()
                        result.kycDossierGenerated.shouldBeTrue()
                        result.hasPassedRiskAnalysis.shouldBeTrue()
                        result.ecmDocumentsSent.shouldBeTrue()
                    }

                    verify(exactly = 1) {
                        ecmProvider.sendSimpleSignUpDocuments(any())
                    }

                    val accountRegister = accountRegisterDbRepository.findByAccountId(existingPartialAccount.id, false)

                    accountRegister.livenessId.shouldNotBeNull()
                    accountRegister.livenessEnrollmentVerification.duplications shouldBe LivenessEnrollmentVerification.Result.Verified(
                        emptyList(),
                    )
                }
            }

            @Nested
            @DisplayName("e falhar")
            inner class RiskAnalysisLivenessFailureTest {
                @BeforeEach
                fun init() {
                    loadUnderReviewPartialAccount()
                }

                @Test
                fun `não deve executar os próximos passos do cadastro`() {
                    every {
                        livenessService.verifyDuplication(any())
                    } returns LivenessEnrollmentVerification(
                        duplications = LivenessEnrollmentVerification.Result.Verified(listOf(AccountId("123"))),
                        fraudIndications = LivenessEnrollmentVerification.Result.Verified(emptyList()),
                    ).right()

                    val now = getZonedDateTime()
                    val response = withGivenDateTime(now) {
                        simpleSignUpService.continueSignUp(event.copy(accountId = accountId.value))
                    }

                    response.isRight().shouldBeTrue()

                    response.map { result ->
                        result.selfieCaptured.shouldBeTrue()
                        result.userContractCaptured.shouldBeTrue()
                        result.kycDossierGenerated.shouldBeTrue()
                        result.hasPassedRiskAnalysis.shouldBeFalse()
                        result.ecmDocumentsSent.shouldBeFalse()
                    }

                    verify {
                        ecmProvider wasNot called
                        messagePublisher wasNot called
                    }
                    verify {
                        registerInstrumentationService.upgraded(any(), any(), any())
                        notificationAdapterMock.notifyRegisterUpgraded(any(), any())
                    }
                }

                @Test
                fun `deve reabrir a conta se as razões da conta ter sido negada sejam todas recuperáveis`() {
                    val existingPartialAccount = accountDbRepository.create(
                        username = simpleSignUpRequest.name,
                        emailAddress = simpleSignUpRequest.email,
                        registrationType = RegistrationType.BASIC,
                    )
                    accountRegisterDbRepository.create(
                        accountId = existingPartialAccount.id,
                        emailAddress = simpleSignUpRequest.email,
                        username = simpleSignUpRequest.name,
                        mobilePhone = MobilePhone(ACCOUNT.mobilePhone),
                        mobilePhoneVerified = true,
                        livenessId = simpleSignUpRequest.livenessId,
                        externalId = simpleSignUpRequest.externalId,
                        documentInfo = DocumentInfo(
                            name = "Jane",
                            cpf = Document(ACCOUNT.document).value,
                            birthDate = simpleSignUpRequest.birthDate,
                            fatherName = "",
                            motherName = "",
                            rg = "",
                            docType = simpleSignUpRequest.document.type,
                            cnhNumber = null,
                            orgEmission = simpleSignUpRequest.document.issuer,
                            expeditionDate = null,
                            birthCity = "",
                            birthState = simpleSignUpRequest.document.issuerRegion,
                        ),
                        monthlyIncome = MonthlyIncome(0, 2_000_00),
                        registrationType = RegistrationType.BASIC,
                        politicallyExposed = PoliticallyExposed(selfDeclared = false, query = null),
                    )
                    every {
                        livenessService.verifyDuplication(any())
                    } returns LivenessEnrollmentVerification(
                        duplications = LivenessEnrollmentVerification.Result.Verified(emptyList()),
                        fraudIndications = LivenessEnrollmentVerification.Result.Verified(emptyList()),
                    ).right()

                    val now = getZonedDateTime()
                    val response = withGivenDateTime(now) {
                        simpleSignUpService.continueSignUp(event.copy(accountId = existingPartialAccount.id.value))
                    }

                    response.isRight().shouldBeTrue()
                    response.map {
                        it.hasPassedRiskAnalysis shouldBe false
                    }

                    verify {
                        notificationAdapterMock.notifyBasicSignUpReopened(
                            existingPartialAccount.id,
                            MobilePhone(ACCOUNT.mobilePhone),
                        )
                    }

                    val accountRegister = accountRegisterDbRepository.findByAccountId(existingPartialAccount.id, false)

                    accountRegister.riskAnalysisFailedReasons.shouldNotBeNull()
                    accountRegister.riskAnalysisFailedReasons!![0] shouldBe RiskAnalysisFailedReason.NAME_MISMATCH
                    accountRegister.accountRecoverable shouldBe true
                    accountRegister.openForUserReview shouldBe true

                    val account = accountDbRepository.findPartialAccountById(existingPartialAccount.id)

                    account.status shouldBe AccountStatus.REGISTER_INCOMPLETE

                    verify { registerInstrumentationService.reopened(any(), any(), any()) }
                }

                @Test
                fun `não deve ser recuperável se uma das razões da conta ter sido negada seja não recuperável`() {
                    val existingPartialAccount = accountDbRepository.create(
                        username = simpleSignUpRequest.name,
                        emailAddress = simpleSignUpRequest.email,
                        registrationType = RegistrationType.BASIC,
                    )
                    accountRegisterDbRepository.create(
                        accountId = existingPartialAccount.id,
                        emailAddress = simpleSignUpRequest.email,
                        username = "Jane",
                        mobilePhone = MobilePhone(ACCOUNT.mobilePhone),
                        mobilePhoneVerified = true,
                        livenessId = simpleSignUpRequest.livenessId,
                        externalId = simpleSignUpRequest.externalId,
                        documentInfo = DocumentInfo(
                            name = "Jane",
                            cpf = Document(ACCOUNT.document).value,
                            birthDate = simpleSignUpRequest.birthDate,
                            fatherName = "",
                            motherName = "",
                            rg = "",
                            docType = simpleSignUpRequest.document.type,
                            cnhNumber = null,
                            orgEmission = simpleSignUpRequest.document.issuer,
                            expeditionDate = null,
                            birthCity = "",
                            birthState = simpleSignUpRequest.document.issuerRegion,
                        ),
                        monthlyIncome = MonthlyIncome(0, 2_000_00),
                        registrationType = RegistrationType.BASIC,
                        politicallyExposed = PoliticallyExposed(selfDeclared = false, query = null),
                    )

                    every { kycService.generate(any()) } returns Pair(
                        storedObject,
                        kycDossier.copy(officialData = kycDossier.officialData.copy(hasObitIndication = true)),
                    ).right()

                    val now = ZonedDateTime.of(2023, 1, 1, 0, 0, 0, 0, brazilTimeZone)
                    val response = withGivenDateTime(now) {
                        simpleSignUpService.continueSignUp(event.copy(accountId = existingPartialAccount.id.value))
                    }

                    response.isRight().shouldBeTrue()

                    val closureDetails = AccountClosureDetails.create(
                        reason = AccountClosureReason.FRAUD,
                        description = "Cadastro rejeitado por análise de risco",
                        at = now,
                    )

                    val accountRegister = accountRegisterDbRepository.findByAccountId(existingPartialAccount.id, false)

                    accountRegister.riskAnalysisFailedReasons!!.shouldContainAll(
                        RiskAnalysisFailedReason.HAS_OBIT_INDICATION,
                        RiskAnalysisFailedReason.NAME_MISMATCH,
                    )

                    accountRegister.accountClosureDetails shouldBe closureDetails

                    accountRegister.accountRecoverable shouldBe false

                    val account = accountDbRepository.findPartialAccountById(existingPartialAccount.id)

                    account.status shouldBe AccountStatus.DENIED

                    assertThrows<ItemNotFoundException> {
                        accountRegisterDbRepository.findByAccountId(
                            existingPartialAccount.id,
                            true,
                        )
                    }

                    verify {
                        registerInstrumentationService.rejected(any(), any(), any())
                        notificationAdapterMock.notifyRegisterDenied(any(), any())
                        crmService.removeContactAsync(existingPartialAccount.id)
                    }
                }

                @Test
                fun `deve mudar para REGISTER_INCOMPLETE se a verificacao na serpro for inconclusiva`() {
                    loadFridayAccountRegister()

                    every { registerService.validateIdentity(accountId) } answers {
                        accountRegisterDbRepository.findByAccountId(
                            accountId,
                            false,
                        ).copy(identityValidationStatus = IdentityValidationStatus.UNKNOWN).right()
                    }

                    val response = simpleSignUpService.continueSignUp(event.copy(accountId = accountId.value))

                    response.isRight() shouldBe true
                    response.map {
                        it.hasPassedRiskAnalysis shouldBe false
                    }

                    accountDbRepository.findPartialAccountById(accountId).status shouldBe AccountStatus.REGISTER_INCOMPLETE

                    with(accountRegisterDbRepository.findByAccountId(accountId)) {
                        riskAnalysisFailedReasons shouldContainExactly listOf(RiskAnalysisFailedReason.UNABLE_TO_VALIDATE_DOCUMENT)
                        openForUserReview shouldBe false
                    }

                    verify {
                        registerInstrumentationService.upgraded(any(), any(), any())
                        notificationAdapterMock.notifyRegisterUpgraded(any(), any())
                    }
                }

                @Test
                fun `deve mudar para REGISTER_INCOMPLETE e a validacao de documento for rejeitada`() {
                    loadFridayAccountRegister()

                    every { registerService.validateIdentity(accountId) } answers {
                        accountRegisterDbRepository.findByAccountId(
                            accountId,
                            false,
                        ).copy(identityValidationStatus = IdentityValidationStatus.REJECTED).right()
                    }

                    val response = simpleSignUpService.continueSignUp(event.copy(accountId = accountId.value))

                    response.isRight() shouldBe true
                    response.map {
                        it.hasPassedRiskAnalysis shouldBe false
                    }

                    accountDbRepository.findPartialAccountById(accountId).status shouldBe AccountStatus.REGISTER_INCOMPLETE

                    with(accountRegisterDbRepository.findByAccountId(accountId)) {
                        riskAnalysisFailedReasons shouldContainExactly listOf(RiskAnalysisFailedReason.DOCUMENT_VALIDATION_REJECTED)
                        openForUserReview shouldBe false
                    }

                    verify {
                        registerInstrumentationService.upgraded(any(), any(), any())
                        notificationAdapterMock.notifyRegisterUpgraded(any(), any())
                    }
                }

                @Test
                fun `deve ser rejeitado quando falhar na serpro mas tiver outra razao que faça o cadastro ser rejeitado`() {
                    loadFridayAccountRegister()

                    every { registerService.validateIdentity(accountId) } answers {
                        accountRegisterDbRepository.findByAccountId(
                            accountId,
                            false,
                        ).copy(identityValidationStatus = IdentityValidationStatus.REJECTED).right()
                    }

                    every { kycService.generate(any()) } returns Pair(
                        storedObject,
                        kycDossier.copy(officialData = kycDossier.officialData.copy(hasObitIndication = true)),
                    ).right()

                    val response = simpleSignUpService.continueSignUp(event.copy(accountId = accountId.value))

                    response.isRight() shouldBe true
                    response.map {
                        it.hasPassedRiskAnalysis shouldBe false
                    }

                    accountDbRepository.findPartialAccountById(accountId).status shouldBe AccountStatus.DENIED

                    with(accountRegisterDbRepository.findByAccountId(accountId, false)) {
                        riskAnalysisFailedReasons shouldContainExactlyInAnyOrder listOf(
                            RiskAnalysisFailedReason.DOCUMENT_VALIDATION_REJECTED,
                            RiskAnalysisFailedReason.HAS_OBIT_INDICATION,
                        )
                        openForUserReview shouldBe false
                    }
                    verify {
                        registerInstrumentationService.rejected(any(), any(), any())
                        notificationAdapterMock.notifyRegisterDenied(any(), any())
                        crmService.removeContactAsync(accountId)
                    }
                }

                @Test
                fun `deve fazer upgrade se for encontrado uma duplicação na verificação do livenessgi`() {
                    every {
                        livenessService.verifyDuplication(any())
                    } returns LivenessEnrollmentVerification(
                        duplications = LivenessEnrollmentVerification.Result.Verified(listOf(AccountId("123"))),
                        fraudIndications = LivenessEnrollmentVerification.Result.Verified(emptyList()),
                    ).right()

                    val now = getZonedDateTime()
                    val response = withGivenDateTime(now) {
                        simpleSignUpService.continueSignUp(event.copy(accountId = accountId.value))
                    }

                    response.isRight().shouldBeTrue()
                    response.map {
                        it.hasPassedRiskAnalysis shouldBe false
                    }
                    val partialAccount = accountDbRepository.findPartialAccountById(accountId)
                    partialAccount.registrationType shouldBe RegistrationType.UPGRADED
                    partialAccount.status shouldBe AccountStatus.REGISTER_INCOMPLETE

                    with(accountRegisterDbRepository.findByAccountId(accountId)) {
                        riskAnalysisFailedReasons shouldContainExactly listOf(RiskAnalysisFailedReason.FACE_MATCH_DUPLICATION)
                        openForUserReview shouldBe false
                        agreementData shouldBe null
                    }

                    verify {
                        registerInstrumentationService.upgraded(any(), any(), any())
                        notificationAdapterMock.notifyRegisterUpgraded(any(), any())
                    }
                }

                @Test
                fun `deve fazer upgrade se for encontrado uma possível fraude na verificação do liveness`() {
                    every {
                        livenessService.verifyDuplication(any())
                    } returns LivenessEnrollmentVerification(
                        duplications = LivenessEnrollmentVerification.Result.Verified(emptyList()),
                        fraudIndications = LivenessEnrollmentVerification.Result.Verified(listOf(AccountId("123"))),
                    ).right()

                    val now = getZonedDateTime()
                    val response = withGivenDateTime(now) {
                        simpleSignUpService.continueSignUp(event.copy(accountId = accountId.value))
                    }

                    response.isRight().shouldBeTrue()
                    response.map {
                        it.hasPassedRiskAnalysis shouldBe false
                    }
                    val partialAccount = accountDbRepository.findPartialAccountById(accountId)
                    partialAccount.registrationType shouldBe RegistrationType.UPGRADED
                    partialAccount.status shouldBe AccountStatus.REGISTER_INCOMPLETE

                    with(accountRegisterDbRepository.findByAccountId(accountId)) {
                        riskAnalysisFailedReasons shouldContainExactly listOf(RiskAnalysisFailedReason.FACE_MATCH_FRAUD_INDICATION)
                        openForUserReview shouldBe false
                        agreementData shouldBe null
                    }

                    verify {
                        registerInstrumentationService.upgraded(any(), any(), any())
                        notificationAdapterMock.notifyRegisterUpgraded(any(), any())
                    }
                }

                @Test
                fun `deve negar a conta se for encontrado uma possível indicação de óbito`() {
                    every {
                        kycService.generate(any())
                    } returns Pair(
                        storedObject,
                        kycDossier.copy(officialData = kycDossier.officialData.copy(hasObitIndication = true)),
                    ).right()

                    val now = ZonedDateTime.of(2023, 1, 1, 0, 0, 0, 0, brazilTimeZone)
                    val response = withGivenDateTime(now) {
                        simpleSignUpService.continueSignUp(event.copy(accountId = accountId.value))
                    }

                    response.isRight().shouldBeTrue()

                    response.map {
                        it.hasPassedRiskAnalysis shouldBe false
                    }

                    val partialAccount = accountDbRepository.findPartialAccountById(accountId)
                    partialAccount.registrationType shouldBe RegistrationType.BASIC
                    partialAccount.status shouldBe AccountStatus.DENIED

                    val closureDetails = AccountClosureDetails.create(
                        reason = AccountClosureReason.FRAUD,
                        description = "Cadastro rejeitado por análise de risco",
                        at = now,
                    )

                    with(accountRegisterDbRepository.findByAccountId(accountId, false)) {
                        riskAnalysisFailedReasons shouldContainExactly listOf(RiskAnalysisFailedReason.HAS_OBIT_INDICATION)
                        accountClosureDetails shouldBe closureDetails
                    }

                    verify {
                        registerInstrumentationService.rejected(any(), any(), any())
                        crmService.removeContactAsync(accountId)
                    }
                }

                @Test
                fun `deve negar a conta se o cpf estiver irregular`() {
                    every {
                        kycService.generate(any())
                    } returns Pair(
                        storedObject,
                        kycDossier.copy(officialData = kycDossier.officialData.copy(regular = false)),
                    ).right()

                    val now = ZonedDateTime.of(2023, 1, 1, 0, 0, 0, 0, brazilTimeZone)
                    val response = withGivenDateTime(now) {
                        simpleSignUpService.continueSignUp(event.copy(accountId = accountId.value))
                    }

                    response.isRight().shouldBeTrue()

                    response.map {
                        it.hasPassedRiskAnalysis shouldBe false
                    }

                    val partialAccount = accountDbRepository.findPartialAccountById(accountId)
                    partialAccount.registrationType shouldBe RegistrationType.BASIC
                    partialAccount.status shouldBe AccountStatus.DENIED

                    val closureDetails = AccountClosureDetails.create(
                        reason = AccountClosureReason.RISK_ANALYSIS,
                        description = "Cadastro rejeitado por análise de risco",
                        at = now,
                    )

                    with(accountRegisterDbRepository.findByAccountId(accountId, false)) {
                        riskAnalysisFailedReasons shouldContainExactly listOf(RiskAnalysisFailedReason.IRREGULAR_DOCUMENT)
                        accountClosureDetails shouldBe closureDetails
                    }

                    verify {
                        registerInstrumentationService.rejected(any(), any(), any())
                        crmService.removeContactAsync(accountId)
                    }
                }

                @Test
                fun `deve negar a conta se for constatado que é uma pessoa menor de idade`() {
                    every {
                        kycService.generate(any())
                    } returns Pair(
                        storedObject,
                        kycDossier.copy(
                            officialData = kycDossier.officialData.copy(
                                birthDate = getLocalDate().minusYears(
                                    10,
                                ),
                            ),
                        ),
                    ).right()

                    val now = ZonedDateTime.of(2023, 1, 1, 0, 0, 0, 0, brazilTimeZone)
                    val response = withGivenDateTime(now) {
                        simpleSignUpService.continueSignUp(event.copy(accountId = accountId.value))
                    }

                    val closureDetails = AccountClosureDetails.create(
                        reason = AccountClosureReason.RISK_ANALYSIS,
                        description = "Cadastro rejeitado por análise de risco",
                        at = now,
                    )

                    response.isRight().shouldBeTrue()

                    response.map {
                        it.hasPassedRiskAnalysis shouldBe false
                    }

                    with(accountDbRepository.findPartialAccountById(accountId)) {
                        registrationType shouldBe RegistrationType.BASIC
                        status shouldBe AccountStatus.DENIED
                    }

                    with(accountRegisterDbRepository.findByAccountId(accountId, false)) {
                        riskAnalysisFailedReasons shouldContainExactly listOf(RiskAnalysisFailedReason.UNDERAGE)
                        accountClosureDetails shouldBe closureDetails
                    }

                    verify {
                        registerInstrumentationService.rejected(any(), any(), any())
                        notificationAdapterMock.notifyRegisterDenied(any(), any())
                        crmService.removeContactAsync(accountId)
                    }
                }

                @Test
                fun `deve falhar se o nome não bater com a receita federal e reabrir o cadastro`() {
                    val existingPartialAccount = accountDbRepository.create(
                        username = simpleSignUpRequest.name,
                        emailAddress = simpleSignUpRequest.email,
                        registrationType = RegistrationType.BASIC,
                    )

                    accountRegisterDbRepository.create(
                        accountId = existingPartialAccount.id,
                        emailAddress = simpleSignUpRequest.email,
                        username = simpleSignUpRequest.name,
                        mobilePhone = MobilePhone(ACCOUNT.mobilePhone),
                        mobilePhoneVerified = true,
                        livenessId = simpleSignUpRequest.livenessId,
                        externalId = simpleSignUpRequest.externalId,
                        documentInfo = DocumentInfo(
                            name = simpleSignUpRequest.name,
                            cpf = Document(ACCOUNT.document).value,
                            birthDate = simpleSignUpRequest.birthDate,
                            fatherName = "",
                            motherName = "",
                            rg = "",
                            docType = simpleSignUpRequest.document.type,
                            cnhNumber = null,
                            orgEmission = simpleSignUpRequest.document.issuer,
                            expeditionDate = null,
                            birthCity = "",
                            birthState = simpleSignUpRequest.document.issuerRegion,
                        ),
                        monthlyIncome = MonthlyIncome(0, 2_000_00),
                        registrationType = RegistrationType.BASIC,
                        politicallyExposed = PoliticallyExposed(selfDeclared = false, query = null),
                    )
                    every {
                        kycService.generate(any())
                    } returns Pair(
                        storedObject,
                        kycDossier.copy(officialData = kycDossier.officialData.copy(name = "jane")),
                    ).right()

                    val now = getZonedDateTime()
                    val response = withGivenDateTime(now) {
                        simpleSignUpService.continueSignUp(event.copy(accountId = existingPartialAccount.id.value))
                    }

                    response.isRight().shouldBeTrue()
                    response.map {
                        it.hasPassedRiskAnalysis shouldBe false
                    }

                    val accountRegister = accountRegisterDbRepository.findByAccountId(existingPartialAccount.id)
                    accountRegister.riskAnalysisFailedReasons.shouldNotBeNull()
                    accountRegister.riskAnalysisFailedReasons!!.isNotEmpty() shouldBe true
                    accountRegister.riskAnalysisFailedReasons!![0] shouldBe RiskAnalysisFailedReason.NAME_MISMATCH
                    accountRegister.openForUserReview shouldBe true

                    val account = accountDbRepository.findPartialAccountById(existingPartialAccount.id)
                    account.status shouldBe AccountStatus.REGISTER_INCOMPLETE

                    verify { registerInstrumentationService.reopened(any(), any(), any()) }
                }

                @Test
                fun `deve fazer upgrade se a pessoa tiver 80 anos ou mais`() {
                    every {
                        kycService.generate(any())
                    } returns Pair(
                        storedObject,
                        kycDossier.copy(
                            officialData = kycDossier.officialData.copy(
                                birthDate = getLocalDate().minusYears(
                                    80,
                                ),
                            ),
                        ),
                    ).right()

                    val now = getZonedDateTime()
                    val response = withGivenDateTime(now) {
                        simpleSignUpService.continueSignUp(event.copy(accountId = accountId.value))
                    }

                    response.isRight().shouldBeTrue()
                    response.map {
                        it.hasPassedRiskAnalysis shouldBe false
                    }

                    val partialAccount = accountDbRepository.findPartialAccountById(accountId)
                    partialAccount.registrationType shouldBe RegistrationType.UPGRADED
                    partialAccount.status shouldBe AccountStatus.REGISTER_INCOMPLETE

                    with(accountRegisterDbRepository.findByAccountId(accountId)) {
                        riskAnalysisFailedReasons shouldContainExactly listOf(RiskAnalysisFailedReason.ELDERLY_PERSON)
                        openForUserReview shouldBe false
                        agreementData shouldBe null
                    }

                    verify {
                        registerInstrumentationService.upgraded(any(), any(), any())
                        notificationAdapterMock.notifyRegisterUpgraded(any(), any())
                    }
                }

                @Test
                fun `deve fazer upgrade se a pessoa tiver alguma sanção`() {
                    every {
                        kycService.generate(any())
                    } returns Pair(
                        storedObject,
                        kycDossier.copy(
                            sanctions = listOf(
                                KycDossierSanctions(
                                    source = "",
                                    type = KycDossierSanctionType.ArrestWarrants,
                                    matchRate = 0,
                                    nameUniquenessScore = 0.0,
                                ),
                            ),
                        ),
                    ).right()

                    val now = getZonedDateTime()
                    val response = withGivenDateTime(now) {
                        simpleSignUpService.continueSignUp(event.copy(accountId = accountId.value))
                    }

                    response.isRight().shouldBeTrue()
                    response.map {
                        it.hasPassedRiskAnalysis shouldBe false
                    }
                    val partialAccount = accountDbRepository.findPartialAccountById(accountId)
                    partialAccount.registrationType shouldBe RegistrationType.UPGRADED
                    partialAccount.status shouldBe AccountStatus.REGISTER_INCOMPLETE

                    with(accountRegisterDbRepository.findByAccountId(accountId)) {
                        riskAnalysisFailedReasons shouldContainExactly listOf(RiskAnalysisFailedReason.HAS_SANCTIONS)
                        agreementData shouldBe null
                    }

                    verify {
                        registerInstrumentationService.upgraded(any(), any(), any())
                        notificationAdapterMock.notifyRegisterUpgraded(any(), any())
                    }
                }

                @Test
                fun `deve fazer upgrade se for uma pessoa exposta na mídia`() {
                    every {
                        kycService.generate(any())
                    } returns Pair(
                        storedObject,
                        kycDossier.copy(
                            mep = kycDossier.mep.copy(celebrityLevel = "A"),
                        ),
                    ).right()

                    val now = getZonedDateTime()
                    val response = withGivenDateTime(now) {
                        simpleSignUpService.continueSignUp(event.copy(accountId = accountId.value))
                    }

                    response.isRight().shouldBeTrue()
                    response.map {
                        it.hasPassedRiskAnalysis shouldBe false
                    }

                    val partialAccount = accountDbRepository.findPartialAccountById(accountId)
                    partialAccount.registrationType shouldBe RegistrationType.UPGRADED
                    partialAccount.status shouldBe AccountStatus.REGISTER_INCOMPLETE

                    with(accountRegisterDbRepository.findByAccountId(accountId)) {
                        riskAnalysisFailedReasons shouldContainExactly listOf(RiskAnalysisFailedReason.MEDIA_EXPOSED_PERSON)
                        openForUserReview shouldBe false
                        agreementData shouldBe null
                    }

                    verify {
                        registerInstrumentationService.upgraded(any(), any(), any())
                        notificationAdapterMock.notifyRegisterUpgraded(any(), any())
                    }
                }

                @ParameterizedTest
                @CsvSource(
                    value =
                    [
                        "DIRECT_PEP,false",
                        "RELATED_TO_PEP,false",
                        "RELATED_TO_PEP,true",
                        "NOT_PEP,true",
                    ],
                )
                fun `deve fazer upgrade se for uma pessoa exposta politicamente`(
                    queryResult: PepQueryResult,
                    selfDeclared: Boolean,
                ) {
                    accountRegisterDbRepository.save(
                        accountRegisterDbRepository.findByAccountId(accountId).copy(
                            politicallyExposed = PoliticallyExposed(selfDeclared = selfDeclared, query = null),
                        ),
                    )
                    every {
                        kycService.generate(any())
                    } returns Pair(
                        storedObject,
                        kycDossier.copy(
                            pep = PepQuery(at = getZonedDateTime(), result = queryResult),
                        ),
                    ).right()

                    val now = getZonedDateTime()
                    val response = withGivenDateTime(now) {
                        simpleSignUpService.continueSignUp(event.copy(accountId = accountId.value))
                    }

                    response.isRight().shouldBeTrue()
                    response.map {
                        it.hasPassedRiskAnalysis shouldBe false
                    }

                    val partialAccount = accountDbRepository.findPartialAccountById(accountId)
                    partialAccount.registrationType shouldBe RegistrationType.UPGRADED
                    partialAccount.status shouldBe AccountStatus.REGISTER_INCOMPLETE

                    with(accountRegisterDbRepository.findByAccountId(accountId)) {
                        riskAnalysisFailedReasons shouldContainExactly listOf(RiskAnalysisFailedReason.POLITICALLY_EXPOSED_PERSON)
                    }

                    verify {
                        registerInstrumentationService.upgraded(any(), any(), any())
                        notificationAdapterMock.notifyRegisterUpgraded(any(), any())
                    }
                }

                @Test
                fun `não deve falhar se o usuário passar pela análise`() {
                    val now = getZonedDateTime()
                    val response = withGivenDateTime(now) {
                        simpleSignUpService.continueSignUp(event.copy(accountId = accountId.value))
                    }

                    response.isRight().shouldBeTrue()
                    response.map {
                        it.hasPassedRiskAnalysis shouldBe true
                    }
                }
            }
        }
    }

    companion object {
        @JvmStatic
        fun invalidUserDataValidationResultsForSignUp(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(UserDataValidationResult(valid = false, accountId = null)),
                Arguments.of(UserDataValidationResult(valid = true, accountId = AccountId("FAKE"))),
            )
        }
    }
}