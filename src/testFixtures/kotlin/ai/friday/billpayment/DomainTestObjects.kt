package ai.friday.billpayment

import DynamoDBUtils.getDynamoDbClient
import DynamoDBUtils.getDynamoDbClientAsync
import ai.friday.billpayment.adapters.api.CreateInvoiceTO
import ai.friday.billpayment.adapters.api.RecipientBankDetailsTO
import ai.friday.billpayment.adapters.api.RequestInvoiceRecipientTO
import ai.friday.billpayment.adapters.celcoin.CelcoinBillValidationResponse
import ai.friday.billpayment.adapters.cognito.CognitoAdapter
import ai.friday.billpayment.adapters.dynamodb.BalanceAuthorizationPaymentOperationConverter
import ai.friday.billpayment.adapters.dynamodb.BankTransferSettlementOperationConverter
import ai.friday.billpayment.adapters.dynamodb.BillSettlementTargetConverter
import ai.friday.billpayment.adapters.dynamodb.BoletoSettlementOperationConverter
import ai.friday.billpayment.adapters.dynamodb.CreditCardAuthorizationPaymentOperationConverter
import ai.friday.billpayment.adapters.dynamodb.CreditCardCashInSettlementTargetConverter
import ai.friday.billpayment.adapters.dynamodb.DEFAULT_LEGACY_ACCOUNT_CONFIGURATION
import ai.friday.billpayment.adapters.dynamodb.FraudPreventionPaymentOperationConverter
import ai.friday.billpayment.adapters.dynamodb.TransactionDynamo
import ai.friday.billpayment.adapters.dynamodb.TransactionEntityConverter
import ai.friday.billpayment.adapters.messaging.SNSEventPublisher
import ai.friday.billpayment.adapters.micronaut.FeaturesConfigurationMicronaut
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.adapters.pdf.FlyingSaucerPDFConverter
import ai.friday.billpayment.adapters.pdf.PDFConfiguration
import ai.friday.billpayment.app.Acquirer
import ai.friday.billpayment.app.AsyncUtils
import ai.friday.billpayment.app.AsyncUtils.callAsync
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.FinancialInstitutionGlobalData
import ai.friday.billpayment.app.GIGU_ENV
import ai.friday.billpayment.app.ME_POUPE_ENV
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountEvent
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountProviderName
import ai.friday.billpayment.app.account.AccountRegisterData
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.Address
import ai.friday.billpayment.app.account.AgreementData
import ai.friday.billpayment.app.account.CreditCard
import ai.friday.billpayment.app.account.CreditCardBrand
import ai.friday.billpayment.app.account.CreditCardChallenge
import ai.friday.billpayment.app.account.CreditCardChallengeStatus
import ai.friday.billpayment.app.account.CreditCardConfiguration
import ai.friday.billpayment.app.account.CreditCardExternalId
import ai.friday.billpayment.app.account.CreditCardPaymentStatus
import ai.friday.billpayment.app.account.DocumentInfo
import ai.friday.billpayment.app.account.Gender
import ai.friday.billpayment.app.account.ImageOptimizer
import ai.friday.billpayment.app.account.LegacyAccountConfiguration
import ai.friday.billpayment.app.account.MonthlyIncome
import ai.friday.billpayment.app.account.PartialAccount
import ai.friday.billpayment.app.account.PepQuery
import ai.friday.billpayment.app.account.PepQueryResult
import ai.friday.billpayment.app.account.PoliticallyExposed
import ai.friday.billpayment.app.account.RegistrationType
import ai.friday.billpayment.app.account.RiskLevel
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.account.StoredObject
import ai.friday.billpayment.app.account.UploadedDocumentImages
import ai.friday.billpayment.app.account.UserPoolEnableUserError
import ai.friday.billpayment.app.account.UserPoolRemoveUserError
import ai.friday.billpayment.app.account.UserPoolSetMfaPreferenceError
import ai.friday.billpayment.app.account.generateSignatureKey
import ai.friday.billpayment.app.account.hasEarlyAccess
import ai.friday.billpayment.app.account.toContractForm
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.Bank
import ai.friday.billpayment.app.banking.BankAccountMode
import ai.friday.billpayment.app.banking.BankStatementItemFlow
import ai.friday.billpayment.app.banking.BankStatementItemType
import ai.friday.billpayment.app.banking.DefaultBankStatementItem
import ai.friday.billpayment.app.banking.ExternalBankAccount
import ai.friday.billpayment.app.banking.FinancialInstitution
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.banking.OmnibusBankAccountConfiguration
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.BatchSchedulingId
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillAdded
import ai.friday.billpayment.app.bill.BillCategoryAdded
import ai.friday.billpayment.app.bill.BillCategoryDeleted
import ai.friday.billpayment.app.bill.BillDenied
import ai.friday.billpayment.app.bill.BillEvent
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillIgnored
import ai.friday.billpayment.app.bill.BillMarkedAsPaid
import ai.friday.billpayment.app.bill.BillMarkedAsPaidCanceled
import ai.friday.billpayment.app.bill.BillMoved
import ai.friday.billpayment.app.bill.BillPaid
import ai.friday.billpayment.app.bill.BillPaymentScheduleCanceled
import ai.friday.billpayment.app.bill.BillPaymentScheduleStarted
import ai.friday.billpayment.app.bill.BillPaymentScheduleUpdated
import ai.friday.billpayment.app.bill.BillPaymentScheduled
import ai.friday.billpayment.app.bill.BillPaymentScheduledBalanceInfo
import ai.friday.billpayment.app.bill.BillPermissionUpdated
import ai.friday.billpayment.app.bill.BillReactivated
import ai.friday.billpayment.app.bill.BillRecipientUpdated
import ai.friday.billpayment.app.bill.BillSchedulePostponed
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillTag
import ai.friday.billpayment.app.bill.BillTagAdded
import ai.friday.billpayment.app.bill.BillTagDeleted
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.bill.BillViewSchedule
import ai.friday.billpayment.app.bill.DescriptionUpdated
import ai.friday.billpayment.app.bill.FichaCompensacaoAdded
import ai.friday.billpayment.app.bill.PaymentFailed
import ai.friday.billpayment.app.bill.PaymentRefunded
import ai.friday.billpayment.app.bill.PaymentRefundedReason
import ai.friday.billpayment.app.bill.PaymentStarted
import ai.friday.billpayment.app.bill.PermissionUpdated
import ai.friday.billpayment.app.bill.Recipient
import ai.friday.billpayment.app.bill.RecurrenceUpdated
import ai.friday.billpayment.app.bill.RegisterUpdated
import ai.friday.billpayment.app.bill.ScheduleCanceledReason
import ai.friday.billpayment.app.bill.SchedulePostponedReason
import ai.friday.billpayment.app.bill.TrackableBill
import ai.friday.billpayment.app.bill.UpdatedRegisterData
import ai.friday.billpayment.app.bill.UpdatedScheduleData
import ai.friday.billpayment.app.bill.calculateEffectiveDate
import ai.friday.billpayment.app.cashIn.CreditCardCashIn
import ai.friday.billpayment.app.contact.BankAccountId
import ai.friday.billpayment.app.contact.Contact
import ai.friday.billpayment.app.contact.ContactId
import ai.friday.billpayment.app.contact.LastUsed
import ai.friday.billpayment.app.contact.SavedBankAccount
import ai.friday.billpayment.app.contact.SavedPixKey
import ai.friday.billpayment.app.dda.DDAConfig
import ai.friday.billpayment.app.dda.DDAProvider
import ai.friday.billpayment.app.email.DefaultEmailService
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.DocumentType
import ai.friday.billpayment.app.integrations.EmailService
import ai.friday.billpayment.app.integrations.EventPublisher
import ai.friday.billpayment.app.integrations.UserPoolAdapter
import ai.friday.billpayment.app.integrations.UserPoolListAuthEventsException
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.liveness.LivenessId
import ai.friday.billpayment.app.metrics.AbstractMetrics
import ai.friday.billpayment.app.metrics.push
import ai.friday.billpayment.app.notification.EmailTemplatesConfiguration
import ai.friday.billpayment.app.notification.TemplatesHtmlToImageConfiguration
import ai.friday.billpayment.app.payment.AmountCalculationModel
import ai.friday.billpayment.app.payment.BillRegisterData
import ai.friday.billpayment.app.payment.BillRegisterStatus
import ai.friday.billpayment.app.payment.BoletoSettlementResult
import ai.friday.billpayment.app.payment.BoletoSettlementStatus
import ai.friday.billpayment.app.payment.CreditCardAuthorization
import ai.friday.billpayment.app.payment.DefaultBillValidationResponse
import ai.friday.billpayment.app.payment.DiscountData
import ai.friday.billpayment.app.payment.DiscountType
import ai.friday.billpayment.app.payment.ErrorSource
import ai.friday.billpayment.app.payment.FichaCompensacaoType
import ai.friday.billpayment.app.payment.FineData
import ai.friday.billpayment.app.payment.FineType
import ai.friday.billpayment.app.payment.InterestData
import ai.friday.billpayment.app.payment.InterestType
import ai.friday.billpayment.app.payment.PaymentData
import ai.friday.billpayment.app.payment.PaymentMethodsDetail
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithBalance
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithCreditCard
import ai.friday.billpayment.app.payment.PaymentOperation
import ai.friday.billpayment.app.payment.ScheduleTo
import ai.friday.billpayment.app.payment.ScheduledBill
import ai.friday.billpayment.app.payment.SettlementData
import ai.friday.billpayment.app.payment.SinglePaymentData
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.app.payment.TransactionType
import ai.friday.billpayment.app.payment.toPayer
import ai.friday.billpayment.app.pfm.BillCategory
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.pfm.WalletBillCategory
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyDetails
import ai.friday.billpayment.app.pix.PixKeyHolder
import ai.friday.billpayment.app.pix.PixKeyOwner
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.pix.PixTransactionError
import ai.friday.billpayment.app.recurrence.BillRecurrence
import ai.friday.billpayment.app.recurrence.RecurrenceFrequency
import ai.friday.billpayment.app.recurrence.RecurrenceId
import ai.friday.billpayment.app.recurrence.RecurrenceRule
import ai.friday.billpayment.app.recurrence.RecurrenceStatus
import ai.friday.billpayment.app.register.kyc.KycDossier
import ai.friday.billpayment.app.register.kyc.KycDossierMepQuery
import ai.friday.billpayment.app.register.kyc.KycDossierOfficialDocumentData
import ai.friday.billpayment.app.register.kyc.KycDossierTaxIdRegion
import ai.friday.billpayment.app.security.Fingerprint
import ai.friday.billpayment.app.security.FingerprintApp
import ai.friday.billpayment.app.security.FingerprintDevice
import ai.friday.billpayment.app.security.FingerprintGeolocation
import ai.friday.billpayment.app.security.FingerprintOS
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.app.vehicledebts.LicensePlate
import ai.friday.billpayment.app.vehicledebts.Vehicle
import ai.friday.billpayment.app.vehicledebts.VehicleStatus
import ai.friday.billpayment.app.wallet.BillPermission
import ai.friday.billpayment.app.wallet.Invite
import ai.friday.billpayment.app.wallet.InviteStatus
import ai.friday.billpayment.app.wallet.MemberPermissions
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletEvent
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.BILL_ID
import ai.friday.billpayment.integration.BILL_ID_2
import ai.friday.billpayment.integration.BILL_ID_3
import ai.friday.billpayment.integration.BILL_ID_4
import ai.friday.billpayment.integration.BILL_ID_5
import ai.friday.billpayment.integration.BILL_ID_6
import ai.friday.billpayment.integration.BILL_ID_7
import ai.friday.billpayment.integration.BILL_ID_8
import ai.friday.billpayment.integration.CONCESSIONARIA_DIGITABLE_LINE
import ai.friday.billpayment.integration.CORRELATION_ID
import ai.friday.billpayment.integration.DIGITABLE_LINE
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.EMAIL
import ai.friday.billpayment.integration.FICHA_DE_COMPENSACAO_DIGITABLE_LINE
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.NAME
import ai.friday.billpayment.integration.PAYMENT_METHOD_EXTERNAL_ID
import ai.friday.billpayment.integration.PAYMENT_METHOD_ID
import ai.friday.billpayment.integration.PAYMENT_METHOD_ID_2
import ai.friday.billpayment.integration.PAYMENT_METHOD_ID_5
import ai.friday.billpayment.integration.PREFECTURE_BARCODE_LINE
import ai.friday.billpayment.integration.WALLET_ID
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.timeFormat
import arrow.core.Either
import arrow.core.right
import ch.qos.logback.classic.Level
import ch.qos.logback.classic.Logger
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.micronaut.context.annotation.Factory
import io.micronaut.context.annotation.Primary
import io.micronaut.context.annotation.Replaces
import io.micronaut.context.annotation.Requires
import io.micronaut.context.annotation.Secondary
import io.mockk.Runs
import io.mockk.coEvery
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.runs
import io.mockk.unmockkObject
import io.mockk.unmockkStatic
import io.via1.communicationcentre.app.receipt.SQSEvent
import jakarta.inject.Singleton
import java.io.ByteArrayOutputStream
import java.math.BigDecimal
import java.math.BigInteger
import java.net.URI
import java.time.DayOfWeek
import java.time.Instant
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.time.temporal.TemporalAdjusters
import java.util.UUID
import java.util.concurrent.Semaphore
import java.util.function.Supplier
import kotlin.reflect.KFunction2
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.runBlocking
import net.javacrumbs.shedlock.core.LockAssert.assertLocked
import org.junit.jupiter.params.provider.Arguments
import org.slf4j.LoggerFactory
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.services.dynamodb.DynamoDbClient
import software.amazon.awssdk.services.sqs.model.Message

const val RECIPIENT_ID = "fake_recipientid"
const val RECIPIENT_PIX_EMAIL_KEY = "<EMAIL>"

val FULL_FORMAT: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")

val CREATED_ON: LocalDateTime = LocalDateTime.parse("2019-09-18 16:51:56", FULL_FORMAT)

val EMAIL_ADDRESS: EmailAddress = EmailAddress("<EMAIL>")

val ACCOUNT = Account(
    accountId = AccountId(ACCOUNT_ID),
    name = NAME,
    emailAddress = EmailAddress("email"),
    document = DOCUMENT,
    documentType = "CPF",
    mobilePhone = "+*************",
    created = getZonedDateTime(),
    updated = getZonedDateTime(),
    status = AccountStatus.ACTIVE,
    configuration = DEFAULT_LEGACY_ACCOUNT_CONFIGURATION.copy(defaultWalletId = WalletId(WALLET_ID)),
    imageUrlSmall = "image_url_small",
    imageUrlLarge = "image_url_large",
    subscriptionType = SubscriptionType.PIX,
)

val PARTIAL_ACCOUNT = PartialAccount(
    id = AccountId(ACCOUNT_ID),
    name = NAME,
    emailAddress = EmailAddress("email"),
    status = AccountStatus.REGISTER_INCOMPLETE,
    role = Role.GUEST,
    statusUpdated = null,
    groups = listOf(),
    registrationType = RegistrationType.BASIC,
    subscriptionType = SubscriptionType.PIX,
)

val DEFAULT_CREDIT_CARD_CONFIGURATION = CreditCardConfiguration(
    quota = 200000,
)

val CREDIT_CARD_CONFIGURATION = LegacyAccountConfiguration(
    creditCardConfiguration = DEFAULT_CREDIT_CARD_CONFIGURATION,
    defaultWalletId = null,
    receiveDDANotification = true,
    receiveNotification = true,
    groups = listOf(AccountGroup.CREDIT_CARD_STANDARD_PLAN),
)

val ACCOUNT_WITH_CREDIT_CARD = ACCOUNT.copy(configuration = CREDIT_CARD_CONFIGURATION)

val transactionId = TransactionId("********")

val mobilePhone = MobilePhone("+*************")

val email = EmailAddress("<EMAIL>")

val paymentMethodId = AccountPaymentMethodId(PAYMENT_METHOD_ID)
val paymentMethodId2 = AccountPaymentMethodId(PAYMENT_METHOD_ID_2)

val VEHICLE_ACTIVE = Vehicle(
    licensePlate = LicensePlate("ABC1234"),
    status = VehicleStatus.ACTIVE,
    description = "Carro",
    accountId = AccountId(ACCOUNT_ID),
)

val VEHICLE_INACTIVE = Vehicle(
    licensePlate = LicensePlate("XYZ1234"),
    status = VehicleStatus.INACTIVE,
    description = "Carro 2",
    accountId = AccountId(ACCOUNT_ID),
)

val VEHICLE_PENDING = Vehicle(
    licensePlate = LicensePlate("DEF1234"),
    status = VehicleStatus.PENDING,
    description = "Carro 3",
    accountId = AccountId(ACCOUNT_ID),
)

fun createPaymentMethodsDetailWithBalance(
    paymentMethodId: AccountPaymentMethodId,
    amount: Long,
    calculationId: String? = null,
) =
    PaymentMethodsDetailWithBalance(
        paymentMethodId = paymentMethodId,
        amount = amount,
        calculationId = calculationId,
    )

fun createSinglePaymentDataWithBalance(
    accountPaymentMethod: AccountPaymentMethod,
    amount: Long,
    calculationId: String? = null,
    payment: PaymentOperation? = null,
) = SinglePaymentData(
    accountPaymentMethod = accountPaymentMethod,
    details = createPaymentMethodsDetailWithBalance(
        paymentMethodId = accountPaymentMethod.id,
        amount = amount,
        calculationId = calculationId,
    ),
    payment = payment,
)

fun createSettlementDataFromBill(bill: Bill, serviceAmountTax: Long = 0): SettlementData {
    return SettlementData(
        settlementTarget = bill,
        serviceAmountTax = serviceAmountTax,
        totalAmount = bill.amountTotal,
        settlementOperation = null,
    )
}

fun createPaymentMethodsDetailWithCreditCard(
    paymentMethodId: AccountPaymentMethodId,
    netAmount: Long,
    feeAmount: Long,
    installments: Int,
    calculationId: String? = null,
) =
    PaymentMethodsDetailWithCreditCard(
        paymentMethodId = paymentMethodId,
        netAmount = netAmount,
        feeAmount = feeAmount,
        fee = 4.0,
        installments = installments,
        calculationId = calculationId,
    )

fun createSinglePaymentDataWithCreditCard(
    accountPaymentMethod: AccountPaymentMethod,
    netAmount: Long,
    feeAmount: Long,
    installments: Int = 1,
    calculationId: String? = null,
    payment: PaymentOperation? = null,
) = SinglePaymentData(
    accountPaymentMethod = accountPaymentMethod,
    details = createPaymentMethodsDetailWithCreditCard(
        paymentMethodId = accountPaymentMethod.id,
        netAmount = netAmount,
        feeAmount = feeAmount,
        installments = installments,
        calculationId = calculationId,
    ),
    payment = payment,
)

val basicAccountRegisterData = AccountRegisterData(
    accountId = AccountId(),
    emailAddress = EmailAddress(email = "<EMAIL>"),
    emailVerified = true,
    emailTokenExpiration = null,
    nickname = "ze",
    birthDate = getLocalDate(),
    document = Document("***********"),
    mobilePhone = MobilePhone("+*************"),
    mobilePhoneVerified = true,
    mobilePhoneTokenExpiration = null,
    created = getZonedDateTime(),
    lastUpdated = getZonedDateTime(),
    upgradeStarted = null,
    livenessId = LivenessId(UUID.randomUUID().toString()),
    uploadedSelfie = StoredObject("region", "bucket", "uploadedSelfie"),
    agreementData = null,
    kycFile = null,
    openForUserReview = false,
    openedForUserReviewAt = null,
    externalId = null,
    riskAnalysisFailedReasons = listOf(),
    riskAnalysisFailedReasonsHistory = listOf(),
    documentInfo = null,
    calculatedGender = null,
    isDocumentEdited = false,
    uploadedCNH = null,
    uploadedDocument = null,
    address = null,
    monthlyIncome = null,
    politicallyExposed = null,
    registrationType = RegistrationType.BASIC,
    fraudListMatch = false,
)

val accountRegisterData = AccountRegisterData(
    accountId = AccountId(ACCOUNT_ID),
    mobilePhoneVerified = false,
    emailAddress = EMAIL_ADDRESS,
    nickname = "Skywalker",
    created = getZonedDateTime().minusDays(1),
    lastUpdated = getZonedDateTime().minusDays(1),
    upgradeStarted = null,
    isDocumentEdited = false,
    documentInfo = null,
    openForUserReview = false,
    openedForUserReviewAt = null,
    calculatedGender = Gender.F,
    externalId = null,
    riskAnalysisFailedReasons = null,
    riskAnalysisFailedReasonsHistory = listOf(),
    registrationType = RegistrationType.BASIC,
    fraudListMatch = false,
)

val accountRegisterDataWithPhoneVerified =
    accountRegisterData.copy(
        mobilePhone = MobilePhone("+*************"),
        mobilePhoneVerified = true,
        mobilePhoneTokenExpiration = 100,
    )

val accountRegisterDataWithEmailVerified =
    accountRegisterDataWithPhoneVerified.copy(
        emailVerified = true,
    )

val rgDocumentInfo = DocumentInfo(
    name = "Luke Skywalker da Silva",
    cpf = "***********",
    birthDate = getLocalDate().minusYears(100).plusDays(1),
    fatherName = "Vader",
    motherName = "Padmé",
    rg = "*********",
    docType = DocumentType.of("RG"),
    cnhNumber = null,
    orgEmission = "DETRAN",
    expeditionDate = getLocalDate().minusYears(70).plusDays(1),
    birthCity = "Rio de Janeiro",
    birthState = "RJ",
)

val cnhDocumentInfo = rgDocumentInfo.copy(
    rg = "",
    docType = DocumentType.of("CNH"),
    cnhNumber = "*********",
)

val accountRegisterDataWithDocumentInfo = accountRegisterDataWithEmailVerified.copy(
    documentInfo = cnhDocumentInfo,
    isDocumentEdited = false,
    uploadedCNH = StoredObject("", "", ""),
    calculatedGender = Gender.M,
)

val accountRegisterDataWithCNHUploaded = accountRegisterData.copy(
    isDocumentEdited = false,
    uploadedCNH = StoredObject("", "", ""),
)

val accountRegisterDataWithRGUploaded = accountRegisterData.copy(
    isDocumentEdited = false,
    uploadedCNH = null,
    uploadedDocument = UploadedDocumentImages(
        front = StoredObject(region = "", bucket = "", key = ""),
        documentType = DocumentType.RG,
        back = null,
    ),
)

val accountRegisterWithEmailNotVerified = accountRegisterData.copy(
    emailAddress = EmailAddress("+<EMAIL>"),
    emailVerified = false,
)

val accountRegisterDataWithAddress = accountRegisterDataWithDocumentInfo.copy(
    address =
    Address(
        streetType = "Rua",
        streetName = "Oswaldo Cruz",
        number = "6",
        complement = "bloco 102",
        neighborhood = "Flamengo",
        city = "Rio de Janeiro",
        state = "RJ",
        zipCode = "********",
    ),
)

val accountRegisterDataWithoutMonthlyIncomeAndAgreement = accountRegisterDataWithAddress.copy(
    uploadedCNH = StoredObject("region", "bucket", "uploadedDocument"),
    uploadedSelfie = StoredObject("region", "bucket", "uploadedSelfie"),
)

val accountRegisterMissingOnlyAgreement = accountRegisterDataWithAddress.copy(
    uploadedCNH = StoredObject("region", "bucket", "uploadedDocument"),
    uploadedSelfie = StoredObject("region", "bucket", "uploadedSelfie"),
    monthlyIncome = MonthlyIncome(0, 2_000_00),
    politicallyExposed = PoliticallyExposed(false, null),
)

val accountRegisterDataMissingAcceptedAt = accountRegisterMissingOnlyAgreement.copy(
    agreementData = AgreementData(
        acceptedAt = null,
        userContractFile = StoredObject("region", "bucket", "userContract.pdf"),
        userContractSignature = accountRegisterMissingOnlyAgreement.toContractForm().generateSignatureKey(),
        declarationOfResidencyFile = StoredObject("region", "bucket", "declarationOfResidencyFile.pdf"),
    ),
)

val accountRegisterDataMissingUpgradeAgreement = accountRegisterMissingOnlyAgreement.copy(
    agreementData = AgreementData(
        acceptedAt = getZonedDateTime(),
        userContractFile = StoredObject("region", "bucket", "userContract.pdf"),
        userContractSignature = accountRegisterMissingOnlyAgreement.toContractForm().generateSignatureKey(),
        declarationOfResidencyFile = StoredObject("region", "bucket", "declarationOfResidencyFile.pdf"),
    ),
)

val accountRegisterCompleted = accountRegisterDataMissingAcceptedAt.copy(
    kycFile = StoredObject("region", "bucket", "kycReport.pdf"),
)

val authorizedCreditCardAuthorization = CreditCardAuthorization(
    Acquirer.CIELO,
    "529aca91-2961-4976-8f7d-9e3f2fa8a0c9",
    CreditCardPaymentStatus.AUTHORIZED,
    "********",
    5000,
    "0",
    "Success",
    tid = "tid",
    authorizationCode = "authorizationCode",
)

fun buildCreditCardAuthorization(
    status: CreditCardPaymentStatus = CreditCardPaymentStatus.AUTHORIZED,
    amount: Long = 5000,
) =
    CreditCardAuthorization(
        acquirer = Acquirer.CIELO,
        transactionId = "transaction-id",
        status = status,
        acquirerTid = "********",
        amount = amount,
        acquirerReturnCode = "0",
        acquirerReturnMessage = "Success",
    )

fun getActiveBill(
    walletId: WalletId = WalletId(WALLET_ID),
    accountId: AccountId? = null,
    schedule: BillViewSchedule? = null,
    billId: BillId = BillId(BILL_ID_4),
    paymentLimitTime: String = "00:00",
    dueDate: LocalDate = getLocalDate().plusDays(3),
    subscriptionFee: Boolean = false,
    effectiveDueDate: LocalDate = getLocalDate().plusDays(3),
    fichaCompensacaoType: FichaCompensacaoType? = null,
    recipient: Recipient = Recipient("Claro"),
    payerDocument: String = "*********09",
    paymentDetails: PaymentMethodsDetail? = null,
    scheduledBy: AccountId? = null,
    amount: Long = 15591L,
): BillView {
    return BillView(
        walletId = walletId,
        assignor = "CLARO NET",
        billType = BillType.CONCESSIONARIA,
        billDescription = "ACTIVE BILL",
        amount = amount,
        billId = billId,
        recipient = recipient,
        paymentLimitTime = LocalTime.parse(paymentLimitTime, timeFormat),
        createdOn = CREATED_ON,
        status = BillStatus.ACTIVE,
        dueDate = dueDate,
        barCode = BarCode.ofDigitable(DIGITABLE_LINE),
        source = if (accountId == null) ActionSource.WalletMailBox(from = EMAIL) else ActionSource.Api(accountId = accountId),
        payerAlias = "Beltrano",
        payerDocument = payerDocument,
        effectiveDueDate = effectiveDueDate,
        schedule = schedule,
        subscriptionFee = subscriptionFee,
        fichaCompensacaoType = fichaCompensacaoType,
        paymentDetails = paymentDetails,
        tags = emptySet(),
        categoryId = null,
        categorySuggestions = listOf(),
        scheduledBy = scheduledBy,
        automaticPixData = null,
        automaticPixAuthorizationMaximumAmount = null,
    )
}

fun getActiveBillFicha(
    walletId: WalletId,
    type: FichaCompensacaoType? = FichaCompensacaoType.CARTAO_DE_CREDITO,
    recipient: Recipient = Recipient("AMERICANAS"),
): BillView {
    return BillView(
        walletId = walletId,
        assignor = "BRADESCO",
        recipient = recipient,
        billType = BillType.FICHA_COMPENSACAO,
        billDescription = "ACTIVE BILL",
        amount = 15591,
        billId = BillId(BILL_ID_4),
        paymentLimitTime = LocalTime.MIDNIGHT,
        createdOn = CREATED_ON,
        status = BillStatus.ACTIVE,
        dueDate = getLocalDate().plusDays(3),
        effectiveDueDate = getLocalDate().plusDays(3),
        barCode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE),
        expirationDate = getLocalDate(),
        source = ActionSource.WalletMailBox(from = EMAIL),
        payerAlias = "Beltrano",
        payerDocument = "*********09",
        fichaCompensacaoType = type,
        subscriptionFee = false,
        tags = emptySet(),
        categoryId = null,
        categorySuggestions = listOf(),
        automaticPixData = null,
        automaticPixAuthorizationMaximumAmount = null,
    )
}

fun getActiveFichaDeCompensacao(
    walletId: WalletId = WalletId(WALLET_ID),
    actionSource: ActionSource = ActionSource.WalletMailBox(from = EMAIL),
): BillView {
    return BillView(
        walletId = walletId,
        assignor = "CLARO NET",
        billType = BillType.FICHA_COMPENSACAO,
        billDescription = "ACTIVE BILL",
        amount = 15591,
        billId = BillId(BILL_ID_6),
        recipient = Recipient("Claro"),
        paymentLimitTime = LocalTime.MIDNIGHT,
        createdOn = CREATED_ON,
        status = BillStatus.ACTIVE,
        dueDate = getLocalDate().plusDays(3),
        effectiveDueDate = getLocalDate().plusDays(3),
        barCode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE),
        expirationDate = getLocalDate(),
        source = actionSource,
        payerAlias = "Beltrano",
        payerDocument = "*********09",
        subscriptionFee = false,
        tags = emptySet(),
        categoryId = null,
        categorySuggestions = listOf(),
        automaticPixData = null,
        automaticPixAuthorizationMaximumAmount = null,
    )
}

fun getIgnoredBill(walletId: WalletId): BillView {
    return BillView(
        walletId = walletId,
        assignor = "CLARO NET",
        billType = BillType.CONCESSIONARIA,
        billDescription = "IGNORED BILL",
        amount = 15591,
        billId = BillId(BILL_ID_5),
        recipient = Recipient("Claro"),
        paymentLimitTime = LocalTime.MIDNIGHT,
        createdOn = CREATED_ON,
        status = BillStatus.IGNORED,
        dueDate = getLocalDate().plusDays(4),
        effectiveDueDate = getLocalDate().plusDays(4),
        barCode = BarCode.ofDigitable(DIGITABLE_LINE),
        source = ActionSource.WalletMailBox(from = EMAIL),
        payerAlias = "Beltrano",
        payerDocument = "*********09",
        subscriptionFee = false,
        tags = emptySet(),
        categoryId = null,
        categorySuggestions = listOf(),
        automaticPixData = null,
        automaticPixAuthorizationMaximumAmount = null,
    )
}

fun getBillWithDueDate(
    walletId: WalletId = WalletId(WALLET_ID),
    daysToSubtract: Long = 2,
    daysToAdd: Long = 0,
    billid: BillId = BillId(BILL_ID_3),
    effectiveDaysToAdd: Long = 0,
    effectiveDaysToSubtract: Long = 0,
): BillView {
    return BillView(
        walletId = walletId,
        assignor = "CLARO NET",
        billType = BillType.CONCESSIONARIA,
        billDescription = "OVERDUE BILL",
        amount = 15591,
        billId = billid,
        recipient = Recipient("Claro"),
        paymentLimitTime = LocalTime.MIDNIGHT,
        createdOn = CREATED_ON,
        status = BillStatus.ACTIVE,
        dueDate = getLocalDate().minusDays(daysToSubtract).plusDays(daysToAdd),
        effectiveDueDate = getLocalDate().minusDays(effectiveDaysToSubtract)
            .plusDays(effectiveDaysToAdd),
        barCode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE),
        payerDocument = "***********",
        payerName = "Fulano",
        payerAlias = "Beltrano",
        source = ActionSource.WalletMailBox(from = EMAIL),
        subscriptionFee = false,
        tags = emptySet(),
        categoryId = null,
        categorySuggestions = listOf(),
        automaticPixData = null,
        automaticPixAuthorizationMaximumAmount = null,
    )
}

fun getActiveInvestmentBill(
    walletId: WalletId = WalletId(WALLET_ID),
): BillView {
    return BillView(
        billId = BillId("BILL-INVESTMENT-BILL"),
        billDescription = "Investment Bill",
        amount = 15591,
        dueDate = getLocalDate().plusDays(3),
        effectiveDueDate = getLocalDate().plusDays(3),
        billType = BillType.INVESTMENT,
        walletId = walletId,
        status = BillStatus.ACTIVE,
        paymentLimitTime = LocalTime.of(16, 30),
        createdOn = CREATED_ON,
        source = ActionSource.Api(ACCOUNT.accountId),
        subscriptionFee = false,
        tags = emptySet(),
        categoryId = PFMCategoryId("PFM-CATEGORY-ID-INVESTIMENTO"),
        categorySuggestions = listOf(),
        goalId = GoalId("GOAL-ID-1"),
        automaticPixData = null,
        automaticPixAuthorizationMaximumAmount = null,
    )
}

fun getPaidBill(
    billId: BillId = BillId(BILL_ID),
    walletId: WalletId = WalletId(WALLET_ID),
    subscriptionFee: Boolean = false,
    dueDate: LocalDate = getLocalDate().plusDays(2),
    accountId: AccountId? = null,
    source: ActionSource = ActionSource.WalletMailBox(from = EMAIL, accountId = accountId),
    barCode: BarCode = BarCode.ofDigitable(CONCESSIONARIA_DIGITABLE_LINE),
    fichaCompensacaoType: FichaCompensacaoType? = null,
    amount: Long = 15591L,
    paymentMethodsDetail: PaymentMethodsDetail? = PaymentMethodsDetailWithBalance(
        amount = amount,
        paymentMethodId = AccountPaymentMethodId(value = PAYMENT_METHOD_ID),
        calculationId = null,
    ),
): BillView {
    return BillView(
        walletId = walletId,
        assignor = "CLARO NET",
        billType = if (barCode.checkIsConcessionaria()) {
            BillType.CONCESSIONARIA
        } else {
            BillType.FICHA_COMPENSACAO
        },
        billDescription = "PAID BILL",
        amount = amount,
        billId = billId,
        recipient = Recipient("Claro"),
        paymentLimitTime = LocalTime.MIDNIGHT,
        createdOn = CREATED_ON,
        status = BillStatus.PAID,
        dueDate = dueDate,
        effectiveDueDate = getLocalDate().plusDays(2),
        barCode = barCode,
        paidDate = LocalDateTime.of(2020, 10, 10, 10, 10, 0),
        source = source,
        amountPaid = amount,
        subscriptionFee = subscriptionFee,
        fichaCompensacaoType = fichaCompensacaoType,
        paymentDetails = paymentMethodsDetail,
        transactionCorrelationId = CORRELATION_ID,
        tags = emptySet(),
        categoryId = null,
        categorySuggestions = listOf(),
        automaticPixData = null,
        automaticPixAuthorizationMaximumAmount = null,
    )
}

fun getInvoiceBill(walletId: WalletId = WalletId(WALLET_ID)): BillView {
    return BillView(
        walletId = walletId,
        billType = BillType.INVOICE,
        billDescription = "PAID BILL",
        amount = 15591,
        billId = BillId(BILL_ID),
        recipient = Recipient("Claro"),
        paymentLimitTime = LocalTime.MIDNIGHT,
        createdOn = CREATED_ON,
        status = BillStatus.ACTIVE,
        dueDate = getLocalDate().plusDays(2),
        effectiveDueDate = getLocalDate().plusDays(2),
        source = ActionSource.Api(ACCOUNT.accountId),
        subscriptionFee = false,
        tags = emptySet(),
        categoryId = null,
        categorySuggestions = listOf(),
        automaticPixData = null,
        automaticPixAuthorizationMaximumAmount = null,
    )
}

val billAddedFicha = FichaCompensacaoAdded(
    billId = BillId(BILL_ID_4),
    created = Instant.now().toEpochMilli(),
    walletId = WalletId(WALLET_ID),
    description = "ACTIVE BILL",
    dueDate = getLocalDate().plusDays(3),
    amount = 15591L,
    barcode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE),
    recipient = Recipient(name = "AMERICANAS", document = DOCUMENT),
    recipientChain = null,
    assignor = "BRADESCO",
    document = DOCUMENT,
    paymentLimitTime = LocalTime.parse("17:00", timeFormat),
    lastSettleDate = getLocalDate().plusDays(1).format(dateFormat),
    expirationDate = "2020-10-05",
    actionSource = ActionSource.WalletMailBox(from = EMAIL_ADDRESS.toString()),
    effectiveDueDate = calculateEffectiveDate(
        getLocalDate().plusDays(3),
        BillType.FICHA_COMPENSACAO,
        BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE),
    ),
    amountTotal = 15591L,
    discount = 0,
    fine = 0,
    interest = 0,
    payerName = null,
    payerAlias = null,
    amountCalculationModel = AmountCalculationModel.UNKNOWN,
    fichaCompensacaoType = FichaCompensacaoType.CH_CHEQUE,
    interestData = null,
    fineData = null,
    discountData = null,
    abatement = null,
    divergentPayment = null,
)

val billAdded = BillAdded(
    billId = BillId(BILL_ID_4),
    created = getZonedDateTime().toInstant().toEpochMilli(),
    walletId = WalletId(WALLET_ID),
    description = "ACTIVE BILL",
    dueDate = getLocalDate().plusDays(3),
    amount = 15591L,
    billType = BillType.CONCESSIONARIA,
    barcode = BarCode.ofDigitable(PREFECTURE_BARCODE_LINE),
    assignor = "CLARO NET",
    document = DOCUMENT,
    paymentLimitTime = LocalTime.parse("17:00", timeFormat),
    lastSettleDate = getLocalDate().plusDays(1).format(dateFormat),
    actionSource = ActionSource.WalletMailBox(from = EMAIL_ADDRESS.toString(), accountId = AccountId(ACCOUNT_ID)),
    effectiveDueDate = calculateEffectiveDate(
        getLocalDate().plusDays(3),
        BillType.CONCESSIONARIA,
        BarCode.ofDigitable(PREFECTURE_BARCODE_LINE),
    ),
)

val billAddedInvestment = BillAdded(
    billId = BillId("BILL-INVESTMENT-BILL-1"),
    created = getZonedDateTime().toInstant().toEpochMilli(),
    walletId = WalletId(WALLET_ID),
    description = "bill de investimento",
    dueDate = getLocalDate().plusDays(3),
    amount = 15591L,
    billType = BillType.INVESTMENT,
    paymentLimitTime = LocalTime.of(16, 30),
    actionSource = ActionSource.GoalInvestment(ACCOUNT.accountId, goalId = GoalId("GOAL-ID-1")),
    effectiveDueDate = getLocalDate().plusDays(3),
    categoryId = PFMCategoryId("PFM-CATEGORY-ID-INVESTIMENTO"),
    goalId = GoalId("GOAL-ID-1"),
)

val billScheduledInvestment = BillPaymentScheduled(
    walletId = billAddedInvestment.walletId,
    created = Instant.now().toEpochMilli() + 2,
    billId = billAddedInvestment.billId,
    actionSource = ActionSource.Api(ACCOUNT.accountId),
    scheduledDate = billAddedInvestment.dueDate,
    amount = billAddedInvestment.amount,
    paymentLimitTime = null,
    infoData = BillPaymentScheduledBalanceInfo(
        amount = billAddedInvestment.amountTotal,
        paymentMethodId = PAYMENT_METHOD_ID,
        calculationId = null,
    ),
    batchSchedulingId = BatchSchedulingId(),
)

val billTagAdded = BillTagAdded(
    billId = billAdded.billId,
    walletId = billAdded.walletId,
    actionSource = ActionSource.System,
    tag = BillTag.ONBOARDING_TEST_PIX,
)

val billTagDeleted = BillTagDeleted(
    billId = billAdded.billId,
    walletId = billAdded.walletId,
    actionSource = ActionSource.System,
    tag = BillTag.ONBOARDING_TEST_PIX,
)

val billCategory = BillCategory(
    categoryId = PFMCategoryId(value = "EDUCACAO"),
    name = "EDUCACAO",
    icon = "EDUCACAO",
    default = true,
)
val billCategoryAdded = BillCategoryAdded(
    billId = billAdded.billId,
    walletId = billAdded.walletId,
    actionSource = ActionSource.System,
    categoryId = billCategory.categoryId,
    category = null,
)

val billCategoryDeleted = BillCategoryDeleted(
    billId = billAdded.billId,
    walletId = billAdded.walletId,
    actionSource = ActionSource.System,
)

val billMoved = BillMoved(
    billId = billAdded.billId,
    created = billAdded.created,
    walletId = billAdded.walletId,
    destinationWalletId = WalletId(value = "OTHER"),
    barCode = billAdded.barcode,
    dueDate = billAdded.dueDate,
    idNumber = null,
    actionSource = billAdded.actionSource,
)

val billAdded2 = BillAdded(
    billId = BillId(BILL_ID_8),
    created = Instant.now().toEpochMilli(),
    walletId = WalletId(WALLET_ID),
    description = "ACTIVE BILL",
    dueDate = getLocalDate().plusDays(3),
    amount = 15591L,
    billType = BillType.CONCESSIONARIA,
    barcode = BarCode.ofDigitable(DIGITABLE_LINE),
    recipient = Recipient(name = "Claro", document = "00000"),
    assignor = "CLARO NET",
    document = DOCUMENT,
    paymentLimitTime = LocalTime.parse("17:00", timeFormat),
    lastSettleDate = getLocalDate().plusDays(1).format(dateFormat),
    actionSource = ActionSource.Api(ACCOUNT.accountId),
    effectiveDueDate = calculateEffectiveDate(
        getLocalDate().plusDays(3),
        BillType.CONCESSIONARIA,
        BarCode.ofDigitable(DIGITABLE_LINE),
    ),
)

val fichaCompensacaoCreditCardAdded = FichaCompensacaoAdded(
    billId = BillId("BILL-${UUID.randomUUID()}"),
    created = billAdded.created,
    walletId = billAdded.walletId,
    description = "ACTIVE BILL",
    dueDate = getLocalDate().plusDays(3),
    amount = 15591L,
    barcode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE),
    assignor = "CLARO NET",
    document = DOCUMENT,
    paymentLimitTime = LocalTime.MIDNIGHT,
    lastSettleDate = getLocalDate().plusDays(1).format(dateFormat),
    actionSource = ActionSource.WalletMailBox(from = EMAIL_ADDRESS.toString()),
    effectiveDueDate = calculateEffectiveDate(
        getLocalDate().plusDays(3),
        BillType.FICHA_COMPENSACAO,
        BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE),
    ),
    fichaCompensacaoType = FichaCompensacaoType.CARTAO_DE_CREDITO,
    expirationDate = getLocalDate().plusDays(3).format(dateFormat),
    amountCalculationModel = AmountCalculationModel.UNKNOWN,
    recipient = Recipient(name = "Fake Recipient", document = "**************"),
    recipientChain = null,
    interestData = null,
    fineData = null,
    discountData = null,
    abatement = null,
    divergentPayment = null,
)

val concessionariaBill = Bill.build(billAdded)

val bankAccount = BankAccount(
    accountType = AccountType.CHECKING,
    bankNo = 111,
    routingNo = 12,
    accountNo = BigInteger("*********"),
    accountDv = "01",
    document = "***********",
)

val internalBankAccount = InternalBankAccount(
    accountType = AccountType.CHECKING,
    bankNo = 111,
    routingNo = 12,
    accountNo = 1002L,
    accountDv = "01",
    document = "***********",
    bankAccountMode = BankAccountMode.PHYSICAL,
)

val invoiceAdded = BillAdded(
    billId = BillId(BILL_ID_7),
    created = Instant.now().toEpochMilli(),
    walletId = WalletId(WALLET_ID),
    description = "ACTIVE INVOICE",
    dueDate = getLocalDate().plusDays(3),
    amount = 15591L,
    billType = BillType.INVOICE,
    recipient = Recipient(name = "Eduardo", document = "***********", bankAccount = bankAccount),
    contactId = ContactId(RECIPIENT_ID),
    paymentLimitTime = LocalTime.parse("17:00", timeFormat),
    actionSource = ActionSource.Api(ACCOUNT.accountId),
    effectiveDueDate = calculateEffectiveDate(
        getLocalDate().plusDays(3),
        BillType.FICHA_COMPENSACAO,
        BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE),
    ),
)

val invoiceIgnored = BillIgnored(
    billId = BillId(BILL_ID_7),
    created = Instant.now().toEpochMilli() + 2,
    walletId = invoiceAdded.walletId,
    actionSource = ActionSource.Api(ACCOUNT.accountId),
)

val invoiceReactivated = BillReactivated(
    billId = BillId(BILL_ID_7),
    created = Instant.now().toEpochMilli() + 3,
    walletId = invoiceAdded.walletId,
    actionSource = ActionSource.Api(ACCOUNT.accountId),
)

val invoicePaymentScheduled = BillPaymentScheduled(
    billId = BillId(BILL_ID_7),
    created = Instant.now().toEpochMilli() + 3,
    walletId = invoiceAdded.walletId,
    actionSource = ActionSource.Api(ACCOUNT.accountId),
    scheduledDate = LocalDate.parse("2020-08-26", dateFormat),
    amount = invoiceAdded.amountTotal,
    paymentLimitTime = null,
    infoData = BillPaymentScheduledBalanceInfo(
        amount = invoiceAdded.amountTotal,
        paymentMethodId = PAYMENT_METHOD_ID,
        calculationId = null,
    ),
    batchSchedulingId = BatchSchedulingId(),
)

val invoicePaymentPostponed = BillSchedulePostponed(
    billId = invoiceAdded.billId,
    created = getZonedDateTime().toInstant().toEpochMilli() + 4,
    walletId = invoiceAdded.walletId,
    actionSource = ActionSource.System,
    reason = SchedulePostponedReason.LIMIT_REACHED,
)

val invoiceScheduleStarted = BillPaymentScheduleStarted(
    billId = BillId(BILL_ID_7),
    created = Instant.now().toEpochMilli() + 4,
    walletId = invoiceAdded.walletId,
    actionSource = ActionSource.Api(ACCOUNT.accountId),
    paymentWalletId = invoiceAdded.walletId,
)

val invoiceScheduleCanceled = BillPaymentScheduleCanceled(
    billId = BillId(BILL_ID_7),
    created = Instant.now().toEpochMilli() + 4,
    walletId = invoiceAdded.walletId,
    actionSource = ActionSource.Api(ACCOUNT.accountId),
    reason = ScheduleCanceledReason.USER_REQUEST,
    batchSchedulingId = BatchSchedulingId(),
)

val invoicePostponedByInsufficientFunds = BillSchedulePostponed(
    billId = invoicePaymentScheduled.billId,
    created = invoicePaymentScheduled.created + 2,
    walletId = invoicePaymentScheduled.walletId,
    actionSource = ActionSource.System,
    reason = SchedulePostponedReason.INSUFFICIENT_FUNDS,
)

val pixAdded = BillAdded(
    billId = BillId("BILL-${UUID.randomUUID()}"),
    amount = 101,
    description = "ArbiPixPaymentAdapterTest2",
    walletId = WalletId(WALLET_ID),
    dueDate = getLocalDate(),
    effectiveDueDate = getLocalDate(),
    recipient = Recipient(
        name = "ENZO YABUSAME MOTTA",
        document = DOCUMENT,
        bankAccount = BankAccount(
            accountType = AccountType.CHECKING,
            bankNo = 341,
            ispb = "********",
            routingNo = 1234,
            accountNo = BigInteger("4321"),
            accountDv = "7",
            document = DOCUMENT,
        ),
    ),
    contactId = ContactId(RECIPIENT_ID),
    actionSource = ActionSource.Api(ACCOUNT.accountId),
    billType = BillType.PIX,
    paymentLimitTime = LocalTime.parse("23:59", timeFormat),
)

val pixPaid = BillPaid(
    billId = pixAdded.billId,
    walletId = pixAdded.walletId,
    acquirer = Acquirer.CIELO,
    acquirerTid = "9897548",
    actionSource = ActionSource.System,
    created = Instant.now().toEpochMilli() + 10,
    transactionId = TransactionId("********"),
)

val billDescriptionUpdated = DescriptionUpdated(
    walletId = billAdded.walletId,
    created = Instant.now().toEpochMilli() + 1,
    billId = BillId(BILL_ID_4),
    actionSource = ActionSource.Api(accountId = AccountId(ACCOUNT_ID)),
    description = "new description",
)

val billRecipientUpdated = BillRecipientUpdated(
    walletId = billAddedFicha.walletId,
    created = Instant.now().toEpochMilli() + 1,
    billId = BillId(BILL_ID_4),
    actionSource = ActionSource.Api(accountId = AccountId(ACCOUNT_ID)),
    recipient = billAddedFicha.recipient.copy(name = "updated"),
)

val billPermissionUpdated = BillPermissionUpdated(
    walletId = billAddedFicha.walletId,
    created = Instant.now().toEpochMilli() + 1,
    billId = BillId(BILL_ID_4),
    actionSource = ActionSource.Api(accountId = AccountId(ACCOUNT_ID)),
    permissionUpdated = PermissionUpdated.VisibilityAdded(AccountId(ACCOUNT_ID)),
)

val billRecurrenceUpdated = RecurrenceUpdated(
    walletId = billAddedFicha.walletId,
    created = Instant.now().toEpochMilli() + 1,
    billId = BillId(BILL_ID_4),
    actionSource = ActionSource.Api(accountId = AccountId(ACCOUNT_ID)),
    recurrenceRule = RecurrenceRule(
        frequency = RecurrenceFrequency.MONTHLY,
        startDate = getLocalDate(),
        pattern = "1",
    ),
)

val pixKeyDetails = PixKeyDetails(
    key = PixKey(
        value = "*********09",
        type = PixKeyType.CPF,
    ),
    holder = PixKeyHolder(
        accountNo = BigInteger("4321"),
        accountDv = "7",
        ispb = "********",
        institutionName = "Test",
        accountType = AccountType.CHECKING,
        routingNo = 1234,
    ),
    owner = PixKeyOwner(
        name = "Teste",
        document = "***********",
    ),
)

val pixIgnored = BillIgnored(
    billId = pixAdded.billId,
    created = pixAdded.created + 1,
    walletId = pixAdded.walletId,
    actionSource = pixAdded.actionSource,
)

val pixKeyAdded = pixAdded.copy(
    billId = BillId(UUID.randomUUID().toString()),
    recipient = Recipient(
        name = "Teste",
        document = "***********",
        alias = "TestAlias",
        bankAccount = null,
        pixKeyDetails = pixKeyDetails,
    ),
)

val pixKeyPaid = BillPaid(
    billId = pixKeyAdded.billId,
    walletId = pixKeyAdded.walletId,
    acquirer = Acquirer.CIELO,
    acquirerTid = "9897548",
    actionSource = ActionSource.System,
    created = Instant.now().toEpochMilli() + 10,
    transactionId = TransactionId("********"),
)

val pixKeyPaymentFailed = PaymentFailed(
    billId = pixKeyAdded.billId,
    walletId = pixKeyAdded.walletId,
    retryable = false,
    errorDescription = PixTransactionError.InvalidPixkey.code,
    errorSource = ErrorSource.SETTLEMENT_ARBI,
    transactionId = transactionId,
)

val pixKeyPaymentStarted = PaymentStarted(
    walletId = pixKeyAdded.walletId,
    billId = pixKeyAdded.billId,
    created = Instant.now().toEpochMilli() + 1,
    transactionId = transactionId,
    actionSource = ActionSource.System,
    correlationId = CORRELATION_ID,
)

val pixKeyPaymentCanceled = BillPaymentScheduleCanceled(
    walletId = pixKeyAdded.walletId,
    created = Instant.now().toEpochMilli() + 2,
    billId = pixKeyAdded.billId,
    actionSource = ActionSource.System,
    reason = ScheduleCanceledReason.BILL_NOT_PAYABLE,
    batchSchedulingId = BatchSchedulingId(),
)

val pixKeyPaymentScheduled = BillPaymentScheduled(
    walletId = pixKeyAdded.walletId,
    created = Instant.now().toEpochMilli() + 2,
    billId = pixKeyAdded.billId,
    actionSource = ActionSource.Api(ACCOUNT.accountId),
    scheduledDate = pixKeyAdded.dueDate,
    amount = pixKeyAdded.amount,
    paymentLimitTime = null,
    infoData = BillPaymentScheduledBalanceInfo(
        amount = pixKeyAdded.amountTotal,
        paymentMethodId = PAYMENT_METHOD_ID,
        calculationId = null,
    ),
    batchSchedulingId = BatchSchedulingId(),
)

val pixPaymentStarted = PaymentStarted(
    walletId = pixAdded.walletId,
    created = Instant.now().toEpochMilli() + 1,
    billId = pixAdded.billId,
    transactionId = transactionId,
    actionSource = ActionSource.System,
    correlationId = CORRELATION_ID,
)

val pixPaymentScheduled = BillPaymentScheduled(
    walletId = pixAdded.walletId,
    created = Instant.now().toEpochMilli() + 2,
    billId = pixAdded.billId,
    actionSource = ActionSource.Api(ACCOUNT.accountId),
    scheduledDate = pixAdded.dueDate,
    amount = pixAdded.amount,
    paymentLimitTime = null,
    infoData = BillPaymentScheduledBalanceInfo(
        amount = pixAdded.amountTotal,
        paymentMethodId = PAYMENT_METHOD_ID,
        calculationId = null,
    ),
    batchSchedulingId = BatchSchedulingId(),
)

val pixPaymentPostponedDailyLimitReached = BillSchedulePostponed(
    billId = pixAdded.billId,
    created = getZonedDateTime().toInstant().toEpochMilli() + 4,
    walletId = pixAdded.walletId,
    actionSource = ActionSource.System,
    reason = SchedulePostponedReason.LIMIT_REACHED,
)

val pixPaymentScheduledCanceled = BillPaymentScheduleCanceled(
    walletId = pixAdded.walletId,
    created = Instant.now().toEpochMilli() + 2,
    billId = pixAdded.billId,
    actionSource = ActionSource.System,
    reason = ScheduleCanceledReason.BILL_NOT_PAYABLE,
    batchSchedulingId = BatchSchedulingId(),
)

val fingerprint = Fingerprint(
    sessionId = "session-test-001",
    app = FingerprintApp(version = "1.0.2-b23"),
    geolocation = FingerprintGeolocation(latitude = "*********0", longitude = "**********"),
    device = FingerprintDevice(
        manufacturer = "Apple",
        model = "14 Pro Max",
        installationId = "**********",
    ),
    os = FingerprintOS(name = "ios", version = "14"),
)

val billPaymentScheduled = BillPaymentScheduled(
    billId = BillId(BILL_ID_4),
    created = Instant.now().toEpochMilli() + 3,
    walletId = billAdded.walletId,
    actionSource = ActionSource.Api(ACCOUNT.accountId),
    scheduledDate = billAdded.effectiveDueDate!!,
    amount = billAdded.amountTotal,
    paymentLimitTime = LocalTime.parse("10:00", timeFormat),
    infoData = BillPaymentScheduledBalanceInfo(
        amount = billAdded.amountTotal,
        paymentMethodId = PAYMENT_METHOD_ID,
        calculationId = null,
    ),
    fingerprint = fingerprint,
    batchSchedulingId = BatchSchedulingId(),
    paymentWalletId = billAdded.walletId,
)

val billPostponedByInsufficientFunds = BillSchedulePostponed(
    billId = billPaymentScheduled.billId,
    created = billPaymentScheduled.created + 2,
    walletId = billPaymentScheduled.walletId,
    actionSource = ActionSource.System,
    reason = SchedulePostponedReason.INSUFFICIENT_FUNDS,
)

val billPaymentScheduleStarted = BillPaymentScheduleStarted(
    billId = BillId(BILL_ID_4),
    created = Instant.now().toEpochMilli() + 3,
    walletId = billAdded.walletId,
    actionSource = ActionSource.System,
    paymentWalletId = billAdded.walletId,
)

val billScheduleCanceled = BillPaymentScheduleCanceled(
    billId = BillId(BILL_ID_4),
    created = Instant.now().toEpochMilli() + 4,
    walletId = billAdded.walletId,
    actionSource = ActionSource.Api(ACCOUNT.accountId),
    reason = ScheduleCanceledReason.USER_REQUEST,
    batchSchedulingId = BatchSchedulingId(),
)

val billScheduleUpdated = BillPaymentScheduleUpdated(
    billId = BillId(BILL_ID_4),
    created = Instant.now().toEpochMilli() + 4,
    walletId = billAdded.walletId,
    actionSource = ActionSource.Api(ACCOUNT.accountId),
    updatedScheduleData = UpdatedScheduleData.NewCalculationId("calculation_id"),
)

val billScheduleCanceledAfterHours = BillPaymentScheduleCanceled(
    billId = BillId(BILL_ID_4),
    created = Instant.now().toEpochMilli() + 4,
    walletId = billAdded.walletId,
    actionSource = ActionSource.Api(ACCOUNT.accountId),
    reason = ScheduleCanceledReason.EXPIRATION,
    batchSchedulingId = BatchSchedulingId(),
)

val billScheduleCanceledAmountHigherThanDailyLimit = BillPaymentScheduleCanceled(
    billId = BillId(BILL_ID_4),
    created = getZonedDateTime().toInstant().toEpochMilli() + 4,
    walletId = billAdded.walletId,
    actionSource = ActionSource.Api(ACCOUNT.accountId),
    reason = ScheduleCanceledReason.AMOUNT_HIGHER_THAN_DAILY_LIMIT,
    batchSchedulingId = BatchSchedulingId(),
)

val billScheduleCanceledAmountChanged = BillPaymentScheduleCanceled(
    billId = BillId(BILL_ID_4),
    created = getZonedDateTime().toInstant().toEpochMilli() + 4,
    walletId = billAdded.walletId,
    actionSource = ActionSource.Api(ACCOUNT.accountId),
    reason = ScheduleCanceledReason.BILL_AMOUNT_CHANGED,
    batchSchedulingId = BatchSchedulingId(),
)

val billScheduleCanceledCantPayWithCurrentCreditCard = BillPaymentScheduleCanceled(
    billId = BillId(BILL_ID_4),
    created = getZonedDateTime().toInstant().toEpochMilli() + 4,
    walletId = billAdded.walletId,
    actionSource = ActionSource.System,
    reason = ScheduleCanceledReason.CANT_PAY_WITH_CURRENT_CREDIT_CARD,
    batchSchedulingId = BatchSchedulingId(),
)

val billPaymentStart = PaymentStarted(
    walletId = billAdded.walletId,
    created = Instant.now().toEpochMilli() + 1,
    billId = BillId(BILL_ID_4),
    transactionId = transactionId,
    actionSource = ActionSource.System,
    correlationId = CORRELATION_ID,
)

val billPaymentSchedulePostponedByInsufficientFunds = BillSchedulePostponed(
    billId = BillId(BILL_ID_4),
    created = Instant.now().toEpochMilli() + 3,
    walletId = billAdded.walletId,
    actionSource = ActionSource.System,
    reason = SchedulePostponedReason.INSUFFICIENT_FUNDS,
)

val billPaymentSchedulePostponedDueToLimitReached = BillSchedulePostponed(
    billId = BillId(BILL_ID_4),
    created = getZonedDateTime().toInstant().toEpochMilli() + 3,
    walletId = billAdded.walletId,
    actionSource = ActionSource.System,
    reason = SchedulePostponedReason.LIMIT_REACHED,
)

val billRegisterUpdatedNotPayable = RegisterUpdated(
    billId = BillId(BILL_ID_4),
    walletId = billAdded.walletId,
    updatedRegisterData = UpdatedRegisterData.NotPayableBill,
)

val billRegisterUpdatedAlreadyPaid = RegisterUpdated(
    billId = BillId(BILL_ID_4),
    walletId = billAdded.walletId,
    updatedRegisterData = UpdatedRegisterData.AlreadyPaidBill(),
)

val billRegisterUpdatedNewAmount = RegisterUpdated(
    billId = BillId(BILL_ID_4),
    walletId = billAdded.walletId,
    updatedRegisterData = UpdatedRegisterData.NewTotalAmount(
        amount = 10,
        abatement = 0,
        discount = 0,
        interest = 0,
        fine = 0,
        amountTotal = 10,
        lastSettleDate = "2020-05-09",
        registrationUpdateNumber = null,
        source = null,
        amountCalculationValidUntil = LocalDate.now(),
    ),
)

val billRegisterUpdatedNewAmountCalculationData = RegisterUpdated(
    billId = BillId(BILL_ID_4),
    walletId = billAdded.walletId,
    updatedRegisterData = UpdatedRegisterData.NewAmountCalculationData(
        fineData = FineData(FineType.VALUE, BigDecimal.valueOf(1.23), LocalDate.now().minusDays(1)),
        interestData = InterestData(InterestType.VALUE, BigDecimal.valueOf(4.56), LocalDate.now().minusDays(1)),
        discountData = DiscountData(DiscountType.UNKNOWN, BigDecimal.valueOf(7.89), LocalDate.now().minusDays(1)),
    ),
)

val billRegisterUpdatedNewDueDate = RegisterUpdated(
    billId = billAdded.billId,
    walletId = billAdded.walletId,
    updatedRegisterData = UpdatedRegisterData.NewDueDate(
        dueDate = billAdded.dueDate.plusDays(1),
        effectiveDueDate = billAdded.effectiveDueDate!!.plusDays(1),
    ),
)

val billPaymentFailedRetryable = PaymentFailed(
    billId = BillId(BILL_ID_4),
    walletId = billAdded.walletId,
    retryable = true,
    errorDescription = "ERROR",
    transactionId = transactionId,
)

val billPaymentFailed = PaymentFailed(
    billId = BillId(BILL_ID_4),
    walletId = billAdded.walletId,
    retryable = false,
    errorDescription = "ERROR",
    transactionId = transactionId,
)

val invoicePaymentStarted = PaymentStarted(
    walletId = invoiceAdded.walletId,
    billId = BillId(BILL_ID_7),
    transactionId = TransactionId("********"),
    actionSource = ActionSource.System,
    created = Instant.now().toEpochMilli() + 5,
    correlationId = CORRELATION_ID,
)

val invoicePaid = BillPaid(
    billId = BillId(BILL_ID_7),
    walletId = invoiceAdded.walletId,
    acquirer = Acquirer.CIELO,
    acquirerTid = "9897548",
    actionSource = ActionSource.System,
    created = Instant.now().toEpochMilli() + 10,
    transactionId = TransactionId("********"),
)

val invoicePaymentFailedRetryable = PaymentFailed(
    billId = BillId(BILL_ID_7),
    walletId = invoiceAdded.walletId,
    retryable = true,
    errorDescription = "ERROR",
    transactionId = TransactionId("********"),
)

val invoicePaymentFailedNotRetryable = PaymentFailed(
    billId = BillId(BILL_ID_7),
    walletId = invoiceAdded.walletId,
    retryable = false,
    errorDescription = "ERROR",
    transactionId = TransactionId("********"),
)

val pixBill = Bill.build(pixAdded)
val invoiceBill = Bill.build(invoiceAdded)

val billPaid = BillPaid(
    created = billAdded.created.plus(1),
    walletId = billAdded.walletId,
    billId = BillId(BILL_ID_4),
    acquirer = authorizedCreditCardAuthorization.acquirer,
    acquirerTid = authorizedCreditCardAuthorization.acquirerTid!!,
    actionSource = ActionSource.System,
    transactionId = transactionId,
)

val billIgnored = BillIgnored(
    created = billAdded.created.plus(1),
    walletId = billAdded.walletId,
    billId = BillId(BILL_ID_4),
    actionSource = ActionSource.System,
)

val billReactivated = BillReactivated(
    billId = BillId(BILL_ID_4),
    created = Instant.now().toEpochMilli() + 3,
    walletId = billAdded.walletId,
    actionSource = ActionSource.Api(ACCOUNT.accountId),
)

val billRefunded = PaymentRefunded(
    billId = BillId(BILL_ID_4),
    created = Instant.now().toEpochMilli() + 3,
    walletId = billAdded.walletId,
    actionSource = ActionSource.Api(ACCOUNT.accountId),
    gateway = FinancialServiceGateway.ARBI,
    reason = PaymentRefundedReason.ERROR,
    transactionId = TransactionId(""),
)

val billMarkedAsPaid = BillMarkedAsPaid(
    billId = BillId(BILL_ID_4),
    created = Instant.now().toEpochMilli() + 3,
    walletId = billAdded.walletId,
    actionSource = ActionSource.Api(accountId = AccountId(ACCOUNT_ID)),
    amountPaid = billAdded.amountTotal,
)

val billMarkedAsPaidCanceled = BillMarkedAsPaidCanceled(
    billId = BillId(BILL_ID_4),
    created = Instant.now().toEpochMilli() + 3,
    walletId = billAdded.walletId,
    actionSource = ActionSource.Api(accountId = AccountId(ACCOUNT_ID)),
)

val billDenied = BillDenied(
    billId = billAdded.billId,
    created = Instant.now().toEpochMilli() + 3,
    walletId = billAdded.walletId,
    actionSource = ActionSource.Api(accountId = AccountId(ACCOUNT_ID)),
)

val creditCard = AccountPaymentMethod(
    id = AccountPaymentMethodId(PAYMENT_METHOD_ID),
    accountId = AccountId(ACCOUNT_ID),
    method = CreditCard(CreditCardBrand.ELO, "brand", "pan", "", binDetails = null, riskLevel = RiskLevel.LOW),
)

val externalCreditCard = AccountPaymentMethod(
    id = AccountPaymentMethodId(PAYMENT_METHOD_EXTERNAL_ID),
    accountId = AccountId(ACCOUNT_ID),
    method = CreditCard(
        brand = CreditCardBrand.MASTERCARD,
        expiryDate = "01/2099",
        pan = "123456******1234",
        securityCode = "",
        binDetails = null,
        holderName = null,
        token = null,
        externalId = CreditCardExternalId("777", AccountProviderName.ME_POUPE),
        mainCard = true,
        riskLevel = RiskLevel.LOW,
    ),
)

val balance = AccountPaymentMethod(
    id = AccountPaymentMethodId("id2"),
    accountId = AccountId(ACCOUNT_ID),
    method = InternalBankAccount(
        accountType = AccountType.CHECKING,
        bankNo = 111,
        routingNo = 4,
        accountNo = 123555,
        accountDv = "1",
        document = "***********",
        bankAccountMode = BankAccountMode.PHYSICAL,
    ),
    created = getZonedDateTime(),
)

val anotherBalance = AccountPaymentMethod(
    id = AccountPaymentMethodId("id3"),
    accountId = AccountId(ACCOUNT_ID),
    method = InternalBankAccount(
        accountType = AccountType.CHECKING,
        bankNo = 111,
        routingNo = 4,
        accountNo = 3434534,
        accountDv = "0",
        document = "***********",
        bankAccountMode = BankAccountMode.PHYSICAL,
    ),
    created = getZonedDateTime(),
)

val arbiBalance = balance.copy(method = (balance.method as InternalBankAccount).copy(bankNo = 213))

val virtualBalance = AccountPaymentMethod(
    id = AccountPaymentMethodId(PAYMENT_METHOD_ID_5),
    accountId = AccountId(ACCOUNT_ID),
    method = InternalBankAccount(
        accountType = AccountType.CHECKING,
        bankNo = 111,
        routingNo = 4,
        accountNo = 123555,
        accountDv = "1",
        document = "***********",
        bankAccountMode = BankAccountMode.VIRTUAL,
    ),
)

val bankDetailsTO = RecipientBankDetailsTO(
    accountType = AccountType.CHECKING,
    bankNo = 111,
    routingNo = 4,
    accountNo = BigInteger("123555"),
    accountDv = "1",
    ispb = "********",
)
val recipientTO = RequestInvoiceRecipientTO(
    name = "Eduardo",
    document = "***********",
    documentType = "CPF",
    bankDetails = bankDetailsTO,
    alias = "alias",
)
val sundayDate = getLocalDate().plusMonths(1).with(TemporalAdjusters.next(DayOfWeek.SUNDAY))
val createInvoiceTO = CreateInvoiceTO(
    description = "description",
    dueDate = sundayDate.format(dateFormat),
    amount = 123,
    recipient = recipientTO,
)

val RECIPIENT_PIX_EVP_KEY = UUID.randomUUID().toString()
val BANK_ACCOUNT_ID = UUID.randomUUID().toString()
val BANK_ACCOUNT_ID2 = UUID.randomUUID().toString()

val WALLET_BILL_CATEGORY = WalletBillCategory(
    walletId = WalletId(WALLET_ID),
    categoryId = PFMCategoryId("TEST-CATEGORY"),
    default = false,
    enabled = true,
    name = "Test Category",
    icon = "CORACAO",
)

fun createSavedRecipient(
    invalidBankAccount: Boolean = false,
    savedPixKey: SavedPixKey = SavedPixKey(value = RECIPIENT_PIX_EMAIL_KEY, type = PixKeyType.EMAIL),
    lastUsed: LastUsed? = null,
    accountId: AccountId = AccountId(ACCOUNT_ID),
) = Contact(
    id = ContactId(RECIPIENT_ID),
    accountId = accountId,
    name = recipientTO.name,
    document = recipientTO.document,
    alias = "alias",
    bankAccounts = listOf(
        SavedBankAccount(
            id = BankAccountId(BANK_ACCOUNT_ID),
            accountType = bankDetailsTO.accountType,
            bankNo = bankDetailsTO.bankNo!!,
            ispb = "********",
            routingNo = bankDetailsTO.routingNo,
            accountNo = bankDetailsTO.accountNo,
            accountDv = bankDetailsTO.accountDv,
        ).apply {
            invalidated = invalidBankAccount
        },
        SavedBankAccount(
            id = BankAccountId(BANK_ACCOUNT_ID2),
            accountType = bankDetailsTO.accountType,
            bankNo = null,
            ispb = "********",
            routingNo = bankDetailsTO.routingNo,
            accountNo = bankDetailsTO.accountNo,
            accountDv = bankDetailsTO.accountDv,
        ),
    ),
    pixKeys = listOf(
        savedPixKey,
        SavedPixKey(
            value = RECIPIENT_PIX_EVP_KEY,
            type = PixKeyType.EVP,
        ),
    ),
    lastUsed = lastUsed,
)

val billRegisterData = BillRegisterData(
    billType = BillType.CONCESSIONARIA,
    assignor = "assignorTest",
    recipient = Recipient(name = "recipient"),
    recipientChain = null,
    payerDocument = "payerTest",
    amount = 100L,
    discount = 0,
    interest = 0,
    fine = 0,
    amountTotal = 100L,
    dueDate = sundayDate,
    paymentLimitTime = "00:00",
    expirationDate = getLocalDate(),
    settleDate = getLocalDate(),
    amountCalculationModel = AmountCalculationModel.UNKNOWN,

    divergentPayment = null,
)
val fichaRegisterData = BillRegisterData(
    billType = BillType.FICHA_COMPENSACAO,
    assignor = "assignorTest",
    recipient = Recipient(name = "recipientTest"),
    recipientChain = null,
    payerDocument = "***********",
    payerName = "Fulano",
    amount = 100L,
    discount = 0,
    interest = 0,
    fine = 0,
    amountTotal = 100L,
    dueDate = sundayDate,
    paymentLimitTime = "00:00",
    expirationDate = getLocalDate(),
    settleDate = getLocalDate(),
    amountCalculationModel = AmountCalculationModel.UNKNOWN,
    fichaCompensacaoType = FichaCompensacaoType.OUTROS,
    idNumber = "2021123105413964730",

    divergentPayment = null,
)

val barcodeNotFoundValidationResponse = DefaultBillValidationResponse(
    billRegisterData = null,
    gateway = FinancialServiceGateway.FRIDAY,
    billRegisterStatus = BillRegisterStatus(
        notPayable = false,
        alreadyPaid = false,
        barcodeNotFound = true,
    ),
    errorDescription = "invalid response",
)

val payableDefaultValidationResponse = DefaultBillValidationResponse(
    billRegisterData = billRegisterData,
    gateway = FinancialServiceGateway.FRIDAY,
    billRegisterStatus = BillRegisterStatus(
        notPayable = false,
        alreadyPaid = false,
        barcodeNotFound = true,
    ),
)

val successConcessionariaValidationResponse =
    CelcoinBillValidationResponse(
        10L,
        "000",
        "SUCESSO",
        billRegisterData,
    )

val notPayableConcessionariaValidationResponse =
    CelcoinBillValidationResponse(
        10L,
        "244",
        "SUCESSO",
        billRegisterData,
    )

val billAddedRegisterData = BillRegisterData(
    billType = BillType.FICHA_COMPENSACAO,
    assignor = "assignorTest",
    recipient = Recipient(name = "recipientTest"),
    recipientChain = null,
    payerDocument = "***********",
    payerName = "Fulano",
    amount = billAdded.amount,
    discount = billAdded.discount,
    interest = billAdded.interest,
    fine = billAdded.fine,
    amountTotal = billAdded.amountTotal,
    dueDate = getLocalDate(),
    paymentLimitTime = "00:00",
    expirationDate = getLocalDate(),
    settleDate = getLocalDate(),
    amountCalculationModel = AmountCalculationModel.UNKNOWN,
    fichaCompensacaoType = FichaCompensacaoType.OUTROS,

    divergentPayment = null,
)
val billAddedValidationResponse =
    CelcoinBillValidationResponse(
        10L,
        "000",
        "SUCESSO",
        billAddedRegisterData,
    )

val scheduledBill = ScheduledBill(
    billAdded.walletId,
    billAdded.billId,
    billAdded.dueDate,
    billAdded.billType,
    billAdded.amountTotal,
    ScheduleTo.ASAP,
    LocalTime.parse("23:59"),
    expires = true,
    paymentMethodsDetail = createPaymentMethodsDetailWithBalance(paymentMethodId2, billAdded.amountTotal),
    batchSchedulingId = BatchSchedulingId(),
    isSelfTransfer = false,
)

val scheduledBill2 = ScheduledBill(
    billAdded2.walletId,
    billAdded2.billId,
    billAdded2.dueDate,
    billAdded2.billType,
    34562L,
    ScheduleTo.ASAP,
    LocalTime.parse("23:59"),
    createPaymentMethodsDetailWithBalance(paymentMethodId2, billAdded.amountTotal),
    batchSchedulingId = BatchSchedulingId(),
    isSelfTransfer = false,
)

val boletoSettlementResult =
    BoletoSettlementResult(
        FinancialServiceGateway.CELCOIN,
        BoletoSettlementStatus.AUTHORIZED,
        "1",
        1,
        ACCOUNT_ID,
        "",
        "",
    )

@Deprecated("use createTransaction() instead.")
val boletoTransaction = Transaction(
    type = TransactionType.BOLETO_PAYMENT,
    payer = ACCOUNT.toPayer(),
    paymentData = createSinglePaymentDataWithBalance(balance, concessionariaBill.amountTotal),
    settlementData = SettlementData(Bill.build(billAdded, billPaymentScheduled), 0, concessionariaBill.amountTotal, boletoSettlementResult),
    nsu = 1,
    actionSource = ActionSource.System,
    walletId = concessionariaBill.walletId,
)

val weeklyRecurrenceNoEndDate = BillRecurrence(
    id = RecurrenceId("RECURRENCE-ID"),
    walletId = WalletId(WALLET_ID),
    description = "fake description",
    amount = 100L,
    rule = RecurrenceRule(frequency = RecurrenceFrequency.WEEKLY, startDate = getZonedDateTime().toLocalDate()),
    contactAccountId = AccountId(ACCOUNT_ID),
    recipientName = "Eduardo",
    recipientDocument = "***********",
    recipientBankAccount = BankAccount(AccountType.CHECKING, 1L, 2L, BigInteger("3"), "4", "***********"),
    actionSource = ActionSource.Api(
        accountId = AccountId(ACCOUNT_ID),
        originalBillId = BillId("13"),
    ),
    created = getZonedDateTime(),
    status = RecurrenceStatus.ACTIVE,
    bills = listOf(),
    billType = BillType.INVOICE,
)

val walletRecurrenceId = RecurrenceId("WALLET-RECURRENCE-ID")
val weeklyWalletRecurrenceNoEndDate = BillRecurrence(
    id = walletRecurrenceId,
    walletId = WalletId("WALLET-RECURRENCE-ID"),
    description = "fake description",
    amount = 100L,
    rule = RecurrenceRule(frequency = RecurrenceFrequency.WEEKLY, startDate = getLocalDate()),
    contactAccountId = AccountId(ACCOUNT_ID),
    recipientName = "Eduardo",
    recipientDocument = "***********",
    recipientBankAccount = BankAccount(AccountType.CHECKING, 1L, 2L, BigInteger("3"), "4", "***********", "123"),
    actionSource = ActionSource.Api(accountId = AccountId(ACCOUNT_ID)),
    created = getZonedDateTime(),
    status = RecurrenceStatus.ACTIVE,
    bills = listOf(),
    billType = BillType.PIX,
)

val CASHIN_TRANSACTIONID: UUID = UUID.randomUUID()

val creditCardCashInTransaction = Transaction(
    id = TransactionId.build(CASHIN_TRANSACTIONID),
    created = getZonedDateTime(),
    type = TransactionType.CASH_IN,
    payer = ACCOUNT.toPayer(),
    paymentData = SinglePaymentData(
        accountPaymentMethod = creditCard,
        details = PaymentMethodsDetailWithCreditCard(
            paymentMethodId = AccountPaymentMethodId(value = ""),
            netAmount = 1,
            feeAmount = 0,
            fee = 4.0,
            installments = 1,
            calculationId = null,
        ),
    ),
    settlementData = SettlementData(
        settlementTarget = CreditCardCashIn(amount = 1, bankAccount = balance),
        serviceAmountTax = 0,
        totalAmount = 1,
    ),
    nsu = 0L,
    actionSource = ActionSource.Api(ACCOUNT.accountId),
    status = TransactionStatus.COMPLETED,
    walletId = WalletId(WALLET_ID),
)

val omnibusPaymentMethodId = "BANK-ACCOUNT-OMNIBUS"

val omnibusBankAccountConfiguration = OmnibusBankAccountConfiguration(
    accountPaymentMethodId = omnibusPaymentMethodId,
    document = "**********",
    bankNo = 1L,
    routingNo = 1L,
    accountNo = "11111",
    name = "VIA1",
)

val bankStatementItem = DefaultBankStatementItem(
    date = getLocalDate(),
    flow = BankStatementItemFlow.CREDIT,
    type = BankStatementItemType.PIX,
    description = "fake",
    operationNumber = "0001",
    isTemporaryOperationNumber = false,
    amount = 123L,
    counterpartName = "fake counterpartName",
    counterpartDocument = "fake counterpartDocument",
    documentNumber = "*********09",
    notificatedAt = null,
)

val bankStatementItemInvestmentRedemption = DefaultBankStatementItem(
    date = getLocalDate(),
    flow = BankStatementItemFlow.CREDIT,
    type = BankStatementItemType.INVESTMENT_REDEMPTION,
    description = "00558-RESGATE APLIC FINANC DIGITAL",
    operationNumber = "*********",
    isTemporaryOperationNumber = false,
    amount = 3002L,
    counterpartName = "",
    counterpartDocument = "***********",
    documentNumber = "0      ",
    notificatedAt = null,
)

fun <T> withUnlockedLock(toBeExecuted: () -> T): T {
    mockkStatic("net.javacrumbs.shedlock.core.LockAssert")
    every { assertLocked() } just runs
    try {
        return toBeExecuted()
    } finally {
        unmockkStatic("net.javacrumbs.shedlock.core.LockAssert")
    }
}

fun <T> withoutLogs(toBeExecuted: () -> T): T {
    val logger = LoggerFactory.getLogger(Logger.ROOT_LOGGER_NAME) as Logger
    val level = logger.level

    try {
        logger.level = Level.OFF
        return toBeExecuted()
    } finally {
        logger.level = level
    }
}

// @Deprecated("", ReplaceWith("withGivenDateTime(time) toBeExecuted", imports = ["ai.friday.morning.date.withGivenDateTime"]))
// fun <T> withGivenDateTime(time: ZonedDateTime, toBeExecuted: () -> T): T {
//    TODO()
// }

fun <T> withGivenImageOptimizer(outputStream: ByteArrayOutputStream, toBeExecuted: () -> T): T {
    mockkObject(ImageOptimizer)
    every { ImageOptimizer.optimize(any(), any()) } returns outputStream
    try {
        return toBeExecuted()
    } finally {
        unmockkObject(ImageOptimizer)
    }
}

class FinancialInstitutionFixture {
    companion object {
        val ITAU = FinancialInstitution(name = "ITAU UNIBANCO S.A.", "********", 341)
        val BRADESCO = FinancialInstitution(name = "BCO BRADESCO S.A.", "********", 237)
        val BB = FinancialInstitution(name = "BANCO DO BRASIL", "0", 1)
    }
}

fun withPixParticipants(vararg data: FinancialInstitution, test: () -> Unit) {
    mockkObject(FinancialInstitutionGlobalData.Companion)
    every { FinancialInstitutionGlobalData.pixParticipants } returns data.asList()
    try {
        test.invoke()
    } finally {
        unmockkObject(FinancialInstitutionGlobalData.Companion)
    }
}

fun <T> withBankList(vararg data: Bank, test: () -> T): T {
    mockkObject(FinancialInstitutionGlobalData.Companion)
    every { FinancialInstitutionGlobalData.bankList } returns data.asList()
    try {
        return test.invoke()
    } finally {
        unmockkObject(FinancialInstitutionGlobalData.Companion)
    }
}

fun <T> withHolidays(vararg data: LocalDate, test: () -> T): T {
    mockkObject(FinancialInstitutionGlobalData.Companion)
    every { FinancialInstitutionGlobalData.holidays } returns data.asList()
    try {
        return test.invoke()
    } finally {
        unmockkObject(FinancialInstitutionGlobalData.Companion)
    }
}

@Suppress("DEPRECATION")
fun <T> withEarlyAccess(accountId: AccountId, toBeExecuted: () -> T): T {
    mockkStatic("ai.friday.billpayment.app.account.EarlyAccessListKt")
    every { eq(accountId).hasEarlyAccess() } returns true
    try {
        return toBeExecuted()
    } finally {
        unmockkStatic("ai.friday.billpayment.app.account.EarlyAccessListKt")
    }
}

private val blockingAsyncCallsSemaphore = Semaphore(1)

fun <T> withBlockingAsyncCalls(toBeExecuted: () -> T): T {
    return try {
        blockingAsyncCallsSemaphore.acquire()
        mockkObject(AsyncUtils)

        coEvery { callAsync(any()) } answers {
            val block = arg<suspend CoroutineScope.() -> Job>(0)
            runBlocking {
                // Usa escopo explícito com Dispatchers.Unconfined (executa de forma síncrona no thread atual)
                val scope = CoroutineScope(Dispatchers.Unconfined)
                block.invoke(scope)
            }
            mockk<Job>(relaxed = true) // Retorna um Job dummy
        }

        toBeExecuted()
    } finally {
        unmockkObject(AsyncUtils)
        blockingAsyncCallsSemaphore.release()
    }
}

class NoStackTraceException(msg: String? = null) : Exception(msg, null, true, false)

@Singleton
@Replaces(CognitoAdapter::class)
class MockUserPoolAdapter : UserPoolAdapter {
    override fun configureUser(username: String, accountId: AccountId, groupNames: List<String>) {
        logger.info("configureUser $username")
    }

    override fun createUser(
        username: String,
        password: String,
        accountId: AccountId,
        emailAddress: EmailAddress,
        phoneNumber: MobilePhone,
        role: Role,
        name: String,
        setMFA: Boolean,
    ) {
        logger.info("createUser $username")
    }

    override fun doesUserExist(username: String): Boolean {
        return true
    }

    override fun isUserEnabled(username: String): Boolean {
        return false
    }

    override fun addUserToGroup(username: String, groupName: String) {
        logger.info("addUserToGroup $username : $groupName ")
    }

    override fun removeAccountId(username: String): Either<UserPoolRemoveUserError, Unit> {
        logger.info("removeAccountId $username ")
        return Unit.right()
    }

    override fun disableUser(username: String): Either<UserPoolRemoveUserError, Unit> {
        logger.info("removeUser $username")
        return Unit.right()
    }

    override fun removeUserFromGroup(username: String, groupName: String) {
        logger.info("removeUserFromGroup $username : $groupName")
    }

    override fun signOutUser(username: String) {
        logger.info("signoutUser $username")
    }

    override fun resetPassword(username: String) {
        logger.info("resetPassword $username")
    }

    override fun setUserPassword(username: String, password: String) {
        logger.info("setUserPassword $username")
    }

    override fun updatePhoneNumber(username: String, phoneNumber: MobilePhone) {
        logger.info("updatePhone $phoneNumber")
    }

    override fun updateEmailAddress(username: String, emailAddress: EmailAddress) {
        logger.info("updateEmail $emailAddress")
    }

    override fun setAccountId(username: String, accountId: AccountId) {
        logger.info("setAccountId $username - ${accountId.value}")
    }

    override fun getAccountId(username: String): AccountId? {
        logger.info("getAccountId")
        return null
    }

    override fun enableUser(username: String, accountId: AccountId?): Either<UserPoolEnableUserError, Unit> {
        logger.info("enableUser $username")
        return Unit.right()
    }

    override fun setMfaPreference(username: String, enabled: Boolean): Either<UserPoolSetMfaPreferenceError, Unit> {
        logger.info("setMfaPreference $username")
        return Unit.right()
    }

    override fun listUserEvents(username: String): Either<UserPoolListAuthEventsException, List<String>> {
        logger.info("listUserEvents $username")
        return listOf("").right()
    }

    companion object {
        private val logger = LoggerFactory.getLogger(MockUserPoolAdapter::class.java)
    }
}

@Replaces(DefaultEmailService::class)
@Secondary
@Singleton
class IncomingEmailProcessorMock : EmailService {

    override fun process(event: SQSEvent) {
        logger.info("process Receipt")
    }

    companion object {
        private val logger = LoggerFactory.getLogger(IncomingEmailProcessorMock::class.java)
    }
}

@Singleton
@Replaces(SNSEventPublisher::class)
class MockEventPublisher : EventPublisher {
    override fun publish(topic: String, message: Any, attributes: Map<String, String>) {
        logger.info("publicando evento $message")
    }

    override fun publish(event: BillEvent, billType: BillType) {
        logger.info("publicando evento $event")
    }

    override fun publish(event: WalletEvent) {
        logger.info("publicando evento $event")
    }

    override fun publish(event: AccountEvent) {
        logger.info("publicando evento $event")
    }

    companion object {
        private val logger = LoggerFactory.getLogger(MockEventPublisher::class.java)
    }
}

@Factory
class MockAmazonDynamoDBFactory {
    @Primary
    @Requires(env = ["test"])
    @Singleton
    fun getAmazonDynamoDb(): AmazonDynamoDB {
        return LocalDbCreationRule.getDynamoDBProxyServer()
    }

    @Primary
    @Singleton
    @Requires(env = ["test"], bean = AmazonDynamoDB::class)
    fun dynamoDbClient(): DynamoDbClient {
        return getDynamoDbClient()
    }

    @Primary
    @Singleton
    @Requires(env = ["test"])
    fun dynamoDbEnhancedClient(): DynamoDbEnhancedClient {
        return DynamoDbEnhancedClient.builder()
            .dynamoDbClient(getDynamoDbClient())
            .build()
    }

    @Primary
    @Singleton
    @Requires(env = ["test"])
    fun dynamoDbEnhancedAsyncClient(): DynamoDbEnhancedAsyncClient {
        return DynamoDbEnhancedAsyncClient.builder()
            .dynamoDbClient(getDynamoDbClientAsync())
            .build()
    }
}

fun LastUsed?.verify(pixKey: String? = null, bankAccount: SavedBankAccount? = null) {
    this.shouldNotBeNull()

    if (this.pixKey == null) {
        this.bankAccountId.shouldNotBeNull()
        this.bankAccountId shouldBe bankAccount?.id
    }

    if (this.bankAccountId == null) {
        this.pixKey.shouldNotBeNull()
        this.pixKey shouldBe pixKey
    }
}

val allFalseFeatureConfiguration = FeaturesConfigurationMicronaut(
    asyncSettlement = false,
    forwardEmailToManualWorkflow = false,
    automaticUserRegister = false,
    zeroAuthEnabled = false,
    blockDuplicateByIdNumber = false,
    userPilotEnabled = false,
    ddaBatchAddAsync = false,
    silenceNotifications = false,
    skipFullImportWhenAccountHasBills = false,
    creditCardChallenge = false,
    optOutNotification = false,
    creditCardQuota = false,
    updateScheduleOnAmountLowered = false,
    updateAmountAfterPaymentWindow = false,
    imageReceipt = false,
)

val externalBankAccount = ExternalBankAccount(
    document = Document(value = DOCUMENT),
    bankNo = 341,
    bankISPB = "********",
    routingNo = 8888,
    accountNo = 1111,
    accountDv = "1",
    accountType = "CACC",
)

fun createDDAConfig(
    maxAddUser: Int = 1,
    maxAddUserAsync: Int = 1,
    ddaProvider: DDAProvider = DDAProvider.ARBI,
) = DDAConfig(
    maxAddUser = maxAddUser,
    maxAddUserAsync = maxAddUserAsync,
    provider = ddaProvider,
    activateWaitMinutes = 90,
)

data class FooRequest(val foo: String = "foo")
data class BarResponse<T>(val requestBody: T? = null)

data class TestHttpResponse(
    val requestHeaders: Map<String, Any>,
    val requestURI: URI,
    val requestBody: BarResponse<FooRequest>,
)

fun buildInvite(
    inviteStatus: InviteStatus = InviteStatus.PENDING,
    created: ZonedDateTime = getZonedDateTime(),
    memberDocument: Document = Document(DOCUMENT),
): Invite {
    val walletFixture = WalletFixture()
    val wallet = walletFixture.buildWallet(otherMembers = listOf(walletFixture.participant))

    return Invite(
        walletId = wallet.id,
        memberDocument = memberDocument.value,
        memberName = NAME,
        memberType = MemberType.ASSISTANT,
        permissions = MemberPermissions(
            viewBills = BillPermission.ONLY_BILLS_ADDED_BY_USER,
            scheduleBills = BillPermission.ONLY_BILLS_ADDED_BY_USER,
            founderContactsEnabled = true,
            viewBalance = true,
            notification = true,
        ),
        status = inviteStatus,
        validUntil = getLocalDate().plusDays(1),
        walletName = wallet.name,
        founderName = walletFixture.founder.name,
        founderDocument = walletFixture.founder.document,
        created = created,
        emailAddress = null,
    )
}

object TrackableBillFixture {
    fun create(
        amountCalculationModel: AmountCalculationModel = AmountCalculationModel.ANYONE,
        dueDate: LocalDate? = null,
        active: Boolean = true,
        billId: String? = null,
        isDda: Boolean = false,
    ): TrackableBill {
        val newBill = billAddedFicha.copy(amountCalculationModel = amountCalculationModel)
        return TrackableBill(
            billId = if (billId != null) BillId(billId) else newBill.billId,
            created = getZonedDateTime(),
            isActive = active,
            barCode = newBill.barcode,
            addedByDda = isDda,
            billType = BillType.FICHA_COMPENSACAO,
            dueDate = dueDate ?: newBill.dueDate,
            amount = newBill.amount,
            amountCalculationModel = newBill.amountCalculationModel,
            fineData = newBill.fineData ?: FineData(),
            interestData = newBill.interestData ?: InterestData(),
            discountData = newBill.discountData ?: DiscountData(),
            abatement = BigDecimal.ZERO,
        )
    }
}

fun getNotScheduledBillsEnoughToNotifyOnePixPay(wallet: Wallet): List<BillView> {
    return listOf(
        getActiveBill(
            walletId = wallet.id,
            accountId = wallet.founder.accountId,
        ).copy(
            billId = BillId(BILL_ID_2),
            dueDate = getLocalDate(),
            effectiveDueDate = getLocalDate(),
            paymentLimitTime = LocalTime.parse("18:00", timeFormat),
        ),
        getActiveBill(
            walletId = wallet.id,
            accountId = wallet.founder.accountId,
        ).copy(
            billId = BillId(BILL_ID_3),
            dueDate = getLocalDate(),
            effectiveDueDate = getLocalDate(),
            paymentLimitTime = LocalTime.parse("14:00", timeFormat),
        ),
    )
}

fun buildCreditCardChallenge(
    status: CreditCardChallengeStatus = CreditCardChallengeStatus.ACTIVE,
    expirationDate: ZonedDateTime = getZonedDateTime().plusDays(1),
    createdAt: ZonedDateTime = getZonedDateTime(),
    attempts: Int = 0,
    accountId: AccountId = AccountId(ACCOUNT_ID),
) =
    CreditCardChallenge(
        paymentMethodId = paymentMethodId,
        accountId = accountId,
        value = 1000,
        alternativeValue = 1001,
        attempts = attempts,
        status = status,
        createdAt = createdAt,
        updatedAt = getZonedDateTime(),
        expiresAt = expirationDate,
        authorizationCode = "any",
        acquirerOrderId = "order-id",
        alternativeAcquirerOrderId = "alternative-order-id",
    )

fun buildKycDossier() = KycDossier(
    gender = null,
    taxId = KycDossierTaxIdRegion(
        region = "SP-RJ",
    ),
    phones = listOf(),
    emails = listOf(),
    mep = KycDossierMepQuery(
        exposureLevel = null,
        celebrityLevel = null,
        unpopularityLevel = null,
    ),
    pep = PepQuery(
        at = getZonedDateTime(),
        result = PepQueryResult.NOT_PEP,
    ),
    officialData = KycDossierOfficialDocumentData(
        provider = "",
        name = "JOHN DOE",
        birthDate = LocalDate.parse("1990-10-10", DateTimeFormatter.ISO_LOCAL_DATE),
        socialName = null,
        hasObitIndication = false,
        deathYear = null,
        regular = true,
        status = "",
    ),
    sanctions = listOf(),
    globalSanctionsList = listOf(),
    motherName = null,
    fatherName = null,
)

fun createTransaction(
    status: TransactionStatus = TransactionStatus.PROCESSING,
    boletoSettlementStatus: BoletoSettlementStatus = BoletoSettlementStatus.AUTHORIZED,
    boletoSettlementGateway: FinancialServiceGateway? = null,
    boletoSettlementErrorCode: String? = null,
    paymentOperation: PaymentOperation? = null,
    settlementData: SettlementData = SettlementData(
        Bill.build(billAdded, billPaymentScheduled),
        0,
        concessionariaBill.amountTotal,
        createBoletoSettlementResult(status = boletoSettlementStatus, gateway = boletoSettlementGateway, errorCode = boletoSettlementErrorCode),
    ),
    calculationId: String? = null,
    nsu: Long? = 1,
    walletId: WalletId? = null,
    transactionId: TransactionId? = null,
    paymentData: PaymentData? = null,
    account: Account? = null,
    createdAt: ZonedDateTime? = null,
    type: TransactionType = TransactionType.BOLETO_PAYMENT,
) = Transaction(
    id = transactionId ?: TransactionId.build(),
    status = status,
    type = type,
    payer = account?.toPayer() ?: ACCOUNT.toPayer(),
    paymentData = paymentData ?: createSinglePaymentDataWithBalance(
        accountPaymentMethod = balance,
        amount = concessionariaBill.amountTotal,
        payment = paymentOperation,
        calculationId = calculationId,
    ),
    settlementData = settlementData,
    nsu = nsu ?: 1,
    actionSource = ActionSource.System,
    walletId = walletId ?: account?.defaultWalletId() ?: concessionariaBill.walletId,
    created = createdAt ?: getZonedDateTime(),
)

fun createBoletoSettlementResult(
    status: BoletoSettlementStatus = BoletoSettlementStatus.AUTHORIZED,
    transactionId: String = "1",
    gateway: FinancialServiceGateway? = null,
    errorCode: String? = null,
) =
    BoletoSettlementResult(
        gateway = gateway ?: FinancialServiceGateway.CELCOIN,
        status = status,
        bankTransactionId = transactionId,
        externalNsu = 1,
        externalTerminal = ACCOUNT_ID,
        errorCode = errorCode ?: "00",
        errorDescription = "",
    )

fun List<Any>.toArgumentsStream() = this.map { Arguments.of(it) }.stream()

fun message(arg: Any): Message = Message.builder().body(getObjectMapper().writeValueAsString(arg)).build()

fun getEnviromentSuffix(environment: String = FRIDAY_ENV): String {
    return when (environment) {
        FRIDAY_ENV -> ".friday"
        ME_POUPE_ENV -> ".me-poupe"
        GIGU_ENV -> ".gigu"
        else -> ".friday"
    }
}

fun createEmailTemplatesConfiguration(environment: String = FRIDAY_ENV): EmailTemplatesConfiguration {
    val suffix = getEnviromentSuffix(environment)

    return EmailTemplatesConfiguration(
        local = EmailTemplatesConfiguration.Local(
            emailVerificationTokenPath = "templates/email-verification-token$suffix",
            kycDossierFormPath = "templates/kyc/kyc-dossier$suffix",
            declarationOfResidencyFormPath = "templates/register/declaration-of-residency$suffix",
            accountStatementReportPath = "templates/statement-report$suffix",
            walletSummaryReportPath = "templates/wallet-summary-report$suffix",
            emailPasswordRecoveryTokenPath = "templates/password-recovery-token$suffix",
            statementPdf = "templates/statement-pdf$suffix",
            pixReceipt = "templates/bill-receipt-attachment$suffix",
            invoiceReceipt = "templates/bill-receipt-attachment$suffix",
            barcodeBillReceipt = "templates/bill-receipt-attachment$suffix",
            mailReceipt = "templates/mail-receipt$suffix",
            inform = "templates/inform$suffix",
            investmentReceipt = "templates/investment-receipt$suffix",
            investmentRedemptionFinished = "templates/mail-receipt-investment-redemption-finished$suffix",
        ),
        ses = EmailTemplatesConfiguration.SES(
            walletInviteAssistantWithoutAccount = "walletInviteAssistantWithoutAccount",
            walletInviteCollaboratorWithoutAccount = "walletInviteCollaboratorWithoutAccount",
            walletInviteReminderAssistantWithoutAccount = "walletInviteReminderAssistantWithoutAccount",
            walletInviteReminderCollaboratorWithoutAccount = "walletInviteReminderCollaboratorWithoutAccount",
        ),
    )
}

fun createTemplatesHtmlToImageConfiguration(environment: String = FRIDAY_ENV): TemplatesHtmlToImageConfiguration {
    val suffix = getEnviromentSuffix(environment = environment)

    return TemplatesHtmlToImageConfiguration(
        instagrammableInvestmentReceipt = "templates/instagrammable-investment-receipt$suffix",
        instagrammableExtraInvestmentReceipt = "templates/instagrammable-extra-investment-receipt$suffix",
        instagrammableGoalCompletedReceipt = "templates/instagrammable-goal-completed-receipt$suffix",
        instagrammableGoalEndDateReachedReceipt = "templates/instagrammable-goal-end-date-reached-receipt$suffix",
    )
}

fun defaultTransactionEntityConverter(billEventRepository: BillEventRepository, walletRepository: WalletRepository, transactionDynamo: TransactionDynamo) = TransactionEntityConverter(
    settlementOperationConverters = listOf(
        BoletoSettlementOperationConverter(),
        BankTransferSettlementOperationConverter(),
    ),
    paymentOperationConverters = listOf(
        FraudPreventionPaymentOperationConverter(),
        CreditCardAuthorizationPaymentOperationConverter(),
        BalanceAuthorizationPaymentOperationConverter(),
    ),
    settlementTargetConverters = listOf(
        BillSettlementTargetConverter(billEventRepository),
        CreditCardCashInSettlementTargetConverter(walletRepository),
    ),
)

fun <T> withMockedMetric(metric: AbstractMetrics, toBeExecuted: () -> T): T {
    val fnRef: KFunction2<Map<String, Any?>, Supplier<Number>, Unit> = metric::push
    val fnRef2: KFunction2<Map<String, Any?>, Number, Unit> = metric::push

    mockkStatic(fnRef, fnRef2)

    every { fnRef(any(), any()) } just Runs
    every { fnRef2(any(), any()) } just Runs

    return try {
        toBeExecuted()
    } finally {
        unmockkStatic(fnRef, fnRef2)
    }
}

val PDF_CONVERTER = FlyingSaucerPDFConverter(
    configuration = PDFConfiguration(
        fontFiles = listOf(
            "templates/fonts/Manrope-Bold.ttf",
            "templates/fonts/Manrope-ExtraBold.ttf",
            "templates/fonts/Manrope-ExtraLight.ttf",
            "templates/fonts/Manrope-Light.ttf",
            "templates/fonts/Manrope-Medium.ttf",
            "templates/fonts/Manrope-Regular.ttf",
            "templates/fonts/Manrope-SemiBold.ttf",
            "templates/fonts/Manrope.ttf",
            "templates/fonts/Montserrat-Black.ttf",
            "templates/fonts/Montserrat-BlackItalic.ttf",
            "templates/fonts/Montserrat-Bold.ttf",
            "templates/fonts/Montserrat-BoldItalic.ttf",
            "templates/fonts/Montserrat-ExtraBold.ttf",
            "templates/fonts/Montserrat-ExtraBoldItalic.ttf",
            "templates/fonts/Montserrat-ExtraLight.ttf",
            "templates/fonts/Montserrat-ExtraLightItalic.ttf",
            "templates/fonts/Montserrat-Italic.ttf",
            "templates/fonts/Montserrat-Light.ttf",
            "templates/fonts/Montserrat-LightItalic.ttf",
            "templates/fonts/Montserrat-Medium.ttf",
            "templates/fonts/Montserrat-MediumItalic.ttf",
            "templates/fonts/Montserrat-Regular.ttf",
            "templates/fonts/Montserrat-SemiBold.ttf",
            "templates/fonts/Montserrat-SemiBoldItalic.ttf",
            "templates/fonts/Montserrat-Thin.ttf",
            "templates/fonts/Montserrat-ThinItalic.ttf",
            "templates/fonts/Montserrat.ttf",
            "templates/fonts/Mulish-Black.ttf",
            "templates/fonts/Mulish-BlackItalic.ttf",
            "templates/fonts/Mulish-Bold.ttf",
            "templates/fonts/Mulish-BoldItalic.ttf",
            "templates/fonts/Mulish-ExtraBold.ttf",
            "templates/fonts/Mulish-ExtraBoldItalic.ttf",
            "templates/fonts/Mulish-ExtraLight.ttf",
            "templates/fonts/Mulish-ExtraLightItalic.ttf",
            "templates/fonts/Mulish-Italic.ttf",
            "templates/fonts/Mulish-Light.ttf",
            "templates/fonts/Mulish-LightItalic.ttf",
            "templates/fonts/Mulish-Medium.ttf",
            "templates/fonts/Mulish-MediumItalic.ttf",
            "templates/fonts/Mulish-Regular.ttf",
            "templates/fonts/Mulish-SemiBold.ttf",
            "templates/fonts/Mulish-SemiBoldItalic.ttf",
            "templates/fonts/Mulish.ttf",
            "templates/fonts/Quicksand-Bold.ttf",
            "templates/fonts/Quicksand-Light.ttf",
            "templates/fonts/Quicksand-Medium.ttf",
            "templates/fonts/Quicksand-Regular.ttf",
            "templates/fonts/Quicksand-SemiBold.ttf",
            "templates/fonts/Quicksand.ttf",
            "templates/fonts/Roboto-Black.ttf",
            "templates/fonts/Roboto-BlackItalic.ttf",
            "templates/fonts/Roboto-Bold.ttf",
            "templates/fonts/Roboto-BoldItalic.ttf",
            "templates/fonts/Roboto-ExtraBold.ttf",
            "templates/fonts/Roboto-ExtraBoldItalic.ttf",
            "templates/fonts/Roboto-ExtraLight.ttf",
            "templates/fonts/Roboto-ExtraLightItalic.ttf",
            "templates/fonts/Roboto-Italic.ttf",
            "templates/fonts/Roboto-Light.ttf",
            "templates/fonts/Roboto-LightItalic.ttf",
            "templates/fonts/Roboto-Medium.ttf",
            "templates/fonts/Roboto-MediumItalic.ttf",
            "templates/fonts/Roboto-Regular.ttf",
            "templates/fonts/Roboto-SemiBold.ttf",
            "templates/fonts/Roboto-SemiBoldItalic.ttf",
            "templates/fonts/Roboto-Thin.ttf",
            "templates/fonts/Roboto-ThinItalic.ttf",
            "templates/fonts/Roboto.ttf",
        ),
        folderName = "target",
        dotsPerPoint = 35f,
        dotsPerPixel = 20,
    ),
)